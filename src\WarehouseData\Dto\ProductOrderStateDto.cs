﻿namespace HlktechIoT.Dto
{
    public class ProductOrderStateDto
    {
        /// <summary>
        /// 是否生成SN
        /// </summary>
        public Boolean NeedSn { get; set; }

        /// <summary>
        /// 是否生成主Mac地址
        /// </summary>
        public Boolean NeedMac { get; set; }

        /// <summary>
        /// 是否生成副Mac地址
        /// </summary>
        public Boolean NeedMac1 { get; set; }

        /// <summary>
        /// 是否开启PCB周期
        /// </summary>
        public Boolean PCBCycle { get; set; }

        /// <summary>
        /// 是否开启屏蔽罩周期
        /// </summary>
        public Boolean ShieldCycle { get; set; }

        /// <summary>
        /// 是否开启主芯片周期
        /// </summary>
        public Boolean MainChipCycle { get; set; }
    }
}
