﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>生产测试数据</summary>
public partial class DeviceTestLogsModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>编号</summary>
    public Int64 DId { get; set; }

    /// <summary>批次号，建议每一批或定一个数字号</summary>
    public Int32 BatchNum { get; set; }

    /// <summary>模块型号</summary>
    public String? ModuleType { get; set; }

    /// <summary>模块id,允许重复</summary>
    public String? ModuleId { get; set; }

    /// <summary>固件版本</summary>
    public String? SdkVersion { get; set; }

    /// <summary>mac地址，允许重复</summary>
    public String DeviceMac { get; set; } = null!;

    /// <summary>去重信息</summary>
    public String? DeduplicateInfo { get; set; }

    /// <summary>检测时间</summary>
    public String? TestTime { get; set; }

    /// <summary>检测工厂</summary>
    public String? TestFactory { get; set; }

    /// <summary>检测工位</summary>
    public String? TestPosition { get; set; }

    /// <summary>监测数据</summary>
    public String? TestData { get; set; }

    /// <summary>销售信息</summary>
    public String? SaleInfo { get; set; }

    /// <summary>客户信息</summary>
    public String? CustomerInfo { get; set; }

    /// <summary>预留字段1</summary>
    public String? Reserve1 { get; set; }

    /// <summary>预留自段2</summary>
    public String? Reserve2 { get; set; }

    /// <summary>是否返厂</summary>
    public Boolean ReturnFactory { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IDeviceTestLogs model)
    {
        Id = model.Id;
        DId = model.DId;
        BatchNum = model.BatchNum;
        ModuleType = model.ModuleType;
        ModuleId = model.ModuleId;
        SdkVersion = model.SdkVersion;
        DeviceMac = model.DeviceMac;
        DeduplicateInfo = model.DeduplicateInfo;
        TestTime = model.TestTime;
        TestFactory = model.TestFactory;
        TestPosition = model.TestPosition;
        TestData = model.TestData;
        SaleInfo = model.SaleInfo;
        CustomerInfo = model.CustomerInfo;
        Reserve1 = model.Reserve1;
        Reserve2 = model.Reserve2;
        ReturnFactory = model.ReturnFactory;
        CreateTime = model.CreateTime;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
