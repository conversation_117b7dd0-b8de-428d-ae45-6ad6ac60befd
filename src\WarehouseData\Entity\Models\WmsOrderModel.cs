﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>出货订单</summary>
public partial class WmsOrderModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int64 Id { get; set; }

    /// <summary>ERP订单编号</summary>
    public String OrderId { get; set; } = null!;

    /// <summary>打单人</summary>
    public Int32 OrderingID { get; set; }

    /// <summary>打单时间</summary>
    public DateTime OrderingTime { get; set; }

    /// <summary>领料人</summary>
    public Int32 PickingID { get; set; }

    /// <summary>领料时间</summary>
    public DateTime PickingTime { get; set; }

    /// <summary>生产人</summary>
    public Int32 ProductionID { get; set; }

    /// <summary>生产时间</summary>
    public DateTime ProductionTime { get; set; }

    /// <summary>审核人</summary>
    public Int32 AuditingID { get; set; }

    /// <summary>审核时间</summary>
    public DateTime AuditingTime { get; set; }

    /// <summary>出货人</summary>
    public Int32 PackID { get; set; }

    /// <summary>出货时间</summary>
    public DateTime PackTime { get; set; }

    /// <summary>打包人</summary>
    public Int32 ShippingID { get; set; }

    /// <summary>打包时间</summary>
    public DateTime ShippingTime { get; set; }

    /// <summary>取消人</summary>
    public Int32 CancelID { get; set; }

    /// <summary>取消时间</summary>
    public DateTime CancelTime { get; set; }

    /// <summary>重复打单</summary>
    public Boolean IsDuplicate { get; set; }

    /// <summary>状态。0为正常，1为取消</summary>
    public Int16 Status { get; set; }

    /// <summary>结束完成</summary>
    public Boolean IsEnd { get; set; }

    /// <summary>是否有备注</summary>
    public Boolean HasRemark { get; set; }

    /// <summary>备注</summary>
    public String? Remark { get; set; }

    /// <summary>备注时间</summary>
    public DateTime RemarkTime { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IWmsOrder model)
    {
        Id = model.Id;
        OrderId = model.OrderId;
        OrderingID = model.OrderingID;
        OrderingTime = model.OrderingTime;
        PickingID = model.PickingID;
        PickingTime = model.PickingTime;
        ProductionID = model.ProductionID;
        ProductionTime = model.ProductionTime;
        AuditingID = model.AuditingID;
        AuditingTime = model.AuditingTime;
        PackID = model.PackID;
        PackTime = model.PackTime;
        ShippingID = model.ShippingID;
        ShippingTime = model.ShippingTime;
        CancelID = model.CancelID;
        CancelTime = model.CancelTime;
        IsDuplicate = model.IsDuplicate;
        Status = model.Status;
        IsEnd = model.IsEnd;
        HasRemark = model.HasRemark;
        Remark = model.Remark;
        RemarkTime = model.RemarkTime;
    }
    #endregion
}
