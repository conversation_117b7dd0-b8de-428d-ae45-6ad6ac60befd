using NewLife;
using NewLife.Data;

using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

using XCode;
using XCode.Cache;
using XCode.Shards;

namespace HlktechIoT.Entity;

public partial class ProductSnMac : CubeEntityBase<ProductSnMac>
{
    #region 对象操作
    static ProductSnMac()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(ProductOrdersId));

        // 按年分表
        Meta.ShardPolicy = new TimeShardPolicy(nameof(Id), Meta.Factory)
        {
            TablePolicy = "{0}_{1:yy}",
            Step = TimeSpan.FromDays(365),
        };

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add(new UserModule { AllowEmpty = false });
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add(new IPModule { AllowEmpty = false });

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (method == DataMethod.Insert && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
            if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
        }*/
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
        //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化ProductSnMac[生产SnMac]数据……");

    //    var entity = new ProductSnMac();
    //    entity.Id = 0;
    //    entity.ProductOrdersId = 0;
    //    entity.Sn = "abc";
    //    entity.Mac = "abc";
    //    entity.Mac1 = "abc";
    //    entity.ShorUrl = "abc";
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化ProductSnMac[生产SnMac]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    /// <summary>生产订单编号</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public ProductOrders? ProductOrders => Extends.Get(nameof(ProductOrders), k => ProductOrders.FindById(ProductOrdersId));

    /// <summary>生产订单编号</summary>
    [Map(nameof(ProductOrdersId), typeof(ProductOrders), "Id")]
    public String? ProductOrdersOrderId => ProductOrders?.OrderId;
    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="productOrdersId">生产订单编号</param>
    /// <param name="sn">设备Sn/DeviceName</param>
    /// <param name="mac">设备Mac地址</param>
    /// <param name="mac1">设备Mac地址</param>
    /// <param name="createTime">创建时间</param>
    /// <param name="start">编号开始</param>
    /// <param name="end">编号结束</param>
    /// <param name="key">关键字</param>
    /// <param name="isConsumed">是否被消费</param>
    /// <param name="isConfirmConsumed">是否确认被消费</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<ProductSnMac> Search(Int64 productOrdersId, String? sn, String? mac, String? mac1, DateTime createTime, DateTime start, DateTime end, String key, Int32 isConsumed, Int32 isConfirmConsumed, PageParameter page)
    {
        var exp = new WhereExpression();

        if (productOrdersId >= 0) exp &= _.ProductOrdersId == productOrdersId;
        if (!sn.IsNullOrEmpty()) exp &= _.Sn == sn;
        if (!mac.IsNullOrEmpty()) exp &= _.Mac == mac;
        if (!mac1.IsNullOrEmpty()) exp &= _.Mac1 == mac1;
        if(isConsumed >= 0) exp &= _.IsConsumed == isConsumed;
        if(isConfirmConsumed >= 0) exp &= _.IsConfirmConsumed == isConfirmConsumed;
        exp &= _.Id.Between(start, end, Meta.Factory.Snow);
        if (!key.IsNullOrEmpty()) exp &= _.Sn.Contains(key) | _.Mac.Contains(key) | _.Mac1.Contains(key);

        return FindAll(exp, page);
    }

    /// <summary>
    /// 根据生产订单Id集合查找
    /// </summary>
    /// <returns></returns>
    public static IList<ProductSnMac> FindAllByProductOrderIds(String Ids,Int32 UId)
    {
        var exp = new WhereExpression();

        var start = (DateTime.Now.Year + "-01-01").ToDateTime();
        var end = DateTime.Now;
        exp &= _.Id.Between(start, end, Meta.Factory.Snow);

        if (!Ids.IsNullOrWhiteSpace())
        {
            exp &= _.ProductOrdersId.In(Ids.Trim(','));
        }
        else if(UId >= 0)
        {
            var ids = ProductOrders.FindAll(ProductOrders._.Id.Between(start, end, Meta.Factory.Snow) & ProductOrders._.CreateUserID == UId).Select(e => e.Id);
            if (ids.Count() == 0) return [];
            exp &= _.ProductOrdersId.In(ids);
        }
        return FindAll(exp);
    }

    // Select Count(Id) as Id,Sn From DH_ProductSnMac Where CreateTime>'2020-01-24 00:00:00' Group By Sn Order By Id Desc limit 20
    static readonly FieldCache<ProductSnMac> _SnCache = new(nameof(Sn))
    {
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    };

    /// <summary>获取设备Sn_DeviceName列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    /// <returns></returns>
    public static IDictionary<String, String> GetSnList() => _SnCache.FindAllName();

    // Select Count(Id) as Id,Mac From DH_ProductSnMac Where CreateTime>'2020-01-24 00:00:00' Group By Mac Order By Id Desc limit 20
    static readonly FieldCache<ProductSnMac> _MacCache = new(nameof(Mac))
    {
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    };

    /// <summary>获取设备Mac地址列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    /// <returns></returns>
    public static IDictionary<String, String> GetMacList() => _MacCache.FindAllName();

    // Select Count(Id) as Id,Mac1 From DH_ProductSnMac Where CreateTime>'2020-01-24 00:00:00' Group By Mac1 Order By Id Desc limit 20
    static readonly FieldCache<ProductSnMac> _Mac1Cache = new(nameof(Mac1))
    {
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    };

    /// <summary>获取设备Mac地址列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    /// <returns></returns>
    public static IDictionary<String, String> GetMac1List() => _Mac1Cache.FindAllName();

    #endregion

    #region 业务操作
    public IProductSnMac ToModel()
    {
        var model = new ProductSnMac();
        model.Copy(this);

        return model;
    }

    /// <summary>
    /// 根据设备Sn/DeviceName获取SnMac列表
    /// </summary>
    /// <param name="sn">Sn/DeviceName</param>
    /// <param name="start"></param>
    /// <param name="end"></param>
    /// <returns>实体列表</returns>
    public static IList<ProductSnMac> FindAllOtherBySn(String? sn, DateTime start, DateTime end)
    {
        if (sn == null) return [];
        if (start <= DateTime.MinValue) return [];
        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now;
        }
        return FindAll(_.Sn == sn & _.Id.Between(start, end, Meta.Factory.Snow), new PageParameter { PageSize = 1 });
    }

    /// <summary>
    /// 根据设备主Mac地址获取SnMac列表
    /// </summary>
    /// <param name="mac"></param>
    /// <param name="start"></param>
    /// <param name="end"></param>
    /// <returns>实体列表</returns>
    public static IList<ProductSnMac> FindAllOtherByMac(String? mac, DateTime start, DateTime end)
    {
        if (mac == null) return [];
        if (start <= DateTime.MinValue) return [];
        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now;
        }
        return FindAll(_.Mac == mac & _.Id.Between(start, end, Meta.Factory.Snow), new PageParameter { PageSize = 1 });
    }

    /// <summary>
    /// 根据设备副Mac地址获取SnMac列表
    /// </summary>
    /// <param name="mac1"></param>
    /// <param name="start"></param>
    /// <param name="end"></param>
    /// <returns>实体列表</returns>
    public static IList<ProductSnMac> FindAllOtherByMac1(String? mac1, DateTime start, DateTime end)
    {
        if (mac1 == null) return [];
        if (start <= DateTime.MinValue) return [];
        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now;
        }
        return FindAll(_.Mac1 == mac1 & _.Id.Between(start, end, Meta.Factory.Snow), new PageParameter { PageSize = 1 });
    }

    /// <summary>
    /// 根据设备生产订单编号获取SnMac列表
    /// </summary>
    /// <param name="productOrdersId">生产订单编号</param>
    /// <param name="start"></param>
    /// <param name="end"></param>
    /// <param name="count">取值数据</param>
    /// <param name="isConsumed">是否已被消费</param>
    /// <param name="selects">字段</param>
    /// <returns>实体列表</returns>
    public static IList<ProductSnMac> FindAllByOtherProductOrdersId(Int64 productOrdersId, DateTime start, DateTime end, Int32 count, String? selects = null, Boolean isConsumed = false)
    {
        if (productOrdersId <= 0) return [];
        if (start <= DateTime.MinValue) return [];
        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now;
        }

        return FindAll(_.ProductOrdersId == productOrdersId & _.IsConsumed == isConsumed & _.Id.Between(start, end, Meta.Factory.Snow), new PageParameter { PageSize = count }, selects);
    }

    /// <summary>
    /// 查找超时未验证的 SN
    /// </summary>
    /// <param name="productOrdersId">生产订单编号</param>
    /// <param name="start">开始时间</param>
    /// <param name="end">结束时间</param>
    /// <param name="timeoutThreshold">超时阈值</param>
    /// <param name="selects">字段</param>
    /// <returns>实体列表</returns>
    public static IList<ProductSnMac> FindTimedOutSnMacs(Int64 productOrdersId, DateTime start, DateTime end, DateTime timeoutThreshold, String? selects = null)
    {
        if (productOrdersId <= 0) return [];
        if (start <= DateTime.MinValue) return [];
        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now;
        }

        var exp = _.ProductOrdersId == productOrdersId & _.IsValidate == false & _.IsConsumed == true &  _.ConsumedTime < timeoutThreshold;
        exp &= _.Id.Between(start, end, Meta.Factory.Snow);

        return FindAll(exp, new PageParameter { PageSize = 0 }, selects);
    }

    /// <summary>
    /// 根据订单号获取剩余数量
    /// </summary>
    /// <param name="productOrdersId">生产订单编号</param>
    /// <param name="start"></param>
    /// <param name="end"></param>
    /// <returns>实体列表</returns>
    public static Int64 FindCountByProductOrdersId(Int64 productOrdersId, DateTime start, DateTime end)
    {
        if (productOrdersId <= 0) return 0;
        if (start <= DateTime.MinValue) return 0;
        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now;
        }
        return FindCount(_.Id.Between(start, end, Meta.Factory.Snow) & _.ProductOrdersId == productOrdersId & _.IsValidate == false);
    }

    /// <summary>
    /// 根据设备主Mac地址获取SnMac列表
    /// </summary>
    /// <param name="sn"></param>
    /// <param name="mac"></param>
    /// <param name="mac1"></param>
    /// <param name="start"></param>
    /// <param name="end"></param>
    /// <returns>实体列表</returns>
    public static IList<ProductSnMac> FindAllOther(String? sn, String? mac, String? mac1, DateTime start, DateTime end)
    {
        if (sn.IsNullOrWhiteSpace() && mac.IsNullOrWhiteSpace() && mac1.IsNullOrWhiteSpace()) return [];
        if (start <= DateTime.MinValue) return [];
        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now;
        }

        var exp = new WhereExpression();
        if (!sn.IsNullOrWhiteSpace())
        {
            exp += _.Sn == sn;
        }
        if (!mac.IsNullOrWhiteSpace())
        {
            exp += (_.Mac == mac | _.Mac1 == mac );
        }
        if (!mac1.IsNullOrWhiteSpace())
        {
            exp += (_.Mac1 == mac1 | _.Mac == mac1);
        }

        return FindAll(exp & _.Id.Between(start, end, Meta.Factory.Snow), new PageParameter { PageSize = 1 });
    }

    #endregion
}