﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>生产测试数据</summary>
public partial interface IDeviceTestLogs
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>编号</summary>
    Int64 DId { get; set; }

    /// <summary>批次号，建议每一批或定一个数字号</summary>
    Int32 BatchNum { get; set; }

    /// <summary>模块型号</summary>
    String? ModuleType { get; set; }

    /// <summary>模块id,允许重复</summary>
    String? ModuleId { get; set; }

    /// <summary>固件版本</summary>
    String? SdkVersion { get; set; }

    /// <summary>mac地址，允许重复</summary>
    String DeviceMac { get; set; }

    /// <summary>去重信息</summary>
    String? DeduplicateInfo { get; set; }

    /// <summary>检测时间</summary>
    String? TestTime { get; set; }

    /// <summary>检测工厂</summary>
    String? TestFactory { get; set; }

    /// <summary>检测工位</summary>
    String? TestPosition { get; set; }

    /// <summary>监测数据</summary>
    String? TestData { get; set; }

    /// <summary>销售信息</summary>
    String? SaleInfo { get; set; }

    /// <summary>客户信息</summary>
    String? CustomerInfo { get; set; }

    /// <summary>预留字段1</summary>
    String? Reserve1 { get; set; }

    /// <summary>预留自段2</summary>
    String? Reserve2 { get; set; }

    /// <summary>是否返厂</summary>
    Boolean ReturnFactory { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
