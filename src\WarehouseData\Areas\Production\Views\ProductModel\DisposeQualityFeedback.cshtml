﻿@using Pek.Configs
@{
    var url = OssSetting.Current.QiNiu.Domain;
}
<style>
    /* 滚动区高度 = 父容器高 - 输入区高 */
    .scroll-body {
        max-height: calc(100vh - 220px); /* 220 可自己调整 */
        overflow-y: auto;
        border-bottom: 1px solid #e6e6e6; /* 与 layui table 线条对齐 */
    }

    /* 吸底输入区 */
    .reply-bar {
        position: sticky;
        bottom: 0;
        background: #fff;
        border-top: 1px solid #e6e6e6;
        padding: 8px 0;
    }

    .reply-action {
        text-align: right;
        margin-top: 8px;
    }

    .video-box {
        position: relative;
        display: inline-block; /* 让多个视频在一行 */
    }

        .video-box video {
            width: 200px;
            height: 150px;
            object-fit: cover;
            border-radius: 4px;
        }

    /* 图标永远居中 */
    .play-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        margin: -14px 0 0 -14px; /* 28px/2 = 14px */
        font-size: 28px;
        color: #fff;
        z-index: 2;
        pointer-events: none;
    }

</style>
<div class="layui-bg-gray" style="padding: 16px;">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">@Model.Name</div>
                <div class="layui-card-body">
                    @*                     <div class="scroll-body"></div> *@
                    <table class="layui-table" lay-skin="line">
                        <tbody>
                            <tr>
                                <td style="font-size:12px">@Model.CreateUser &nbsp;&nbsp;&nbsp; @Model.CreateTime</td>
                            </tr>
                            <tr>
                                <td>
                                    <div>
                                        <div style="color:black">@Model.Content</div>
                                        <div style="display:flex;flex-wrap:wrap; gap:6px;">
                                            @if (Model.PicPath != null && Model.PicPath != "")
                                            {
                                                @foreach (var item in Model.PicPath.Split(","))
                                                {
                                                    var path = (url + "/" + item).Replace("\\", "/");
                                                    <img src="@path" style="max-width:200px" height="150">
                                                }
                                            }
                                            @if (Model.VideoPath != null && Model.VideoPath != "")
                                            {
                                                @foreach (var item in Model.VideoPath.Split(","))
                                                {
                                                    var path = (url + "/" + item).Replace("\\", "/");
                                                    <div class="video-box">
                                                        <video src="@path" width="200" height="150"></video>
                                                        <!-- 悬浮播放图标 -->
                                                        <i class="layui-icon layui-icon-play play-icon"></i>
                                                    </div>
                                                }
                                            }
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            @foreach (var modelEx in ViewBag.data)
                            {
                                <tr>
                                    <td style="font-size:12px">@modelEx.CreateUser &nbsp;&nbsp;&nbsp; @modelEx.CreateTime</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div>
                                            <div style="color:black">@modelEx.Content</div>
                                            <div style="display:flex;flex-wrap:wrap; gap:6px;">
                                                @if (modelEx.PicPath != null && modelEx.PicPath != "")
                                                {
                                                    @foreach (var item in modelEx.PicPath.Split(","))
                                                    {
                                                        var path = (url + "/" + item).Replace("\\", "/");
                                                        <img src="@path" style="max-width:200px" height="150">
                                                    }
                                                }
                                                @if (modelEx.VideoPath != null && modelEx.VideoPath != "")
                                                {
                                                    @foreach (var item in modelEx.VideoPath.Split(","))
                                                    {
                                                        var path = (url + "/" + item).Replace("\\", "/");
                                                        <div class="video-box">
                                                            <video src="@path"></video>
                                                            <!-- 悬浮播放图标 -->
                                                            <i class="layui-icon layui-icon-play play-icon"></i>
                                                        </div>
                                                    }
                                                }
                                            </div>
                                            <div>
                                                @if (modelEx.FilePath != null && modelEx.FilePath != "")
                                                {
                                                    @foreach (var item in modelEx.FilePath.Split(","))
                                                    {
                                                        var path = (url + "/" + item).Replace("\\", "/");
                                                        <a href="@path" target="_blank" style="color:dodgerblue">@path</a>
                                                    }
                                                }
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            }

                        </tbody>
                    </table>

                    <div class="reply-bar">
                        <!-- 预览区 -->
                        <div id="previewBox">
                            <!-- 图片/视频同一排 -->
                            <div id="mediaRow" style="display:flex;flex-wrap:wrap;align-items:flex-start;gap:6px;"></div>

                            <!-- 附件换行 -->
                            <div id="fileRow" style="margin-top:8px;"></div>
                        </div>

                        <!-- 上传图标区：放在 textarea 上方 -->
                        <div style="margin-bottom:6px;">
                            <!-- 图片 -->
                            <i class="layui-icon layui-icon-picture upload-icon"
                               data-type="img" title="@T("上传图片")" style="font-size:20px;color:#1E9FFF;cursor:pointer;"></i>
                            &nbsp;
                            <!-- 视频 -->
                            <i class="layui-icon layui-icon-video upload-icon"
                               data-type="video" title="@T("上传视频")" style="font-size:20px;color:#1E9FFF;cursor:pointer;"></i>
                            &nbsp;
                            <!-- 附件 -->
                            <i class="layui-icon layui-icon-file upload-icon"
                               data-type="file" title="@T("上传附件")" style="font-size:20px;color:#1E9FFF;cursor:pointer;"></i>
                        </div>

                        <textarea class="layui-textarea" placeholder="@T("请输入")" id="content"></textarea>

                        <!-- 三个隐藏域分别存三类路径 -->
                        <input type="hidden" id="imgPath" />
                        <input type="hidden" id="videoPath" />
                        <input type="hidden" id="filePath" />

                        <div class="reply-action">
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="btnNo">@T("不予处理")</button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="btnSend">@T("发送")</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script asp-location="Footer">
    layui.use(['upload','dgcommon','layer'],function(){
        var upload = layui.upload;
        var os = layui.dgcommon;
        var layer = layui.layer;

        // 三个独立集合
        var imgArr   = [];
        var videoArr = [];
        var fileArr  = [];

        // 创建上传实例（统一给图标绑定）
        $('.upload-icon').each(function () {
            var $icon = $(this);
            var type  = $icon.data('type');
            var cfg   = {
                elem: this,          // 图标本身作为触发元素
                url: '@Url.Action("UploadFiles")',
                multiple: true,
                done: function (res) {
                    if (res.success) {
                        os.success('@T("上传成功")');
                        var item = res.data;
                        switch (type) {
                            case 'img':
                                imgArr.push(item.FilePath);
                                renderPreview(item.FilePath, 'img');
                                break;
                            case 'video':
                                videoArr.push(item.FilePath);
                                renderPreview(item.FilePath, 'video');
                                break;
                            case 'file':
                                fileArr.push(item.FilePath);
                                renderPreview(item.FilePath, 'file');
                                break;
                        }
                        refreshHiddenInputs();
                    } else {
                        os.warning(res.msg);
                    }
                }
            };
            console.log("type===",type)
            switch (type) {
                case 'img':
                    cfg.accept = 'images';
                    break;
                case 'video':
                    cfg.accept = 'video';
                    break;
                case 'file':
                    cfg.accept = 'file';
                    break;
            }
            upload.render(cfg);
        });

        // 渲染预览
        function renderPreview(path, type) {
            var url  = '@OssSetting.Current.QiNiu.Domain' + '/' + path;
            var tpl  = '';      // 最终 html
            var $box = null;    // 要插入的容器

            if (type === 'img') {
                tpl  =  '<div class="prev-item" data-path="' + path + '" data-type="' + type + '" style="position:relative;">' +
                            '<img src="' + url + '" style="width:80px;height:80px;object-fit:cover;border-radius:2px;">' +
                            '<i class="layui-icon layui-icon-close-fill del-file"></i>' +
                        '</div>';
                $box = $('#mediaRow');
            } else if (type === 'video') {
                tpl  =  '<div class="prev-item" data-path="' + path + '" data-type="' + type + '" style="position:relative;">' +
                            '<video src="' + url + '" style="width:80px;height:80px;object-fit:cover;border-radius:2px;" controls muted></video>' +
                            '<i class="layui-icon layui-icon-close-fill del-file"></i>' +
                        '</div>';
                $box = $('#mediaRow');
            } else { // file
                var name = path.split('/').pop();
                tpl  =  '<div class="prev-item" data-path="' + path + '" data-type="' + type + '">' +
                            '<a href="' + url + '" target="_blank" style="color:#01AAED;">' + name + '</a>' +
                            '<i class="layui-icon layui-icon-close-fill del-file"></i>' +
                        '</div>';
                $box = $('#fileRow');
            }

            $box.append(tpl);
        }

        // 更新隐藏域
        function refreshHiddenInputs() {
            $('#imgPath').val(imgArr.join(','));
            $('#videoPath').val(videoArr.join(','));
            $('#filePath').val(fileArr.join(','));
        }

        // 删除
        $(document).on('click', '.del-file', function () {
            var $item = $(this).closest('.prev-item');
            var path  = $item.data('path');
            var type  = $item.data('type');

            switch (type) {
                case 'img':   imgArr   = imgArr.filter(p => p !== path);   break;
                case 'video': videoArr = videoArr.filter(p => p !== path); break;
                case 'file':  fileArr  = fileArr.filter(p => p !== path);  break;
            }
            $item.remove();
            refreshHiddenInputs();
        });

        $("#btnSend").click(function(){
            var content = $('#content').val();
            var imgPath = $('#imgPath').val();
            var videoPath = $('#videoPath').val();
            var filePath = $('#filePath').val();
            $.post('@Url.Action("DisposeQualityFeedback")',{Id:'@Model.Id',content,filePath,imgPath,videoPath},function(res){
                if(res.success){
                    os.success(res.msg);
                    window.location.reload();
                }else{
                    os.warning(res.msg);
                }
            })
        })

        $("#btnNo").click(function(){
            $.post('@Url.Action("EditQualityFeedbackStatus")',{Id:'@Model.Id'},function(res){
                if(res.success){
                    os.success(res.msg);
                    window.location.reload();
                }else{
                    os.warning(res.msg);
                }
            })
        })


        //图片点击
        $(document).on('click', 'img[src]', function () {
            var src = $(this).attr('src');
            layer.open({
                type: 1,
                title: false,
                closeBtn: 1,
                area: ['auto', 'auto'],       // 按原图大小
                shadeClose: true,
                // 关键：给内容包一层带内边距的 div
                content: `<div style="padding:2rem;">
                             <img src="${src}" style="display:block;max-width:90vw;max-height:90vh;">
                          </div>`
            });
        });
        // 视频点击 -> 弹窗播放
        $(document).on('click', 'video[src]', function () {
            var src = $(this).attr('src');
            layer.open({
                type: 1,
                title: false,
                closeBtn: 1,
                area: ['80vw', '80vh'],
                shadeClose: true,
                content: `<video src="${src}" controls autoplay style="width:100%;height:100%;background:#000;"></video>`
            });
        });

        // 监听 textarea 的回车
        $('#content').on('keydown', function (e) {
            if (e.keyCode === 13 && !e.shiftKey) {   // Enter 且没有按 Shift
                e.preventDefault();                  // 阻止默认换行
                $('#btnSend').trigger('click');      // 模拟点击发送
            }
        });

        /* 等页面所有资源（图片、视频、字体）加载完毕后再滚到底 */
        window.addEventListener('load', function () {
            // 保险起见再延迟一帧，确保浏览器已渲染完
            requestAnimationFrame(function () {
                window.scrollTo({
                    top:  document.body.scrollHeight,
                    left: 0,
                    behavior: 'auto'   // 如果想平滑滚动改为 'smooth'
                });
            });
        });
    })
</script>