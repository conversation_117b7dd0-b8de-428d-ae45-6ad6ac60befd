﻿using DG.Web.Framework;

using DH.Models;

using HlktechIoT.Dto;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using Pek.Models;
using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>
/// 菜单列表
/// </summary>
[DisplayName("菜单管理")]
[Description("菜单管理列表，管理项目下的所有菜单")]
[AdminArea]
[DHMenu(78,ParentMenuName = "System", CurrentMenuUrl = "~/{area}/Menu", CurrentMenuName = "MenuList", LastUpdate = "20240124")]
public class MenuController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 78;

    /// <summary>
    /// 菜单列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("菜单列表")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>菜单信息列表</summary>
    /// <returns></returns>
    [DisplayName("菜单信息列表")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult GetMenuList()
    {
        var fact = ManageProvider.Menu;
        var menus = fact!.Root.Childs;  // 获取一级菜单

        var list = new List<MenuDto>();
        foreach (var item in menus)
        {
            var model = new MenuDto
            {
                id = item.ID,
                name = $"{item.DisplayName}({item.Name})",
                menuUrl = item.Url,
                sort = item.Sort,
                enable = item.Visible,
            };

            if (!item.Icon.IsNullOrWhiteSpace())
            {
                model.icon = $"layui-icon iconfont {item.Icon}";
            }

            if (item.Childs != null && item.Childs.Any())
            {
                model.powerType = 0;
            }
            else
            {
                model.powerType = 1;
            }
            list.Add(model);

            if (item.Childs != null && item.Childs.Any())
            {
                foreach (var item1 in item.Childs)
                {
                    var model1 = new MenuDto
                    {
                        id = item1.ID,
                        name = $"{item1.DisplayName}({item1.Name})",
                        menuUrl = item1.Url,
                        sort = item1.Sort,
                        parentId = item.ID,
                        enable = item1.Visible,
                    };

                    if (!item1.Icon.IsNullOrWhiteSpace())
                    {
                        model1.icon = $"layui-icon iconfont {item1.Icon}";
                    }

                    if (item1.Childs != null && item1.Childs.Any())
                    {
                        model1.powerType = 0;
                    }
                    else
                    {
                        model1.powerType = 1;
                    }

                    list.Add(model1);

                    if (item1.Childs != null && item1.Childs.Any())
                    {
                        foreach (var item2 in item1.Childs)
                        {
                            var model2 = new MenuDto
                            {
                                id = item2.ID,
                                name = $"{item2.DisplayName}({item2.Name})",
                                menuUrl = item2.Url,
                                sort = item2.Sort,
                                parentId = model1.id,
                                enable = item2.Visible,
                            };

                            if (!item2.Icon.IsNullOrWhiteSpace())
                            {
                                model2.icon = $"layui-icon iconfont {item2.Icon}";
                            }

                            if (item2.Childs != null && item2.Childs.Any())
                            {
                                model2.powerType = 0;
                            }
                            else
                            {
                                model2.powerType = 1;
                            }

                            list.Add(model2);
                        }
                    }
                }
            }

        }

        return Json(new { code = 0, msg = "...", count = menus.Count, data = list });
    }

    ///// <summary>添加菜单</summary>
    ///// <returns></returns>
    //[DisplayName("添加菜单")]
    //[EntityAuthorize(PermissionFlags.Insert)]
    //public IActionResult AddMenu()
    //{
    //    return View();
    //}

    /// <summary>修改菜单</summary>
    /// <returns></returns>
    [DisplayName("修改菜单")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult EditMenu(Int32 Id)
    {
        var model = XCode.Membership.Menu.FindByID(Id);
        if (model == null)
        {
            return Content(GetResource("菜单不存在"));
        }

        return View(model);
    }

    /// <summary>修改菜单</summary>
    /// <returns></returns>
    [DisplayName("修改菜单")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult ModifyState(Int32 Id, Boolean status)
    {
        var result = new DResult();

        var model = XCode.Membership.Menu.FindByID(Id);
        if (model == null)
        {
            result.success = false;
            return Json(result);
        }
        model.Visible = status;
        model.Update();

        result.msg = GetResource("修改成功");
        result.success = true;
        return Json(result);
    }

    /// <summary>修改菜单</summary>
    /// <returns></returns>
    [DisplayName("修改菜单")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult EditMenu(Int32 Id, String Icon, String DisplayName, String Name, Int32 Sort)
    {
        var result = new DResult();

        var model = XCode.Membership.Menu.FindByID(Id);
        if (model == null)
        {
            result.msg = GetResource("菜单不存在");
            return Json(result);
        }

        model.Icon = Icon;
        model.DisplayName = DisplayName;
        model.Name = Name;
        model.Sort = Sort;
        model.Update();

        result.msg = GetResource("修改成功");
        result.success = true;
        return Json(result);
    }

    /// <summary>删除菜单</summary>
    /// <returns></returns>
    [DisplayName("删除菜单")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult Delete(Int32 Id)
    {
        var model = XCode.Membership.Menu.FindByID(Id);
        if (model.Childs.Any())
        {
            return Json(new DResult() { success = false, msg = GetResource("有子菜单，请先调整子菜单") });
        }

        model.Delete();

        return Json(new DResult() { success = true, msg = GetResource("删除成功") });
    }
}