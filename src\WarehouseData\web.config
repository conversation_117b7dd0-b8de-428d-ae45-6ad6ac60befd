﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<system.webServer>
		<modules>
			<!-- Remove WebDAV module so that we can make DELETE requests -->
			<remove name="WebDAVModule" />
		</modules>
		<handlers>
			<!-- Remove WebDAV module so that we can make DELETE requests -->
			<remove name="WebDAV" />
			<remove name="aspNetCore"/>
			<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified"/>
		</handlers>
		<!-- When deploying on Azure, make sure that "dotnet" is installed and the path to it is registered in the PATH environment variable or specify the full path to it -->
		<aspNetCore requestTimeout="23:00:00" processPath="%LAUNCHER_PATH%" arguments="%LAUNCHER_ARGS%" forwardWindowsAuthToken="false" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" startupTimeLimit="3600" hostingModel="InProcess">
			<handlerSettings>
				<handlerSetting name="experimentalEnableShadowCopy" value="true" />
				<handlerSetting name="shadowCopyDirectory" value="ShadowCopyDirectory/" />
				<!-- Only enable handler logging if you encounter issues-->
				<!--<handlerSetting name="debugFile" value=".\logs\aspnetcore-debug.log" />-->
				<!--<handlerSetting name="debugLevel" value="FILE,TRACE" />-->
			</handlerSettings>
		</aspNetCore>
		<httpProtocol>
			<customHeaders>
				<remove name="X-Powered-By" />
				<!-- Protects against XSS injections. ref.: https://www.veracode.com/blog/2014/03/guidelines-for-setting-security-headers/ -->
				<add name="X-XSS-Protection" value="1; mode=block" />
				<!-- Protects against Clickjacking attacks. ref.: http://stackoverflow.com/a/22105445/1233379 -->
				<add name="X-Frame-Options" value="SAMEORIGIN" />
				<!-- Protects against MIME-type confusion attack. ref.: https://www.veracode.com/blog/2014/03/guidelines-for-setting-security-headers/ -->
				<add name="X-Content-Type-Options" value="nosniff" />
				<!-- Protects against Clickjacking attacks. ref.: https://www.owasp.org/index.php/HTTP_Strict_Transport_Security_Cheat_Sheet -->
				<add name="Strict-Transport-Security" value="max-age=31536000; includeSubDomains" />
				<!-- CSP modern XSS directive-based defence, used since 2014. ref.: http://content-security-policy.com/ -->
				<add name="Content-Security-Policy" value="default-src 'self'; connect-src *; font-src * data:; frame-src *; img-src * data:; media-src *; object-src *; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline';" />
				<!-- Prevents from leaking referrer data over insecure connections. ref.: https://scotthelme.co.uk/a-new-security-header-referrer-policy/ -->
				<add name="Referrer-Policy" value="same-origin" />
				<!-- Permissions-Policy is a new header that allows a site to control which features and APIs can be used in the browser. ref.: https://w3c.github.io/webappsec-permissions-policy/ -->
				<add name="Permissions-Policy" value="accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=*, usb=()" />
			</customHeaders>
		</httpProtocol>
	</system.webServer>
</configuration>