{"name": "layui", "version": "2.9.8", "description": "Classic modular Front-End UI library", "keywords": ["layui", "components", "front-end", "framework", "library", "ui", "web"], "homepage": "https://layui.dev", "license": "MIT", "bugs": {"url": "https://github.com/layui/layui/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/layui/layui.git"}, "main": "dist/layui.js", "files": ["dist"], "scripts": {"build": "gulp", "release": "npm run build && npm rum release-zip", "release-zip": "gulp release"}, "devDependencies": {"gulp": "^4.0.2", "gulp-uglify": "^3.0.2", "gulp-clean-css": "^4.3.0", "gulp-concat": "^2.6.1", "gulp-replace": "^1.1.4", "gulp-header": "^2.0.9", "gulp-sourcemaps": "^3.0.0", "gulp-zip": "^5.1.0", "del": "^6.1.1", "minimist": "^1.2.8"}, "dependencies": {}}