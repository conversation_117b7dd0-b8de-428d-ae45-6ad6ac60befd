{"version": 3, "sources": ["ui/default/skin.mobile.css"], "names": [], "mappings": ";;;;;;AAOA,gCACE,IAAK,QACL,QAAS,MAEX,kCACE,OAAQ,EACR,WAAY,QACZ,OAAQ,QACR,MAAO,KACP,YAAa,EACb,OAAQ,EACR,QAAS,EACT,QAAS,EACT,4BAA6B,YAE7B,YAAa,KACb,YAAa,OAEf,wCACE,QAAS,QAEX,mCACE,QAAS,QAEX,2CACE,QAAS,QAEX,sCACE,QAAS,QAEX,0CACE,QAAS,QAEX,wCACE,QAAS,QAEX,yCACE,QAAS,QAEX,kCACE,QAAS,QAEX,oCACE,QAAS,QAEX,4CACE,QAAS,QAEX,0CACE,QAAS,QAEX,uCACE,QAAS,QAEX,uCACE,QAAS,QAEX,kCACE,QAAS,QAEX,oCACE,QAAS,QAEX,mCACE,QAAS,QAEX,sCACE,QAAS,QAEX,kCACE,QAAS,QAEX,wCACA,2CACE,QAAS,QAEX,kCACE,QAAS,QAEX,kCACE,QAAS,QAEX,0CACE,QAAS,QAEX,wCACE,QAAS,QAGX,sCADA,2CAEE,QAAS,QAEX,2CACE,QAAS,QAEX,2CACE,QAAS,QAGX,2CADA,2CAEE,YAAa,WACb,UAAW,IAEb,uCACE,QAAS,QAEX,kCACE,QAAS,QAEX,qCAEE,QAAS,WACT,YAAa,WACb,UAAW,IACX,YAAa,IAEf,gCACE,QAAS,KACT,YAAa,IAEf,gCACE,QAAS,KACT,YAAa,IAEf,gCACE,QAAS,KACT,YAAa,IAEf,8DACE,YAAa,OACb,QAAS,KACT,gBAAiB,OACjB,WAAY,kBACZ,OAAQ,KACR,SAAU,SACV,IAAK,EACL,MAAO,KAET,gGACE,YAAa,OACb,cAAe,IACf,QAAS,KACT,eAAgB,OAChB,YAAa,WACb,UAAW,IACX,gBAAiB,cAEnB,iHACE,YAAa,OACb,QAAS,KACT,gBAAiB,OACjB,cAAe,IACf,OAAQ,MACR,MAAO,MAET,oIACE,YAAa,OACb,QAAS,KACT,gBAAiB,OACjB,eAAgB,OAChB,UAAW,IAEb,gDACE,oIACE,UAAW,OAGf,kKACE,YAAa,OACb,QAAS,KACT,gBAAiB,OACjB,cAAe,IACf,OAAQ,MACR,MAAO,MACP,iBAAkB,KAClB,MAAO,QAET,0KACE,QAAS,QACT,YAAa,cAAgB,CAAE,WAEjC,8MACE,QAAS,EAEX,mEACE,WAAY,KACZ,OAAQ,KACR,OAAQ,EACR,QAAS,KACT,eAAgB,OAChB,KAAM,EACN,SAAU,MACV,MAAO,EACP,IAAK,EAEP,yEACE,SAAU,SAEZ,gEACE,QAAS,KACT,UAAW,EAEb,uEACE,QAAS,eACT,UAAW,EACX,OAAQ,eAEV,sCACE,SAAU,OAEZ,sFACE,WAAY,KAEd,0BACE,WAAY,KACZ,QAAS,KACT,KAAM,EAAE,EAAE,KACV,QAAS,EAEX,kDACE,YAAa,OACb,iBAAkB,KAClB,cAAe,IAAI,MAAM,KACzB,QAAS,KACT,KAAM,EACN,OAAQ,MACR,MAAO,KAGT,qHACE,YAAa,OACb,QAAS,KACT,OAAQ,KACR,YAAa,EAEf,yHACE,YAAa,OACb,QAAS,KACT,OAAQ,KACR,KAAM,EAER,mJACE,WAAY,QAEd,6JACE,UAAW,EAEb,wJACE,aAAc,KACd,cAAe,KAEjB,sLACE,YAAa,OACb,QAAS,KACT,OAAQ,IACR,YAAa,IACb,aAAc,IAEhB,6NACE,WAAY,QACZ,MAAO,KAET,mIACA,kIACE,WAAY,QACZ,MAAO,QAKT,+GACE,YAAa,OACb,QAAS,KACT,OAAQ,KACR,KAAM,EACN,eAAgB,KAChB,YAAa,KAIf,iJACE,QAAS,KACT,WAAY,MACZ,SAAU,OACV,aAAc,EACd,cAAe,EACf,SAAU,SACV,MAAO,KAET,yLACE,QAAS,KACT,OAAQ,KACR,WAAY,KAAK,uBAA2B,KAC5C,MAAO,KAET,kOACE,QAAS,KACT,KAAM,EAAE,EAAE,KACV,gBAAiB,cACjB,MAAO,KAET,wOACE,YAAa,WAEf,kQACE,QAAS,KACT,UAAW,EACX,SAAU,SAEZ,oSACE,mBAAoB,OAChB,WAAY,OAChB,WAAY,QACZ,OAAQ,KACR,cAAe,IACf,MAAO,KACP,UAAW,KACX,YAAa,IACb,OAAQ,KACR,cAAe,IACf,SAAU,SACV,MAAO,EAET,yUACE,QAAS,KAGX,4PADA,gQAEE,YAAa,OACb,QAAS,KAGX,oQADA,wQAEE,YAAa,OACb,QAAS,KACT,YAAa,IACb,OAAQ,KACR,aAAc,KACd,cAAe,KAGjB,+SADA,mTAEE,WAAY,OAEd,wIACE,MAAO,KACP,UAAW,KACX,YAAa,KACb,OAAQ,EAAE,IACV,YAAa,IAEf,kKACE,MAAO,QAET,uJACA,0JACE,YAAa,KACb,aAAc,KAEhB,uJACA,0JACE,YAAa,KACb,aAAc,KAEhB,sIACE,QAAS,KACT,KAAM,EACN,YAAa,EACb,aAAc,EACd,QAAS,MAAO,EAChB,SAAU,SAEZ,4KACE,YAAa,OACb,QAAS,KACT,UAAW,EACX,OAAQ,KAEV,6MACE,WAAY,KACZ,QAAS,KACT,KAAM,EACN,OAAQ,KACR,cAAe,KACf,WAAY,KAEd,0KACE,aAAc,IACd,cAAe,IAEjB,oNACE,YAAa,OACb,QAAS,KACT,UAAW,EACX,OAAQ,KAEV,oPACE,WAAY,+FACZ,QAAS,KACT,KAAM,EACN,OAAQ,KACR,cAAe,KACf,WAAY,KAEd,2MAEE,WAAY,KACZ,OAAQ,KACR,cAAe,KACf,WAAY,KACZ,MAAO,MAET,2MAEE,WAAY,KACZ,OAAQ,KACR,cAAe,KACf,WAAY,KACZ,MAAO,MAET,mKAME,YAAa,OACb,gBAAiB,YACjB,iBAAkB,QAClB,OAAQ,KAAM,MAAM,oBACpB,cAAe,IACf,OAAQ,EACR,MAAO,KACP,QAAS,KACT,OAAQ,KACR,gBAAiB,OACjB,KAAM,MACN,OAAQ,KACR,SAAU,SACV,IAAK,EACL,WAAY,OAAO,MAAM,4BACzB,MAAO,KAET,+LACE,OAAQ,KAAM,MAAM,sBAEtB,kJACA,mHACE,YAAa,OACb,QAAS,KACT,OAAQ,KACR,KAAM,EAER,kJACE,eAAgB,OAChB,gBAAiB,OAEnB,kJACE,YAAa,OACb,QAAS,KAEX,yLACE,OAAQ,KAEV,6IACE,QAAS,KAEX,qHACE,WAAY,KACZ,OAAQ,KACR,cAAe,EACf,MAAO,QACP,UAAW,EACX,UAAW,MACX,eAAgB,KAChB,aAAc,IACd,YAAa,KAEf,gJAEE,MAAO,KAET,kIAEE,MAAO,KAGT,uBACE,WAAY,KACZ,QAAS,KACT,SAAU,OACV,MAAO,KAET,uDACE,WAAY,OAAO,IAAK,SAE1B,qDACE,WAAY,OAAO,IAAK,QAE1B,oDACE,UAAW,EAEb,sFACE,UAAW,EAGb,wFACE,WAAY,MAEd,+CACE,0DACE,WAAY,OAGhB,4GACE,wFACE,WAAY,OAIhB,4BACE,YAAa,WACb,QAAS,IAAI,MAAM,KACnB,SAAU,OACV,SAAU,SACV,MAAO,KAET,wCACE,QAAS,KACT,eAAgB,OAChB,OAAQ,KACR,SAAU,SACV,MAAO,KAET,sDACE,WAAY,UAAU,IAAK,YAE7B,wDACE,cAAe,IAAI,MAAM,KACzB,MAAO,QACP,OAAQ,QACR,QAAS,KACT,QAAS,IAAI,IACb,SAAU,SAEZ,0GACE,MAAO,QACP,QAAS,QACT,YAAa,cAAgB,CAAE,WAEjC,kGACE,MAAO,QACP,QAAS,QACT,YAAa,cAAgB,CAAE,WAC/B,aAAc,IACd,cAAe,IACf,SAAU,SACV,MAAO,EAET,6FACE,YAAa,cAAgB,CAAE,WAC/B,aAAc,IACd,cAAe,IACf,SAAU,SACV,MAAO,EAGT,6DADA,6DAEE,YAAa,OACb,WAAY,KACZ,WAAY,QACZ,MAAO,QACP,QAAS,KACT,WAAY,MACZ,aAAc,IACd,cAAe,IAEjB,8FACA,8DACE,UAAW,iBAEb,+FACA,+DACE,UAAW,aAEb,6FACA,6DACE,UAAW,gBAEb,WACE,YAAa,eACb,WAAY,OACZ,YAAa,IACb,IAAK,sCAAwC,eAE/C,gCACE,gCACA,sCACE,UAAW,MAGf,gCACE,gCACA,sCACE,UAAW,MAGf,qBACE,YAAa,cAAgB,CAAE,WAEjC,uBACE,YAAa,OACb,QAAS,KACT,gBAAiB,OAEnB,gBACE,YAAa,OACb,QAAS,KACT,OAAQ,KAEV,qEACE,iBAAkB,KAClB,MAAO,KAET,6DAEE,iBAAkB,QAClB,cAAe,IACf,OAAQ,IACR,MAAO,KACP,UAAW,IACX,OAAQ,MACR,SAAU,MACV,MAAO,IACP,MAAO,MACP,YAAa,OACb,QAAS,KACT,gBAAiB,OAEnB,gDACE,6DACE,UAAW,OAGf,wGACE,OAAQ,MACR,SAAU,OAEZ,+GACE,OAAQ,KAEV,oGACE,QAAS,KAMX,6CACE,QAAS,KAEX,4GACE,2FACE,OAAQ", "file": "skin.mobile.min.css", "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n */\n/* RESET all the things! */\n.tinymce-mobile-outer-container {\n  all: initial;\n  display: block;\n}\n.tinymce-mobile-outer-container * {\n  border: 0;\n  box-sizing: initial;\n  cursor: inherit;\n  float: none;\n  line-height: 1;\n  margin: 0;\n  outline: 0;\n  padding: 0;\n  -webkit-tap-highlight-color: transparent;\n  /* TBIO-3691, stop the gray flicker on touch. */\n  text-shadow: none;\n  white-space: nowrap;\n}\n.tinymce-mobile-icon-arrow-back::before {\n  content: \"\\e5cd\";\n}\n.tinymce-mobile-icon-image::before {\n  content: \"\\e412\";\n}\n.tinymce-mobile-icon-cancel-circle::before {\n  content: \"\\e5c9\";\n}\n.tinymce-mobile-icon-full-dot::before {\n  content: \"\\e061\";\n}\n.tinymce-mobile-icon-align-center::before {\n  content: \"\\e234\";\n}\n.tinymce-mobile-icon-align-left::before {\n  content: \"\\e236\";\n}\n.tinymce-mobile-icon-align-right::before {\n  content: \"\\e237\";\n}\n.tinymce-mobile-icon-bold::before {\n  content: \"\\e238\";\n}\n.tinymce-mobile-icon-italic::before {\n  content: \"\\e23f\";\n}\n.tinymce-mobile-icon-unordered-list::before {\n  content: \"\\e241\";\n}\n.tinymce-mobile-icon-ordered-list::before {\n  content: \"\\e242\";\n}\n.tinymce-mobile-icon-font-size::before {\n  content: \"\\e245\";\n}\n.tinymce-mobile-icon-underline::before {\n  content: \"\\e249\";\n}\n.tinymce-mobile-icon-link::before {\n  content: \"\\e157\";\n}\n.tinymce-mobile-icon-unlink::before {\n  content: \"\\eca2\";\n}\n.tinymce-mobile-icon-color::before {\n  content: \"\\e891\";\n}\n.tinymce-mobile-icon-previous::before {\n  content: \"\\e314\";\n}\n.tinymce-mobile-icon-next::before {\n  content: \"\\e315\";\n}\n.tinymce-mobile-icon-large-font::before,\n.tinymce-mobile-icon-style-formats::before {\n  content: \"\\e264\";\n}\n.tinymce-mobile-icon-undo::before {\n  content: \"\\e166\";\n}\n.tinymce-mobile-icon-redo::before {\n  content: \"\\e15a\";\n}\n.tinymce-mobile-icon-removeformat::before {\n  content: \"\\e239\";\n}\n.tinymce-mobile-icon-small-font::before {\n  content: \"\\e906\";\n}\n.tinymce-mobile-icon-readonly-back::before,\n.tinymce-mobile-format-matches::after {\n  content: \"\\e5ca\";\n}\n.tinymce-mobile-icon-small-heading::before {\n  content: \"small\";\n}\n.tinymce-mobile-icon-large-heading::before {\n  content: \"large\";\n}\n.tinymce-mobile-icon-small-heading::before,\n.tinymce-mobile-icon-large-heading::before {\n  font-family: sans-serif;\n  font-size: 80%;\n}\n.tinymce-mobile-mask-edit-icon::before {\n  content: \"\\e254\";\n}\n.tinymce-mobile-icon-back::before {\n  content: \"\\e5c4\";\n}\n.tinymce-mobile-icon-heading::before {\n  /* TODO: Translate */\n  content: \"Headings\";\n  font-family: sans-serif;\n  font-size: 80%;\n  font-weight: bold;\n}\n.tinymce-mobile-icon-h1::before {\n  content: \"H1\";\n  font-weight: bold;\n}\n.tinymce-mobile-icon-h2::before {\n  content: \"H2\";\n  font-weight: bold;\n}\n.tinymce-mobile-icon-h3::before {\n  content: \"H3\";\n  font-weight: bold;\n}\n.tinymce-mobile-outer-container .tinymce-mobile-disabled-mask {\n  align-items: center;\n  display: flex;\n  justify-content: center;\n  background: rgba(51, 51, 51, 0.5);\n  height: 100%;\n  position: absolute;\n  top: 0;\n  width: 100%;\n}\n.tinymce-mobile-outer-container .tinymce-mobile-disabled-mask .tinymce-mobile-content-container {\n  align-items: center;\n  border-radius: 50%;\n  display: flex;\n  flex-direction: column;\n  font-family: sans-serif;\n  font-size: 1em;\n  justify-content: space-between;\n}\n.tinymce-mobile-outer-container .tinymce-mobile-disabled-mask .tinymce-mobile-content-container .mixin-menu-item {\n  align-items: center;\n  display: flex;\n  justify-content: center;\n  border-radius: 50%;\n  height: 2.1em;\n  width: 2.1em;\n}\n.tinymce-mobile-outer-container .tinymce-mobile-disabled-mask .tinymce-mobile-content-container .tinymce-mobile-content-tap-section {\n  align-items: center;\n  display: flex;\n  justify-content: center;\n  flex-direction: column;\n  font-size: 1em;\n}\n@media only screen and (min-device-width:700px) {\n  .tinymce-mobile-outer-container .tinymce-mobile-disabled-mask .tinymce-mobile-content-container .tinymce-mobile-content-tap-section {\n    font-size: 1.2em;\n  }\n}\n.tinymce-mobile-outer-container .tinymce-mobile-disabled-mask .tinymce-mobile-content-container .tinymce-mobile-content-tap-section .tinymce-mobile-mask-tap-icon {\n  align-items: center;\n  display: flex;\n  justify-content: center;\n  border-radius: 50%;\n  height: 2.1em;\n  width: 2.1em;\n  background-color: white;\n  color: #207ab7;\n}\n.tinymce-mobile-outer-container .tinymce-mobile-disabled-mask .tinymce-mobile-content-container .tinymce-mobile-content-tap-section .tinymce-mobile-mask-tap-icon::before {\n  content: \"\\e900\";\n  font-family: 'tinymce-mobile', sans-serif;\n}\n.tinymce-mobile-outer-container .tinymce-mobile-disabled-mask .tinymce-mobile-content-container .tinymce-mobile-content-tap-section:not(.tinymce-mobile-mask-tap-icon-selected) .tinymce-mobile-mask-tap-icon {\n  z-index: 2;\n}\n.tinymce-mobile-android-container.tinymce-mobile-android-maximized {\n  background: #ffffff;\n  border: none;\n  bottom: 0;\n  display: flex;\n  flex-direction: column;\n  left: 0;\n  position: fixed;\n  right: 0;\n  top: 0;\n}\n.tinymce-mobile-android-container:not(.tinymce-mobile-android-maximized) {\n  position: relative;\n}\n.tinymce-mobile-android-container .tinymce-mobile-editor-socket {\n  display: flex;\n  flex-grow: 1;\n}\n.tinymce-mobile-android-container .tinymce-mobile-editor-socket iframe {\n  display: flex !important;\n  flex-grow: 1;\n  height: auto !important;\n}\n.tinymce-mobile-android-scroll-reload {\n  overflow: hidden;\n}\n:not(.tinymce-mobile-readonly-mode) > .tinymce-mobile-android-selection-context-toolbar {\n  margin-top: 23px;\n}\n.tinymce-mobile-toolstrip {\n  background: #fff;\n  display: flex;\n  flex: 0 0 auto;\n  z-index: 1;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar {\n  align-items: center;\n  background-color: #fff;\n  border-bottom: 1px solid #cccccc;\n  display: flex;\n  flex: 1;\n  height: 2.5em;\n  width: 100%;\n  /* Make it no larger than the toolstrip, so that it needs to scroll */\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group {\n  align-items: center;\n  display: flex;\n  height: 100%;\n  flex-shrink: 1;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group > div {\n  align-items: center;\n  display: flex;\n  height: 100%;\n  flex: 1;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group.tinymce-mobile-exit-container {\n  background: #f44336;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group.tinymce-mobile-toolbar-scrollable-group {\n  flex-grow: 1;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group .tinymce-mobile-toolbar-group-item {\n  padding-left: 0.5em;\n  padding-right: 0.5em;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group .tinymce-mobile-toolbar-group-item.tinymce-mobile-toolbar-button {\n  align-items: center;\n  display: flex;\n  height: 80%;\n  margin-left: 2px;\n  margin-right: 2px;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group .tinymce-mobile-toolbar-group-item.tinymce-mobile-toolbar-button.tinymce-mobile-toolbar-button-selected {\n  background: #c8cbcf;\n  color: #cccccc;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group:first-of-type,\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group:last-of-type {\n  background: #207ab7;\n  color: #eceff1;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar {\n  /* Note, this file is imported inside .tinymce-mobile-context-toolbar, so that prefix is on everything here. */\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group {\n  align-items: center;\n  display: flex;\n  height: 100%;\n  flex: 1;\n  padding-bottom: 0.4em;\n  padding-top: 0.4em;\n  /* Make any buttons appearing on the left and right display in the centre (e.g. color edges) */\n  /* For widgets like the colour picker, use the whole height */\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog {\n  display: flex;\n  min-height: 1.5em;\n  overflow: hidden;\n  padding-left: 0;\n  padding-right: 0;\n  position: relative;\n  width: 100%;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain {\n  display: flex;\n  height: 100%;\n  transition: left cubic-bezier(0.4, 0, 1, 1) 0.15s;\n  width: 100%;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen {\n  display: flex;\n  flex: 0 0 auto;\n  justify-content: space-between;\n  width: 100%;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen input {\n  font-family: Sans-serif;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-input-container {\n  display: flex;\n  flex-grow: 1;\n  position: relative;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-input-container .tinymce-mobile-input-container-x {\n  -ms-grid-row-align: center;\n      align-self: center;\n  background: inherit;\n  border: none;\n  border-radius: 50%;\n  color: #888;\n  font-size: 0.6em;\n  font-weight: bold;\n  height: 100%;\n  padding-right: 2px;\n  position: absolute;\n  right: 0;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-input-container.tinymce-mobile-input-container-empty .tinymce-mobile-input-container-x {\n  display: none;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-icon-previous,\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-icon-next {\n  align-items: center;\n  display: flex;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-icon-previous::before,\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-icon-next::before {\n  align-items: center;\n  display: flex;\n  font-weight: bold;\n  height: 100%;\n  padding-left: 0.5em;\n  padding-right: 0.5em;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-icon-previous.tinymce-mobile-toolbar-navigation-disabled::before,\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serialised-dialog .tinymce-mobile-serialised-dialog-chain .tinymce-mobile-serialised-dialog-screen .tinymce-mobile-icon-next.tinymce-mobile-toolbar-navigation-disabled::before {\n  visibility: hidden;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-dot-item {\n  color: #cccccc;\n  font-size: 10px;\n  line-height: 10px;\n  margin: 0 2px;\n  padding-top: 3px;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-dot-item.tinymce-mobile-dot-active {\n  color: #c8cbcf;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-icon-large-font::before,\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-icon-large-heading::before {\n  margin-left: 0.5em;\n  margin-right: 0.9em;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-icon-small-font::before,\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-icon-small-heading::before {\n  margin-left: 0.9em;\n  margin-right: 0.5em;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider {\n  display: flex;\n  flex: 1;\n  margin-left: 0;\n  margin-right: 0;\n  padding: 0.28em 0;\n  position: relative;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider .tinymce-mobile-slider-size-container {\n  align-items: center;\n  display: flex;\n  flex-grow: 1;\n  height: 100%;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider .tinymce-mobile-slider-size-container .tinymce-mobile-slider-size-line {\n  background: #cccccc;\n  display: flex;\n  flex: 1;\n  height: 0.2em;\n  margin-bottom: 0.3em;\n  margin-top: 0.3em;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider.tinymce-mobile-hue-slider-container {\n  padding-left: 2em;\n  padding-right: 2em;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider.tinymce-mobile-hue-slider-container .tinymce-mobile-slider-gradient-container {\n  align-items: center;\n  display: flex;\n  flex-grow: 1;\n  height: 100%;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider.tinymce-mobile-hue-slider-container .tinymce-mobile-slider-gradient-container .tinymce-mobile-slider-gradient {\n  background: linear-gradient(to right, hsl(0, 100%, 50%) 0%, hsl(60, 100%, 50%) 17%, hsl(120, 100%, 50%) 33%, hsl(180, 100%, 50%) 50%, hsl(240, 100%, 50%) 67%, hsl(300, 100%, 50%) 83%, hsl(0, 100%, 50%) 100%);\n  display: flex;\n  flex: 1;\n  height: 0.2em;\n  margin-bottom: 0.3em;\n  margin-top: 0.3em;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider.tinymce-mobile-hue-slider-container .tinymce-mobile-hue-slider-black {\n  /* Not part of theming */\n  background: black;\n  height: 0.2em;\n  margin-bottom: 0.3em;\n  margin-top: 0.3em;\n  width: 1.2em;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider.tinymce-mobile-hue-slider-container .tinymce-mobile-hue-slider-white {\n  /* Not part of theming */\n  background: white;\n  height: 0.2em;\n  margin-bottom: 0.3em;\n  margin-top: 0.3em;\n  width: 1.2em;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider .tinymce-mobile-slider-thumb {\n  /* vertically centering trick (margin: auto, top: 0, bottom: 0). On iOS and Safari, if you leave\n     * out these values, then it shows the thumb at the top of the spectrum. This is probably because it is\n     * absolutely positioned with only a left value, and not a top. Note, on Chrome it seems to be fine without\n     * this approach.\n    */\n  align-items: center;\n  background-clip: padding-box;\n  background-color: #455a64;\n  border: 0.5em solid rgba(136, 136, 136, 0);\n  border-radius: 3em;\n  bottom: 0;\n  color: #fff;\n  display: flex;\n  height: 0.5em;\n  justify-content: center;\n  left: -10px;\n  margin: auto;\n  position: absolute;\n  top: 0;\n  transition: border 120ms cubic-bezier(0.39, 0.58, 0.57, 1);\n  width: 0.5em;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-slider .tinymce-mobile-slider-thumb.tinymce-mobile-thumb-active {\n  border: 0.5em solid rgba(136, 136, 136, 0.39);\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serializer-wrapper,\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group > div {\n  align-items: center;\n  display: flex;\n  height: 100%;\n  flex: 1;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-serializer-wrapper {\n  flex-direction: column;\n  justify-content: center;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-toolbar-group-item {\n  align-items: center;\n  display: flex;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-toolbar-group-item:not(.tinymce-mobile-serialised-dialog) {\n  height: 100%;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group .tinymce-mobile-dot-container {\n  display: flex;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group input {\n  background: #ffffff;\n  border: none;\n  border-radius: 0;\n  color: #455a64;\n  flex-grow: 1;\n  font-size: 0.85em;\n  padding-bottom: 0.1em;\n  padding-left: 5px;\n  padding-top: 0.1em;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group input::-webkit-input-placeholder {\n  /* WebKit, Blink, Edge */\n  color: #888;\n}\n.tinymce-mobile-toolstrip .tinymce-mobile-toolbar.tinymce-mobile-context-toolbar .tinymce-mobile-toolbar-group input::placeholder {\n  /* WebKit, Blink, Edge */\n  color: #888;\n}\n/* dropup */\n.tinymce-mobile-dropup {\n  background: white;\n  display: flex;\n  overflow: hidden;\n  width: 100%;\n}\n.tinymce-mobile-dropup.tinymce-mobile-dropup-shrinking {\n  transition: height 0.3s ease-out;\n}\n.tinymce-mobile-dropup.tinymce-mobile-dropup-growing {\n  transition: height 0.3s ease-in;\n}\n.tinymce-mobile-dropup.tinymce-mobile-dropup-closed {\n  flex-grow: 0;\n}\n.tinymce-mobile-dropup.tinymce-mobile-dropup-open:not(.tinymce-mobile-dropup-growing) {\n  flex-grow: 1;\n}\n/* TODO min-height for device size and orientation */\n.tinymce-mobile-ios-container .tinymce-mobile-dropup:not(.tinymce-mobile-dropup-closed) {\n  min-height: 200px;\n}\n@media only screen and (orientation: landscape) {\n  .tinymce-mobile-dropup:not(.tinymce-mobile-dropup-closed) {\n    min-height: 200px;\n  }\n}\n@media only screen and (min-device-width : 320px) and (max-device-width : 568px) and (orientation : landscape) {\n  .tinymce-mobile-ios-container .tinymce-mobile-dropup:not(.tinymce-mobile-dropup-closed) {\n    min-height: 150px;\n  }\n}\n/* styles menu */\n.tinymce-mobile-styles-menu {\n  font-family: sans-serif;\n  outline: 4px solid black;\n  overflow: hidden;\n  position: relative;\n  width: 100%;\n}\n.tinymce-mobile-styles-menu [role=\"menu\"] {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  position: absolute;\n  width: 100%;\n}\n.tinymce-mobile-styles-menu [role=\"menu\"].transitioning {\n  transition: transform 0.5s ease-in-out;\n}\n.tinymce-mobile-styles-menu .tinymce-mobile-styles-item {\n  border-bottom: 1px solid #ddd;\n  color: #455a64;\n  cursor: pointer;\n  display: flex;\n  padding: 1em 1em;\n  position: relative;\n}\n.tinymce-mobile-styles-menu .tinymce-mobile-styles-collapser .tinymce-mobile-styles-collapse-icon::before {\n  color: #455a64;\n  content: \"\\e314\";\n  font-family: 'tinymce-mobile', sans-serif;\n}\n.tinymce-mobile-styles-menu .tinymce-mobile-styles-item.tinymce-mobile-styles-item-is-menu::after {\n  color: #455a64;\n  content: \"\\e315\";\n  font-family: 'tinymce-mobile', sans-serif;\n  padding-left: 1em;\n  padding-right: 1em;\n  position: absolute;\n  right: 0;\n}\n.tinymce-mobile-styles-menu .tinymce-mobile-styles-item.tinymce-mobile-format-matches::after {\n  font-family: 'tinymce-mobile', sans-serif;\n  padding-left: 1em;\n  padding-right: 1em;\n  position: absolute;\n  right: 0;\n}\n.tinymce-mobile-styles-menu .tinymce-mobile-styles-separator,\n.tinymce-mobile-styles-menu .tinymce-mobile-styles-collapser {\n  align-items: center;\n  background: #fff;\n  border-top: #455a64;\n  color: #455a64;\n  display: flex;\n  min-height: 2.5em;\n  padding-left: 1em;\n  padding-right: 1em;\n}\n.tinymce-mobile-styles-menu [data-transitioning-destination=\"before\"][data-transitioning-state],\n.tinymce-mobile-styles-menu [data-transitioning-state=\"before\"] {\n  transform: translate(-100%);\n}\n.tinymce-mobile-styles-menu [data-transitioning-destination=\"current\"][data-transitioning-state],\n.tinymce-mobile-styles-menu [data-transitioning-state=\"current\"] {\n  transform: translate(0%);\n}\n.tinymce-mobile-styles-menu [data-transitioning-destination=\"after\"][data-transitioning-state],\n.tinymce-mobile-styles-menu [data-transitioning-state=\"after\"] {\n  transform: translate(100%);\n}\n@font-face {\n  font-family: 'tinymce-mobile';\n  font-style: normal;\n  font-weight: normal;\n  src: url('fonts/tinymce-mobile.woff?8x92w3') format('woff');\n}\n@media (min-device-width: 700px) {\n  .tinymce-mobile-outer-container,\n  .tinymce-mobile-outer-container input {\n    font-size: 25px;\n  }\n}\n@media (max-device-width: 700px) {\n  .tinymce-mobile-outer-container,\n  .tinymce-mobile-outer-container input {\n    font-size: 18px;\n  }\n}\n.tinymce-mobile-icon {\n  font-family: 'tinymce-mobile', sans-serif;\n}\n.mixin-flex-and-centre {\n  align-items: center;\n  display: flex;\n  justify-content: center;\n}\n.mixin-flex-bar {\n  align-items: center;\n  display: flex;\n  height: 100%;\n}\n.tinymce-mobile-outer-container .tinymce-mobile-editor-socket iframe {\n  background-color: #fff;\n  width: 100%;\n}\n.tinymce-mobile-editor-socket .tinymce-mobile-mask-edit-icon {\n  /* Note, on the iPod touch in landscape, this isn't visible when the navbar appears */\n  background-color: #207ab7;\n  border-radius: 50%;\n  bottom: 1em;\n  color: white;\n  font-size: 1em;\n  height: 2.1em;\n  position: fixed;\n  right: 2em;\n  width: 2.1em;\n  align-items: center;\n  display: flex;\n  justify-content: center;\n}\n@media only screen and (min-device-width:700px) {\n  .tinymce-mobile-editor-socket .tinymce-mobile-mask-edit-icon {\n    font-size: 1.2em;\n  }\n}\n.tinymce-mobile-outer-container:not(.tinymce-mobile-fullscreen-maximized) .tinymce-mobile-editor-socket {\n  height: 300px;\n  overflow: hidden;\n}\n.tinymce-mobile-outer-container:not(.tinymce-mobile-fullscreen-maximized) .tinymce-mobile-editor-socket iframe {\n  height: 100%;\n}\n.tinymce-mobile-outer-container:not(.tinymce-mobile-fullscreen-maximized) .tinymce-mobile-toolstrip {\n  display: none;\n}\n/*\n  Note, that if you don't include this (::-webkit-file-upload-button), the toolbar width gets\n  increased and the whole body becomes scrollable. It's important!\n */\ninput[type=\"file\"]::-webkit-file-upload-button {\n  display: none;\n}\n@media only screen and (min-device-width : 320px) and (max-device-width : 568px) and (orientation : landscape) {\n  .tinymce-mobile-ios-container .tinymce-mobile-editor-socket .tinymce-mobile-mask-edit-icon {\n    bottom: 50%;\n  }\n}\n"]}