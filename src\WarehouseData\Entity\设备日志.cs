﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>设备日志</summary>
[Serializable]
[DataObject]
[Description("设备日志")]
[BindIndex("IX_DH_DeviceLogs_DId_Process", false, "DId,Process")]
[BindIndex("IX_DH_DeviceLogs_CreateUserID", false, "CreateUserID")]
[BindIndex("IX_DH_DeviceLogs_CreateTime", false, "CreateTime")]
[BindTable("DH_DeviceLogs", Description = "设备日志", ConnName = "DH", DbType = DatabaseType.None)]
public partial class DeviceLogs : IDeviceLogs, IEntity<IDeviceLogs>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int32 _DId;
    /// <summary>设备Id</summary>
    [DisplayName("设备Id")]
    [Description("设备Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("DId", "设备Id", "")]
    public Int32 DId { get => _DId; set { if (OnPropertyChanging("DId", value)) { _DId = value; OnPropertyChanged("DId"); } } }

    private Int32 _Process;
    /// <summary>操作工序。1为打单，2为领料，3为生产，4为生产审核，5为打包，6为取消订单</summary>
    [DisplayName("操作工序")]
    [Description("操作工序。1为打单，2为领料，3为生产，4为生产审核，5为打包，6为取消订单")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Process", "操作工序。1为打单，2为领料，3为生产，4为生产审核，5为打包，6为取消订单", "")]
    public Int32 Process { get => _Process; set { if (OnPropertyChanging("Process", value)) { _Process = value; OnPropertyChanged("Process"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IDeviceLogs model)
    {
        Id = model.Id;
        DId = model.DId;
        Process = model.Process;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "DId" => _DId,
            "Process" => _Process,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "DId": _DId = value.ToInt(); break;
                case "Process": _Process = value.ToInt(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static DeviceLogs? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }
    #endregion

    #region 字段名
    /// <summary>取得设备日志字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>设备Id</summary>
        public static readonly Field DId = FindByName("DId");

        /// <summary>操作工序。1为打单，2为领料，3为生产，4为生产审核，5为打包，6为取消订单</summary>
        public static readonly Field Process = FindByName("Process");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得设备日志字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>设备Id</summary>
        public const String DId = "DId";

        /// <summary>操作工序。1为打单，2为领料，3为生产，4为生产审核，5为打包，6为取消订单</summary>
        public const String Process = "Process";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";
    }
    #endregion
}
