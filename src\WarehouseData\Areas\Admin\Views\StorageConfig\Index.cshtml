﻿@using Pek.Configs
@{
    Html.AppendTitleParts(T("存储配置").Text);
}
<style asp-location="true">
    .layui-form-label {
        width: 150px;
    }

    .layui-form-item.btn {
        margin-top: 20px;
        text-align: center;
    }
</style>
<div class="layui-card">
    <div class="layui-card-header">@T("存储配置")</div>
    <div class="layui-card-body" pad15>
        <form class="layui-form" lay-filter="">
            <div class="layui-form-item">
                <label class="layui-form-label">@T("七牛云授权密钥(AccessKey)")</label>
                <div class="layui-input-block">
                    <input type="text" name="AccessKey" value="@OssSetting.Current.QiNiu.AccessKey" class="layui-input" style="width: 250px !important;">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("七牛云密钥(SecretKey)")</label>
                <div class="layui-input-block">
                    <input type="text" name="SecretKey" value="@OssSetting.Current.QiNiu.SecretKey" class="layui-input" style="width: 250px !important;">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("七牛云存储空间块(Bucket)")</label>
                <div class="layui-input-block">
                    <input type="text" name="Bucket" value="@OssSetting.Current.QiNiu.Bucket" class="layui-input" style="width: 250px !important;">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("七牛云存储基本路径(BasePath)")</label>
                <div class="layui-input-block">
                    <input type="text" name="BasePath" value="@OssSetting.Current.QiNiu.BasePath" class="layui-input" style="width: 250px !important;">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("七牛云绑定域名(Domain)")</label>
                <div class="layui-input-block">
                    <input type="text" name="Domain" value="@OssSetting.Current.QiNiu.Domain" class="layui-input" style="width: 250px !important;">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">@T("测试上传")</label>
                <div class="layui-input-block">
                    <button type="button" class="layui-btn" id="uploadFile">
                        <i class="layui-icon">&#xe67c;</i>@T("选择文件")
                    </button>
                    <div id="upload-demo-preview" style="margin-top: 10px;"></div>
                </div>
            </div>

            <div class="layui-form-item btn">
                <div class="layui-input-block">
                    <button type="button" class="pear-btn pear-btn-primary pear-btn-normal" lay-submit lay-filter="set_website">@T("确认保存")</button>
                </div>
            </div>
        </form>
    </div>
</div>
<script asp-location="Footer">
    layui.use(['abp', 'form','upload'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var upload = layui.upload;

        upload.render({
            elem: '#uploadFile'
            , url: '@Url.Action("UploadFiles")' //改成您自己的上传接口
            , done: function (res) {
                if (!res.success) { //失败打印
                    abp.notify.error(res.msg);
                    return;
                }
                abp.notify.success('@T("上传成功")');
                $('#upload-demo-preview').append('<a href="'+res.data.FileUrl+'" target="_blank">'+res.data.FileUrl+'</a><br>');
            }
            ,before: function (obj) {

            }
            , accept: 'file' //允许上传的文件类型

        });

        //自定义验证
        form.verify({

        });

        form.on('submit(set_website)', function (data) {
            var waitIndex = layer.load(2);
            $.post("@Url.Action("UpdateInfo")", data.field, function (result) {
                layer.close(waitIndex);
                if (result.success) {
                    abp.notify.success(result.msg);
                } else {
                    abp.notify.error(result.msg);
                }
            });
        });
    });
</script>