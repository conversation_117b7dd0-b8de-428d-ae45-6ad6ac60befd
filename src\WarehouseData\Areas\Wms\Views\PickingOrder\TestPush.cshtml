﻿@model WmsPickingOrder
@{
    Html.AppendTitleParts(T("调测推送").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
        white-space:nowrap;
        min-width:110px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
    .select{
        max-width: 420px !important;
        border: 1px solid #e6e6e6;
    }
    .flex {
        display: flex;
        justify-content: center;
        place-items: center;
    }
</style>
<script asp-location="Head">
    var closeThis = '@T("关 闭 当 前")';
    var closeOther = '@T("关 闭 其 他")';
    var closeAll = '@T("关 闭 全 部")';

    var jsMenuStyle = "@T("菜单风格")";
    var jsTopStyle = "@T("顶部风格")";
    var jsMenu = "@T("菜单")";
    var jsView = "@T("视图")";
    var jsBanner = "@T("通栏")";
    var jsThroughColor = "@T("通色")";
    var jsFooter = "@T("页脚")";
    var jsMoreSettings = "@T("更多设置")";
    var jsOpen = "@T("开")";
    var jsClose = "@T("关")";
    var jsThemeColor = "@T("主题配色")";
    var layuiNoData = '@T("无数据")';
    var layuiAsc = "@T("升序")";
    var layuiDesc = "@T("降序")";

    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
</script>
<div class="containers">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("操作时间")</label>
            <div class="layui-input-inline" style="min-width:320px;">
                <div class="layui-input-inline">
                    <input type="text" name="ProcessTime" id="ProcessTime" readonly placeholder="@T("选择推送日期")" autocomplete="off" class="layui-input">
                </div>
            </div>
        </div>


        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("用户姓名")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <div id="demo1" style=" width: 100%;"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("状态类型")</label>
            <div class="layui-input-inline" style="width: 300px;">
                <select name="PType">
                    <option value="">@T("请选择")</option>
                    <option value="1">@T("已打单")</option>
                    <option value="2">@T("已领料")</option>
                    <option value="3">@T("生产中")</option>
                    <option value="4">@T("生产审核")</option>
                    <option value="5">@T("打包完成")</option>
                </select>
            </div>
        </div>

        <input type="hidden" name="DTime" id="DTime" value="@Model.OrderingTime.ToFullString()"/>
        <div class="layui-form-item btn">
            <input hidden name="OrderId" value="@Model.OrderId" /> 
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" style="margin-left:20px" value="@T("推送")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var laydate = layui.laydate;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        var UName = null; //选择用户的值

        
        var demo1 = xmSelect.render({
            el: '#demo1',
            radio: true, //设置单选
            name: 'UName',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            disabled: false, // 设置禁用
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];

                // 接口数据
                $.post('@Url.Action("SearchUser")', { key: val, page: pageIndex }, function (res) {
                    // console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                            // console.log(res.data);
                            // demo1.setValue(res.data); // 设置初始值，编辑才需要
                            cb(res.data, res.extdata);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择
                if (data.arr.length > 0) {
                    UName = data.arr[0].name;
                }

            }
        });
         //时间插件
        var ProcessTimeDate = laydate.render({
            elem: '#ProcessTime',
            btns: ['clear', "confirm"],//只显示清空和确定按钮
            type: 'datetime',       // 设置日期选择类型为年月
            format: 'yyyy-MM-dd HH:MM:ss',   // 设置日期的格式，这里是年-月
            done: function (value, date) {
                $("#ProcessTime").val(value);
                checkDateValidity();
            },
            choose: function (date) {
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });

        function checkDateValidity() {
            var ProcessTimeValue = $("#ProcessTime").val();

            if (ProcessTimeValue ) {
                var ProcessTimeDate = new Date(ProcessTimeValue + "-01");
            }
        }

        

        form.on('submit(Submit)', function (data) {
             console.log(data);
            if (data.field.ProcessTime.length == 0) {
                abp.notify.warn("@T("开始时间不能为空")");
                return;
            }

            if (data.field.UName.length == 0) {
                abp.notify.warn("@T("用户不能为空")");
                return;
            }

            if (data.field.PType.length == 0) {
                abp.notify.warn("@T("状态类型不能为空")");
                return;
            }
            data.field.UName = UName

            var waitIndex = parent.layer.load(2);
            abp.ajax({
                url: "@Url.Action("TestPush")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>