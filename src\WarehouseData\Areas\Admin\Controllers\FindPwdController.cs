﻿using DG.Web.Framework;

using DH.Entity;
using DH.Helpers;
using DH.Services.Common;
using DH.Services.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

using NewLife;

using Pek;
using Pek.Helpers;
using Pek.Models;
using Pek.Webs;

using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>
/// 找回密码控制器
/// </summary>
[AdminArea]
public class FindPwdController : BaseAdminControllerX {
    /// <summary>
    /// 密码服务
    /// </summary>
    private readonly PasswordService _passwordService;

    public FindPwdController()
    {
        _passwordService = new PasswordService();
    }

    /// <summary>
    /// 找回密码
    /// </summary>
    /// <returns></returns>
    [DisplayName("找回密码")]
    public IActionResult Index()
    {
        var returnUrl = GetRequest("r");
        if (returnUrl.IsNullOrEmpty()) returnUrl = GetRequest("ReturnUrl");
        ViewBag.ReturnUrl = returnUrl;

        ViewBag.PageTitle = GetResource("找回密码");

        return View();
    }

    /// <summary>
    /// 找回密码
    /// </summary>
    /// <param name="UserName">账户</param>
    /// <param name="CheckCode">图片验证码</param>
    /// <param name="CodeVal">短信验证码</param>
    /// <param name="password">确认密码</param>
    /// <returns></returns>
    [AllowAnonymous]
    [DisplayName("找回密码")]
    public IActionResult ChangePwd([FromForm] String UserName, [FromForm] String CheckCode, [FromForm] String CodeVal, [FromForm] String password)
    {
        var returnUrl = GetRequest("r");
        if (returnUrl.IsNullOrEmpty()) returnUrl = GetRequest("ReturnUrl");

        var res = new DResult();

        if (!DG.Setting.Current.AllowManageFindPassword)
        {
            res.msg = GetResource("未开启找回密码功能，请联系管理员");
            return Json(res);
        }

        UserName = UserName.SafeString().Trim();
        if (UserName.IsNullOrEmpty())
        {
            res.msg = GetResource("账户不能为空");
            return Json(res);
        }

        CheckCode = CheckCode.SafeString().Trim();
        if (CheckCode.IsNullOrEmpty())
        {
            res.msg = GetResource("图片验证码不能为空");
            return Json(res);
        }

        password = password.SafeString().Trim();
        if (password.IsNullOrEmpty())
        {
            res.msg = GetResource("密码不能为空");
            return Json(res);
        }

        var RTypes = -1;
        if (ValidateHelper.IsMobile(UserName))
        {
            RTypes = 1;
        }
        else if (ValidateHelper.IsEmail(UserName))
        {
            RTypes = 2;
        }

        if (RTypes == -1)
        {
            res.msg = GetResource("请输入正确的手机号码/邮箱");
            return Json(res);
        }

        if (CodeVal.IsNullOrEmpty())
        {
            if (RTypes == 1) //短信
            {
                res.msg = GetResource("短信验证码不能为空");
            }
            else
            {
                res.msg = GetResource("邮箱验证码不能为空");
            }
            return Json(res);
        }

        //if (!_passwordService.Valid(password))
        //{
        //    res.msg = GetResource("密码太弱，要求8位起且包含数字大小写字母和符号");
        //    return Json(res);
        //}
        if (password.Length < 8 || password.Length > 32)
        {
            res.msg = GetResource("请将密码限制在8到32位");
            return Json(res);
        }

        #region 验证图片验证码
        var cResult = CommonHelpers.CheckCode(CheckCode);
        if (!cResult.success)
        {
            return Json(cResult);
        }
        #endregion

        var smsname = HttpContext.Session.GetString("smsname");
        var smscode = HttpContext.Session.GetString("smscode");

        if (smsname.IsNullOrEmpty() || smscode.IsNullOrEmpty())
        {
            if (RTypes == 1) //短信
            {
                res.msg = GetResource("短信验证码过期");
            }
            else
            {
                res.msg = GetResource("邮箱验证码过期");
            }
            return Json(res);
        }

        if (smsname != UserName || smscode != CodeVal)
        {
            if (RTypes == 1) //短信
            {
                res.msg = GetResource("短信验证码不正确");
            }
            else
            {
                res.msg = GetResource("邮箱验证码不正确");
            }
            return Json(res);
        }

        if (RTypes == 1) //短信
        {
            var Model = UserE.FindByMobile(UserName);
            if (Model == null)
            {
                res.msg = GetResource("该手机号未注册账户");
                return Json(res);
            }

            Model.Password = ManageProvider.Provider?.PasswordProvider.Hash(password);
            Model.Update();
        }
        else if (RTypes == 2) //邮箱
        {
            var Model = UserE.FindByMail(UserName);
            if (Model == null)
            {
                res.msg = GetResource("该邮箱未注册账户");
                return Json(res);
            }

            Model.Password = ManageProvider.Provider?.PasswordProvider.Hash(password);
            Model.Update();
        }
        else
        {
            res.msg = GetResource("修改密码失败,请输入正确格式邮箱或手机号");
            return Json(res);
        }
        res.msg = GetResource("成功找回密码，请返回登录!");
        res.locate = Url.Action("Index", "Login")?.AppendReturn(returnUrl);
        res.success = true;
        return Json(res);
    }

}