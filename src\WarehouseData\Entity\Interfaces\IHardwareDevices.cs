﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>硬件设备</summary>
public partial interface IHardwareDevices
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>设备Mac地址</summary>
    String Mac { get; set; }

    /// <summary>设备号</summary>
    String? Code { get; set; }

    /// <summary>设备类型。0为扫码枪</summary>
    Dto.HardwareType HType { get; set; }

    /// <summary>设备型号</summary>
    String DeviceModel { get; set; }

    /// <summary>备注</summary>
    String? Remark { get; set; }

    /// <summary>绑定用户ID</summary>
    Int32 BindUserID { get; set; }

    /// <summary>绑定用户姓名。冗余字段</summary>
    String? BindUserRealName { get; set; }

    /// <summary>绑定用户名</summary>
    String? BindUser { get; set; }

    /// <summary>设备所在工序。1为打单，2为领料，3为生产，4为生产审核，5为出货/入库，6为人为结束，7为取消订单，8为设置，9为打包</summary>
    Dto.HardwareStatus Status { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
