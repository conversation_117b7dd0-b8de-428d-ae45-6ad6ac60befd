﻿@{
    Layout = null;

    var dgPage = (Pek.Webs.HttpContext.Current.Request.RouteValues["controller"] + "_" + Pek.Webs.HttpContext.Current.Request.RouteValues["action"]).ToLower();
}
<!DOCTYPE html>
@* <script async src="~/js/jquery.min.js" asp-append-version="true"></script> *@
<script src="~/libs/layui/layui.js" asp-append-version="true"></script>
@* <script src="~/font/plug-js/echarts/echarts.js" asp-append-version="true"></script> *@
<script src="~/lib/microsoft/signalr/dist/browser/signalr.js" asp-append-version="true"></script>
<script src="~/lib/msgpack5/dist/msgpack5.js" asp-append-version="true"></script>
<script src="~/lib/microsoft/signalr-protocol-msgpack/dist/browser/signalr-protocol-msgpack.js" asp-append-version="true"></script>
<script src="~/js/initSignalr1.js" asp-append-version="true"></script>
<script src="~/js/xm-select.js" asp-append-version="true"></script>
<script src="~/js/Storage.js" asp-append-version="true"></script>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仓库智慧大屏可视化</title>
    <!-- 引入layui -->
    <link rel="stylesheet" href="~/libs/layui/css/layui.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/font/css/index.css" asp-append-version="true" />
</head>

<style>


</style>
<body>
    <style>
    </style>
    <div id="app">
        <div class="title">
            仓库智慧大屏可视化
            <div class="searchBox">
                <input type="text" name="key" id="key" placeholder="请输入关键词" class="layui-input transparent">
                <div class="layui-form form">
                    <button class="layui-btn transparent" type="submit" lay-submit data-id="search">
                        <img src="./font/images/search_icon.png" alt="搜索">
                    </button>
                </div>
            </div>
        </div>
        <!-- 左侧 -->
        <div class="boxContent">
            <div class="item_left">
                <div class="item_left_box" data-index="1">
                    <div class="leftTile">已打单</div>
                    <div class="layui-card">

                        <table class="layui-hide" id="left_table1"></table>

                    </div>
                </div>
                <div class="item_left_box" data-index="2">
                    <div class="leftTile">已领料</div>
                    <div class="layui-card">

                        <table class="layui-hide" id="left_table2"></table>

                    </div>
                </div>
                <div class="item_left_box" data-index="3">
                    <div class="leftTile">生产中</div>
                    <div class="layui-card">
                        <table class="layui-hide" id="left_table3"></table>

                    </div>
                </div>
                <div class="item_left_box" data-index="4">
                    <div class="leftTile">待打包</div>
                    <div class="layui-card">

                        <table class="layui-hide" id="left_table4"></table>

                    </div>
                </div>
            </div>
            <!-- 中间 -->
            <div class="item_center">
                <div class="item_center1">
                    <div class="centerTile">总制造单入库数据统计</div>
                    <div id="feedViewBox">
                        <div class="task" style="margin-top: 3vh;" data-id="pickingOrderCount" data-fn="select,time,input" data-index="01" lay-submit>
                            <div style="line-height:6vh;text-indent:2vw;">总订单数量</div>
                            <div data-name="pickingOrderCount" style="color: #02d9fd;font-size:1.8vh;font-weight:600;">0</div>
                        </div>
                        <div class="task" style="margin-top: 3vh;" data-id="pickingOrderTimeOutCount" data-fn="input" data-index="02" lay-submit>
                            <div style="line-height:6vh;text-indent:2vw;">总超时订单</div>
                            <div data-name="pickingOrderTimeOutCount" style="color: #557bf1;font-size:1.8vh;font-weight:600;">0</div>
                        </div>

                        <div class="task"  data-id="pWaitPicking" data-index="03" data-fn="time,input" lay-submit>
                            <div style="line-height:6vh;">待领料</div>
                            <div data-name="pWaitPicking" style="color: #2c63fd;font-size:1.8vh;font-weight:600;">0</div>
                        </div>
                        <div class="task"  data-id="pWaitProduction" data-index="04" data-fn="time,input" lay-submit>
                            <div style="line-height:6vh;">待生产</div>
                            <div data-name="pWaitProduction" style="color: #91cc75;font-size:1.8vh;font-weight:600;">0</div>
                        </div>

                        <div class="task" data-id="pInProduction" data-index="05" data-fn="" lay-submit>
                            <div style="line-height:6vh;">生产中</div>
                            <div data-name="pInProduction" style="color: #fac858;font-size:1.8vh;font-weight:600;">0</div>
                        </div>
                        <div class="task" data-id="pIntosStorage" data-index="06" data-fn="time,input" lay-submit>
                            <div style="line-height:6vh;">待入库</div>
                            <div data-name="pIntosStorage" style="color: #ee6666;font-size:1.8vh;font-weight:600;">0</div>
                        </div>

                        <div class="task" data-id="pickingOrderIntoCount" data-index="07" data-fn="time,input" lay-submit>
                            <div style="line-height:6vh;">已入库</div>
                            <div data-name="pickingOrderIntoCount" style="color: #14fa24;font-size:1.8vh;font-weight:600;">0</div>
                        </div>
                    </div>
                </div>
                <div class="item_center2">
                    <div class="centerTile2">今日制造单入库统计</div>
                    <div id="feedViewBox">
                        <div class="task" style="margin-top: 3vh;height: 27%;" data-id="pTodayCount" data-index="01" lay-submit>
                            <div style="line-height:6vh;text-indent:2vw;">订单数量</div>
                            <div data-name="pTodayCount" style="color: #02d9fd;font-size:1.8vh;font-weight:600;">0</div>
                        </div>

                        <div class="task" style="height: 27%; margin-top: 3vh;" data-id="pTodayWaitPicking" data-index="03" lay-submit>
                            <div style="line-height:6vh;">待领料</div>
                            <div data-name="pTodayWaitPicking" style="color: #2c63fd;font-size:1.8vh;font-weight:600;">0</div>
                        </div>
                        <div class="task" data-id="pTodayWaitProduction" data-index="04" lay-submit style="height: 27%;">
                            <div style="line-height:6vh;">待生产</div>
                            <div data-name="pTodayWaitProduction" style="color: #91cc75;font-size:1.8vh;font-weight:600;">0</div>
                        </div>
                        <div class="task" data-id="pTodayInProduction" data-index="05" lay-submit style="height: 27%;">
                            <div style="line-height:6vh;">生产中</div>
                            <div data-name="pTodayInProduction" style="color: #fac858;font-size:1.8vh;font-weight:600;">0</div>
                        </div>

                        <div class="task" data-id="pTodayIntosStorage" data-index="06" lay-submit style="height: 27%;">
                            <div style="line-height:6vh;">待入库</div>
                            <div data-name="pTodayIntosStorage" style="color: #ee6666;font-size:1.8vh;font-weight:600;">0</div>
                        </div>

                        <div class="task" data-id="pTodayIntoCount" data-index="07" lay-submit style="height: 27%;">
                            <div style="line-height:6vh;">已入库</div>
                            <div data-name="pTodayIntoCount" style="color: #14fa24;font-size:1.8vh;font-weight:600;">0</div>
                        </div>


                    </div>
                </div>
            </div>
            <!-- 右边 -->
            <div class="item_right">
                <div class="item_right1">
                    <div class="rightTile">总出货数据统计</div>
                    <div id="taskViewBox">
                        <div class="task" style="margin-top: 3vh;" data-id="AllCount" data-fn="select,time,input" data-index="01" lay-submit>
                            <div style="line-height:6vh;text-indent:2vw;">总订单数量</div>
                            <div data-name="AllCount" style="color: #02d9fd;font-size:1.8vh;font-weight:600;">0</div>
                        </div>
                        <div class="task" style="margin-top: 3vh;" data-id="TimeoutCount" data-fn="input" data-index="02" lay-submit>
                            <div style="line-height:6vh;text-indent:2vw;">总超时订单</div>
                            <div data-name="TimeoutCount" style="color: #557bf1;font-size:1.8vh;font-weight:600;">0</div>
                        </div>

                        <div class="task" data-id="WaitPicking" data-index="03" data-fn="time,input" lay-submit>
                            <div style="line-height:6vh;">待领料</div>
                            <div data-name="WaitPicking" style="color: #2c63fd;font-size:1.8vh;font-weight:600;">0</div>
                        </div>
                        <div class="task" data-id="WaitProduction" data-index="04" data-fn="time,input" lay-submit>
                            <div style="line-height:6vh;">待生产</div>
                            <div data-name="WaitProduction" style="color: #91cc75;font-size:1.8vh;font-weight:600;">0</div>
                        </div>

                        <div class="task" data-id="InProduction" data-index="05" data-fn="" lay-submit>
                            <div style="line-height:6vh;">生产中</div>
                            <div data-name="InProduction" style="color: #fac858;font-size:1.8vh;font-weight:600;">0</div>
                        </div>
                        <div class="task" data-id="WaitPack" data-index="06" data-fn="time,input" lay-submit>
                            <div style="line-height:6vh;">待打包</div>
                            <div data-name="WaitPack" style="color: #ee6666;font-size:1.8vh;font-weight:600;">0</div>
                        </div>

                        <div class="task" data-id="OutboundCount" data-index="07" data-fn="time,input" lay-submit>
                            <div style="line-height:6vh;">已出库</div>
                            <div data-name="OutboundCount" style="color: #14fa24;font-size:1.8vh;font-weight:600;">0</div>
                        </div>
                    </div>
                </div>
                <div class="item_right2">
                    <div class="rightTile2">今日出货统计</div>
                    <div id="taskViewBox">

                        <div class="task" style="margin-top: 3vh;height: 27%;" data-id="TodayCount" data-index="01" lay-submit>
                            <div style="line-height:6vh;text-indent:2vw;">订单数量</div>
                            <div data-name="TodayCount" style="color: #02d9fd;font-size:1.8vh;font-weight:600;">0</div>
                        </div>
                        @* <div class="task" style="margin-top: 3vh;height: 27%;" data-index="02">
                        <div style="line-height:6vh;text-indent:2vw;">暂无统计</div>
                        <div data-name="TimeoutCount" style="color: #557bf1;font-size:1.8vh;font-weight:600;">0</div>
                        </div> *@


                        <div class="task" style="margin-top: 3vh;height: 27%;" data-id="TodayWaitPicking" data-index="03" lay-submit>
                            <div style="line-height:6vh;">待领料</div>
                            <div data-name="TodayWaitPicking" style="color: #2c63fd;font-size:1.8vh;font-weight:600;">0</div>
                        </div>
                        <div class="task" data-id="TodayWaitProduction" data-index="04" lay-submit style="height: 27%;">
                            <div style="line-height:6vh;">待生产</div>
                            <div data-name="TodayWaitProduction" style="color: #91cc75;font-size:1.8vh;font-weight:600;">0</div>
                        </div>


                        <div class="task" data-id="TodayInProduction" data-index="05" lay-submit style="height: 27%;">
                            <div style="line-height:6vh;">生产中</div>
                            <div data-name="TodayInProduction" style="color: #fac858;font-size:1.8vh;font-weight:600;">0</div>
                        </div>

                        <div class="task" data-id="TodayWaitPack" data-index="06" lay-submit style="height: 27%;">
                            <div style="line-height:6vh;">待打包</div>
                            <div data-name="TodayWaitPack" style="color: #ee6666;font-size:1.8vh;font-weight:600;">0</div>
                        </div>

                        <div class="task" data-id="TodayoutboundCount" data-index="07" lay-submit style="height: 27%;">
                            <div style="line-height:6vh;">已出库</div>
                            <div data-name="TodayoutboundCount" style="color: #14fa24;font-size:1.8vh;font-weight:600;">0</div>
                        </div>


                    </div>
                </div>
                <div class="item_right">
                </div>
                <!-- 饼图 -->
                @* <div class="echarts">
                <div class="rightTile">重要消息提示</div>
                <!-- 统计图 -->
                <div id="barBox">
                <div id="bar"></div>
                </div>
                </div> *@
            </div>
        </div>

        @* 搜索结果弹窗 *@
        <div class="mask" id="mask">
            <div class="searchResponse" id="searchResponse">
                <div class="searchResponse_title">
                    <text id="response">
                        <i class="layui-icon layui-icon-search"></i> 搜索结果..
                    </text>
                </div>

                @* 渲染搜索结果*@
                <div class="searchResponse_data">
                    @* <div class="searchResponse_data_label">编号</div>
                    <div class="searchResponse_data_text" data-field="Id"></div> *@
                    <div class="searchResponse_data_label">订单编号</div>
                    <div class="searchResponse_data_text" data-field="OrderId"></div>

                    <div class="searchResponse_data_label" style="opacity:0;">打单人</div>
                    <div class="searchResponse_data_text" style="opacity:0;" data-field="OrderingUser"></div>

                    <div class="searchResponse_data_label">打单人</div>
                    <div class="searchResponse_data_text" data-field="OrderingUser"></div>

                    <div class="searchResponse_data_label">打单时间</div>
                    <div class="searchResponse_data_text" data-field="OrderingTime" data-time="true"></div>

                    <div class="searchResponse_data_label">领料人</div>
                    <div class="searchResponse_data_text" data-field="PickingUser"></div>

                    <div class="searchResponse_data_label">领料时间</div>
                    <div class="searchResponse_data_text" data-field="PickingTime" data-time="true"></div>

                    <div class="searchResponse_data_label">生产人</div>
                    <div class="searchResponse_data_text" data-field="ProductionUser"></div>

                    <div class="searchResponse_data_label">生产时间</div>
                    <div class="searchResponse_data_text" data-field="ProductionTime" data-time="true"></div>

                    <div class="searchResponse_data_label">生产审核</div>
                    <div class="searchResponse_data_text" data-field="AuditingUser"></div>

                    <div class="searchResponse_data_label">审核时间</div>
                    <div class="searchResponse_data_text" data-field="AuditingTime" data-time="true"></div>

                    <div class="searchResponse_data_label" id="isOrder">打包人</div>
                    <div class="searchResponse_data_text" data-field="PackUser"></div>

                    <div class="searchResponse_data_label" id="isOrderTime">打包时间</div>
                    <div class="searchResponse_data_text" data-field="PackTime" data-time="true"></div>

                </div>
                @* <div class="layui-card" id="centerTable">
                <div class="layui-card-body">
                <table class="layui-hide" id="responseTable"></table>
                </div>
                </div> *@

                <img src="~/font/images/close.png" class="close" id="close">
            </div>
        </div>
        @* 每个任务数据列表 *@
        <div class="mask" id="taskMask">
            <div class="taskTableBox">
                <!-- 标题  -->
                <div class="item_center_title">
                    <span class="item_center_title_span" id="taskTableTitle"></span>
                </div>
                <div style="margin-top:5vh"></div>
                <!-- 功能栏开始 -->
                <div class="function" id="function" style="display: none;">
                    <div class="function_item" style="width: 18vw;display: none;" id="_select">
                        <div class="taskLabel" style="width: 17%;min-width: 40px;"> 搜索：</div>
                        <div class="taskKey" style="width: 70%;">
                            <div id="demo1" style=" width: 100%; max-height:30px !important;"></div>
                        </div>
                    </div>
                    <div class="function_item" style="width: 25vw;min-width: 500px;overflow: hidden;display: flex;" id="_time">
                        <div class="taskLabel" style="min-width: 70px;">筛选时间：</div>
                        <div class="taskKey" style="width: 80%;min-width:400px;">
                            <input type="text" name="start" id="start" placeholder="开始时间" autocomplete="off">
                            <div style="padding: 0px .5vw;">-</div>
                            <input type="text" name="end" id="end" placeholder="结束时间" autocomplete="off">
                        </div>
                    </div>
                    <div class="function_item" style="width:18vw;display: none;" id="_input">
                        <div class="taskLabel" style="width:fit-content;min-width: 50px;text-align: left;">订单号：</div>
                        <div class="taskKey" style="width:70%">
                            <input type="text" name="key" id="taskKey" placeholder="请输入" autocomplete="off">
                        </div>
                    </div>
                </div>
                <!-- 功能栏结尾 -->
                <!-- 每个表格 -->
                <div class="layui-card" id="taskTableBox">
                    <table class="layui-hide" id="taskTable"></table>
                </div>
            </div>
            <!-- 关闭按钮 -->
            <img src="~/font/images/close.png" class="close" id="closeMask">
        </div>
    </div>
</body>
<script type="module" defer="defer">
    //将网址强制https
    var currentUrl = window.location.href;
    if (window.location.protocol !== 'https:') {
        currentUrl = currentUrl.replace('http:', 'https:');
        window.location.href = currentUrl; //重载网页
    }
    let store = new Storage()

    var form = layui.form;
    var layer = layui.layer;
    var taskTableData = {};
    var loadIndex = 0;
    var isFirstRenderTaskTable = true;
    var xmData = {};
    var isFirstOpenXMSelect = true; //用于控制第一次打开xm-select  重点是不要二次请求
    // 导入中间部分的table options
    import left_table1_options from './font/tables/left_table1.js';
    import left_table2_options from './font/tables/left_table2.js';
    import left_table3_options from './font/tables/left_table3.js';
    import left_table4_options from './font/tables/left_table4.js';
    import centerTableOptions from './font/tables/centerTable.js';
    // 01.导入每个任务数据的table options
    const tableList = ['AllCount', 'TimeoutCount', 'WaitPicking', 'InProduction', 'WaitProduction', 'WaitPack', 'OutboundCount', 'pickingOrderCount', 'pickingOrderTimeOutCount', 'pWaitPicking', 'pWaitProduction', 'pInProduction', 'pIntosStorage', 'pickingOrderIntoCount']
    const taskTableName = {}
    tableList.forEach(async (table, index) => {
        const modulePath = `./font/tables/${table}.js`;
        await import(modulePath).then((module) => {
            taskTableName[table + 'TableOptions'] = module.default;
        });
    });
    //02.每个任务的点击事件
    form.on('submit()', async function (data) {
        var $ = layui.$;
        let dom = $(data.elem)[0]
        let className = $(dom).attr("data-id");
        const keyword = $("#key").val().toUpperCase();
        const isSearchOrder = keyword.includes('SD');
        if (isSearchOrder) {
            $('#isOrder').html('打包人');
            $('#isOrderTime').html('打包时间');
        } else {
            $('#isOrder').html('入库人');
            $('#isOrderTime').html('入库时间');
        }
        if (className == 'search') { //当点击搜索按钮
            try {
                const url = isSearchOrder ? '@Url.Action("Search", "Order")' : '@Url.Action("SearchMD", "Order")';
                return await searchAPI(url, keyword)
            } catch (err) {
                if (err.status === 401 || err.status === 403) {
                    await printMessage()
                    await originData()
                    return;
                }
                layer.msg('网络连接失败', { icon: 2, skin: 'layerMsg' });
                console.log('打印报错',err);
            }

        }
        loadIndex = layui.layer.load(0, { shade: false });
        showTaskMask(className)
    })
    /** 03.打开任务表格 */
    async function showTaskMask(className) {
        var $ = layui.$;
        $("#function").css("display", "flex")
        $("#taskMask").css("width", "100%")
        $("#taskMask").css("height", "100%")
        $("#taskMask").css("opacity", "100%")
        let option = taskTableName[className + 'TableOptions']; //一次赋值
        // console.log('对比 -->',option.cols);
        if (className.includes('Today')) { //01.如果点击的是today的则重新调接口拿今日的数据就行；
            className = className.replace("Today", "")
            className === 'Count' ? className = "AllCount" : '';
            className === 'pCount' ? className = "pickingOrderCount" : '';
            className === 'pIntoCount' ? className = 'pickingOrderIntoCount' : '';
            className = className[0] === 'p' ? className : (className[0].toUpperCase() + className.slice(1));
            option = taskTableName[className + 'TableOptions'] //二次赋值
            option.title = option.title.replace("全部", "今日")
            let start = getCurrentDate() + ' 00:00:00'
            let end = getCurrentDate() + ' 23:59:59'
            $("#start").val(start);
            $("#end").val(end);
            option.where = {
                start,
                end,
            }
            option.headers.Authorization = 'Bearer ' + store.get('AccessToken') //添加token
        } else { //02.全部
            option.title = option.title.replace("今日", "全部")
            option.headers.Authorization = 'Bearer ' + store.get('AccessToken') //添加token
            option.where = {}
            $("#start").val('');
            $("#end").val('');
        }
        var dom = layui.$(`div[data-id="${className}"]`)
        var fn = $(dom).attr('data-fn'); //拥有能力
        if (fn.includes('select')) {
            $("#_select").css("display", "flex")
            //** 渲染xm-select选择器 */
            setTimeout(() => {
                layui.layer.close(loadIndex)
                renderSelect()
            }, 1100);
        }
        if (fn.includes('time')) {
            $("#_time").css("display", "flex")
        }
        if (fn.includes('input')) {
            $("#_input").css("display", "flex")
        }
        // 配置表格其它信息
        $("#taskTableTitle").text(option.title)

        setTimeout(() => {
            layui.layer.close(loadIndex)
            renderTaskTable(option, className)
        }, 1100);
    }
    var needReRender = false
    /** 04.渲染表格 */
    async function renderTaskTable(table_options,className) {
        await layui.table.render(table_options)
        // console.log('看看options --》',table_options,className);
        // // taskTableData = table_options //保存;
        // // if (table_options.cols != undefined && table_options.cols.length == 13) {
        // //     taskTableData.cols.splice(12,1)//删除最后一项
        // //     console.log('看看options --》',taskTableData.cols);
        // // }
        // if (isFirstRenderTaskTable || className == 'TimeoutCount' || needReRender) {
        //     await layui.table.render(table_options)
        //     isFirstRenderTaskTable = false //--当初次打开页面后就不再执行render而是reloadData代替数据更新行为
        //     needReRender = false;
        //     if (className == 'TimeoutCount' ) {
        //         needReRender = true;
        //     }
        // } else {

        // }
    }
    /** 05.关闭任务表格-逻辑 */
    function closeTaskMask() {
        var $ = layui.$;
        $("#function").css("display", "none")
        $("#taskMask").css("width", "0%")
        $("#taskMask").css("height", "0%")
        $("#taskMask").css("opacity", "0%")
        $("#_select").css("display", "none")
        $("#_time").css("display", "none")
        $("#_input").css("display", "none")
        $("#start").val('')
        $("#end").val('')
        $("#taskKey").val('')
        uId = '';
        if (xmData != '' && xmData != null && xmData != undefined && xmData.options != undefined) { // 清空xm-select
            xmData.setValue([])
        }
        // xmData.options.selectedKeyCode = ''
        setTimeout(async () => {
            await layui.table.reloadData('taskTable', {
                data: [{}], url: ''
            })
            layui.table.clearCacheKey('taskTable');
        }, 500);
    }
    // 点击-关闭
    document.getElementById("closeMask").addEventListener("click", function (event) {
        event.stopPropagation(); //阻止默认行为
        closeTaskMask()
        isFirstOpenXMSelect = true;
    });
    layui.table.on('tool', function (obj) {
        if (obj.event != 'reader') {
            return;
        }
        let remark = obj.data.Remark;
        let remarktime = obj.data.RemarkTime;
        let titles = "";
        if (remarktime.substring(0, 4) == "0001") {
            titles = "备注";
        } else {
            titles = '备注(' + `${remarktime}` + ')'
        }
        layui.layer.open({
            type: 1,
            title: titles,
            content: `<div id="remark">${remark}</div>`,
            area: ['300px', '200px'],
            skin: 'layui-layer-lan', //加上边框
        })
    });
    // 获取当前日期
    function getCurrentDate() {
        var today = new Date();
        var year = today.getFullYear();
        var month = String(today.getMonth() + 1).padStart(2, '0');
        var day = String(today.getDate()).padStart(2, '0');
        var currentDate = year + '-' + month + '-' + day;
        return currentDate;
    }
    var uId = '';
    // 渲染xm-select选择器
    function renderSelect() {
        var $ = layui.$
        xmData = xmSelect.render({
            el: '#demo1',
            radio: true, //单选
            name: 'uId',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            // filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            disabled: false, // 设置禁用
            style: {
                minHeight: '30px',
                minWidth: '175px',
                width: '100%',
                height: '3vh',
                lineHeight: '3vh',
                backgroundColor: 'rgb(13, 47, 119)',
                border: 'none',
            },
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];

                // 接口数据
                $.ajax({
                    url: '/Api/V1/Order/SearchUser?page=' + pageIndex,
                    method: "post",
                    headers: {
                        "content-type": "application/x-www-form-urlencoded",
                        Lng: '@language.UniqueSeoCode',
                        Id: Date.now(),
                        Authorization: 'Bearer ' + store.get('AccessToken'),

                    },
                    data: {
                        keyword: val,
                    }
                })
                    .done(res => {
                        // console.log(res);
                        if (res.success) {
                            if (res.data != null) {
                                if (res.extdata.data != null) {
                                    demo1.setValue(res.extdata.data)// 传入一个-默认值-数组
                                }
                                cb(res.data, res.extdata.PageCount);
                            }
                        }
                        else {
                            cb(obj, 0);
                            layer.msg(res.Message);
                        }
                    })
                    .fail(function (jqXHR, textStatus, errorThrown) {
                        layer.msg('网络连接失败', { icon: 2, skin: 'layerMsg' });
                    })
            },
            on: function (data, cb) {  // 监听选择
                if(isFirstOpenXMSelect){
                    isFirstOpenXMSelect = false;
                    return;
                }
                // console.log('选择：',data)
                if (data.arr.length == 0) {
                    uId = ''
                    setTimeout(() => {
                        window.active.reload()
                    }, 200)
                    return;
                }
                if (data.arr.length > 0) {
                    uId = data.arr[0].value
                    setTimeout(() => {
                        window.active.reload()
                    }, 200);
                }
            },
        });
    }

    // 监听输入订单
    layui.$("#taskKey").on("input", function (e) {
        // console.log(e)
        window.active.reload()
    });


    window.active = {
        reload: function () {
            let where = {
                key: layui.$("#taskKey").val(),
                start: layui.$("#start").val(),
                end: layui.$("#end").val(),
                uId,
            }
            for (const key in where) {
            @* console.log(where[key]) *@
                        if (where[key] == '' || where[key] == null || where[key] == undefined) {
                    delete where[key]
                }
            }
            layui.table.resize('taskTable')
            layui.table.reloadData('taskTable', { page: { curr: 1 }, where })
        }
    }
    var laydate = layui.laydate;
    var $ = layui.$;
    //时间插件
    var startDate = laydate.render({
        id: 'startDate',
        elem: '#start',
        btns: ['clear', "confirm"],//只显示清空和确定按钮
        type: 'datetime',       // 设置日期选择类型为年月
        format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
        theme: '#07379e',
        choose: function (date) {
            laydate.close(); // 关闭日期选择器弹窗
        },
        done: function (value, date) {
            $("#start").val(value);
            checkDateValidity();
        }
    });

    var endDate = laydate.render({
        id: 'endDate',
        elem: '#end',
        btns: ["clear", "confirm"],
        type: 'datetime',       // 设置日期选择类型为年月
        format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
        theme: '#07379e',
        choose: function (date) {
            laydate.close(); // 关闭日期选择器弹窗
        },
        done: function (value, date) {
            $("#end").val(value);
             checkDateValidity();
        }
    });

    function checkDateValidity() {
        var $ = layui.$;
        var startValue = $("#start").val();
        var endValue = $("#end").val();

        if (startValue && endValue) {
            // if (startValue.substr(0, 4) != endValue.substr(0, 4)) {
            //     layui.layer.msg('开始时间和结束时间必须在同一年，请重新选择。', { icon: 2, skin: 'layerMsg' });
            //     $("#start").val(""); // 清空开始时间输入框
            //     $("#end").val("");   // 清空结束时间输入框
            //     return;
            // }
            window.active.reload()

        }
    }







    // 点击-关闭   ----------------以下为大屏表格及功能逻辑 ------------------------------
    document.getElementById("close").addEventListener("click", function (event) {
        event.stopPropagation();
        hidden()
    });
    /** 调用搜索API */
    const searchAPI = async (url, orderId) => {
        var $ = layui.$
        var loadingIndex = layer.load(0)
        await $.ajax({
            url,
            method: "post",
            headers: {
                    "content-type": "application/x-www-form-urlencoded",
                    Lng: '@language.UniqueSeoCode',
                    Id: Date.now(),
                    Authorization: 'Bearer ' + store.get('AccessToken'), //该请求无需token

                },
                data: {
                    orderId
                }
            })
            .done(res => {
                if (res.Code == 1) {
                    visible(res.Data)
                } else {
                    layer.msg(res.Message);
                }
            })
            .fail(function (jqXHR, textStatus, errorThrown) {
                layer.msg('网络连接失败', { icon: 2, skin: 'layerMsg' });
            })
        layer.close(loadingIndex)
    }
    //显示搜索结果
    const visible = (data) => { //data:是一个对象
        var $ = layui.$
        var childrenList = $(".searchResponse_data").children()
        for (var i = 0; i < childrenList.length; i++) {
            if (i % 2 != 0) { //内容
                var key = childrenList[i].dataset.field
                if (childrenList[i].dataset.time != undefined && data[key][0] == 0) {
                    continue;
                }

                if (childrenList[i].dataset.IsDuplicate != undefined) {
                    childrenList[i].dataset.IsDuplicate == false ? childrenList[i].innerHTML = '否' : childrenList[i].innerHTML = '是'
                    continue;
                }
                childrenList[i].innerHTML = data[key]
    @* console.log(childrenList[i].dataset.field) *@
                    }
        }
        $("#mask").css("width", "100%")
        $("#mask").css("height", "100%")
        $("#mask").css("opacity", "100%")
        setTimeout(() => {
            $("#searchResponse").css("display", "block")
        }, 400)
    }
    //隐藏搜索结果
    const hidden = () => {
        var $ = layui.$
        $("#mask").css("width", "0%")
        $("#mask").css("height", "0%")
        $("#mask").css("opacity", "0")
        var old_data = $(".searchResponse_data_text").children().prevObject

        for (let i = 0; i < old_data.length; i++) {//清空搜索结果
            old_data[i].innerHTML = ''
        }
        setTimeout(() => {
            $("#searchResponse").css("display", "none")
        }, 400)
    }

    //-----------------------------  上面为弹窗表格相关  ------------以下为大屏渲染及滚动逻辑相关--------------------------------------//
    layui.layer.load(0)
    let originData = [];
    let centerTable_availableDataCount = 0; //初始化-可显示的数据量
    var reRreshCount = 0;

    //计算某个表格在当前窗口的-数据可视数量
    const computeTheBox_AvailableDatacount = (options) => { //ps：需要传入一个layui表格的基本配置项
        const boxHeight = options.height.replace('px', '') //01-1.获取当前区域的可视高度
        const A_cellHeight = layui.$("td")[0] === undefined ? 30 : layui.$("td")[0].offsetHeight  //02.获取任意一个表格-单元格的高度
        //03.获取到某个区域表格可视的数据数量  --这里暂时写死为centerTable
        centerTable_availableDataCount = (Math.ceil(boxHeight / A_cellHeight))
    }
    //截取数据
    const silceCenterDataFN = async (length) => {
        if (originData.NoList.length == 0) {
            return
        }
        let NoList = originData.NoList //每次都获取到最新的长度
        if (length != undefined) { //01.初始化
            console.log('初始化长度：', length);
            if (NoList.length <= length) {
                for (var i = 0; i < NoList.length; i++) {
                    if (NoList[i] == undefined) {
                        return;
                    }
                    centerTableOptions.data.push(NoList[i])
                }
                return;
            }
            //正常初始化
            for (var i = 0; i < length; i++) {
                if (NoList[i] == undefined) {
                    return;
                }
                centerTableOptions.data.push(NoList[i])
            }
            return;
        }
        length = centerTableOptions.data.length
        // 02.正常截取
        if (length <= (NoList.length + 1)) { //如果中间表格长度还比数据库长度小
            for (var i = length; i < (length + 1); i++) {
                if (NoList[i] == undefined) {
                    return;
                }
                centerTableOptions.data.push(NoList[i])
            }
        }
    }
    /** 返回登录页 */
    const backLogin = function () {
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {  // 生产模式下的逻辑
            localStorage.clear();
            store.clear();
            let currentURL = window.location.href;
            let path = window.location.href.split("/")[3]
            let newURL
            path === 'BigData' ? newURL = currentURL.replace("BigData", "UserLogin?isLogin=false") : newURL = currentURL.replace("bigData", "UserLogin?isLogin=false")
            @* window.location.href = newURL; *@
                console.log('返回-未登录', newURL);

        } else {  // 开发模式下的逻辑
            localStorage.clear();
            store.clear();
            let currentURL = window.location.href;
            let path = window.location.href.split("/")[3]
            let newURL
            path === 'BigData' ? newURL = currentURL.replace("BigData", "UserLogin?isLogin=false") : newURL = currentURL.replace("bigData", "UserLogin?isLogin=false")
            @* window.location.href = newURL; *@
                console.log('返回-未登录', newURL);
        }
    }
    /** 拉取初始数据 */
    const getOriginData = async () => {
        var $ = layui.$;
        var layer = layui.layer

        await $.ajax({
            url: '@Url.Action("GetData", "Order")',
            method: "post",
            headers: {
                Lng: '@language.UniqueSeoCode',
                Id: Date.now(),
                TimeStamp: Date.now(),
                Signature: '5454',
                Nonce: '54',
                Authorization: 'Bearer ' + store.get('AccessToken'),
            },
            success: (res) => {
                originData = res.Data
                left_table1_options.data = res.Data.OrderingList.reverse()
                left_table2_options.data = res.Data.PickingList.reverse()
                left_table3_options.data = res.Data.ProductionList.reverse()
                left_table4_options.data = res.Data.PackList.reverse()
                @* originData.NoList = []//测试用
                centerTableOptions.data = [] //测试用
                 for (let i = 0; i < 23; i++) {//测试用
                    originData.NoList.push({OrderId:i})
                } *@
                if (originData.NoList.length > 0) {
                    centerTableOptions.data = []
                }
                silceCenterDataFN(centerTable_availableDataCount) //一开始-传页面可视的单元格数量给它  centerTable_availableDataCount
                init() //02.初始化数据

                layer.closeAll()

            },
            error: function (err){
                console.log('请求失败：',err.status)
                if (err.status === 401) {
                    _refreshToken()
                    return;
                }
            }
        })
    }

    // 03.渲染table
    const init = async (type) => {
        setTimeout(() => {
            renderTable(left_table1_options)//渲染左一数据
            renderTable(left_table2_options)//渲染左一数据
            renderTable(left_table3_options)//渲染左三数据
            renderTable(left_table4_options)//渲染左四数据
            renderTable(centerTableOptions) //渲染中间数据
            initTask()//渲染
            if (type == undefined) { //第一次初始化不滚动
                @* setTimeout(() => {
                    scrollCenterTableToBottom() //开始滚动中间表格数据
                }, 500) *@
            }
        }, 0)
    }
    init('first') //初始化表格，为了做渲染优化，拿到td在当前窗口的高度。。。再去决定一次渲染多少数据
    setTimeout(async () => {
        computeTheBox_AvailableDatacount(centerTableOptions) //防止一些网速快的人
        // 01.拉取原始数据
        await getOriginData()
    }, 600)

    @* 找到最近的可滚动祖先 *@
        function getScrollParent(node) {
            if (node == null) {
                return null;
            }
            if (node.scrollHeight > node.clientHeight) {
                return node;
            } else {
                return getScrollParent(node.parentNode);
            }
        }
    /** 滚动中间表格  ---所有未完成订单 */
    async function scrollCenterTableToBottom() {
        var $ = layui.$
    @* var fakeDom = $(`div[data-table='center_table']`)[0]; *@
    @* var totalHeight = $("tbody")[4].scrollHeight //01.获取  --  总 ---可滚动的高度  --暂时弃用 *@
        let avilableScrollDom;  //02.获取 可滚动的元素
        let A_cellHeight;//03.获取一个单元格的高度
    @* const availableViewHeight = Math.ceil(A_cellHeight * centerTable_availableDataCount) // 窗口不滚动情况下直视高度 --暂时弃用 *@
        let top = 0
        let count = centerTableOptions.data.length
        //替换
        var timer1 = setInterval(async () => {
            let centerData_length = centerTableOptions.data.length;
            let originData_length = originData.NoList.length
            avilableScrollDom = getScrollParent($("tbody")[4])
            A_cellHeight = $(".layui-table-cell")[0].offsetHeight
            //拦截 - 当数据表长度比可视数量小
            if (originData_length < centerTable_availableDataCount) { //直接赋值
                // console.log('直接赋值')
                centerTableOptions.data = [...originData.NoList];
                return await layui.table.reloadData('centerTable', {
                    data: centerTableOptions.data
                    , scrollPos: 'fixed'
                });
            }
            else//01.正常滚动，两个条件都可
                //01-1.截取值滚动
                if (centerData_length <= (originData_length + 1) && centerData_length > 23) { //这里会多截取两个undefined去顶上来下面被css盖住的两个数据
                    //  console.log('01-1.截取值滚动',centerData_length,originData.NoList,(originData_length + 1));
                    await silceCenterDataFN()
                    await layui.table.reloadData('centerTable', {
                        data: centerTableOptions.data
                        , scrollPos: 'fixed'
                    });
                    top += A_cellHeight
                    count += centerData_length
                        avilableScrollDom && avilableScrollDom.scrollTo({ top: top, behavior: 'smooth' })
                    // console.log('?',getScrollParent($("tbody")[4]),centerData_length,originData_length)
                }
                else //01-2.截取完整后
                    if (centerData_length >= (originData_length + 1)) {
                        if (count >= (originData_length + 4)) { // 滚动完毕 无脑+4
                            //  console.log('滚动完毕');
                            setTimeout(() => {
                                count = centerTable_availableDataCount
                                top = 0
                                avilableScrollDom && avilableScrollDom.scrollTo({ top: top, behavior: 'smooth' })
                            }, 2000);
                        } else { //接着滚动
                            // console.log('接着滚动',count,(originData_length + 4));
                            count += 1
                            top += A_cellHeight
                            avilableScrollDom && avilableScrollDom.scrollTo({ top: top, behavior: 'smooth' })
                        }

                    }
        }, 3000) //测试 3000
        // 判断当前网络状态
        const online = navigator.onLine;
        if (!online) {
            clearInterval(timer1)
            console.log('网络连接已断开');
            return;
        }
        console.log('网络连接正常', online)
    }
    @* 格式化日期和时间 *@
        function formatTimestamp(timestamp) {
            const date = new Date(timestamp);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hour = String(date.getHours()).padStart(2, '0');
            const minute = String(date.getMinutes()).padStart(2, '0');
            const second = String(date.getSeconds()).padStart(2, '0');
            const formattedDate = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
            return formattedDate;
        }
    // 获取推送数据
    /**
    * param {newData} 新推送过来的数据---Object
    * param {options} 要更新的layui表格-初始化后-的数据与配置项  --Array
    *    isReceice 判断是否为推送过来的数据，如果是则不渲染那么勤；如果不是就是init初始的时候调用的
    */
    const receiceData = async (newData, options, isReceice = false) => { //新数据，要更新的表格的配置项，是否为推送更新的

        if (options.id == 'centerTable') { //ps:如果等于中间表格的话，也就是已出库了，需要删掉中间表格对应的订单数据

            // //01.先删originData的
            if (originData.NoList == undefined) {
                return;
            }
            originData.NoList.forEach(item => {
                if (item.OrderId === newData.OrderId) {
                    originData.NoList.splice(originData.NoList.indexOf(item), 1)
                }
            });

            // //02.删除options的
             let _centerTableData = options.data.filter(item => item.OrderId != newData.OrderId);

            //03.更新渲染
            await renderTable(_centerTableData, isReceice)

        } else if (options === 'store') {
            const timestamp = new Date(newData.ProcessTime).getTime()
            @* 调整数据格式 *@
            newData.ProcessTime = formatTimestamp(timestamp)
        } else {
            // 格式化日期和时间
            const timestamp = new Date(newData.ProcessTime).getTime()
            @* 调整数据格式 *@
            newData.ProcessTime = formatTimestamp(timestamp)
            //更新中间表格数据  --加到原始数据Array里滚动函数，会有逻辑自动更新
            originData.NoList != undefined ? originData.NoList.push(newData) : originData.NoList = []
            //把数据unshift到left_table第一行去
            options.data.unshift(newData)//----万不能将表达式写道函数传参里
            await renderTable(options, isReceice)
        }
        // console.log('收到新的数据推送：',newData,'--来自--',options.id)
    }
    @* 高亮第一行tr *@
    const lightCell = (code) => {
        var $ = layui.$;
        var currentFirstCell = $(`div[data-table="${code}"]`); //找到本列表的第一个元素
        let isDom = currentFirstCell[0]
        if (isDom != undefined) {
            let dom = isDom.parentNode.parentNode.parentNode //找到tr
    @* console.log(dom) *@
                dom.style.transition = 'all 0.5s'
                dom.style.backgroundColor = '#022371'
            setTimeout(() => {
                dom.style.backgroundColor = 'rgb(3, 16, 81)'
            }, 1000)
        }
    }
    @* 删除过期数据  --暂时弃用*@
    const deleteData = () => {
        if (left_table1_options.data.length > 3) {
            left_table1_options.data.splice(2, 1)
        }
    }
    /** 屏幕右边第一个类盒子 */
    const initTask = () => {
        const tableList = ['AllCount', 'TimeoutCount', 'WaitPicking', 'WaitProduction', 'InProduction', 'WaitPack', 'OutboundCount',
            'TodayCount', 'TodayInProduction', 'TodayWaitPack', 'TodayWaitPicking', 'TodayWaitProduction', 'TodayoutboundCount']
        // 渲染出货统计
        for (var i = 0; i < tableList.length; i++) {
            var fakeDom = layui.$(`div[data-name='${tableList[i]}']`)[0]
            if (fakeDom != undefined) {
                var dom = fakeDom
                @* console.log('dom => ', dom) *@
                layui.$(dom).text(originData[tableList[i]])
            }
        }
        // 渲染入库统计
        const feedInfo = ['pickingOrderCount', 'pickingOrderTimeOutCount', 'pWaitPicking', 'pWaitProduction', 'pInProduction',
         'pIntosStorage', 'pickingOrderIntoCount', 'pTodayCount', 'pTodayWaitPicking', 'pTodayWaitProduction', 'pTodayInProduction', 'pTodayIntosStorage', 'pTodayIntoCount']
        // 直接使用 originData 中的数据，不再创建 feedData 变量
        for (var i = 0; i < feedInfo.length; i++) {
            var fakeDom = layui.$(`div[data-name='${feedInfo[i]}']`)[0]
            if (fakeDom != undefined) {
                var dom = fakeDom
                // 判断属性属于 All 还是 Today
                if (feedInfo[i].startsWith('pToday') && originData.PickingOrder) {
                    // 属于 Today
                    layui.$(dom).text(originData.PickingOrder.Today[feedInfo[i]])
                } else {
                    // 属于 All
                    layui.$(dom).text(originData.PickingOrder?.All[feedInfo[i]])
                }
            }
        }
    }
    /** 渲染传进来的某一个任务数据 */
    const renderTask = (className,ProcessTime) => {
        if(ProcessTime == undefined || ProcessTime == 'NaN-NaN-NaN' || ProcessTime == '0000-00-00 00:00:00' ||
            ProcessTime == '' || ProcessTime == null || ProcessTime.length == 0){
                return;
            }
        try {
            let isTodayData = true; //默认为true，，如果是今天的数据，就把今日的数据做统计更新，如果不是则更新总数；
            // 格式化日期和时间
            const list = ProcessTime.split("-")[2]
            const date = Number(list.split(" ")[0])

            const today = new Date().getDate();

            today == date?isTodayData = true:isTodayData = false;
            // 制造单入库数据
            // 直接使用 originData 中的数据，不再创建 feedData 变量
            if (className == 'WaitPicking') {//今日待领料 +1 待领料+1 总订单+1 今日总订单+1
                isTodayData? layui.$(`div[data-name='TodayWaitPicking']`).text((originData['TodayWaitPicking'] += 1)):'';
                layui.$(`div[data-name='WaitPicking']`).text((originData['WaitPicking'] += 1));
                layui.$(`div[data-name='AllCount']`).text((originData['AllCount'] += 1));
                isTodayData?layui.$(`div[data-name='TodayCount']`).text((originData['TodayCount'] += 1)):'';

            } else if (className == 'WaitProduction') { // 今日待领料-1  待领料-1  |  今日待生产 +1 待生产 +1
                //WaitPicking  待领料 -1
                if(Number(layui.$(`div[data-name='WaitPicking']`).text()) != 0){
                    layui.$(`div[data-name='WaitPicking']`).text((originData['WaitPicking'] -= 1));
                }
                if(Number(layui.$(`div[data-name='TodayWaitPicking']`).text()) != 0){
                    isTodayData?layui.$(`div[data-name='TodayWaitPicking']`).text((originData['TodayWaitPicking'] -= 1)):'';
                }
                //WaitProduction 待生产 +1
                isTodayData?layui.$(`div[data-name='TodayWaitProduction']`).text((originData['TodayWaitProduction'] += 1)):'';
                layui.$(`div[data-name='WaitProduction']`).text((originData['WaitProduction'] += 1))

            } else if (className == 'InProduction') {//  今日待生产-1   待生产-1  |  今日生产中 +1 生产中 +1
                //WaitProduction 待生产 -1
                if(Number(layui.$(`div[data-name='WaitProduction']`).text()) != 0){
                    layui.$(`div[data-name='WaitProduction']`).text((originData['WaitProduction'] -= 1));
                };
                if(Number(layui.$(`div[data-name='TodayWaitProduction']`).text()) != 0){
                    isTodayData?layui.$(`div[data-name='TodayWaitProduction']`).text((originData['TodayWaitProduction'] -= 1)):'';
                };
                //InProduction 生产中 +1
                isTodayData?layui.$(`div[data-name='TodayInProduction']`).text((originData['TodayInProduction'] += 1)):'';
                layui.$(`div[data-name='InProduction']`).text((originData['InProduction'] += 1))

            } else if (className == 'WaitPack') { //      今日生产中-1   生产中-1  | 今日待打包+1  待打包 +1
                //InProduction 生产中 -1
                if(Number(layui.$(`div[data-name='InProduction']`).text()) != 0){
                    layui.$(`div[data-name='InProduction']`).text((originData['InProduction'] -= 1));
                };
                if(Number(layui.$(`div[data-name='TodayInProduction']`).text()) != 0){
                    isTodayData?layui.$(`div[data-name='TodayInProduction']`).text((originData['TodayInProduction'] -= 1)):'';
                }
                //WaitPack 待打包 +1
                isTodayData?layui.$(`div[data-name='TodayWaitPack']`).text((originData['TodayWaitPack'] += 1)):''
                layui.$(`div[data-name='WaitPack']`).text((originData['WaitPack'] += 1))

            } else if (className == 'OutboundCount') { //      今日待打包-1   待打包-1  | 今日已出库+1  已出库 +1
                //WaitPack 待打包 -1
                if(Number(layui.$(`div[data-name='WaitPack']`).text()) != 0){
                    layui.$(`div[data-name='WaitPack']`).text((originData['WaitPack'] -= 1))
                };
                if(Number(layui.$(`div[data-name='TodayWaitPack']`).text()) != 0){
                    isTodayData?layui.$(`div[data-name='TodayWaitPack']`).text((originData['TodayWaitPack'] -= 1)):'';
                };
                //OutboundCount +1 出库
                isTodayData?layui.$(`div[data-name='TodayoutboundCount']`).text((originData['TodayoutboundCount'] += 1)):''
                layui.$(`div[data-name='OutboundCount']`).text((originData['OutboundCount'] += 1))
            } else if (className === 'pWaitPicking') { // 已打单 今日待领料 +1 待领料+1 总订单+1 今日总订单+1
                // 更新今日待领料
                if(isTodayData){
                    // 更新 originData 中的数据
                    originData.PickingOrder.Today.pTodayWaitPicking += 1;
                    // 更新 UI
                    layui.$(`div[data-name='pTodayWaitPicking']`).text(originData.PickingOrder.Today.pTodayWaitPicking);
                }

                // 更新待领料
                originData.PickingOrder.All.pWaitPicking += 1;
                layui.$(`div[data-name='pWaitPicking']`).text(originData.PickingOrder.All.pWaitPicking);

                // 更新总订单数量
                originData.PickingOrder.All.pickingOrderCount += 1;
                layui.$(`div[data-name='pickingOrderCount']`).text(originData.PickingOrder.All.pickingOrderCount);

                // 更新今日总订单
                if(isTodayData){
                    originData.PickingOrder.Today.pTodayCount += 1;
                    layui.$(`div[data-name='pTodayCount']`).text(originData.PickingOrder.Today.pTodayCount);
                }
            }   else if (className === 'pWaitProduction') { // 已领料  今日待领料-1  待领料-1  |  今日待生产 +1 待生产 +1
                    // pWaitPicking 待领料 - 1
                    // 直接从 originData 中获取并更新数据
                    if(Number(layui.$(`div[data-name='pWaitPicking']`).text()) != 0){
                        // 更新 originData 中的数据
                        originData.PickingOrder.All.pWaitPicking -= 1;
                        // 更新 UI
                        layui.$(`div[data-name='pWaitPicking']`).text(originData.PickingOrder.All.pWaitPicking);
                    };

                    // pTodayWaitPicking 今日待领料 - 1
                    if(Number(layui.$(`div[data-name='pTodayWaitPicking']`).text()) != 0 && isTodayData){
                        // 更新 originData 中的数据
                        originData.PickingOrder.Today.pTodayWaitPicking -= 1;
                        // 更新 UI
                        layui.$(`div[data-name='pTodayWaitPicking']`).text(originData.PickingOrder.Today.pTodayWaitPicking);
                    };

                    //pTodayWaitProduction 今日生产中 +1
                    if(isTodayData){
                        // 更新 originData 中的数据
                        originData.PickingOrder.Today.pTodayWaitProduction += 1;
                        // 更新 UI
                        layui.$(`div[data-name='pTodayWaitProduction']`).text(originData.PickingOrder.Today.pTodayWaitProduction);
                    }

                    //pWaitProduction 生产中 +1
                    // 更新 originData 中的数据
                    originData.PickingOrder.All.pWaitProduction += 1;
                    // 更新 UI
                    layui.$(`div[data-name='pWaitProduction']`).text(originData.PickingOrder.All.pWaitProduction);
            }   else if (className === 'pInProduction') {
                // 更新待生产数据
                if(Number(layui.$(`div[data-name='pWaitProduction']`).text()) != 0){
                    // 更新 originData 中的数据
                    originData.PickingOrder.All.pWaitProduction -= 1;
                    // 更新 UI
                    layui.$(`div[data-name='pWaitProduction']`).text(originData.PickingOrder.All.pWaitProduction);
                };

                // 更新今日待生产数据
                if(Number(layui.$(`div[data-name='pTodayWaitProduction']`).text()) != 0 && isTodayData){
                    // 更新 originData 中的数据
                    originData.PickingOrder.Today.pTodayWaitProduction -= 1;
                    // 更新 UI
                    layui.$(`div[data-name='pTodayWaitProduction']`).text(originData.PickingOrder.Today.pTodayWaitProduction);
                };

                // 更新生产中数据
                // 更新今日生产中
                if(isTodayData){
                    // 更新 originData 中的数据
                    originData.PickingOrder.Today.pTodayInProduction += 1;
                    // 更新 UI
                    layui.$(`div[data-name='pTodayInProduction']`).text(originData.PickingOrder.Today.pTodayInProduction);
                }

                // 更新生产中总数
                originData.PickingOrder.All.pInProduction += 1;
                layui.$(`div[data-name='pInProduction']`).text(originData.PickingOrder.All.pInProduction);
            }   else if (className === 'pIntosStorage') { // 待入库
                // 更新生产中数据
                if(Number(layui.$(`div[data-name='pInProduction']`).text()) != 0){
                    // 更新 originData 中的数据
                    originData.PickingOrder.All.pInProduction -= 1;
                    // 更新 UI
                    layui.$(`div[data-name='pInProduction']`).text(originData.PickingOrder.All.pInProduction);
                };

                // 更新今日生产中数据
                if(Number(layui.$(`div[data-name='pTodayInProduction']`).text()) != 0 && isTodayData){
                    // 更新 originData 中的数据
                    originData.PickingOrder.Today.pTodayInProduction -= 1;
                    // 更新 UI
                    layui.$(`div[data-name='pTodayInProduction']`).text(originData.PickingOrder.Today.pTodayInProduction);
                };

                // 更新待入库数据
                // 更新今日待入库
                if(isTodayData){
                    // 更新 originData 中的数据
                    originData.PickingOrder.Today.pTodayIntosStorage += 1;
                    // 更新 UI
                    layui.$(`div[data-name='pTodayIntosStorage']`).text(originData.PickingOrder.Today.pTodayIntosStorage);
                }

                // 更新待入库总数
                originData.PickingOrder.All.pIntosStorage += 1;
                layui.$(`div[data-name='pIntosStorage']`).text(originData.PickingOrder.All.pIntosStorage);
            } else if (className === 'pickingOrderlntoCount') { // 已入库
                // 更新生产中数据
                if(Number(layui.$(`div[data-name='pIntosStorage']`).text()) != 0){
                    // 更新 originData 中的数据
                    originData.PickingOrder.All.pIntosStorage -= 1;
                    // 更新 UI
                    layui.$(`div[data-name='pIntosStorage']`).text(originData.PickingOrder.All.pIntosStorage);
                };

                // 更新今日生产中数据
                if(Number(layui.$(`div[data-name='pTodayIntosStorage']`).text()) != 0 && isTodayData){
                    // 更新 originData 中的数据
                    originData.PickingOrder.Today.pTodayIntosStorage -= 1;
                    // 更新 UI
                    layui.$(`div[data-name='pTodayIntosStorage']`).text(originData.PickingOrder.Today.pTodayIntosStorage);
                };

                // 更新待入库数据
                // 更新今日待入库
                if(isTodayData){
                    // 更新 originData 中的数据
                    originData.PickingOrder.Today.pTodayIntoCount += 1;
                    // 更新 UI
                    layui.$(`div[data-name='pTodayIntoCount']`).text(originData.PickingOrder.Today.pTodayIntoCount);
                }

                // 更新待入库总数
                originData.PickingOrder.All.pickingOrderIntoCount += 1;
                layui.$(`div[data-name='pickingOrderIntoCount']`).text(originData.PickingOrder.All.pickingOrderIntoCount);
            }
        } catch (err) {
            console.log('时间格式有误',err);
            // layui.layer.msg('时间格式有误', { icon: 2, time:60000 });
            return;
        }
    }

    /** 渲染table */
    const renderTable = (table_options, isReceice = false) => {
    @* console.log(table_options.elem +'的表格高度：',table_options.data) *@
    @* 需要转异步等dom渲染完成 *@
            setTimeout(async () => {
                await layui.table.render(table_options)
                if (isReceice && table_options.id != 'centerTable') { //如果  -为推送数据
                    setTimeout(() => {
                        lightCell(table_options.id)//把tableId(标识)传给它,要确定是哪个表格要进行高亮
                    }, 0)
                }
            }, 0)

    }
    /** 防抖函数 */
        function debounce(delay) {
            let timeoutId;
            return function (delay) {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    // console.log('触发');
                    location.reload()
                }, delay);
            }(delay)
        }

    //当浏览器尺寸发生变化
    window.addEventListener('resize', () => {
        debounce(200)
    });

    // SignalR处理
    var notifyUrl = '@DHWeb.GetSiteUrl()';
    var dgpage = '@dgPage';
    var sid = '@Sid';

    var connect;
    function initConnect() {
        `${notifyUrl}/bigdata-hub?dgpage=${dgpage}&sid=${sid}`//01.推送过来的数据
        @* console.log(`${notifyUrl}/bigdata-hub?dgpage=${dgpage}&sid=${sid}`) *@
            connect = initSignalr({
                delay: 0,
                url: `${notifyUrl}/bigdata-hub?dgpage=${dgpage}&sid=${sid}`,
                loggingLevel: signalR.LogLevel.Error,
                onNotify: dealNotify,
                onLine: function (data) {
                    @* console.log(`${JSON.stringify(data)}`) *@
                    }
            });
    }
    initConnect();
    @* let index = 0; //测试推送；
    var timer =  setInterval(() => {
        index += 1
        index === 24?clearInterval(timer):''
        dealNotify({TenantType:'Ordering',NotifyObj:{OrderId:index, ProcessTime: '2025-04-10 18:10:23'}})
    }, 1000); *@
    // 处理推送逻辑
    function dealNotify(data) {
        JSON.stringify(data)
        console.log(data)
        switch (data.TenantType) {
            case "Refresh":  // 刷新
                location.reload();
                break;

            case "Ordering":  // 左侧已打单
                // 中间区域在数组最下方增加内容
                receiceData(data.NotifyObj, left_table1_options, true) //02.告诉它执行更新列表
                renderTask('WaitPicking',data.NotifyObj.ProcessTime) //  --已打单后- 待领料
                break;

            case "Picking":  // 左侧已领料
                // 中间区域更新状态、操作人和工时
                receiceData(data.NotifyObj, left_table2_options, true) //02.告诉它执行更新列表
                renderTask('WaitProduction',data.NotifyObj.ProcessTime) //  --已领料后- 待生产
                break;

            case "Production":  // 左侧生产中
                // 中间区域更新状态、操作人和工时
                receiceData(data.NotifyObj, left_table3_options, true) //02.告诉它执行更新列表
                renderTask('InProduction',data.NotifyObj.ProcessTime) //  --生产中
                break;

            case "Pack":  // 左侧待打包
                // 中间区域更新状态、操作人和工时
                receiceData(data.NotifyObj, left_table4_options, true) //02.告诉它执行更新列表
                renderTask('WaitPack',data.NotifyObj.ProcessTime)  //  --待打包
                break;
            case "Finish":  // 订单完成
                // 移除中间区域记录
                receiceData(data.NotifyObj, centerTableOptions, true) //02.告诉它执行更新列表
                renderTask('OutboundCount',data.NotifyObj.ProcessTime)  // --已出库
                break;
            case "POrdering": // 打单
                receiceData(data.NotifyObj, 'store', true) // 转换时间
                renderTask('pWaitPicking',data.NotifyObj.ProcessTime) //  --已打单后- 待领料
                break;
            case "PPicking": // 已领料
                receiceData(data.NotifyObj, 'store', true) // 转换时间
                renderTask('pWaitProduction',data.NotifyObj.ProcessTime) //  --已领料后- 待生产
                break;

            case "PProduction":  // 左侧生产中
                // 中间区域更新状态、操作人和工时
                receiceData(data.NotifyObj, 'store', true) // 转换时间
                renderTask('pInProduction',data.NotifyObj.ProcessTime) //  --生产中
                break;
            case "PPack":  // 待入库
                // 中间区域更新状态、操作人和工时
                receiceData(data.NotifyObj, 'store', true) // 转换时间
                renderTask('pIntosStorage',data.NotifyObj.ProcessTime) //  --生产中
                break;
            case "PFinish":
                // 中间区域更新状态、操作人和工时
                receiceData(data.NotifyObj, 'store', true) // 转换时间
                renderTask('pickingOrderlntoCount',data.NotifyObj.ProcessTime) //  --生产中
            case "PushRight1":  // 推送右上数据

                break;
        }
    }
    /** 检查token是否有过期 */
    function printMessage() {
        let storage = new Storage()
        var AccessToken = storage.get("AccessToken");
        var RefreshToken = storage.get("RefreshToken");
        var AccessTokenUtcExpires = storage.get("AccessTokenUtcExpires");
        var RefreshUtcExpires = storage.get("RefreshUtcExpires");
        var Remember = storage.get("remember");

        var now = new Date().getTime(); // 获取当前时间戳

        var seconds = Math.floor((RefreshUtcExpires - now) / 1000); //  计算时间戳与当前时间之间的秒数
        var seconds1 = Math.floor((AccessTokenUtcExpires - now) / 1000); //  计算时间戳与当前时间之间的秒数

        if (AccessTokenUtcExpires === null || RefreshUtcExpires === null || seconds < 0) {
            $.ajax
                ({
                    url: '/Logout?ReturnState=1',  // 退出登录
                    dataType: 'json',
                    method: 'POST',
                    data:
                    {

                    },
                    success: function (data) {
                        if (data.code == 0) {
                            storage.remove("AccessToken");
                            storage.remove("RefreshToken");
                            storage.remove("AccessTokenUtcExpires");
                            storage.remove("RefreshUtcExpires");
                            storage.remove("remember");

                            window.location.href = "@DG.Setting.Current.LoginUrl";
                        }
                    }
                })
            return;
        }

        if (seconds1 < 900) {
            _refreshToken()
        }

    }
    /** 刷新token */
    function _refreshToken() {
        let storage = new Storage()
        var AccessToken = storage.get("AccessToken");
        var RefreshToken = storage.get("RefreshToken");
        var AccessTokenUtcExpires = storage.get("AccessTokenUtcExpires");
        var RefreshUtcExpires = storage.get("RefreshUtcExpires");
        var Remember = storage.get("remember");

        var now = new Date().getTime(); // 获取当前时间戳
        $.ajax
        ({
            url: '/Site/RefreshToken',  // 刷新Token
            dataType: 'json',
            method: 'POST',
            data:
            {
                RefreshToken: RefreshToken,
                RefreshExpireMinutes: Remember
            },
            success: function (res) {
                console.log(res);

                if (!res.success) {
                    $.ajax
                    ({
                    url: '/Logout?ReturnState=1',  // 退出登录
                    dataType: 'json',
                    method: 'POST',
                    data:
                    {

                    },
                    success: function (data) {
                        if (data.code == 0) {
                            storage.remove("AccessToken");
                            storage.remove("RefreshToken");
                            storage.remove("AccessTokenUtcExpires");
                            storage.remove("RefreshUtcExpires");
                            storage.remove("remember");

                            window.location.href = "@DG.Setting.Current.LoginUrl";
                        }
                    }
                })
                    return;
                }

                var seconds2 = res.data.RefreshUtcExpires - now; // 计算时间戳与当前时间之间的秒数

                storage.set("AccessToken", res.data.AccessToken, seconds2);
                storage.set("RefreshToken", res.data.RefreshToken, seconds2);
                storage.set("AccessTokenUtcExpires", res.data.AccessTokenUtcExpires, seconds2);
                storage.set("RefreshUtcExpires", res.data.RefreshUtcExpires, seconds2);
                storage.set("remember", Remember, seconds2);
                //因为不能async 只能 这样晚点拉数据了；
                setTimeout(()=>{
                    getOriginData()
                },200)
            }
        })
    }



    setInterval(printMessage, 60000); // 每隔1分钟输出一次
</script>
</html>