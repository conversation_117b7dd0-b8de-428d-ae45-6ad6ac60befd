﻿@{
    Html.AppendTitleParts(T("产品型号").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/components/dynamic-operation-column.js");

    // Css
    Html.AppendCssFileParts(ResourceLocation.Head, "~/css/components/dynamic-operation-column.css");
}

<style asp-location="true">
    .layui-table td,
    .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }

    xm-select {
        margin-top: 10px;
        line-height: 30px;
        min-height: 30px !important;
    }

    .layui-form-item {
        display: flex;
        flex-wrap: wrap;
        justify-content: center:
    }

    label {
        white-space: nowrap;
    }
</style>
<script asp-location="Head">
    var pleaseSelect = '@T("请选择")';
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>



<form class="layui-form dg-form">
    <div class="layui-form-item"
        style="margin-bottom: 15px; padding: 15px 10px; display: flex; flex-wrap: wrap; align-items: center; gap: 20px;">
        <!-- 订单号搜索 -->
        <div style="display: flex; align-items: center; white-space: nowrap;">
            <label class="layui-form-label" style="width: auto; margin: 0 8px 0 0; padding: 0;">@T("关键字")：</label>
            <div class="layui-input-inline" style="width: 180px; margin: 0;">
                <input type="text" name="Name" id="key" placeholder="@T("产品型号名称/物料编号")" autocomplete="off"
                    class="layui-input">
            </div>
        </div>

    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    // 设置变量保存选中行信息
    let ids = new Array();
    // 保存当前页全部数据id，点击全选时使用
    let tableIds = new Array();

    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;
        var laydate = layui.laydate;

        // 使用通用动态操作列组件
        var operationButtons = [
            @if (this.Has((PermissionFlags)4))
            {
                @:{ 
                        @:text: '@T("编辑")',
                @:event: 'edit',
                @:    class: 'pear-btn pear-btn-primary',
                @:condition: function (d) { return true; },
                @:alwaysShow: true
                        @:},
            }
            @if (this.Has((PermissionFlags)8))
            {
                @:{ 
                            @:text: '@T("删除")',
                @:event: 'del',
                @:    class: 'pear-btn pear-btn-danger',
                @:condition: function (d) { return true; },
                @:alwaysShow: true
                            @:},
            }
            @if (this.Has((PermissionFlags)16))
            {
                @:{ 
                            @:text: '@T("反馈")',
                @:event: 'feed',
                @:    class: 'pear-btn pear-btn-primary',
                @:condition: function (d) { return true; },
                @:alwaysShow: true
                            @:},
            }
            @if (this.Has((PermissionFlags)16))
            {
                @:{ 
                            @:text: '@T("品质")',
                @:event: 'quality',
                @:    class: 'pear-btn pear-btn-warming',
                @:condition: function (d) { return true; },
                @:alwaysShow: true
                            @:},
            }
        ];

    // 初始化动态操作列组件
    var operationColumnWidth = window.dynamicOperationColumn.init({
        buttons: operationButtons,
        tableId: 'tablist',
        debug: true  // 开启调试模式
    });

    table.render({
        elem: '#tablist'
        , url: '@Url.Action("GetList")'
        , page: true //开启分页
        , toolbar: '#user-toolbar'
        , defaultToolbar: [{
            title: '@T("刷新")',
            layEvent: 'refresh',
            icon: 'layui-icon-refresh',
        }, 'filter', 'print']
        , cellMinWidth: 60
        , cols: [[
            { type: 'checkbox', minWidth: 60 }
            , { field: 'Id', title: '@T("编号")', minWidth: 60 }
            , { field: 'Name', title: '@T("型号名称")', minWidth: 140 }
            , { field: 'Material', title: '@T("物料编号")', minWidth: 140 }
            , { field: 'SoftwareName', title: '@T("软件工程师")', minWidth: 140 }
            , { field: 'HardwareName', title: '@T("硬件工程师")', minWidth: 140 }
                , { title: '@T("类型")', minWidth: 140,templet:(d)=>{
                    if(d.PType == 0){
                        return '@T("无")'
                    }else if(d.PType == 1){
                        return '@T("电源")'
                    }else{
                        return ''
                    }
                } }
            , { field: 'Status', title: '@T("启用")', templet: '#switchTpl', minWidth: 130 }
            , {
                title: '@T("生成Sn/五元组")', minWidth: 120, templet: (d) => {
                    if (d.NeedSn) {
                        return '@T("是")'
                    }
                    return '@T("否")'
                }
            }
            , {
                title: '@T("生成主Mac")', minWidth: 120, templet: (d) => {
                    if (d.NeedMac) {
                        return '@T("是")'
                    }
                    return '@T("否")'
                }
            }
            , {
                title: '@T("生成副Mac")', minWidth: 120, templet: (d) => {
                    if (d.NeedMac1) {
                        return '@T("是")'
                    }
                    return '@T("否")'
                }
            }
            , { field: 'CreateUser', title: '@T("创建者")', minWidth: 140 }
            , {
                title: '@T("创建时间")', minWidth: 160, templet: (d) => {
                    if (d.CreateTime != undefined && d.CreateTime[0] != 0) {
                        return `<div>${d.CreateTime}</div>`
                    }
                    return `<div></div>`
                }
            }
            , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: operationColumnWidth }
        ]]
        , limit: 13
        , limits: [10, 13, 20, 30, 50, 100]
        , height: 'full-160'
        , id: 'tables'
        , done: function (res) {
            // 设置当前页全部数据Id到全局变量
            // tableIds = res.data.map(function (value) {
            //     return value.Id;
            // });

            // 设置当前页选中项
            $.each(res.data, function (idx, val) {
                if (ids.indexOf(val.Id) > -1) {
                    val["LAY_CHECKED"] = 'true';
                    //找到对应数据改变勾选样式，呈现出选中效果
                    let index = val['LAY_INDEX'];
                    $('tr[data-index=' + index + '] input[type="checkbox"]').click();
                    form.render('checkbox'); //刷新checkbox选择框渲染
                }
            });
            // 获取表格勾选状态，全选中时设置全选框选中
            let checkStatus = table.checkStatus('tables');
            if (checkStatus.isAll) {
                $('.layui-table-header th[data-field="0"] input[type="checkbox"]').prop('checked', true);
                form.render('checkbox'); //刷新checkbox选择框渲染
            }

            // 使用通用组件应用操作列宽度
            window.dynamicOperationColumn.delayApplyWidth('tablist', 300, true);
            console.log('产品型号列表表格渲染完成，已应用动态操作列宽度');
        },
    });

    // 监听勾选事件
    table.on('checkbox(tool)', function (obj) {
        if (obj.checked == true) {
            if (obj.type == 'one') {
                ids.push(obj.data.Id);
            } else {
                for (let i = 0; i < tableIds.length; i++) {
                    //当全选之前选中了部分行进行判断，避免重复
                    if (ids.indexOf(tableIds[i]) == -1) {
                        ids.push(tableIds[i]);
                    }
                }
            }
        } else {
            if (obj.type == 'one') {
                let i = ids.length;
                while (i--) {
                    if (ids[i] == obj.data.Id) {
                        ids.splice(i, 1);
                    }
                }
            } else {
                let i = ids.length;
                while (i--) {
                    if (tableIds.indexOf(ids[i]) != -1) {
                        ids.splice(i, 1);
                    }
                }
            }
        }
    });

    let firmwaresId = undefined;
    window.active = {
        reload: function () {
            table.reload('tables', {
                page: {
                    curr: 1
                },
                where: {
                    Name: $("#key").val(),
                    start: $("#start").val(),
                    end: $("#end").val(),
                    progress: $("#progress").val(),
                    status: $("#status").val(),
                    hasRemark: $("#hasRemark").val(),
                    firmwaresId: firmwaresId === undefined ? '' : firmwaresId
                },
                done: function (res, curr, count) {
                    try {
                        setTimeout(function () {
                            window.dynamicOperationColumn.delayApplyWidth('tablist', 300, true);
                            console.log('产品型号列表表格渲染完成，已应用动态操作列宽度');
                        }, 300);
                    } catch (error) {
                        console.error('表格重载done回调中出错:', error);
                    }
                }
            })
        }
    }

    form.on('select(progress)', function (data) {
        ids = [];
        active.reload('tables')
    });

    form.on('select(status)', function (data) {
        ids = [];
        active.reload('tables')
    });

    form.on('select(hasRemark)', function (data) {
        ids = [];
        active.reload('tables')
    });

    //监听开关事件
    form.on('switch(statusedit)', function (data) {
        //开关是否开启，true或者false
        var checked = data.elem.checked;
        //获取所属属性值
        var id = data.elem.attributes['data-id'].nodeValue;
        $.post('@Url.Action("ModifyState")', { id: id, status: checked }, function (res) {
            if (res.success) {
                abp.notify.success(res.msg);
            }
            else {
                abp.notify.error(res.msg);
            }
        });
    });

    // 监听输入订单
    $("#key").on("input", function (e) {
        ids = [];
        active.reload('tables', {
            where: { "orderId": $("#key").val() },
        })
    });

    table.on('toolbar(tool)', function (obj) {
        let data = obj.config
        var that = this
        if (obj.event === 'add') {
            window.add(data);
        } else if (obj.event === 'refresh') {
            active.reload();
        } else if (obj.event === 'refresh1') {
            window.refresh(data);
        } else if (obj.event === 'exportall') {
            var loadIndex = layui.layer.load();
            // 创建表单
            var form = $('<form></form>').attr('action', '@Url.Action("ExportAll")').attr('method', 'POST');
            form.append($('<input>').attr('type', 'hidden').attr('name', 'ids').attr('value', ids.length <= 0 ? "" : ids.join(',')));
            form.append($('<input>').attr('type', 'hidden').attr('name', 'key').attr('value', $("#key").val()));
            form.append($('<input>').attr('type', 'hidden').attr('name', 'start').attr('value', $("#start").val()));
            form.append($('<input>').attr('type', 'hidden').attr('name', 'end').attr('value', $("#end").val()));
            form.append($('<input>').attr('type', 'hidden').attr('name', 'progress').attr('value', $("#progress").val()));
            form.append($('<input>').attr('type', 'hidden').attr('name', 'status').attr('value', $("#status").val()));
            form.append($('<input>').attr('type', 'hidden').attr('name', 'hasRemark').attr('value', $("#hasRemark").val()));
            form.append($('<input>').attr('type', 'hidden').attr('name', 'UId').attr('value', UId === undefined ? '' : UId));
            // 将表单添加到body并提交
            layui.layer.close(loadIndex);
            form.appendTo('body').submit().remove();
        }
    });
    table.on('tool(tool)', function (obj) {
        var data = obj.data;
        if (obj.event === 'del') {
            parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                $.post('@Url.Action("Delete")', { Id: data.Id }, function (data) {
                    if (data.success) {
                        abp.notify.success(data.msg);
                        active.reload();
                    } else {
                        abp.notify.warn(data.msg);
                    }
                });
                parent.layer.close(index);
            });
        } else if (obj.event === 'edit') {
            window.edit(data);
        } else if (obj.event === 'readLogs') {
            window.readLogs(data);
        } else if (obj.event === 'TestPush') {
            window.TestPush(data);
        } else if (obj.event === 'end') {
            window.end(data);
        } else if (obj.event === 'cancel') {
            window.cancel(data);
        } else if (obj.event === 'setting') {
            window.setting(data);
        } else if (obj.event === 'review') {
            window.review(data);
        } else if (obj.event === 'feed') {
            window.feed(data);
        }else if (obj.event === 'quality') {
            window.quality(data);
        }
    });

    window.quality = function (data) {
        top.layui.dg.popupRight({
            id: 'quality'
            , title: ' @T("品质反馈")'
            , closeBtn: 1
            , area: ['1280px']
            , success: function (obj, index) {
                $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("QualityFeedback")' + abp.utils.formatString("?ProductTypeId={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
            }
        });
    }

    window.feed = function (data) {
        top.layui.dg.popupRight({
            id: 'feed'
            , title: ' @T("问题反馈")'
            , closeBtn: 1
            , area: ['1280px']
            , success: function (obj, index) {
                $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("FeedbackIndex")' + abp.utils.formatString("?Id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
            }
        });
    }

    window.review = function (data) {
        layer.open({
            type: 2,
            title: "@T("审核生产订单")",
            content: "@Url.Action("Audit")" + abp.utils.formatString("?Id={0}", data.Id),
            area: ["455px", "436px"],
            shade: 0.1,
            btn: ['@T("确定")', '@T("取消")'],
            yes: function (index, layero) {
                window['layui-layer-iframe' + index].submitForm();
            }
        });
    }

    window.saveCallback = function (data) {
        parent.layer.close(data.index);
        abp.notify.success(data.msg);
        table.reload("tables");
    }

    window.add = function (data) {
        top.layui.dg.popupRight({
            id: 'Add'
            , title: ' @T("新增")'
            , closeBtn: 1
            , area: ['580px']
            , success: function () {
                $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Add")" frameborder="0" class="layadmin-iframe" name="iframe' + data.index + '"></iframe>');
            }
        });
    }

    window.refresh = function (data) {
        parent.layer.confirm('@T("确认刷新投屏吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
            $.post('@Url.Action("Refresh")', { Id: data.Id }, function (data) {
                if (data.success) {
                    abp.notify.success(data.msg);
                    active.reload();
                } else {
                    abp.notify.warn(data.msg);
                }
            });
            parent.layer.close(index);
        });
    }
    window.edit = function (data) {
        top.layui.dg.popupRight({
            id: 'Edit'
            , title: ' @T("编辑")'
            , closeBtn: 1
            , area: ['580px']
            , success: function (obj, index) {
                $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Update")' + abp.utils.formatString("?id={0}&dTime={1}", data.Id, data.OrderingTime) + '" frameborder="0" class="layadmin-iframe"></iframe>');
            }
        });
    }
    window.readLogs = function (data) {
        top.layui.dg.popupRight({
            id: 'readLogs'
            , title: ' @T("查看日志")'
            , closeBtn: 1
            , area: ['880px']
            , success: function (obj, index) {
                $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("LogIndex")' + abp.utils.formatString("?Id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
            }
        });
    }

    window.TestPush = function (data) {
        top.layui.dg.popupRight({
            id: 'TestPush'
            , title: ' @T("调测推送")'
            , closeBtn: 1
            , area: ['580px']
            , success: function (obj, index) {
                $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("TestPush")' + abp.utils.formatString("?OrderId={0}&dTime={1}", data.OrderId, data.OrderingTime) + '" frameborder="0" class="layadmin-iframe"></iframe>');
            }
        });
    }

    window.end = function (data) {
        parent.layer.confirm('@T("确认完结订单吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
            $.post('@Url.Action("EndOrder")', { orderId: data.OrderId, dTime: data.OrderingTime }, function (data) {
                if (data.success) {
                    abp.notify.success(data.msg);
                    active.reload();
                } else {
                    abp.notify.warn(data.msg);
                }
            });
            parent.layer.close(index);
        });
    }

    window.cancel = function (data) {
        parent.layer.confirm('@T("确认取消订单吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
            $.post('@Url.Action("CancelOrder")', { orderId: data.OrderId, dTime: data.OrderingTime }, function (data) {
                if (data.success) {
                    abp.notify.success(data.msg);
                    active.reload();
                } else {
                    abp.notify.warn(data.msg);
                }
            });
            parent.layer.close(index);
        });
    }

    window.setting = function (data) {
        // console.log(data,data.OrderingTime);
        top.layui.dg.popupRight({
            id: 'setting'
            , title: ' @T("设置")'
            , closeBtn: 1
            , area: ['580px']
            , success: function (obj, index) {
                $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Settings")' + abp.utils.formatString("?OrderId={0}&dTime={1}", data.OrderId, data.OrderingTime) + '" frameborder="0" class="layadmin-iframe"></iframe>');
            }
        });

    }

    window.details = function (data) {
        top.layui.dg.popupRight({
            id: 'DeviceDetails'
            , title: ' @T("查看")' + '(' + data.Name + ')'
            , closeBtn: 1
            , area: ['780px']
            , success: function () {
                $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Details")' + abp.utils.formatString("?id={0}&dTime={1}", data.Id, data.OrderingTime) + '" frameborder="0" class="layadmin-iframe"></iframe>');
            }
        });
    }
    // //时间插件
    // var startDate = laydate.render({
    //     elem: '#start',
    //     btns: ['clear', "confirm"],//只显示清空和确定按钮
    //     type: 'datetime',       // 设置日期选择类型为年月
    //     format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
    //     value: currentYear + '-01-01 00:00:00', // 设置默认值为当年1月1日
    //     choose: function (date) {
    //         // 用户选择日期的回调函数
    //         // 在这里可以处理用户选择日期后的逻辑
    //         laydate.close(); // 关闭日期选择器弹窗
    //     }
    // });

    // var endDate = laydate.render({
    //     elem: '#end',
    //     btns: ["clear", "confirm"],
    //     type: 'datetime',       // 设置日期选择类型为年月
    //     format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
    //     value: currentDate + ' 23:59:59', // 设置默认值为当年1月1日
    //     choose: function (date) {
    //         console.log(date);
    //         // 用户选择日期的回调函数
    //         // 在这里可以处理用户选择日期后的逻辑
    //         laydate.close(); // 关闭日期选择器弹窗
    //     }
    // });

    function checkDateValidity() {
        var startValue = $("#start").val();
        var endValue = $("#end").val();

        if (startValue && endValue) {
            ids = [];
            active.reload("tables")

        }
    }

    window.warning = function (msg) {
        os.warning(msg);
    }
    });
</script>
<script type="text/html" id="tool">
    <div class="operation-column">
        {{#  layui.each(window.operationButtons, function(index, button){ }}
            {{#  var isEnabled = button.condition(d); }}
            {{#  var buttonClass = button.class + ' pear-btn-xs'; }}
            {{#  if(!isEnabled){ }}
                {{#  buttonClass += ' disabled-button'; }}
            {{#  } }}
            <a class="{{buttonClass}}" lay-event="{{isEnabled ? button.event : 'disabled'}}" 
               title="{{!isEnabled ? '@T("当前状态下不可操作")' : ''}}"
               data-enabled="{{isEnabled}}">{{button.text}}</a>
        {{#  }); }}
    </div>
</script>
<script type="text/html" id="user-toolbar">
    @if (this.Has((PermissionFlags)2))
    {
                    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
                        <i class="layui-icon layui-icon-add-1"></i>
                    @T("新增")
                    </button>
    }
</script>
<script type="text/html" id="switchTpl">
    <input type="checkbox" name="status" data-id="{{d.Id}}" lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" {{ d.Status ? 'checked' : ''}}>
</script>