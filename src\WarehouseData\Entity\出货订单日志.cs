﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>出货订单日志</summary>
[Serializable]
[DataObject]
[Description("出货订单日志")]
[BindIndex("IX_DH_WmsOrderLogs_OrderId_Process", false, "OrderId,Process")]
[BindIndex("IX_DH_WmsOrderLogs_CreateUserID", false, "CreateUserID")]
[BindIndex("IX_DH_WmsOrderLogs_CreateTime", false, "CreateTime")]
[BindTable("DH_WmsOrderLogs", Description = "出货订单日志", ConnName = "DH", DbType = DatabaseType.None)]
public partial class WmsOrderLogs : IWmsOrderLogs, IEntity<IWmsOrderLogs>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String _OrderId = null!;
    /// <summary>ERP订单编号</summary>
    [DisplayName("ERP订单编号")]
    [Description("ERP订单编号")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("OrderId", "ERP订单编号", "", Master = true)]
    public String OrderId { get => _OrderId; set { if (OnPropertyChanging("OrderId", value)) { _OrderId = value; OnPropertyChanged("OrderId"); } } }

    private Int32 _Process;
    /// <summary>操作工序。1为打单，2为领料，3为生产，4为生产审核，5为出货，6为人为结束，7为取消订单，8为设置，9为打包</summary>
    [DisplayName("操作工序")]
    [Description("操作工序。1为打单，2为领料，3为生产，4为生产审核，5为出货，6为人为结束，7为取消订单，8为设置，9为打包")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Process", "操作工序。1为打单，2为领料，3为生产，4为生产审核，5为出货，6为人为结束，7为取消订单，8为设置，9为打包", "")]
    public Int32 Process { get => _Process; set { if (OnPropertyChanging("Process", value)) { _Process = value; OnPropertyChanged("Process"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "", DataScale = "timeShard:yy")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _Remark;
    /// <summary>备注</summary>
    [DisplayName("备注")]
    [Description("备注")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Remark", "备注", "")]
    public String? Remark { get => _Remark; set { if (OnPropertyChanging("Remark", value)) { _Remark = value; OnPropertyChanged("Remark"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IWmsOrderLogs model)
    {
        Id = model.Id;
        OrderId = model.OrderId;
        Process = model.Process;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        Remark = model.Remark;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "OrderId" => _OrderId,
            "Process" => _Process,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "Remark" => _Remark,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "OrderId": _OrderId = Convert.ToString(value); break;
                case "Process": _Process = value.ToInt(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "Remark": _Remark = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static WmsOrderLogs? FindById(Int32 id)
    {
        if (id < 0) return null;

        return Find(_.Id == id);
    }

    /// <summary>根据创建时间查找</summary>
    /// <param name="createTime">创建时间</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrderLogs> FindAllByCreateTime(DateTime createTime)
    {
        if (createTime.Year < 1000) return [];

        return FindAll(_.CreateTime == createTime);
    }
    #endregion

    #region 数据清理
    /// <summary>清理指定时间段内的数据</summary>
    /// <param name="start">开始时间。未指定时清理小于指定时间的所有数据</param>
    /// <param name="end">结束时间</param>
    /// <returns>清理行数</returns>
    public static Int32 DeleteWith(DateTime start, DateTime end)
    {
        if (start == end) return Delete(_.CreateTime == start);

        return Delete(_.CreateTime.Between(start, end));
    }

    /// <summary>删除指定时间段内的数据表</summary>
    /// <param name="start">开始时间</param>
    /// <param name="end">结束时间</param>
    /// <returns>清理行数</returns>
    public static Int32 DropWith(DateTime start, DateTime end)
    {
        return Meta.AutoShard(start, end, session =>
        {
            try
            {
                return session.Execute($"Drop Table {session.FormatedTableName}");
            }
            catch (Exception ex)
            {
                XTrace.WriteException(ex);
                return 0;
            }
        }
        ).Sum();
    }
    #endregion

    #region 字段名
    /// <summary>取得出货订单日志字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>ERP订单编号</summary>
        public static readonly Field OrderId = FindByName("OrderId");

        /// <summary>操作工序。1为打单，2为领料，3为生产，4为生产审核，5为出货，6为人为结束，7为取消订单，8为设置，9为打包</summary>
        public static readonly Field Process = FindByName("Process");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>备注</summary>
        public static readonly Field Remark = FindByName("Remark");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得出货订单日志字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>ERP订单编号</summary>
        public const String OrderId = "OrderId";

        /// <summary>操作工序。1为打单，2为领料，3为生产，4为生产审核，5为出货，6为人为结束，7为取消订单，8为设置，9为打包</summary>
        public const String Process = "Process";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>备注</summary>
        public const String Remark = "Remark";
    }
    #endregion
}
