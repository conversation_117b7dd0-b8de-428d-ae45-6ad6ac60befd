﻿@{
    Html.AppendTitleParts(T("库存告警").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}

<style asp-location="true">
    .layui-table td,
    .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }

    xm-select {
        margin-top: 10px;
        line-height: 30px;
        min-height: 30px !important;
    }

    .layui-form-item {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
    }

    label {
        white-space: nowrap;
    }
</style>

<script asp-location="Head">
    var pleaseSelect = '@T("请选择")';
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<form class="layui-form dg-form" id="importForm" enctype="multipart/form-data">
    <div class="layui-form-item" style="margin-bottom: 3px;">
        <label class="layui-form-label" style="width: auto;margin:10px 5px 0 10px;"> @T("物料编号")：</label>
        <div class="layui-input-inline" style="width: auto;margin-top:10px;">
            <input type="text" name="materialNumber" id="materialNumber" placeholder="@T("请输入物料编号")" autocomplete="off"
                class="layui-input">
        </div>

        <label class="layui-form-label" style="width: auto;margin:10px 5px 0 10px;"> @T("规格型号")：</label>
        <div class="layui-input-inline" style="width: auto;margin-top:10px;">
            <input type="text" name="specificationModel" id="specificationModel" placeholder="@T("请输入规格型号(模糊查询)")"
                autocomplete="off" class="layui-input">
        </div>

        <label class="layui-form-label" style="width: auto;margin:10px 5px 0 10px;"> @T("仓库编号")：</label>
        <div class="layui-input-inline" style="width: auto;margin-top:10px;">
            <input type="text" name="warehouseNumber" id="warehouseNumber" placeholder="@T("请输入仓库编号")"
                autocomplete="off" class="layui-input">
        </div>

        <div class="layui-inline" style="padding-top: 10px;">
            <label class="layui-form-label" style="width: auto;margin:0px 0px 0 10px;">@T("创建时间")：</label>
            <div class="layui-inline" id="ID-laydate-range">
                <div class="layui-input-inline">
                    <input type="text" name="start" id="start" readonly placeholder="@T("开始时间")" autocomplete="off"
                        class="layui-input">
                </div>

                <div class="layui-form-mid">-</div>
                <div class="layui-input-inline">
                    <input type="text" name="end" id="end" readonly placeholder="@T("结束时间")" autocomplete="off"
                        class="layui-input">
                </div>
            </div>
        </div>
    </div>
    <input type="file" id="importFile" name="file" style="display: none;" />
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    // 设置变量保存选中行信息
    let ids = new Array();
    // 保存当前页全部数据id，点击全选时使用
    let tableIds = new Array();

    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree', 'laydate', 'upload', 'element', 'layer'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;
        var laydate = layui.laydate;
        var upload = layui.upload;
        var element = layui.element;
        var layer = layui.layer;

        // 日期范围 - 左右面板独立选择模式
        laydate.render({
            elem: '#ID-laydate-range',
            range: ['#start', '#end'],
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            done: function (value, date) {
                $("#start").val(value.split(" - ")[0]);
                $("#end").val(value.split(" - ")[1]);
                checkDateValidity();
            },
            choose: function (date) {
                laydate.close();
            }
        });

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetList")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 60
            , cols: [[
                { type: 'checkbox', width: 60 }
                , { field: 'Id', title: '@T("编号")', width: 80 }
                , { field: 'MaterialNumber', title: '@T("物料编号")', width: 140 }
                , { field: 'MaterialName', title: '@T("物料名称")', width: 140 }
                , { field: 'Category', title: '@T("种类")', width: 100 }
                , { field: 'SpecificationModel', title: '@T("规格型号")', width: 140 }
                , { field: 'Unit', title: '@T("单位")', width: 80 }
                , { field: 'WarehouseNumber', title: '@T("仓库编号")', width: 140 }
                , { field: 'WarehouseName', title: '@T("仓库名称")', width: 140 }
                , { field: 'AlarmQuantity', title: '@T("告警数量")', width: 100 }
                , { field: 'CreateTime', title: '@T("创建时间")', width: 160 }
                , { field: 'UpdateTime', title: '@T("更新时间")', width: 160 }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', minWidth: 180 }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'tables'
            , done: function (res) {
                // 设置当前页全部数据Id到全局变量
                tableIds = res.data.map(function (value) {
                    return value.Id;
                });

                // 设置当前页选中项
                $.each(res.data, function (idx, val) {
                    if (ids.indexOf(val.Id) > -1) {
                        val["LAY_CHECKED"] = 'true';
                        //找到对应数据改变勾选样式，呈现出选中效果
                        let index = val['LAY_INDEX'];
                        $('tr[data-index=' + index + '] input[type="checkbox"]').click();
                        form.render('checkbox'); //刷新checkbox选择框渲染
                    }
                });
                // 获取表格勾选状态，全选中时设置全选框选中
                let checkStatus = table.checkStatus('tables');
                if (checkStatus.isAll) {
                    $('.layui-table-header th[data-field="0"] input[type="checkbox"]').prop('checked', true);
                    form.render('checkbox'); //刷新checkbox选择框渲染
                }
            }
        });

        // 监听勾选事件
        table.on('checkbox(tool)', function (obj) {
            if (obj.checked == true) {
                if (obj.type == 'one') {
                    ids.push(obj.data.Id);
                } else {
                    for (let i = 0; i < tableIds.length; i++) {
                        //当全选之前选中了部分行进行判断，避免重复
                        if (ids.indexOf(tableIds[i]) == -1) {
                            ids.push(tableIds[i]);
                        }
                    }
                }
            } else {
                if (obj.type == 'one') {
                    let i = ids.length;
                    while (i--) {
                        if (ids[i] == obj.data.Id) {
                            ids.splice(i, 1);
                        }
                    }
                } else {
                    let i = ids.length;
                    while (i--) {
                        if (tableIds.indexOf(ids[i]) != -1) {
                            ids.splice(i, 1);
                        }
                    }
                }
            }
        });

        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        where: {
                            materialNumber: $("#materialNumber").val(),
                            specificationModel: $("#specificationModel").val(),
                            warehouseNumber: $("#warehouseNumber").val(),
                            start: $("#start").val(),
                            end: $("#end").val()
                        }
                    });
            }
        }

        $("#materialNumber").on("input", function (e) {
            active.reload();
        });

        $("#specificationModel").on("input", function (e) {
            active.reload();
        });

        $("#warehouseNumber").on("input", function (e) {
            active.reload();
        });

        table.on('toolbar(tool)', function (obj) {
            let data = obj.config
            var that = this
            if (obj.event === 'refresh') {
                active.reload();
            } else if (obj.event === 'exportall') {
                // 创建表单数据
                var formData = new FormData();
                formData.append('ids', ids.join(','));
                formData.append('materialNumber', $("#materialNumber").val());
                formData.append('specificationModel', $("#specificationModel").val());
                formData.append('warehouseNumber', $("#warehouseNumber").val());
                formData.append('start', $("#start").val());
                formData.append('end', $("#end").val());

                // 发送请求
                $.ajax({
                    url: '@Url.Action("ExportAll")',
                    type: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    xhrFields: {
                        responseType: 'blob' // 将响应类型设置为blob
                    },
                    success: function (res, status, xhr) {
                        var disposition = xhr.getResponseHeader('Content-Disposition');
                        if (disposition && disposition.indexOf('attachment') !== -1) {
                            // 如果是文件，创建一个下载链接并点击
                            var link = document.createElement('a');
                            var url = window.URL.createObjectURL(res);
                            link.href = url;
                            link.download = 'InventoryAlarm_' + new Date().toISOString().slice(0, 19).replace(/:/g, '-') + '.xlsx';
                            document.body.appendChild(link);
                            link.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(link);
                        } else {
                            abp.notify.warn('@T("库存数据没有缓存，请重新导入")');
                        }
                    },
                    error: function () {
                        abp.notify.warn('@T("库存数据没有缓存，请重新导入")');
                    }
                });
            } else if (obj.event === 'import') {
                $("#importFile").click();
            } else if (obj.event === 'delete') {
                if (ids.length === 0) {
                    abp.notify.warn('@T("请选择要删除的记录")');
                    return;
                }
                parent.layer.confirm('@T("确认删除选中的记录吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("DeleteMultiple")', { idss: ids.join(',') }, function (data) {
                        if (data.success) {
                            abp.notify.success(data.msg);
                            ids = [];
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            }
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success(data.msg);
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'edit') {
                window.edit(data);
            }
        });

        window.saveCallback = function (data) {
            parent.layer.close(data.index);
            abp.notify.success(data.msg);
            table.reload("tables");
        }

        window.edit = function (data) {
            top.layui.dg.popupRight({
                id: 'Edit'
                , title: ' @T("编辑库存告警")'
                , closeBtn: 1
                , area: ['580px']
                , success: function (obj, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Edit")' + abp.utils.formatString("?id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }
        // 文件上传
        $("#importFile").on("change", function () {
            var formData = new FormData($("#importForm")[0]);

            // 弹出模态对话框，内容为进度条
            var index = layer.open({
                type: 1,
                title: '@T("文件上传中")',
                closeBtn: 0, // 不显示关闭按钮
                shadeClose: false, // 不允许点击遮罩关闭
                shade: 0.5, // 遮罩透明度
                area: ['400px', '110px'], // 宽高
                content: '<div class="layui-progress layui-progress-big" lay-showPercent="true" lay-filter="uploadProgress" style="margin: 20px;"><div class="layui-progress-bar layui-bg-blue" lay-percent="0%"></div></div>'
            });

            // 初始化进度条
            layui.element.progress('uploadProgress', '0%');

            $.ajax({
                url: '@Url.Action("Import")',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                xhr: function () {
                    var xhr = new XMLHttpRequest();
                    // 上传进度
                    xhr.upload.addEventListener('progress', function (evt) {
                        if (evt.lengthComputable) {
                            var percentComplete = Math.floor((evt.loaded / evt.total) * 50); // 上传阶段最大50%
                            layui.element.progress('uploadProgress', percentComplete + '%');
                        }
                    }, false);

                    // 上传完成，进度条设为50%
                    layui.element.progress('uploadProgress', '50%');

                    // **修改弹出框标题为“文件解析中”**
                    layer.title('@T("文件解析中")', index);

                    // 模拟文件解析进度
                    var parseProgress = 50;
                    var parseInterval = setInterval(function () {
                        if (parseProgress < 99) {
                            parseProgress++;
                            layui.element.progress('uploadProgress', parseProgress + '%');
                        } else {
                            clearInterval(parseInterval);
                            // 解析完成，进度设为100%
                            layui.element.progress('uploadProgress', '100%');
                        }
                    }, 1000); // 每1000ms增加1%

                    return xhr;
                },
                success: function (res) {
                    if (res.success) {
                        // 提示成功信息
                        abp.notify.success(res.msg);
                        active.reload();
                        // 关闭弹出框
                        setTimeout(function () {
                            layer.close(index);
                        }, 500);

                        // 弹出右侧窗口显示导入数据
                        layer.open({
                            type: 1,
                            title: '@T("导入结果")',
                            area: ['1000px', '800px'], // 增加窗口大小
                            offset: 'r',
                            content: '<div id="importResultTable"></div>',
                            success: function (layero, index) {
                                table.render({
                                    elem: '#importResultTable',
                                    data: res.data,
                                    cols: [[
                                        { field: 'MaterialNumber', title: '@T("物料编号")', width: 180 },
                                        { field: 'MaterialName', title: '@T("物料名称")', width: 180 },
                                        { field: 'Category', title: '@T("种类")', width: 120 },
                                        { field: 'SpecificationModel', title: '@T("规格型号")', width: 180 },
                                        { field: 'Unit', title: '@T("单位")', width: 100 },
                                        { field: 'WarehouseNumber', title: '@T("仓库编号")', width: 180 },
                                        { field: 'WarehouseName', title: '@T("仓库名称")', width: 180 },
                                        { field: 'AlarmQuantity', title: '@T("告警数量")', width: 120 },
                                        { field: 'AvailableQuantity', title: '@T("可用数")', width: 120 },
                                        { field: 'CreateTime', title: '@T("创建时间")', width: 200 },
                                        { field: 'UpdateTime', title: '@T("更新时间")', width: 200 }
                                    ]]
                                });
                            }
                        });

                    } else {
                        abp.notify.warn(res.msg);
                        // 关闭弹出框
                        setTimeout(function () {
                            layer.close(index);
                        }, 500);
                    }

                    // 重置文件输入控件的值
                    $('#importFile').val('');
                },
                error: function () {
                    // 请求错误，关闭弹出框
                    layer.close(index);
                    abp.notify.error('@T("文件上传失败")');

                    // 重置文件输入控件的值
                    $('#importFile').val('');
                }
            });
        });

    });

    function checkDateValidity() {
        var startDate = new Date($('#start').val());
        var endDate = new Date($('#end').val());
        if (startDate > endDate) {
            abp.notify.warn('@T("开始时间不能大于结束时间")');
        } else {
            active.reload();
        }
    }
</script>

<script type="text/html" id="user-toolbar">
    <div class="layui-btn-container">
        <button class="layui-btn pear-btn-primary pear-btn-md" lay-event="import">@T("导入")</button>
        <button class="layui-btn pear-btn-primary pear-btn-md" lay-event="exportall">@T("导出")</button>
        <button class="layui-btn pear-btn-danger pear-btn-md" lay-event="delete">@T("批量删除")</button>
    </div>
</script>

<script type="text/html" id="tool">
    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="edit">@T("编辑")</a>
    <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="del">@T("删除")</a>
</script>
