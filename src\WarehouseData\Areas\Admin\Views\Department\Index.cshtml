﻿@{
    Html.AppendTitleParts(T("部门管理").Text);
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }
</style>

<script asp-location="Head">
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("名称")：</label>
            <div class="layui-input-inline">
                <input type="text" name="name" id="name" placeholder="@T("请输入部门名称")" autocomplete="off" class="layui-input" lay-filter="name">
            </div>
        </div>
        <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="organization-query">
            <i class="layui-icon layui-icon-search"></i>
            @T("查询")
        </button>
    </div>
</form>

<div class="layui-row layui-col-space15">
    <div class="layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-body">
                <div style="overflow: hidden">
                    <ul id="organization-tree" class="dtree"></ul>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-col-md9">
        <div class="layui-card">
            <div class="layui-card-body">
                <table id="organization-table" lay-filter="organization-table"></table>
            </div>
        </div>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['table', 'abp', 'dg', 'form', 'dtree', 'dgcommon'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var dtree = layui.dtree;
        var os = layui.dgcommon;

        dtree.render({
            elem: "#organization-tree",
            initLevel: "10",
            line: true,
            ficon: ["1", "-1"],
            icon: ["0", "2"],
            method: 'get',
            url: "@Url.Action("GetOrganizationUnitList", "Department")",
            response: {
                statusName: "code",
                message: "msg",
                statusCode: 200,
                title: "displayName"
            },
            dataStyle: "layuiStyle",
            dataFormat: "list"
        });

        dtree.on("node(organization-tree)", function (obj) {
            table.reload('organization-table', {
                where: { "id": obj.param.nodeId },
                page: {
                    curr: 1
                }
            })
        });

        table.render({
            elem: "#organization-table",
            url: "@Url.Action("GetPagedOrganizationUnit", "Department")",
            height: 'full-150',
            page: false,
            limit: 20,
            cols: [
                [{
                    type: 'checkbox'
                },
                {
                    title: '@T("部门名称")',
                    field: 'Name',
                    align: 'center'
                },
                {
                    title: '@T("上级部门")',
                    field: 'ParentName',
                    align: 'center'
                },
                {
                    title: '@T("成员数量")',
                    field: 'MemberCount',
                    align: 'center'
                },
                {
                    title: '',
                    toolbar: '#tool',
                    align: 'center',
                    width: 130
                }
                ]
            ],
            skin: 'line',
            loading: true,
            toolbar: '#organization-toolbar',
            defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
        });

        table.on('tool(organization-table)', function (obj) {
            var data = obj.data;
            if (obj.event === 'edit') {
                window.edit(data);
            } else if (obj.event === "del") {
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.ID }, function (data) {
                        if (data.success) {
                            abp.notify.success('@T("删除成功")');
                            active.reload();
                        } else {
                            //alert(data.msg);
                            //os.success(data.msg);
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            }
        });

        table.on('toolbar(organization-table)', function (obj) {
            if (obj.event === 'add') {
                window.add();
                //top.layui.dg.popupRight({
                //    id: 'EditUser'
                //    , title: ' @T("新增部门")'
                //    , closeBtn: 1
                //    , area: ['710px']
                //    , success: function () {
                //        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("EditUser")" frameborder="0" class="layadmin-iframe"></iframe>');
                //    }

                //});
            } else if (obj.event === 'refresh') {
                active.reload();
            } else if (obj.event === 'batchRemove') {
                let checkData = table.checkStatus(obj.config.id).data;
                if (checkData.length === 0) {
                    parent.layer.msg("@T("未选中数据")", {
                        icon: 3,
                        time: 1000
                    });
                    return false;
                }
                var ids = checkData.map(function (d) { return { "id": d.ID }; });
                window.remove(ids);
            }
        });

        form.on('submit(organization-query)', function (data) {
            table.reload('organization-table', {
                where: data.field,
                page: {
                    curr: 1
                }
            })
            return false;
        });

        window.active = {
            reload: function () {
                table.reload('organization-table',
                    {
                        where: {
                            name: $("#name").val(),
                        }
                    });
                dtree.reload("organization-tree");
            }
        }

        window.warning = function (msg) {
            abp.notify.warn(msg);
        }

        window.saveCallback = function (data) {
            parent.layer.close(data.index);
            abp.notify.success(data.msg);
            table.reload("organization-table");
            dtree.reload("organization-tree");
        }

        window.add = function () {
            let currentNode = dtree.getNowParam("organization-tree");
            parent.layer.open({
                type: 2,
                title: currentNode.nodeId == undefined ? '@T("添加部门")' : '@T("添加子部门")',
                content: "@Url.Action("Add", "Department")" + abp.utils.formatString("?parentId={0}", currentNode.nodeId == undefined ? "" : currentNode.nodeId),
                area: ['450px', '219px'],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    parent.window['layui-layer-iframe' + index].submitForm();
                }
            });
        }

        window.edit = function (data) {
            parent.layer.open({
                type: 2,
                title: '@T("编辑部门信息")',
                content: "@Url.Action("Edit", "Department")" + abp.utils.formatString("?id={0}", data.ID),
                area: ['450px', '219px'],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index) {
                    parent.window['layui-layer-iframe' + index].submitForm();
                }
            });
        }

        window.remove = function (data) {
            parent.layer.confirm('@T("确定删除吗?")', {
                icon: 3,
                title: '@T("提示")'
            }, function (index) {
                parent.layer.close(index);
                let waitIndex = parent.layer.load(2);
                abp.ajax({
                    url: "@Url.Action("DeleteMore", "Department")",
                    data: JSON.stringify(data),
                    abpHandleError: false
                }).done(function (data) {
                    if (data.success) {
                        abp.notify.success(data.msg);
                        table.reload('organization-table');
                        dtree.reload("organization-tree");
                    } else {
                        abp.notify.warn(data.msg);
                    }
                }).fail(function (jqXHR) {
                    parent.layer.msg(jqXHR.message, { icon: 5 });
                }).always(function () {
                    parent.layer.close(waitIndex);
                });
            });
        }

    });
</script>

<script type="text/html" id="organization-toolbar">
    @if (this.Has((PermissionFlags)2)){
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>@T("新增")
    </button>
    }
    @if (this.Has((PermissionFlags)8)){
    <button class="pear-btn pear-btn-danger pear-btn-md" lay-event="batchRemove">
        <i class="layui-icon layui-icon-delete"></i>@T("删除")
    </button>
    }
</script>

<script type="text/html" id="tool">
    @if (this.Has((PermissionFlags)4)){
    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="edit"> @T("编辑")</a>
    }
    @if (this.Has((PermissionFlags)8)){
    <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="del"> @T("删除")</a>
    }
</script>