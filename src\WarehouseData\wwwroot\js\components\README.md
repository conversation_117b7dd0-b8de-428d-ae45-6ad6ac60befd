# 动态操作列组件使用说明

## 概述
动态操作列组件是一个通用解决方案，用于LayUI表格中自动计算和渲染操作列宽度，支持基于权限的按钮显示/禁用控制。

## 文件结构
```
wwwroot/
├── css/
│   └── components/
│       └── dynamic-operation-column.css    # 样式文件
└── js/
    └── components/
        └── dynamic-operation-column.js     # 核心JS组件
```

## 使用方法

### 1. 引入资源文件
在页面中引入CSS和JS文件：

```html
<!-- 引入CSS -->
<link href="~/css/components/dynamic-operation-column.css" rel="stylesheet" />

<!-- 引入JS (需要在jQuery和LayUI之后) -->
<script src="~/js/components/dynamic-operation-column.js"></script>
```

### 2. 配置按钮
```javascript
// 定义按钮配置
var operationButtons = [
    { 
        text: '审核', 
        event: 'review', 
        class: 'pear-btn pear-btn-success', 
        condition: function(d) { return d.Status == 0 || d.Status == 1; }, 
        alwaysShow: true 
    },
    { 
        text: '编辑', 
        event: 'edit', 
        class: 'pear-btn pear-btn-primary', 
        condition: function(d) { return true; },
        alwaysShow: true
    },
    { 
        text: '删除', 
        event: 'del', 
        class: 'pear-btn pear-btn-danger', 
        condition: function(d) { return d.Status == 0; }, 
        alwaysShow: true
    }
];
```

### 3. 初始化组件
```javascript
// 初始化动态操作列
var operationColumnWidth = window.dynamicOperationColumn.init({
    buttons: operationButtons,
    tableId: 'tablist',  // 表格ID
    debug: true  // 开启调试模式
});
```

### 4. 在表格配置中使用
```javascript
table.render({
    elem: '#tablist',
    // ... 其他配置
    cols: [[
        // ... 其他列
        { 
            fixed: 'right', 
            title: '操作', 
            toolbar: '#tool', 
            width: operationColumnWidth  // 使用计算出的宽度
        }
    ]],
    done: function(res) {
        // 表格渲染完成后应用宽度
        window.dynamicOperationColumn.delayApplyWidth(300, true);
    }
});
```

### 5. 模板配置
```html
<script type="text/html" id="tool">
    <div class="operation-column">
        {{#  layui.each(window.operationButtons, function(index, button){ }}
            {{# if(button.alwaysShow || (button.condition && button.condition(d))) { }}
                {{# var isDisabled = button.condition && !button.condition(d); }}
                {{# var disabledClass = isDisabled ? ' disabled-button' : ''; }}
                {{# var eventAttr = isDisabled ? 'data-event="disabled"' : 'lay-event="' + button.event + '"'; }}
                <a class="{{button.class}}{{disabledClass}}" {{eventAttr}}>{{button.text}}</a>
            {{# } }}
        {{# }); }}
    </div>
</script>
```

## 按钮配置参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| `text` | string | 按钮显示文本 |
| `event` | string | 按钮事件名称 |
| `class` | string | 按钮CSS类名 |
| `condition` | function | 按钮可操作条件函数，接收行数据作为参数 |
| `alwaysShow` | boolean | 是否始终显示按钮（即使条件不满足也显示，但会置为禁用状态） |

## API 方法

### init(config)
初始化组件
- `config.buttons`: 按钮配置数组
- `config.tableId`: 表格ID
- `config.debug`: 是否开启调试模式

### calculateOperationColumnWidth()
计算操作列宽度

### applyOperationColumnWidth(useMeasurement)
应用操作列宽度
- `useMeasurement`: 是否使用DOM测量

### delayApplyWidth(delay, useMeasurement)
延迟应用宽度
- `delay`: 延迟时间（毫秒）
- `useMeasurement`: 是否使用DOM测量

### updateButtons(buttons)
更新按钮配置

### getOperationColumnWidth()
获取当前计算的列宽度

## 使用示例

完整的使用示例请参考生产订单页面的实现。

## 注意事项

1. 确保在LayUI和jQuery加载完成后再引入此组件
2. 表格ID必须正确设置，否则DOM测量功能无法正常工作
3. 建议在表格的`done`回调中调用`delayApplyWidth`方法
4. 开启调试模式可以在控制台查看详细的计算过程
5. 按钮的`condition`函数返回值决定按钮是否可操作，但`alwaysShow`为true时按钮始终显示