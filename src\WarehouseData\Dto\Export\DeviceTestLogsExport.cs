﻿using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

namespace HlktechIoT.Dto.Export
{
    [ExcelExporter(Name = "Sheet1", AutoFitAllColumn = true, TableStyle = OfficeOpenXml.Table.TableStyles.None, AutoCenter = true)]
    public class DeviceTestLogsExport
    {
        /// <summary> 
        /// 批次号
        /// </summary> 
        [ExporterHeader(DisplayName = "批次号", IsBold = false)]
        public int? BatchNum { get; set; }

        /// <summary> 
        /// 模块型号
        /// </summary> 
        [ExporterHeader(DisplayName = "模块型号", IsBold = false)]
        public string? ModuleType { get; set; }

        /// <summary> 
        /// 模块id
        /// </summary> 
        [ExporterHeader(DisplayName = "模块id", IsBold = false)]
        public string? ModuleId { get; set; }

        /// <summary> 
        /// 固件版本
        /// </summary> 
        [ExporterHeader(DisplayName = "固件版本", IsBold = false)]
        public string? SdkVersion { get; set; }

        /// <summary> 
        /// mac地址
        /// </summary> 
        [ExporterHeader(DisplayName = "mac地址", IsBold = false)]
        public string? DeviceMac { get; set; }

        /// <summary> 
        /// 去重信息
        /// </summary> 
        [ExporterHeader(DisplayName = "去重信息", IsBold = false)]
        public string? DeduplicateInfo { get; set; }

        /// <summary> 
        /// 检测时间
        /// </summary> 
        [ExporterHeader(DisplayName = "检测时间", IsBold = false, Format = "yyyy-MM-dd HH:mm:ss", Width = 20)]
        public string? TestTime { get; set; }

        /// <summary> 
        /// 检测工厂
        /// </summary> 
        [ExporterHeader(DisplayName = "检测工厂", IsBold = false)]
        public string? TestFactory { get; set; }

        /// <summary> 
        /// 检测工位
        /// </summary> 
        [ExporterHeader(DisplayName = "检测工位", IsBold = false)]
        public string? TestPosition { get; set; }

        /// <summary> 
        /// 监测数据
        /// </summary> 
        [ExporterHeader(DisplayName = "监测数据", IsBold = false)]
        public string? TestData { get; set; }

        /// <summary> 
        /// 销售信息
        /// </summary> 
        [ExporterHeader(DisplayName = "销售信息", IsBold = false)]
        public string? SaleInfo { get; set; }

        /// <summary> 
        /// 客户信息
        /// </summary> 
        [ExporterHeader(DisplayName = "客户信息", IsBold = false)]
        public string? CustomerInfo { get; set; }

        /// <summary> 
        /// 预留字段1
        /// </summary> 
        [ExporterHeader(DisplayName = "预留字段1", IsBold = false)]
        public string? Reserve1 { get; set; }

        /// <summary> 
        /// 预留字段2
        /// </summary> 
        [ExporterHeader(DisplayName = "预留字段2", IsBold = false)]
        public string? Reserve2 { get; set; }

        /// <summary> 
        /// 创建时间
        /// </summary> 
        [ExporterHeader(DisplayName = "创建时间", IsBold = false, Format = "yyyy-MM-dd HH:mm:ss", Width = 20)]
        public DateTime? CreateTime { get; set; }

        /// <summary> 
        /// 创建者
        /// </summary> 
        [ExporterHeader(DisplayName = "创建者", IsBold = false)]
        public string? CreateUser { get; set; }

        /// <summary> 
        /// 创建地址
        /// </summary> 
        [ExporterHeader(DisplayName = "创建地址", IsBold = false)]
        public string? CreateIP { get; set; }

        /// <summary> 
        /// 更新时间
        /// </summary> 
        [ExporterHeader(DisplayName = "更新时间", IsBold = false, Format = "yyyy-MM-dd HH:mm:ss", Width = 20)]
        public DateTime? UpdateTime { get; set; }

        /// <summary> 
        /// 更新者
        /// </summary> 
        [ExporterHeader(DisplayName = "更新者", IsBold = false)]
        public string? UpdateUser { get; set; }

        /// <summary> 
        /// 更新地址
        /// </summary> 
        [ExporterHeader(DisplayName = "更新地址", IsBold = false)]
        public string? UpdateIP { get; set; }
    }
}
