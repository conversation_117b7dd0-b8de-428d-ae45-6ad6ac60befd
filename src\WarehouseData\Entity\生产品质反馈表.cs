﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>生产品质反馈表</summary>
[Serializable]
[DataObject]
[Description("生产品质反馈表")]
[BindIndex("IX_DH_ProductionQualityFeedback_Name", false, "Name")]
[BindIndex("IX_DH_ProductionQualityFeedback_ProductTypeId", false, "ProductTypeId")]
[BindIndex("IX_DH_ProductionQualityFeedback_ProductOrderId", false, "ProductOrderId")]
[BindIndex("IX_DH_ProductionQualityFeedback_Status", false, "Status")]
[BindIndex("IX_DH_ProductionQualityFeedback_DType", false, "DType")]
[BindTable("DH_ProductionQualityFeedback", Description = "生产品质反馈表", ConnName = "DH", DbType = DatabaseType.None)]
public partial class ProductionQualityFeedback : IProductionQualityFeedback, IEntity<IProductionQualityFeedback>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String _Name = null!;
    /// <summary>反馈标题</summary>
    [DisplayName("反馈标题")]
    [Description("反馈标题")]
    [DataObjectField(false, false, false, 100)]
    [BindColumn("Name", "反馈标题", "", Master = true)]
    public String Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private String? _Content;
    /// <summary>反馈内容</summary>
    [DisplayName("反馈内容")]
    [Description("反馈内容")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("Content", "反馈内容", "")]
    public String? Content { get => _Content; set { if (OnPropertyChanging("Content", value)) { _Content = value; OnPropertyChanged("Content"); } } }

    private Int32 _DType;
    /// <summary>反馈类型 0待分配 1软件 2硬件</summary>
    [DisplayName("反馈类型0待分配1软件2硬件")]
    [Description("反馈类型 0待分配 1软件 2硬件")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("DType", "反馈类型 0待分配 1软件 2硬件", "")]
    public Int32 DType { get => _DType; set { if (OnPropertyChanging("DType", value)) { _DType = value; OnPropertyChanged("DType"); } } }

    private String? _PicPath;
    /// <summary>图片。多个图片用,分隔</summary>
    [DisplayName("图片")]
    [Description("图片。多个图片用,分隔")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("PicPath", "图片。多个图片用,分隔", "")]
    public String? PicPath { get => _PicPath; set { if (OnPropertyChanging("PicPath", value)) { _PicPath = value; OnPropertyChanged("PicPath"); } } }

    private String? _VideoPath;
    /// <summary>视频。多个视频用,分隔</summary>
    [DisplayName("视频")]
    [Description("视频。多个视频用,分隔")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("VideoPath", "视频。多个视频用,分隔", "")]
    public String? VideoPath { get => _VideoPath; set { if (OnPropertyChanging("VideoPath", value)) { _VideoPath = value; OnPropertyChanged("VideoPath"); } } }

    private Int16 _Status;
    /// <summary>状态。0为待处理，1为处理中，2为已处理，3为不予处理 4已完结</summary>
    [DisplayName("状态")]
    [Description("状态。0为待处理，1为处理中，2为已处理，3为不予处理 4已完结")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Status", "状态。0为待处理，1为处理中，2为已处理，3为不予处理 4已完结", "")]
    public Int16 Status { get => _Status; set { if (OnPropertyChanging("Status", value)) { _Status = value; OnPropertyChanged("Status"); } } }

    private Int32 _ProductTypeId;
    /// <summary>产品型号编号</summary>
    [DisplayName("产品型号编号")]
    [Description("产品型号编号")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ProductTypeId", "产品型号编号", "")]
    public Int32 ProductTypeId { get => _ProductTypeId; set { if (OnPropertyChanging("ProductTypeId", value)) { _ProductTypeId = value; OnPropertyChanged("ProductTypeId"); } } }

    private Int64 _ProductOrderId;
    /// <summary>生产订单编号</summary>
    [DisplayName("生产订单编号")]
    [Description("生产订单编号")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ProductOrderId", "生产订单编号", "")]
    public Int64 ProductOrderId { get => _ProductOrderId; set { if (OnPropertyChanging("ProductOrderId", value)) { _ProductOrderId = value; OnPropertyChanged("ProductOrderId"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductionQualityFeedback model)
    {
        Id = model.Id;
        Name = model.Name;
        Content = model.Content;
        DType = model.DType;
        PicPath = model.PicPath;
        VideoPath = model.VideoPath;
        Status = model.Status;
        ProductTypeId = model.ProductTypeId;
        ProductOrderId = model.ProductOrderId;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "Name" => _Name,
            "Content" => _Content,
            "DType" => _DType,
            "PicPath" => _PicPath,
            "VideoPath" => _VideoPath,
            "Status" => _Status,
            "ProductTypeId" => _ProductTypeId,
            "ProductOrderId" => _ProductOrderId,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "Content": _Content = Convert.ToString(value); break;
                case "DType": _DType = value.ToInt(); break;
                case "PicPath": _PicPath = Convert.ToString(value); break;
                case "VideoPath": _VideoPath = Convert.ToString(value); break;
                case "Status": _Status = Convert.ToInt16(value); break;
                case "ProductTypeId": _ProductTypeId = value.ToInt(); break;
                case "ProductOrderId": _ProductOrderId = value.ToLong(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static ProductionQualityFeedback? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据反馈标题查找</summary>
    /// <param name="name">反馈标题</param>
    /// <returns>实体列表</returns>
    public static IList<ProductionQualityFeedback> FindAllByName(String name)
    {
        if (name.IsNullOrEmpty()) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Name.EqualIgnoreCase(name));

        return FindAll(_.Name == name);
    }

    /// <summary>根据产品型号编号查找</summary>
    /// <param name="productTypeId">产品型号编号</param>
    /// <returns>实体列表</returns>
    public static IList<ProductionQualityFeedback> FindAllByProductTypeId(Int32 productTypeId)
    {
        if (productTypeId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.ProductTypeId == productTypeId);

        return FindAll(_.ProductTypeId == productTypeId);
    }

    /// <summary>根据生产订单编号查找</summary>
    /// <param name="productOrderId">生产订单编号</param>
    /// <returns>实体列表</returns>
    public static IList<ProductionQualityFeedback> FindAllByProductOrderId(Int64 productOrderId)
    {
        if (productOrderId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.ProductOrderId == productOrderId);

        return FindAll(_.ProductOrderId == productOrderId);
    }

    /// <summary>根据状态查找</summary>
    /// <param name="status">状态</param>
    /// <returns>实体列表</returns>
    public static IList<ProductionQualityFeedback> FindAllByStatus(Int16 status)
    {
        if (status < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Status == status);

        return FindAll(_.Status == status);
    }

    /// <summary>根据反馈类型0待分配1软件2硬件查找</summary>
    /// <param name="dType">反馈类型0待分配1软件2硬件</param>
    /// <returns>实体列表</returns>
    public static IList<ProductionQualityFeedback> FindAllByDType(Int32 dType)
    {
        if (dType < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.DType == dType);

        return FindAll(_.DType == dType);
    }
    #endregion

    #region 字段名
    /// <summary>取得生产品质反馈表字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>反馈标题</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>反馈内容</summary>
        public static readonly Field Content = FindByName("Content");

        /// <summary>反馈类型 0待分配 1软件 2硬件</summary>
        public static readonly Field DType = FindByName("DType");

        /// <summary>图片。多个图片用,分隔</summary>
        public static readonly Field PicPath = FindByName("PicPath");

        /// <summary>视频。多个视频用,分隔</summary>
        public static readonly Field VideoPath = FindByName("VideoPath");

        /// <summary>状态。0为待处理，1为处理中，2为已处理，3为不予处理 4已完结</summary>
        public static readonly Field Status = FindByName("Status");

        /// <summary>产品型号编号</summary>
        public static readonly Field ProductTypeId = FindByName("ProductTypeId");

        /// <summary>生产订单编号</summary>
        public static readonly Field ProductOrderId = FindByName("ProductOrderId");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得生产品质反馈表字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>反馈标题</summary>
        public const String Name = "Name";

        /// <summary>反馈内容</summary>
        public const String Content = "Content";

        /// <summary>反馈类型 0待分配 1软件 2硬件</summary>
        public const String DType = "DType";

        /// <summary>图片。多个图片用,分隔</summary>
        public const String PicPath = "PicPath";

        /// <summary>视频。多个视频用,分隔</summary>
        public const String VideoPath = "VideoPath";

        /// <summary>状态。0为待处理，1为处理中，2为已处理，3为不予处理 4已完结</summary>
        public const String Status = "Status";

        /// <summary>产品型号编号</summary>
        public const String ProductTypeId = "ProductTypeId";

        /// <summary>生产订单编号</summary>
        public const String ProductOrderId = "ProductOrderId";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";
    }
    #endregion
}
