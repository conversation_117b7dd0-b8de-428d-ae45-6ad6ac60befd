﻿@{
    Html.AppendTitleParts(T("模板设置").Text);
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }
</style>

<script asp-location="Head">
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("模板标识")：</label>
            <div class="layui-input-inline">
                <input type="text" name="name" id="name" placeholder="@T("请输入模板标识")" autocomplete="off" class="layui-input" lay-filter="name">
            </div>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetList")'
            , page: false //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 120
            , cols: [[
                { field: 'Id', title: '@T("Id")', width: 50 },
                { field: 'MCode', title: '@T("标识")', width: 200 },
                { field: 'MName', title: '@T("模板描述")', templet: function (d) { return d.MName; } },
                { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: 150 }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'tables'
        });

        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        where: {
                            name: $("#name").val(),
                        }
                    });
            }
        }

        $("#name").on("input", function (e) {
            active.reload();
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'detail') {
                layer.msg('ID：' + data.ID + ' @T("的查看操作")');
            } else if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success('@T("删除成功")');
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'edit') {
                top.layui.dg.popupRight({
                    id: 'Edit'
                    , title: ' @T("编辑模板"):' + data.MCode
                    , closeBtn: 1
                    , area: ['780px']
                    , success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Edit")' + abp.utils.formatString("?id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                    }
                });
            }
        });

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'add') {
                top.layui.dg.popupRight({
                    id: 'RoleDetail'
                    , title: ' @T("新增模板")'
                    , closeBtn: 1
                    , area: ['780px']
                    , success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Add")" frameborder="0" class="layadmin-iframe"></iframe>');
                    }
                });
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

    });
</script>

<script type="text/html" id="tool">
    @if (this.Has((PermissionFlags)4)){
    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="edit"> @T("编辑")</a>
    }
    @if (this.Has((PermissionFlags)8)){
    <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="del"> @T("删除")</a>
    }
</script>
<script type="text/html" id="user-toolbar">
    @if (this.Has((PermissionFlags)2)){
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        @T("新增")
    </button>
    }
</script>