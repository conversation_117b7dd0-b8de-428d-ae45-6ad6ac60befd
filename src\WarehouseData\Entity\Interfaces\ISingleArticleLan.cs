﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>单页文章翻译</summary>
public partial interface ISingleArticleLan
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>单页文章Id</summary>
    Int32 SId { get; set; }

    /// <summary>所属语言Id</summary>
    Int32 LId { get; set; }

    /// <summary>文章标题</summary>
    String? Name { get; set; }

    /// <summary>内容</summary>
    String? Content { get; set; }

    /// <summary>上传的文件</summary>
    String? FileUrl { get; set; }

    /// <summary>Md5</summary>
    String? Md5 { get; set; }

    /// <summary>扩展字段1</summary>
    String? Ex1 { get; set; }
    #endregion
}
