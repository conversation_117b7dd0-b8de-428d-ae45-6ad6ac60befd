﻿using DG.Web.Framework;

using DH.Core.Infrastructure;
using DH.Entity;
using DH.Models;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Common;
using NewLife.Reflection;
using Pek.Models;
using Pek.Systems;
using Pek.Webs;

using System.ComponentModel;
using System.Runtime.InteropServices;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers {
    /// <summary>
    ///服务器信息
    /// </summary>
    [DisplayName("服务器信息")]
    [Description("系统所在服务器相关信息")]
    [AdminArea]
    [DHMenu(70,ParentMenuName = "System", CurrentMenuUrl = "~/{area}/Server", CurrentMenuName = "Server", LastUpdate = "20240124")]
    public class ServerController : BaseAdminControllerX
    {
        /// <summary>菜单顺序。扫描是会反射读取</summary>
        protected static Int32 MenuOrder { get; set; } = 70;

        private readonly IHostApplicationLifetime _applicationLifetime;

        static ServerController() => MachineInfo.RegisterAsync();

        public ServerController(IHostApplicationLifetime appLifetime)
        {
            _applicationLifetime = appLifetime;
        }

        /// <summary>
        /// 服务器信息
        /// </summary>
        /// <returns></returns>
        [DisplayName("服务器信息")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult Index(String id)
        {
            ViewBag.Act = id;
            ViewBag.Config = SysConfig.Current;

            var asms = GetMyAssemblies().OrderBy(e => e.Name).OrderByDescending(e => e.Compile);

            ViewBag.MyAsms = asms.Where(e =>
                !e.Name.Contains("Microsoft.") &&
                !e.Name.Contains("Autofac") &&
                !e.Name.Contains("System.Security") &&
                !e.Name.Contains("System.Configuration") &&
                !e.Name.Contains("XC.") &&
                !e.Name.Contains("Newtonsoft.") &&
                !e.Name.Contains("SixLabors.") &&
                !e.Name.Contains("Jering."))
                .ToArray();

            ViewBag.MyAllAsms = asms.ToArray();

            var Asms = AssemblyX.GetAssemblies(null).ToArray();
            Asms = Asms.OrderBy(e => e.Name).OrderByDescending(e => e.Compile).ToArray();
            ViewBag.Asms = Asms;

            return (id + "").ToLower() switch
            {
                "processmodules" => View("ProcessModules"),
                "assembly" => View("Assembly"),
                "session" => View("Session"),
                "servervar" => View("ServerVar"),
                _ => View(),
            };
        }

        /// <summary>获取当前应用程序的所有程序集，不包括系统程序集，仅限本目录</summary>
        /// <returns></returns>
        public static List<AssemblyX> GetMyAssemblies()
        {
            var list = new List<AssemblyX>();
            var hs = new HashSet<String>(StringComparer.OrdinalIgnoreCase);
            var cur = AppDomain.CurrentDomain.BaseDirectory.CombinePath("../").GetFullPath();
            foreach (var asmx in AssemblyX.GetAssemblies())
            {
                // 加载程序集列表很容易抛出异常，全部屏蔽
                try
                {
                    if (asmx.FileVersion.IsNullOrEmpty()) continue;

                    var file = asmx.Asm.Location;
                    if (file.IsNullOrEmpty()) continue;

                    if (file.StartsWith("file:///"))
                    {
                        file = file.TrimStart("file:///");
                        if (Path.DirectorySeparatorChar == '\\')
                            file = file.Replace('/', '\\');
                        else
                            file = file.Replace('\\', '/').EnsureStart("/");
                    }
                    if (!file.StartsWithIgnoreCase(cur)) continue;

                    if (!hs.Contains(file))
                    {
                        hs.Add(file);
                        list.Add(asmx);
                    }
                }
                catch { }
            }
            return list;
        }

        /// <summary>重启</summary>
        /// <returns></returns>
        [DisplayName("重启")]
        [EntityAuthorize((PermissionFlags)16)]
        public IActionResult Restart()
        {
            var webhelper = EngineContext.Current.Resolve<IWebHelper>();
            webhelper.RestartAppDomain();

            //if (ApplicationHelper.IsIIS)
            //{
            //    WebHelper.RestartAppDomain();
            //}
            //else
            //{
            //    var manager = ApplicationManager.Load();

            //    try
            //    {
            //        var p = Process.GetCurrentProcess();
            //        var fileName = p.MainModule.FileName;
            //        var args = Environment.CommandLine.TrimStart(Path.ChangeExtension(fileName, ".dll")).Trim();
            //        args += " -delay";

            //        WriteLog("Restart", true, $"fileName={fileName} args={args}");

            //        Process.Start(fileName, args);

            //        ThreadPool.QueueUserWorkItem(s =>
            //        {
            //            Thread.Sleep(100);

            //            // 本进程退出
            //            manager.Stop();
            //            Thread.Sleep(200);
            //            //p.Kill();
            //            Environment.Exit(0);
            //        });
            //    }
            //    catch (Exception ex)
            //    {
            //        XTrace.WriteException(ex);

            //        manager.Restart();
            //    }
            //}

            UserE.WriteLog(LocaleStringResource.GetResource("重启"), true, String.Format(LocaleStringResource.GetResource("用户[{0}]执行了重启"), ManageProvider.User?.Name));
            return JsonRefresh("重启成功", 5);
        }

        /// <summary>
        /// 释放内存，参考之前的Runtime方法
        /// </summary>
        /// <returns></returns>
        [DisplayName("释放内存")]
        [EntityAuthorize((PermissionFlags)32)]
        public IActionResult MemoryFree()
        {
            NativeHelper.FreeMemory();

            UserE.WriteLog(LocaleStringResource.GetResource("释放内存"), true, String.Format(LocaleStringResource.GetResource("用户[{0}]执行了释放内存"), ManageProvider.User.Name));
            return RedirectToAction(nameof(Index));
        }

        [DllImport("kernel32.dll")]
        private static extern Boolean SetProcessWorkingSetSize(IntPtr proc, Int32 min, Int32 max);
    }
}
