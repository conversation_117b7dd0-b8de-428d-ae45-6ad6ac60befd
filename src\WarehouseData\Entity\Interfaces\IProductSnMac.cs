﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>生产SnMac</summary>
public partial interface IProductSnMac
{
    #region 属性
    /// <summary>编号</summary>
    Int64 Id { get; set; }

    /// <summary>生产订单编号</summary>
    Int64 ProductOrdersId { get; set; }

    /// <summary>订单号。冗余字段</summary>
    String OrderId { get; set; }

    /// <summary>设备Sn/DeviceName</summary>
    String? Sn { get; set; }

    /// <summary>设备Mac地址</summary>
    String? Mac { get; set; }

    /// <summary>设备Mac地址</summary>
    String? Mac1 { get; set; }

    /// <summary>对应的短网址</summary>
    String? ShorUrl { get; set; }

    /// <summary>文件路径</summary>
    String? FilePath { get; set; }

    /// <summary>测试结果。以Json的方式存储，比如某一项为key,成功与否为value，方便后续解析json根据key和value来渲染到页面上</summary>
    String? Content { get; set; }

    /// <summary>用户上传数据</summary>
    String? DataInfo { get; set; }

    /// <summary>是否允许重复</summary>
    Boolean IsRepetition { get; set; }

    /// <summary>是否允许重复时间段</summary>
    DateTime IsRepetitionDate { get; set; }

    /// <summary>是否生产校验</summary>
    Boolean IsValidate { get; set; }

    /// <summary>是否已被消费</summary>
    Boolean IsConsumed { get; set; }

    /// <summary>消费时间</summary>
    DateTime ConsumedTime { get; set; }

    /// <summary>是否确认消费</summary>
    Boolean IsConfirmConsumed { get; set; }

    /// <summary>确认消费时间</summary>
    DateTime ConfirmConsumedTime { get; set; }

    /// <summary>消费时间</summary>
    DateTime Double { get; set; }

    /// <summary>PCB周期</summary>
    String? PCBCycle { get; set; }

    /// <summary>屏蔽罩周期</summary>
    String? ShieldCycle { get; set; }

    /// <summary>主芯片周期</summary>
    String? MainChipCycle { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
