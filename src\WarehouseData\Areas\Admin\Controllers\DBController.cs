﻿using System.ComponentModel;
using System.Diagnostics;
using System.Dynamic;

using DG.Web.Framework;

using DH.Models;

using Microsoft.AspNetCore.Mvc;

using MySqlConnector;

using NewLife;
using NewLife.Collections;
using NewLife.Log;
using NewLife.Reflection;

using Pek.Configs;
using Pek.IO;
using Pek.Models;

using XCode.DataAccessLayer;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>
/// 数据库管理
/// </summary>
[DisplayName("数据库管理")]
[Description("数据库管理列表")]
[AdminArea]
[DHMenu(89,ParentMenuName = "System", CurrentMenuUrl = "", CurrentMenuName = "DBP", LastUpdate = "20240124")]
public class DBPController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 89;
}

/// <summary>数据备份</summary> 
[DisplayName("数据库")]
[Description("用于备份整个系统数据库")]
[AdminArea]
[DHMenu(25,ParentMenuName = "DBP", ParentMenuDisplayName = "数据库管理", CurrentMenuUrl = "~/{area}/DB", CurrentMenuName = "DBBak", CurrentIcon = "&#xe6f5;", LastUpdate = "20240124")]
public class DBController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 25;

    /// <summary>
    /// 数据备份
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("数据备份")]
    public IActionResult Index()
    {
        var list = new List<DbItem>();
        var dir = NewLife.Setting.Current.BackupPath.GetBasePath().AsDirectory();
        var listTable = new List<IDataTable>();

        ViewBag.DBCount = DAL.ConnStrs?.Count;

        //Double Size = 0;

        // 读取配置文件
        if (DAL.ConnStrs?.Count > 0)
        {
            foreach (var item in DAL.ConnStrs!.ToArray())
            {
                var dal = DAL.Create(item.Key);
                if (list.Exists(e => e.DataBase == dal.Db.DatabaseName))
                    continue;

                var di = new DbItem
                {
                    Name = item.Key,
                    ConnStr = item.Value,
                    Type = dal.DbType,
                    DataBase = dal.Db.DatabaseName
                };

                var t = Task.Run(() =>
                {
                    try
                    {
                        return dal.Db.ServerVersion;
                    }
                    catch { return null; }
                });

                if (t.Wait(300)) di.Version = t.Result;

                if (dir.Exists) di.Backups = dir.GetFiles($"{dal.ConnName}_*", SearchOption.TopDirectoryOnly).Length;
                list.Add(di);
            }
        }

        return View(list);
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("搜索备份数据")]
    public IActionResult GetPage()
    {
        var list = new List<DbItem>();
        var dir = NewLife.Setting.Current.BackupPath.GetBasePath().AsDirectory();
        //var listTable = new List<IDataTable>();

        ViewBag.DBCount = DAL.ConnStrs!.Count;

        Double Size = 0;

        // 读取配置文件
        foreach (var item in DAL.ConnStrs.ToArray())
        {
            var dal = DAL.Create(item.Key);

            if (list.Exists(e => e.DataBase == dal.Db.DatabaseName))
                continue;

            //listTable.AddRange(dal.Tables);

            if (dal.DbType == DatabaseType.SQLite)
            {
                var ConnStrArray = dal.ConnStr.SplitAsDictionary();
                var DBFiles = $"{ConnStrArray["Data Source"].Replace("\\", "/")}".AsFile();

                Size += (Double)DBFiles.Length / 1024 / 1024;
            }
            else if (dal.DbType == DatabaseType.MySql)
            {
                var sql = $"select table_name,table_rows,data_length+index_length,round((data_length + index_length) / 1024 / 1024, 2) data from information_schema.tables where table_schema = '{dal.Db.DatabaseName}'";
                var dt = dal.Query(sql);
                foreach (var row in dt.Rows)
                {
                    Size += row[3].ToDouble();
                }
            }

            var di = new DbItem
            {
                Name = item.Key,
                ConnStr = item.Value,
                Type = dal.DbType,
                DataBase = dal.Db.DatabaseName
            };

            var t = Task.Run(() =>
            {
                try
                {
                    return dal.Db.ServerVersion;
                }
                catch { return null; }
            });
            if (t.Wait(300)) di.Version = t.Result;

            if (dir.Exists) di.Backups = dir.GetFiles($"{dal.ConnName}_*", SearchOption.TopDirectoryOnly).Length;
            list.Add(di);
        }

        //ViewBag.TableCount = listTable.Count;
        //ViewBag.Size = Size.ToString("#0.00") + "MB";

        return Json(new { code = 0, msg = "success", data = list });
    }

    /// <summary>
    /// 数据还原
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("数据还原")]
    public IActionResult Restore()
    {
        var BackupPath = NewLife.Setting.Current.BackupPath.GetFullPath();
        BackupPath.EnsureDirectory(false);
        var list = FileSystemObject.GetDirectoryInfos(BackupPath, FsoMethod.All);

        Double Size = 0;
        foreach (var item in list)
        {
            Size += item.size;
        }

        ViewBag.Size = ((Double)Size / 1024 / 1024).ToString("#0.00");
        ViewBag.Count = list.Count;

        return View(list);
    }

    /// <summary>
    /// 备份数据库
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [EntityAuthorize((PermissionFlags)16)]
    [DisplayName("备份数据库")]
    public IActionResult Backup(String dbName)
    {
        var sw = Stopwatch.StartNew();

        var dal = DAL.Create(dbName);

        //var bak = dal.Db.CreateMetaData().SetSchema(DDLSchema.BackupDatabase, dal.ConnName, null, false);
        object? bak = null;
        if (dal.DbType == DatabaseType.SQLite)
        {
            bak = dal.Db.CreateMetaData().Invoke("Backup", dal.ConnName, null, false);
        }
        else if (dal.DbType == DatabaseType.MySql)
        {
            Task.Run(() =>
            {
                using (MySqlConnection conn = new MySqlConnection(dal.ConnStr))
                {
                    using (MySqlCommand cmd = new MySqlCommand())
                    {
                        using (MySqlBackup mb = new MySqlBackup(cmd))
                        {
                            conn.Open();
                            cmd.Connection = conn;

                            var bak = $"{dal.ConnName}_{DateTime.Now:yyyyMMddHHmmss}.sql";
                            bak = NewLife.Setting.Current.BackupPath.CombinePath(bak);
                            XTrace.WriteLine("获取到的bak==" + bak);
                            mb.ExportToFile(bak);

                            conn.Close();
                        }
                    }
                }
            });
        }
        sw.Stop();
        WriteLog("备份", $"备份数据库 {dbName} 到 {bak}，耗时 {sw.Elapsed}", UserHost);
        //return Json(new DResult { success = true, msg = GetResource("备份成功") });
        return Json(new DResult { success = true, msg = GetResource("备份成功") + GetResource("备份数据库") + " " + dbName + " " + GetResource("到") + bak + GetResource(",耗时") + sw.Elapsed });
    }

    /// <summary>
    /// 备份并压缩数据库
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)16)]
    [DisplayName("备份并压缩数据库")]
    public IActionResult BackupAndCompress(String dbName)
    {
        var sw = Stopwatch.StartNew();

        var dal = DAL.Create(dbName);
        //var bak = dal.Db.CreateMetaData().SetSchema(DDLSchema.BackupDatabase, dal.ConnName, null, true);
        //var bak = dal.Db.CreateMetaData().Invoke("Backup", dal.ConnName, null, true);
        var bak = $"{dal.ConnName}_{DateTime.Now:yyyyMMddHHmmss}.zip";
        bak = NewLife.Setting.Current.BackupPath.CombinePath(bak);
        var tables = dal.Tables;
        dal.BackupAll(tables, bak);

        sw.Stop();
        WriteLog("备份", $"备份数据库 {dbName} 并压缩到 {bak}，耗时 {sw.Elapsed}", UserHost);
        return Json(new DResult { success = true, msg = GetResource("备份成功") });
        //return Prompt(new PromptModel { Message = GetResource("备份成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 下载表结构
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("下载表结构")]
    public IActionResult Download(String dbName)
    {
        var dal = DAL.Create(dbName);
        var xml = DAL.Export(dal.Tables);

        WriteLog("下载", "下载数据库架构 " + dbName, UserHost);

        return File(xml.GetBytes(), "application/xml", dbName + ".xml");
    }

    /// <summary>
    /// 下载数据库备份
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("下载数据库备份")]
    public IActionResult DownFile(String name)
    {
        var bakFile = NewLife.Setting.Current.BackupPath.CombinePath(name);

        var Files = bakFile.AsFile();

        if (!Files.Exists)
        {
            //return Prompt(new PromptModel { Message = GetResource("文件不存在") });
            return Json(new DResult { success = false, msg = GetResource("文件不存在") });
        }

        WriteLog("下载", "下载数据库备份 " + name, UserHost);

        return File(Files.ReadBytes(), "application/octet-stream", name);
    }

    /// <summary>
    /// 删除备份
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除备份")]
    public IActionResult Del(String name)
    {
        var bakFile = NewLife.Setting.Current.BackupPath.CombinePath(name);

        bakFile.AsFile().Delete();

        WriteLog("删除", "删除备份 " + name, UserHost);

        return Json(new DResult { success = true, msg = GetResource("删除成功") });
        //return Prompt(new PromptModel { Message = GetResource("删除成功"), IsOk = true, BackUrl = Url.Action("Restore") });
    }

    /// <summary>
    /// 还原数据库
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)32)]
    [DisplayName("还原数据库")]
    public IActionResult RestoreAll(String name)
    {
        if (name.IsNullOrWhiteSpace() || name.IndexOf("_") == -1)
        {
            return Json(new DResult() { msg = GetResource("文件不存在或文件名未按格式") });
        }

        var dbName = name.Split('_')[0];
        var dal = DAL.Create(dbName);
        var tables = dal.Tables;

        if (dal.DbType == DatabaseType.SQLite)
        {
            var sb = Pool.StringBuilder.Get();
            foreach (var item in tables)
            {
                sb.Append($"DELETE FROM '{item.TableName}';DELETE FROM sqlite_sequence WHERE name = '{item.TableName}';");
            }
            dal.Execute(sb.Put(true));
            dal.Execute("VACUUM");

            var bakFile = NewLife.Setting.Current.BackupPath.CombinePath(name);

            dal.RestoreAll(bakFile);
        }
        else if (dal.DbType == DatabaseType.MySql)
        {
            var sb = Pool.StringBuilder.Get();

            foreach (var item in tables)
            {
                sb.Append($"TRUNCATE TABLE `{item.TableName}`;");
            }
            dal.Execute(sb.Put(true));

            var bakFile = NewLife.Setting.Current.BackupPath.CombinePath(name);

            dal.RestoreAll(bakFile);
        }
        else if (dal.DbType == DatabaseType.MySql)
        {
            var sb = Pool.StringBuilder.Get();

            foreach (var item in tables)
            {
                sb.Append($"TRUNCATE TABLE {item.TableName};");
            }
            dal.Execute(sb.Put(true));

            var bakFile = NewLife.Setting.Current.BackupPath.CombinePath(name);

            dal.RestoreAll(bakFile);
        }
        else if (dal.DbType == DatabaseType.PostgreSQL)
        {
            var sb = Pool.StringBuilder.Get();

            foreach (var item in tables)
            {
                sb.Append($"TRUNCATE TABLE {item.TableName} RESTART IDENTITY;");
            }
            dal.Execute(sb.Put(true));

            var bakFile = NewLife.Setting.Current.BackupPath.CombinePath(name);

            dal.RestoreAll(bakFile);
        }

        return Json(new DResult() { code = 10000, msg = GetResource("还原成功") });
    }

    public class TableViewModel
    {
        public string TableName { get; set; }

        //每个表的列
        public List<IDataColumn> Columns { get; set; }

        //每个表的索引
        public List<IDataIndex> Indexes { get; set; }

        //每个表的主键
        public List<IDataColumn> PRIMARY { get; set; }
    }

    /// <summary>
    /// 管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)64)]
    [DisplayName("管理")]
    public IActionResult Manage(String Name)
    {
        dynamic viewModel = new ExpandoObject();
        viewModel.ConnStr = DAL.ConnStrs[Name];

        var dal = DAL.Create(Name);

        viewModel.DBType = dal.DbType;
        viewModel.Name = Name;
        viewModel.Tables = dal.Tables.Select(t => new TableViewModel { TableName = t.TableName, Columns = t.Columns, Indexes = t.Indexes, PRIMARY = t.PrimaryKeys.ToList() }).ToList();

        //var a  = dal.Tables.Select(t => new TableViewModel { TableName = t.TableName, Columns = t.Columns, Indexes = t.Indexes, PRIMARY = t.PrimaryKeys.ToList() }).ToList();
        //foreach (var item in a)
        //{
        //    XTrace.WriteLine("111" + item.TableName);
        //    foreach (var primaryKeys in item.PRIMARY)
        //    {
        //        XTrace.WriteLine(primaryKeys.GetType().ToString());
        //        XTrace.WriteLine(primaryKeys.Name.ToString());
        //    }
        //}
        //XTrace.WriteLine(Name);

        return View(viewModel);
    }

    public class ExecuteSqlRequest
    {
        public string DbName { get; set; }
        public string SqlQuery { get; set; }
    }

    /// <summary>
    /// 管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)64)]
    [DisplayName("管理")]
    [HttpPost]
    public IActionResult RowCount(string dbName, string tbName, string filterQuery)
    {
        var dal = DAL.Create(dbName);
        var SqlQuery = "select count(*) from " + tbName + filterQuery;

        var result = dal.Session.Query(SqlQuery);

        if (dal.DbType == DatabaseType.PostgreSQL)
        {
            var count = result.Tables[0].Rows[0]["count"];

            return Json(count);
        }
        else
        {
            // 提取 count(*) 的值
            var count = result.Tables[0].Rows[0]["count(*)"];

            return Json(count);
        }
    }

    /// <summary>
    /// 管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)64)]
    [DisplayName("管理")]
    [HttpPost]
    public IActionResult Manage([FromBody] ExecuteSqlRequest request)
    {
        // 改进的防注入检测
        var dangerousKeywords = new[] { "DROP", "DELETE", "INSERT", "UPDATE", "--", ";" };
        var sqlQuery = request.SqlQuery.ToUpper();

        foreach (var keyword in dangerousKeywords)
        {
            // 使用正则表达式检查关键字是否作为独立单词出现
            // 而不是作为其他单词的一部分 (如 updatelogs 不应该被视为包含 UPDATE)
            var pattern = $@"\b{keyword}\b";
            if (System.Text.RegularExpressions.Regex.IsMatch(sqlQuery, pattern))
            {
                return Json(new { success = false, msg = "检测到危险的SQL关键字，操作被拒绝。" });
            }
        }

        var dal = DAL.Create(request.DbName);
        var result = dal.Session.Query(request.SqlQuery);

        return Json(result);
    }

    /// <summary>
    /// 修改链接
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改链接")]
    public IActionResult EditSetting(String Name)
    {
        dynamic viewModel = new ExpandoObject();
        viewModel.ConnStr = DAL.ConnStrs[Name];

        var dal = DAL.Create(Name);

        viewModel.DBType = dal.DbType;
        viewModel.Name = Name;

        return View(viewModel);
    }

    /// <summary>
    /// 修改链接
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改链接")]
    [HttpPost]
    public IActionResult EditSetting(String Name, DatabaseType DBType, String ConnStr)
    {
        ConfigFileHelper.AddOrUpdateAppSetting($"ConnectionStrings:{Name}:connectionString", ConnStr);

        switch (DBType)
        {
            case DatabaseType.SQLite:
                ConfigFileHelper.AddOrUpdateAppSetting($"ConnectionStrings:{Name}:providerName", "Sqlite");
                break;

            case DatabaseType.MySql:
                ConfigFileHelper.AddOrUpdateAppSetting($"ConnectionStrings:{Name}:providerName", "MySql.Data.MySqlClient");
                break;

            case DatabaseType.PostgreSQL:
                ConfigFileHelper.AddOrUpdateAppSetting($"ConnectionStrings:{Name}:providerName", "PostgreSQL.Data.PostgreSQLClient");
                break;

            case DatabaseType.SqlServer:
                ConfigFileHelper.AddOrUpdateAppSetting($"ConnectionStrings:{Name}:providerName", "System.Data.SqlClient");
                break;
        }

        return Json(new DResult { success = true, msg = GetResource("修改成功") });
    }

    #region 日志
    private static void WriteLog(String action, String remark, String? ip = null) => LogProvider.Provider.WriteLog(typeof(DBController), action, true, remark, ip: ip);
    #endregion
}