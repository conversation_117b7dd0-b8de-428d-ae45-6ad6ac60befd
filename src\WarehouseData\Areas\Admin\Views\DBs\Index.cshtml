﻿@{
    Html.AppendTitleParts(T("数据库备份列表").Text);
}

<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }
</style>

<script asp-location="Head">
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<div class="layui-card">
    <div class="layui-card-header">@T("数据库备份列表")</div>
    <div class="layui-card-body">
        <div class="table-body">
            <table class="layui-hide" id="tablist" lay-filter="tool"></table>
        </div>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetPage")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 80
            , cols: [[
                { field: 'name', title: '@T("文件名称")' }
                , { field: 'size', title: '@T("大小")' }
                , { field: 'lastWriteTime', title: '@T("备份时间")', sort: true }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', align: "center", width: 262 }
            ]]
            , limit: 14
            , limits: [10, 14, 20, 30, 50, 100]
            , height: 'full-120'
            , id: 'tables'
        });

        var active = {
            reload: function () {
                table.reload('tables');
            }
        }

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === "del") {
                layer.confirm('@T("确认删除吗")?', { icon: 3, btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Del")', { name: data.name }, function (data) {
                        if (data.success) {
                            os.success('@T("删除成功")');
                            active.reload();
                        } else {
                            os.warning(data.msg);
                        }
                    });
                    layer.close(index);
                });
            }
            if (obj.event === "huifu") {
                layer.confirm('@T("您确定要恢复吗?恢复数据之前会清空现有数据")', { icon: 3, btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("RestoreAll")', { name: data.name }, function (data) {
                        if (data.success) {
                            os.success('@T("删除成功")');
                            active.reload();
                        } else {
                            os.warning(data.msg);
                        }
                    });
                    layer.close(index);
                });
            }
            if (obj.event === "Download") {
                var href = '@Url.Action("DownFile")?name=' + data.name;
                $(this).attr("href", href);
            }
        })

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'add') {
                layuiIndex = os.OpenNoTop('@T("新增用户")', "@Url.Action("EditUser")", '420px', '350px', function (layero, index) {
                    if ($("#state").val() == 1) abp.notify.success("@T("新增成功")");
                    active.reload();
                });
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

    });
</script>

<script type="text/html" id="tool">
    <a href="" class="pear-btn pear-btn-primary pear-btn-xs" lay-event="Download"> @T("下载")</a>
    <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="del"> @T("删除")</a>
    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="huifu"> @T("恢复")</a>
</script>