﻿using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

namespace HlktechIoT.Dto.Export;

/// <summary>
/// 导入翻译数据
/// </summary>
[ExcelImporter(IsLabelingError = true)]
public class LocalStringImport {
    /// <summary> 
    /// 语言
    /// </summary>
    [ImporterHeader(Name = "语言", ColumnIndex = 1)]
    public string? Culture { get; set; }

    /// <summary> 
    /// 翻译项
    /// </summary>
    [ImporterHeader(Name = "翻译项", ColumnIndex = 2)]
    public string? LanKey { get; set; }

    /// <summary> 
    /// 翻译内容
    /// </summary>
    [ImporterHeader(Name = "翻译内容", ColumnIndex = 3)]
    public string? LanValue { get; set; }

    /// <summary> 
    /// 翻译结果
    /// </summary> 
    [ImporterHeader(Name = "翻译结果", ColumnIndex = 4)]
    public string? TransValue { get; set; }
}