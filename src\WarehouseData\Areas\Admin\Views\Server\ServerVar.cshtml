﻿@using Microsoft.AspNetCore.Http
@using NewLife.Reflection
@using NewLife
@using HttpContext = Pek.Webs.HttpContext
@inject IHttpContextAccessor HttpContextAccessor
@{
    Layout = null;

    ViewBag.Title = "服务器变量列表";

    var httpContext = HttpContextAccessor.HttpContext;
    var req = httpContext.Request;
}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit|ie-stand">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width" />
    @*<link href="/Content/bootstrap.min.css" rel="stylesheet" />

    <script src="/Scripts/jquery1111.min.js"></script>
    <script src="/Scripts/bootstrap.min.js"></script>*@

    <style>
        .container .table > tbody > tr > td {
            padding: 5px 5px;
        }

        .container .table {
            margin: 0;
            background-color: #fff;
            color: #494e52;
            font-size: 13px;
        }

        .table-bordered {
            border: 1px solid #ddd;
        }

        .table > tbody > tr > td {
            border-top: 0;
            border-bottom: 1px solid #e7e7e7;
        }

        .container .table > thead > tr > th, .table > tbody > tr > td {
            vertical-align: middle;
        }

        .table-bordered > tbody > tr > td, .table-bordered > tbody > tr > th, .table-bordered > tfoot > tr > td, .table-bordered > tfoot > tr > th, .table-bordered > thead > tr > td, .table-bordered > thead > tr > th {
            border: 1px solid #ddd;
        }

        .container .table thead tr th {
            border: 0;
            height: 45px;
            font-weight: 900 !important;
        }

        .table thead th:first-child {
            border-top-left-radius: 5px;
        }

        table {
            border-spacing: 0;
            border-collapse: collapse;
        }

        .table#list, .table.category_table, .table#applyList, .table#HandSlideDatagrid, .table#typeDatagrid, .table#shopDatagrid, .table#topicGrid, .table#listAutoReplay, .table#productList {
            margin-top: 5px;
            margin-bottom: 60px;
        }

        .table {
            width: 100%;
            max-width: 100%;
            margin-bottom: 20px;
        }

            .table thead {
                background-color: #f9f9f9;
                border-bottom: 1px solid #e7e7e7;
                border-top-left-radius: 15px !important;
                border-top-right-radius: 15px !important;
                color: #494e52;
                font-size: 14px;
            }
    </style>
</head>
<body>
    <div class="container" style="width:100%;">
        <div>
            <table class="table table-bordered table-hover table-striped table-condensed">
                <tr>
                    <th colspan="6">
                        @T("服务器变量列表")
                    </th>
                </tr>
                <tr>
                    <th>@T("名称")</th>
                    <th>@T("数值")</th>
                </tr>
                @foreach (var kv in req.Headers)
                {
                    var v = kv.Value.ToString();
                    v = v.Replace("\r\n", "</br>");
                    var key = kv.Key;
                    if (key.EqualIgnoreCase("HTTP_COOKIE")) { v = v.Replace(";", "</br>"); }
                    <tr>
                        <td>@key</td>
                        <td>@Html.Raw(v)</td>
                    </tr>
                }
            </table>
            <table class="table table-bordered table-hover table-striped table-condensed">
                <tr>
                    <th colspan="6">
                        @T("Request变量列表")&nbsp;&nbsp;&nbsp;&nbsp;@req.GetType().FullName
                    </th>
                </tr>
                <tr>
                    <th>@T("名称")</th>
                    <th>@T("数值")</th>
                </tr>
                @foreach (var pi in req.GetType().GetProperties())
                {
                    var type = pi.PropertyType;
                    if (pi.GetIndexParameters().Length > 0 || (type != typeof(String)
                                                               && type != typeof(Uri)
                                                               && type != typeof(PathString)
                                                               && type != typeof(HostString)
                                                               && !typeof(Boolean).IsAssignableFrom(type)
                                                               && !typeof(String).IsAssignableFrom(type)))
                    {
                        continue;
                    }
                    var v = req.GetValue(pi) + "";
                    v = v.Replace("\r\n", "</br>");
                    <tr>
                        <td>@pi.Name</td>
                        <td>@Html.Raw(v)</td>
                    </tr>
                }
            </table>
        </div>
    </div>
</body>
</html>