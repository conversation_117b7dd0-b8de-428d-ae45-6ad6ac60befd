﻿using DG.Web.Framework;

using DH.Core.Infrastructure;
using DH.RateLimter;

using HlktechIoT.Common;
using HlktechIoT.Dto;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Caching;

using Pek.Helpers;
using Pek.Mime;
using Pek.Models;
using Pek.MVC;
using Pek.Swagger;

using XCode.Membership;

using YRY.Web.Controllers;

namespace HlktechIoT.Controllers;

/// <summary>
/// 公共接口
/// </summary>
[Produces("application/json")]
[CustomRoute(ApiVersions.V1)]
[DHAuthorize]
[ApiExplorerSettings(IgnoreApi =true)]
public class CommonController : ControllerBaseX {
    /// <summary>
    /// 获取未读通知数量
    /// </summary>
    /// <returns></returns>
    [HttpGet("GetHeadMessage")]
    public IActionResult GetHeadMessage()
    {
        var list = new List<MessageDto>();
        var model = new MessageDto();
        model.id = 1;
        model.title = GetResource("通知");
        model.children = new List<MessageChild>();
        model.children.Add(new MessageChild
        {
            id = 1,
            avatar = "https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png",
            title = GetResource("欢迎使用本系统"),
            context = GetResource("欢迎使用本系统"),
            form = GetResource("系统"),
            time = GetResource("刚刚")
        }); ;

        list.Add(model);

        return Json(list);
    }

    /// <summary>
    /// 获取用户头像
    /// </summary>
    /// <returns></returns>
    [HttpGet("GetProfilePicture")]
    public FileResult GetProfilePicture()
    {
        var profilePictureContent = GetUserProfilePicture();
        if (profilePictureContent.IsNullOrWhiteSpace())
        {
            return GetDefaultProfilePictureInternal();
        }

        return File(Convert.FromBase64String(profilePictureContent), MimeTypes.ImagePng);
    }

    private String GetUserProfilePicture()
    {
        var profilePictureContent = string.Empty;

        var user = ManageProvider.User;
        if (!user.Avatar.IsNullOrWhiteSpace())
        {
            var file = user.Avatar.AsFile();
            if (file.Exists)
            {
                profilePictureContent = Convert.ToBase64String(file.ReadBytes());
            }
        }

        return profilePictureContent;
    }

    protected FileResult GetDefaultProfilePictureInternal()
    {
        return File(Path.Combine("images", "avatar.jpg"), MimeTypes.ImageJpeg);


    }

    /// <summary>
    /// 验证码
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpGet("GetVierificationCode")]
    //[ApiSignature]
    [RateValve(Policy = Policy.Ip, Limit = 600, Duration = 3600)]
    public IActionResult GetVierificationCode()
    {
        var result = new DGResult();

        var code = Randoms.RandomStr(4);
        var data = new
        {
            img = VierificationCodeHelpers.CreateBase64Image(code),
            uuid = Guid.NewGuid()
        };

        var _cache = EngineContext.Current.Resolve<ICache>();
        _cache.Set(data.uuid.ToString(), code, 300);

        result.Code = StateCode.Ok;
        result.Data = data;
        return result;
    }

    /// <summary>
    /// 验证码
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpGet("GetCode")]
    [RateValve(Policy = Policy.Ip, Limit = 600, Duration = 3600)]
    public IActionResult GetCode([FromQuery] Int32 CodeLength = 4, [FromQuery] Int32 Width = 100, [FromQuery] Int32 Height = 100, [FromQuery] Int32 FontSize = 14)
    {
        var result = new DGResult();

        var img = ValidateCode.CreateValidateGraphic(CodeLength, Width, Height, FontSize);
        var code = img.code;
        var data = new
        {
            img = img.base64,
            uuid = Guid.NewGuid()
        };

        var _cache = EngineContext.Current.Resolve<ICache>();
        _cache.Set(data.uuid.ToString(), code, 300);

        result.Code = StateCode.Ok;
        result.Data = data;
        return result;
    }
}
