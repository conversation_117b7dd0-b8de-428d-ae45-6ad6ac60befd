﻿using DG.Web.Framework;

using DH;
using DH.Entity;
using DH.Models;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Infrastructure;

using NewLife;
using NewLife.Log;
using NewLife.Serialization;
using Pek.Exceptions;
using Pek.Models;
using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>
///角色列表
/// </summary>
[DisplayName("角色管理")]
[Description("角色管理列表")]
[AdminArea]
[DHMenu(79,ParentMenuName = "System", CurrentMenuUrl = "~/{area}/Roles", CurrentMenuName = "Roles", LastUpdate = "20240124")]
public class RolesController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 79;

    // 注入 IActionDescriptorCollectionProvider
    private readonly IActionDescriptorCollectionProvider _actionDescriptorCollectionProvider;

    public RolesController(IActionDescriptorCollectionProvider actionDescriptorCollectionProvider)
    {
        _actionDescriptorCollectionProvider = actionDescriptorCollectionProvider;
    }

    // 判断控制器是否存在的方法
    public bool IsControllerExists(string controllerName)
    {
        var actionDescriptors = _actionDescriptorCollectionProvider.ActionDescriptors.Items;
        return actionDescriptors.Any(descriptor =>
            string.Equals(descriptor.RouteValues["controller"], controllerName, StringComparison.OrdinalIgnoreCase)
            && descriptor is ControllerActionDescriptor);
    }

    /// <summary>
    /// 角色管理列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("角色管理列表")]
    public IActionResult Index()
    {
        ViewBag.IsControllerExists = IsControllerExists("RoleEx");

        return View();
    }

    /// <summary>
    /// 角色列表接口
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("角色列表")]
    public IActionResult GetRole()
    {
        var provider = ManageProvider.Provider;
        if (provider?.Current.ID == 1)
        {
            var roles = Role.FindAllWithCache().OrderBy(e => e.ID).Select(item =>
            {
                var (Name, Remark) = RoleLan.FindByRIdAndLId(item.ID, WorkingLanguage.Id, true);
                return new { Id = item.ID, Name, Remark, item.IsSystem };
            });

            return Json(new { code = 0, msg = "success", count = Role.Meta.Count, data = roles });
        }
        else
        {
            var roles = Role.FindAllWithCache().Where(e => e.ID != 1).OrderBy(e => e.ID).Select(item =>
            {
                var (Name, Remark) = RoleLan.FindByRIdAndLId(item.ID, WorkingLanguage.Id, true);
                return new { Id = item.ID, Name, Remark, item.IsSystem };
            });

            return Json(new { code = 0, msg = "success", count = Role.Meta.Count, data = roles });
        }
    }

    /// <summary>设备详情</summary>
    /// <returns></returns>
    [DisplayName("角色编辑详情")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult EditDetail(int Id)
    {
        ViewBag.LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
        var model = Role.FindByID(Id);
        return View(model);
    }

    /// <summary>设备详情</summary>
    /// <returns></returns>
    [DisplayName("角色新增详情")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult CreateDetail()
    {
        dynamic viewModel = new ExpandoObject();

        viewModel.LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
        return View(viewModel);
    }

    /// <summary>新增角色权限</summary>
    /// <returns></returns>
    [DisplayName("新增角色权限")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult Add(String Name, String Remark, string IsSystem, String IsAdmin)
    {
        if (Name.IsNullOrWhiteSpace())
            return Json(new { success = false, msg = GetResource("标准下的角色名称不能为空") });

        var model = Role.FindByName(Name);
        if (model != null) return Json(new { success = false, msg = GetResource("角色名已存在") });

        var entity = new Role();
        entity.Name = Name;
        entity.Remark = Remark;
        entity.Enable = true;
        entity.IsSystem = IsSystem == "on";

        // 保存权限项
        var menus = XCode.Membership.Menu.Root.AllChilds;
        var dels = new List<Int32>();

        // 遍历所有权限资源
        foreach (var item in menus)
        {
            // 是否授权该项
            var has = GetBool("p" + item.ID);
            if (!has)
                dels.Add(item.ID);
            else
            {
                // 遍历所有权限子项
                var any = false;
                foreach (var pf in item.Permissions)
                {
                    var has2 = GetBool("pf" + item.ID + "_" + pf.Key);

                    if (has2)
                        entity.Set(item.ID, (PermissionFlags)pf.Key);
                    else
                        entity.Reset(item.ID, (PermissionFlags)pf.Key);
                    any |= has2;
                }
                // 如果原来没有权限，这是首次授权，且右边没有勾选任何子项，则授权全部
                if (!any & !entity.Has(item.ID)) entity.Set(item.ID);
            }
        }
        // 删除已经被放弃权限的项
        foreach (var item in dels)
        {
            if (entity.Has(item)) entity.Permissions.Remove(item);
        }
        entity.Insert();

        var modelRoleEx = new RoleEx();
        modelRoleEx.Id = entity.ID;
        modelRoleEx.IsAdmin = IsAdmin == "on";
        modelRoleEx.Insert();

        var list = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取哪些语言
        var addlist = new List<RoleLan>();
        foreach (var item in list)
        {
            var addmodel = new RoleLan();
            addmodel.LId = item.Id;
            addmodel.Name = GetRequest($"[{item.Id}].Name");
            addmodel.Remark = GetRequest($"[{item.Id}].Remark");
            addmodel.RId = entity.ID;
            addlist.Add(addmodel);
        }
        if (addlist.Insert(true) > 0)
        {
            return Json(new { success = true, code = 200, msg = GetResource("创建成功") });
        }
        else
        {
            return Json(new { success = false, code = 0, msg = GetResource("创建失败") });
        }
    }

    /// <summary>编辑角色权限</summary>
    /// <returns></returns>
    [DisplayName("编辑角色权限")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Edit(Int32 Id, String Name, String Remark, string IsSystem, String IsAdmin)
    {
        if (Name.IsNullOrWhiteSpace())
            return Json(new { success = false, msg = GetResource("标准下的角色名称不能为空") });

        var model = Role.FindByName(Name);
        if (model != null && model.ID != Id) return Json(new { success = false, msg = GetResource("角色名已存在") });

        var entity = Role.FindByID(Id);
        entity.Name = Name;
        entity.Remark = Remark;
        entity.IsSystem = IsSystem == "on";

        if (Id > 1)
        {
            // 保存权限项
            var menus = XCode.Membership.Menu.Root.AllChilds;

            var dels = new List<Int32>();

            // 遍历所有权限资源
            foreach (var item in menus)
            {
                // 是否授权该项
                var has = GetBool("p" + item.ID);
                if (!has)
                    dels.Add(item.ID);
                else
                {
                    // 遍历所有权限子项
                    var any = false;
                    foreach (var pf in item.Permissions)
                    {
                        var has2 = GetBool("pf" + item.ID + "_" + pf.Key);

                        if (has2)
                            entity.Set(item.ID, (PermissionFlags)pf.Key);
                        else
                            entity.Reset(item.ID, (PermissionFlags)pf.Key);
                        any |= has2;
                    }
                    // 如果原来没有权限，这是首次授权，且右边没有勾选任何子项，则授权全部
                    if (!any & !entity.Has(item.ID)) entity.Set(item.ID);
                }
            }
            // 删除已经被放弃权限的项
            foreach (var item in dels)
            {
                if (entity.Has(item)) entity.Permissions.Remove(item);
            }
        }

        entity.Update();

        var modelRoleEx = RoleEx.FindById(entity.ID);
        if (modelRoleEx != null)
        {
            if (Id != 1)//当是超级管理员时IsSystem不可更改 
            {
                modelRoleEx.IsAdmin = IsAdmin == "on";
                modelRoleEx.Update();
            }
        }
        else
        {
            modelRoleEx = new RoleEx();
            modelRoleEx.Id = entity.ID;
            modelRoleEx.IsAdmin = IsAdmin == "on";
            modelRoleEx.Insert();
        }

        var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

        var list = RoleLan.FindAllByRId(entity.ID);  //获取到的当前权限的语言
        using (var tran1 = RoleLan.Meta.CreateTrans())
        {
            foreach (var item in Languagelist)
            {
                var ex = list.Find(x => x.LId == item.Id);
                if (ex != null)
                {
                    ex.Name = GetRequest($"[{item.Id}].Name");
                    ex.Remark = GetRequest($"[{item.Id}].Remark");
                    ex.Update();
                }
                else
                {
                    ex = new RoleLan();
                    ex.Name = GetRequest($"[{item.Id}].Name");
                    ex.Remark = GetRequest($"[{item.Id}].Remark");
                    ex.LId = item.Id;
                    ex.RId = entity.ID;
                    ex.Insert();
                }
            }
            tran1.Commit();
        }

        return Json(new { success = true, code = 200, msg = GetResource("编辑成功") });
    }

    Boolean GetBool(String name)
    {
        var v = GetRequest(name);
        if (v.IsNullOrEmpty()) return false;

        v = v.Split(",")[0];

        if (!v.EqualIgnoreCase("true", "false")) throw new DHException(GetResource("非法布尔值Request[{0}]={1}"), name, v);

        return v.ToBoolean();
    }

    /// <summary>删除角色权限</summary>
    /// <returns></returns>
    [DisplayName("删除角色权限")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult Delete(Int32 id)
    {
        var model = Role.FindByID(id);

        if (model != null)
        {
            if (model.IsSystem == true) return Json(new DResult() { success = false, msg = GetResource("不允许删除系统角色") });

            if (UserE.FindCount(UserE._.RoleID == id) > 0) return Json(new DResult() { success = false, msg = GetResource("该角色下还有账号，不允许删除") });

            if (DG.Setting.Current.IsOnlyManager && DHSetting.Current.DefaultRole == model.Name) return Json(new DResult() { success = false, msg = GetResource("不允许删除系统默认角色") });

            model.Delete();
        }

        return Json(new DResult() { success = true, msg = GetResource("删除成功") });
    }

}