// const layui = require('./node_modules/layui/dist/layui.js')


    let centerTableOptions = {
      elem: '#centerTable',
      id:'centerTable',
      cols: [[ //标题栏
          {field: 'OrderId', title: '订单号',width:"20%",templet:(d)=>{
            return '<div data-table="center_table" class="center_table_td">'+d.OrderId+'</div>'
          }},
          {field: 'Status', title: '当前状态',width:"17%",},
          {field: 'ProcessUser', title: '操作人',width:"14%",},
          {field: 'ProcessTime', title: '打号时间',width:"30%",},
          {field: 'ManHour', title: '工时',width:"15%",},
      ]],
      data: [{OrderId: '无数据',}]
      ,cellMinWidth: 80
      ,
        page: false, // 是否显示分页
        limit: 5, // 每页默认显示的数量
        height: (()=>{
          let tableHeight = layui.$('div.item_center').height()
          return ((tableHeight)  + 'px')
        })(),
        parseData: function(res){ // res 即为原始返回的数据
         console.log('表格初始化了',res);
        },
        error: function(e, msg) {
          console.log('调用错误：',e, msg)
        },
        done: function (res, curr, count) {
            // layui.$('tr').css({'background-color': 'red', 'color': 'black'});
        }
      }
// 导出js
export default centerTableOptions
