﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>电源生产数据</summary>
public partial class PowerProductionModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int64 Id { get; set; }

    /// <summary>订单号</summary>
    public String OrderId { get; set; } = null!;

    /// <summary>模块物料编号</summary>
    public String? Material { get; set; }

    /// <summary>产品型号编号</summary>
    public Int32 ProductTypeId { get; set; }

    /// <summary>电源型号配置编号</summary>
    public Int32 PowerTypeItemId { get; set; }

    /// <summary>SN</summary>
    public String? Sn { get; set; }

    /// <summary>测试时间</summary>
    public DateTime TestTime { get; set; }

    /// <summary>备注</summary>
    public String? Remak { get; set; }

    /// <summary>测试结果。使用Json存储</summary>
    public String? Content { get; set; }

    /// <summary>测试结果。成功或者失败</summary>
    public Boolean TestResult { get; set; }

    /// <summary>检测工厂编号</summary>
    public String? CompanyId { get; set; }

    /// <summary>检测工厂名称</summary>
    public String? CompanyName { get; set; }

    /// <summary>测试工位</summary>
    public String? TestStation { get; set; }

    /// <summary>批次字段1</summary>
    public String? BatchField1 { get; set; }

    /// <summary>批次字段2</summary>
    public String? BatchField2 { get; set; }

    /// <summary>批次字段3</summary>
    public String? BatchField3 { get; set; }

    /// <summary>批次字段4</summary>
    public String? BatchField4 { get; set; }

    /// <summary>批次字段5</summary>
    public String? BatchField5 { get; set; }

    /// <summary>批次字段6</summary>
    public String? BatchField6 { get; set; }

    /// <summary>批次字段7</summary>
    public String? BatchField7 { get; set; }

    /// <summary>批次字段8</summary>
    public String? BatchField8 { get; set; }

    /// <summary>批次字段9</summary>
    public String? BatchField9 { get; set; }

    /// <summary>批次字段10</summary>
    public String? BatchField10 { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IPowerProduction model)
    {
        Id = model.Id;
        OrderId = model.OrderId;
        Material = model.Material;
        ProductTypeId = model.ProductTypeId;
        PowerTypeItemId = model.PowerTypeItemId;
        Sn = model.Sn;
        TestTime = model.TestTime;
        Remak = model.Remak;
        Content = model.Content;
        TestResult = model.TestResult;
        CompanyId = model.CompanyId;
        CompanyName = model.CompanyName;
        TestStation = model.TestStation;
        BatchField1 = model.BatchField1;
        BatchField2 = model.BatchField2;
        BatchField3 = model.BatchField3;
        BatchField4 = model.BatchField4;
        BatchField5 = model.BatchField5;
        BatchField6 = model.BatchField6;
        BatchField7 = model.BatchField7;
        BatchField8 = model.BatchField8;
        BatchField9 = model.BatchField9;
        BatchField10 = model.BatchField10;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
    }
    #endregion
}
