﻿using DG.Web.Framework;

using DH.AspNetCore.MVC.Filters;
using DH.Core.Infrastructure;
using DH.Entity;
using DH.Permissions.Identity.JwtBearer;
using DH.RateLimter;
using DH.Services.Services;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.JsonWebTokens;

using NewLife;
using NewLife.Caching;
using NewLife.Log;

using Pek;
using Pek.Exceptions;
using Pek.Helpers;
using Pek.Models;
using Pek.MVC;
using Pek.Swagger;

using System.Security.Claims;

using XCode;
using XCode.Membership;

namespace HlktechIoT.Controllers.Api;

/// <summary>
/// 供应商接口
/// </summary>
[Produces("application/json")]
[CustomRoute(ApiVersions.V1)]
[Authorize("jwt")]
public class AccountController : ApiControllerBaseX {
    /// <summary>
    /// Jwt令牌构建器
    /// </summary>
    public IJsonWebTokenBuilder TokenBuilder { get; }

    /// <summary>
    /// Jwt令牌存储器
    /// </summary>
    public IJsonWebTokenStore TokenStore { get; }

    /// <summary>
    /// 令牌Payload存储器
    /// </summary>
    public ITokenPayloadStore PayloadStore { get; }

    /// <summary>
    /// 密码服务
    /// </summary>
    private readonly PasswordService? _passwordService;

    /// <summary>
    /// 初始化一个<see cref="AccountController"/>类型的实例
    /// </summary>
    /// <param name="tokenBuilder">Jwt令牌构建器</param>
    /// <param name="tokenStore">Jwt令牌存储器</param>
    /// <param name="payloadStore">令牌Payload存储器</param>
    /// <param name="passwordService">密码管理</param>
    public AccountController(IJsonWebTokenBuilder tokenBuilder, IJsonWebTokenStore tokenStore, ITokenPayloadStore payloadStore, PasswordService passwordService)
    {
        TokenBuilder = tokenBuilder;
        TokenStore = tokenStore;
        PayloadStore = payloadStore;
        _passwordService = passwordService;
    }

    /// <summary>
    /// 厂商/供应商用户登录
    /// </summary>
    /// <param name="Name">手机号/邮箱</param>
    /// <param name="Password">密码</param>
    /// <param name="Code">验证码</param>
    /// <param name="CodeId">验证码Id</param>
    /// <param name="Id">请求标识。建议为时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("Login")]
    [ApiSignature]
    [RateValve(Policy = Policy.Ip, Limit = 60, Duration = 3600)]
    public IActionResult Login([FromForm] String Name, [FromForm] String Password, [FromForm] String Code, [FromForm] String CodeId, [FromHeader] String Id, [FromHeader] String Lng)
    {
        var result = new DvResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.id = Id;

        try
        {
            var _cache = EngineContext.Current.Resolve<ICache>();
            if (!_cache.ContainsKey(CodeId))
            {
                result.message = GetResource("验证码过期", Lng);
                return result;
            }

            if (!_cache.Get<String>(CodeId).EqualIgnoreCase(Code))
            {
                result.message = GetResource("验证码不正确", Lng);
                return result;
            }

            if (Name.IsNullOrWhiteSpace())
            {
                result.message = GetResource("账号不能为空", Lng);
                return result;
            }

            if (Password.IsNullOrWhiteSpace())
            {
                result.message = GetResource("密码不能为空", Lng);
                return result;
            }

            if (!ValidateHelper.IsMobile(Name) && !ValidateHelper.IsEmail(Name))
            {
                result.message = GetResource("请输入正确的手机号码/邮箱", Lng);
                return result;
            }
            if (string.IsNullOrWhiteSpace(Password))
            {
                result.message = GetResource("请填写密码", Lng);
                return result;
            }
            var provider = ManageProvider.Provider;
            if (ModelState.IsValid && provider?.Login(Name, Password, false) != null)
            {
                var model = ManageProvider.User;
                if (model!.Enable == false)
                {
                    result.message = GetResource("用户被禁用", Lng);
                    return result;
                }

                var modelUserDetail = UserDetail.FindById(model.ID);
                if (modelUserDetail == null)
                {
                    modelUserDetail = new UserDetail
                    {
                        Id = model.ID
                    };
                    modelUserDetail.Insert();
                }

                if (modelUserDetail.UType != UserKinds.Vendor)
                {
                    result.message = GetResource("未被授权", Lng);
                    return result;
                }

                model.LastLogin = DateTime.Now;
                model.LastLoginIP = DHWeb.IP;
                model.Logins++;
                (model as IEntity)!.Update();

                var payload = new Dictionary<string, string>
                {
                    ["clientId"] = ManageProvider.User?.ID.ToString() ?? String.Empty,
                    [ClaimTypes.Sid] = ManageProvider.User?.ID.ToString() ?? String.Empty,
                    [ClaimTypes.Name] = ManageProvider.User?.Name ?? String.Empty,
                    [ClaimTypes.NameIdentifier] = ManageProvider.User?.Name ?? String.Empty,
                    ["From"] = "Web",
                };
                var resultToken = TokenBuilder.Create(payload);  //获取Token

                result.data = new { Token = resultToken, UId = ManageProvider.User?.ID };
                result.code = StateCode.Ok;
            }
            else
            {
                result.message = GetResource("用户名和密码不匹配", Lng);
                return result;
            }
            result.code = StateCode.Ok;
            return result;
        }
        catch (DHException ex)
        {
            result.message = GetResource(ex.Message, Lng);
            return result;
        }
        catch (EntityException ex)
        {
            result.message = GetResource(ex.Message, Lng);
            XTrace.WriteLine($"登录出错的信息：{result.message}_{ex.Message}_{GetResource(ex.Message, Lng)}_{Lng}");
            return result;
        }
        catch (Exception ex)
        {
            var innerEx = GerInnerException(ex);
            var showerrmsg = GetResource("未知错误", Lng);
            if (innerEx is DHException)
            {
                showerrmsg = GetResource(innerEx.Message, Lng);
            }
            else
            {
                XTrace.Log.Error(string.Format(GetResource("用户登录异常", Lng), Name, ex));
            }
            result.message = showerrmsg;
            return result;
        }
    }

    /// <summary>
    /// 普通用户登录
    /// </summary>
    /// <param name="Name">手机号/邮箱</param>
    /// <param name="Password">密码</param>
    /// <param name="Code">验证码</param>
    /// <param name="CodeId">验证码Id</param>
    /// <param name="Id">请求标识。建议为时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("UserLogin")]
    [ApiSignature]
    [RateValve(Policy = Policy.Ip, Limit = 60, Duration = 3600)]
    public IActionResult UserLogin([FromForm] String Name, [FromForm] String Password, [FromForm] String Code, [FromForm] String CodeId, [FromHeader] string Id, [FromHeader] String Lng)
    {
        var result = new DvResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.id = Id;

        try
        {
            var _cache = EngineContext.Current.Resolve<ICache>();
            if (!_cache.ContainsKey(CodeId))
            {
                result.message = GetResource("验证码过期", Lng);
                return result;
            }

            if (!_cache.Get<String>(CodeId).EqualIgnoreCase(Code))
            {
                result.message = GetResource("验证码不正确", Lng);
                return result;
            }

            if (Name.IsNullOrWhiteSpace())
            {
                result.message = GetResource("账号不能为空", Lng);
                return result;
            }

            if (Password.IsNullOrWhiteSpace())
            {
                result.message = GetResource("密码不能为空", Lng);
                return result;
            }

            if (!ValidateHelper.IsMobile(Name) && !ValidateHelper.IsEmail(Name))
            {
                result.message = GetResource("请输入正确的手机号码/邮箱", Lng);
                return result;
            }

            var provider = ManageProvider.Provider;
            if (ModelState.IsValid && provider?.Login(Name, Password, false) != null)
            {
                var model = ManageProvider.User;
                if (model!.Enable == false)
                {
                    result.message = GetResource("用户被禁用", Lng);
                    return result;
                }

                var modelUserDetail = UserDetail.FindById(model.ID);
                if (modelUserDetail == null)
                {
                    modelUserDetail = new UserDetail
                    {
                        Id = model.ID
                    };
                    modelUserDetail.Insert();
                }

                model.LastLogin = DateTime.Now;
                model.LastLoginIP = DHWeb.IP;
                model.Logins++;
                (model as IEntity)!.Update();

                var payload = new Dictionary<string, string>
                {
                    ["clientId"] = ManageProvider.User?.ID.ToString() ?? String.Empty,
                    [ClaimTypes.Sid] = ManageProvider.User?.ID.ToString() ?? String.Empty,
                    [ClaimTypes.Name] = ManageProvider.User?.Name ?? String.Empty,
                    [ClaimTypes.NameIdentifier] = ManageProvider.User?.Name ?? String.Empty,
                    ["From"] = "Web",
                    ["companyName"] = modelUserDetail.CompanyName.IsNullOrWhiteSpace() ? "" : modelUserDetail.CompanyName,
                };
                var resultToken = TokenBuilder.Create(payload);  //获取Token

                result.data = new { Token = resultToken, UId = ManageProvider.User?.ID, RealName = modelUserDetail.TrueName };
                result.code = StateCode.Ok;

                //// 缓存
                //var client = new WebClient();
                //var res = await client.Post("https://fl.hlktech.com/api/v1/http2/updatecheckcache")
                //    .IgnoreSsl()
                //    .ContentType(HttpContentType.FormUrlEncoded)
                //    .Header("Id", IdHelper.GetNextId())
                //    .Header("Lng", Lng)
                //    .Data("CacheKey", Id)
                //    .Data("UId", ManageProvider.User?.ID ?? 0)
                //    .Data("UName", ManageProvider.User?.Name ?? String.Empty)
                //    .Retry(3)
                //    .WhenCatch<HttpRequestException>(ex =>
                //    {
                //        return $"请求失败：{ex.StackTrace}";
                //    })
                //    .ResultStringAsync();

                //if (res.Contains("请求失败"))
                //{
                //    result.ErrCode = 1001;
                //    result.message = GetResource("请求失败", Lng);
                //    return result;
                //}

                //var dGResult = res.ToJsonEntity<DGResult>();
                //if (dGResult?.Code != StateCode.Ok)
                //{
                //    result.ErrCode = 1002;
                //    result.message = GetResource("请求失败", Lng);
                //    return result;
                //}
            }
            else
            {
                result.message = GetResource("用户名和密码不匹配", Lng);
                return result;
            }
            result.code = StateCode.Ok;
            return result;
        }
        catch (DHException ex)
        {
            result.message = GetResource(ex.Message, Lng);
            return result;
        }
        catch (EntityException ex)
        {
            result.message = GetResource(ex.Message, Lng);
            XTrace.WriteLine($"登录出错的信息：{result.message}_{ex.Message}_{GetResource(ex.Message, Lng)}_{Lng}");
            return result;
        }
        catch (Exception ex)
        {
            var innerEx = GerInnerException(ex);
            var showerrmsg = GetResource("未知错误", Lng);
            if (innerEx is DHException)
            {
                showerrmsg = GetResource(innerEx.Message, Lng);
            }
            else
            {
                XTrace.Log.Error(string.Format(GetResource("用户登录异常", Lng), Name, ex));
            }
            result.message = showerrmsg;
            return result;
        }
    }

    /// <summary>
    /// 退出登录
    /// </summary>
    /// <param name="AccessToken">AccessToken</param>
    /// <param name="RefreshToken">RefreshToken</param>
    /// <param name="Id">请求标识。建议为时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <returns></returns>
    [HttpPost("Logout")]
    [ApiSignature]
    [RateValve(Policy = Policy.Ip, Limit = 60, Duration = 3600)]
    public IActionResult Logout([FromForm] String AccessToken, [FromForm] String RefreshToken, [FromHeader] string Id, [FromHeader] String Lng)
    {
        var result = new DvResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.id = Id;

        //var UId = HttpContext.Items["clientId"].SafeString();
        var UId = Pek.Helpers.DHWeb.Identity.GetValue(ClaimTypes.Sid);

        var model = UserE.FindByID(UId.ToInt());
        if (model == null)
        {
            result.message = GetResource("未获取到当前用户数据！", Lng);
            return result;
        }

        //DingTalkRobot.OapiRobotText($"测试推送/r/n用户{model.Name}退出登录，当前绑定设备ID为{modelUserEx.BindJiGuangId}，提交的设备ID值为：{BindId}", new List<string>(), false);

        TokenStore.RemoveToken(AccessToken);
        TokenStore.RemoveRefreshToken(RefreshToken);

        var provider = ManageProvider.Provider;
        provider?.Logout();

        UserE.WriteLog(LocaleStringResource.GetResource("退出登录", Lng), true, String.Format(LocaleStringResource.GetResource("用户[{0}]退出登录", Lng), model.Name));

        result.code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 用户注销账号
    /// </summary>
    /// <param name="Lng">语言标识符</param>
    /// <param name="Id">请求标识。建议为时间戳</param>
    /// <returns></returns>
    [HttpPost("DeleteUser")]
    [ApiSignature]
    [RateValve(Policy = Policy.Ip, Limit = 60, Duration = 3600)]
    public IActionResult DeleteUser([FromHeader] string Id, [FromHeader] String Lng)
    {
        var result = new DvResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.id = Id;

        var UId = Pek.Helpers.DHWeb.Identity.GetValue(ClaimTypes.Sid);

        var model = UserE.FindByID(UId.ToInt());

        if (model == null)
        {
            result.message = GetResource("未获取到当前用户数据！", Lng);
            return result;
        }

        using (var tran1 = UserE.Meta.CreateTrans())
        {
            UserE.Delete(UserE._.ID == model.ID);
            UserDetail.Delete(UserDetail._.Id == model.ID);

            tran1.Commit();
        }
        UserE.Meta.Cache.Clear("", true);
        UserDetail.Meta.Cache.Clear("", true);

        result.code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 刷新令牌
    /// </summary>
    /// <param name="RefreshToken">刷新令牌值</param>
    /// <param name="Lng">语言标识符</param>
    /// <param name="Id">请求标识。建议为时间戳</param>
    /// <returns></returns>
    /// <remarks>注意：刷新令牌时要注意不要传AccessToken。</remarks>
    [AllowAnonymous]
    [HttpPost("RefreshToken")]
    [ApiSignature]
    [RateValve(Policy = Policy.Ip, Limit = 60, Duration = 3600)]
    public IActionResult RefreshToken([FromForm] String RefreshToken, [FromHeader] string Id, [FromHeader] String Lng)
    {
        var result = new DvResult();

        var _cache = EngineContext.Current.Resolve<ICache>();
        var key = $"dh-{RefreshToken}";
        if (_cache.ContainsKey(key))
        {
            result.code = StateCode.Ok;
            result.data = new { Token = _cache.Get<JsonWebToken>(key) };
            return result;
        }

        var token = TokenBuilder.Refresh(RefreshToken, 10);
        _cache.Set(key, token, 10);

        result.code = StateCode.Ok;
        result.data = new { Token = token };

        return result;
    }

    /// <summary>
    /// 修改密码
    /// </summary>
    /// <param name="OldPassword">旧密码</param>
    /// <param name="NewPassword">新密码</param>
    /// <param name="ComfimPassword">确认密码</param>
    /// <param name="Lng">语言标识符</param>
    /// <returns></returns>
    [HttpPost("ChangePassword")]
    [ApiSignature]
    [RateValve(Policy = Policy.Ip, Limit = 60, Duration = 3600)]
    public IActionResult ChangePassword([FromForm] String OldPassword, [FromForm] String NewPassword, [FromForm] String ComfimPassword, [FromHeader] String Lng)
    {
        var result = new DvResult();

        if (NewPassword.IsNullOrWhiteSpace())
        {
            result.message = GetResource("新密码不能为空", Lng);
            return result;
        }

        if (NewPassword != ComfimPassword)
        {
            result.message = GetResource("两次密码不一致", Lng);
            return result;
        }

        if (ComfimPassword.Length < 6 || ComfimPassword.Length > 32)
        {
            result.message = GetResource("密码长度不能小于6，或大于32位", Lng);
            return result;
        }

        var UId = Pek.Helpers.DHWeb.Identity.GetValue(ClaimTypes.Sid);

        var model = UserE.FindByID(UId.ToInt());

        if (model == null)
        {
            result.message = GetResource("未获取到当前用户数据！", Lng);
            return result;
        }

        if (ManageProvider.Provider?.PasswordProvider.Verify(OldPassword, model.Password!) == false)
        {
            result.message = GetResource("旧密码错误", Lng);
            return result;
        }

        model.Password = ComfimPassword;
        model.Update();

        result.code = StateCode.Ok;
        result.message = GetResource("密码修改成功", Lng);

        UserE.WriteLog(LocaleStringResource.GetResource("修改密码", Lng), true, String.Format(LocaleStringResource.GetResource("用户[{0}]调用修改密码接口", Lng), model.Name));
        return result;
    }

}
