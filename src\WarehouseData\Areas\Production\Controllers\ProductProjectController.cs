﻿using DG.Enums;
using DG.Web.Framework;
using DH;
using DH.Core.Infrastructure;
using HlktechIoT.Data;
using HlktechIoT.Dto;
using HlktechIoT.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using NewLife;
using NewLife.Caching;
using NewLife.Data;
using NewLife.Log;
using NewLife.Serialization;
using Pek;
using Pek.Helpers;
using Pek.Iot;
using Pek.Models;
using Pek.Webs;
using SharpCompress.Common;
using System.Collections.Concurrent;
using System.ComponentModel;
using XCode;
using XCode.Membership;
using YRY.Web.Controllers.Common;
using static iTextSharp.text.pdf.AcroFields;

namespace HlktechIoT.Areas.Production.Controllers
{
    /// <summary>产品项目</summary>
    [DisplayName("产品项目")]
    [Description("产品项目")]
    [ProductionArea]
    [DHMenu(95, ParentMenuName = "ProductionsManager", ParentMenuDisplayName = "生产管理", ParentMenuUrl = "", ParentMenuOrder = 30, ParentIcon = "layui-icon-component", CurrentMenuUrl = "~/{area}/ProductProject", CurrentMenuName = "ProductProjectList", LastUpdate = "20250616")]
    public class ProductProjectController : BaseAdminControllerX
    {
        protected static Int32 MenuOrder { get; set; } = 95;

        /// <summary>
        /// 产品项目列表
        /// </summary>
        /// <returns></returns>
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 产品项目列表
        /// </summary>
        /// <param name="Key">关键字</param>
        /// <param name="page">页码</param>
        /// <param name="limit">条数</param>
        /// <returns></returns>
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult GetList(String Key, Int32 page, Int32 limit)
        {
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "Id",
                Desc = true,
            };
            var data = ProductProject.Search(-1, null, DateTime.MinValue, DateTime.MinValue, Key, pages).Select(e => new
            {
                e.Id,
                e.Name,
                ProductTypeName = e.ProductType?.Name,
                e.ProjectNo,
                e.Status,
                e.AuditStatus,
                e.CreateTime,
                e.CreateUser,
            });
            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
        }

        /// <summary>
        /// 搜索产品型号
        /// </summary>
        /// <param name="keyword">关键字</param>
        /// <param name="page">页码</param>
        /// <param name="Id">产品型号id</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("搜索产品型号")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult SearchProductType(String keyword, Int32 page, Int32 Id)
        {
            var res = new DResult();

            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = 10,
                RetrieveTotalCount = true,
                Sort = ProductType._.Id,
                Desc = true,
            };

            res.data = ProductType.Search(0, keyword, true, DateTime.MinValue, DateTime.MinValue, "", pages).Select(e =>
            {
                return new
                {
                    name = e.Name,
                    value = e.Id,
                    e.NeedSn,
                    e.NeedMac,
                    e.NeedMac1,
                    e.PCBCycle,
                    e.ShieldCycle,
                    e.MainChipCycle,
                };
            });

            res.success = true;

            var model = ProductType.FindById(Id);

            if (model == null)
            {
                res.extdata = new { pages.PageCount };
            }
            else
            {
                res.extdata = new { pages.PageCount, data = new List<object>() { new { name = model.Name, value = model.Id, NeedSn = model.NeedSn, NeedMac = model.NeedMac, model.NeedMac1, model.PCBCycle, model.ShieldCycle, model.MainChipCycle } } };
            }

            return Json(res);
        }

        /// <summary>
        /// 添加产品项目
        /// </summary>
        /// <returns></returns>
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add()
        {
            return View();
        }

        /// <summary>
        /// 添加产品项目
        /// </summary>
        /// <param name="Name">名称</param>
        /// <param name="ProjectNo">项目编号</param>
        /// <param name="Status">启用状态</param>
        /// <param name="Remark">备注</param>
        /// <param name="ProductTypeId">产品型号id</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add([FromForm] String Name, [FromForm] String ProjectNo, [FromForm] Boolean Status, [FromForm] String Remark,[FromForm]Int32 ProductTypeId)
        {
            DResult res = new();
            if (Name.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("名称不能为空");
                return Json(res);
            }
            if (ProductTypeId <= 0)
            {
                res.msg = GetResource("产品型号不能为空");
                return Json(res);
            }
            var modelProductType = ProductType.FindById(ProductTypeId);
            if (modelProductType == null)
            {
                res.msg = GetResource("产品型号不存在");
                return Json(res);
            }
            var modelProductProject = ProductProject.FindByName(Name);
            if(modelProductProject != null)
            {
                res.msg = GetResource("名称已存在");
                return Json(res);
            }
            var model = new ProductProject()
            {
                Name = Name,
                ProjectNo = ProjectNo,
                ProductTypeId = ProductTypeId,
                Status = Status,
                Remark = Remark,
            };

            var list = ProductDataCategory.FindAll(ProductDataCategory._.Must == true).Select(e=>e.Id);
            if(list.Count() > 0)
            {
                model.DataCategoryIds = "," + list.Join() + ",";
            }   
            model.Insert();
            res.success = true;
            res.msg = GetResource("添加成功");
            return Json(res);
        }

        /// <summary>
        /// 编辑产品项目
        /// </summary>
        /// <param name="Id">项目id</param>
        /// <returns></returns>
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Update(Int32 Id)
        {
            var model = ProductProject.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("项目信息不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 编辑产品项目
        /// </summary>
        /// <param name="Name">名称</param>
        /// <param name="ProjectNo">项目编号</param>
        /// <param name="Status">启用状态</param>
        /// <param name="Remark">备注</param>
        /// <param name="DataCategoryIds">必须上传的资料分类</param>
        /// <param name="Id">项目id</param>
        /// <param name="ProductTypeId">产品类型id</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Update([FromForm] String Name, [FromForm] String ProjectNo, [FromForm] Boolean Status, [FromForm] String Remark, [FromForm] String DataCategoryIds, [FromForm]Int32 Id, [FromForm] Int32 ProductTypeId)
        {
            DResult res = new();
            if (Name.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("名称不能为空");
                return Json(res);
            }
            if (ProductTypeId <= 0)
            {
                res.msg = GetResource("产品型号不能为空");
                return Json(res);
            }
            var modelProductType = ProductType.FindById(ProductTypeId);
            if (modelProductType == null)
            {
                res.msg = GetResource("产品型号不存在");
                return Json(res);
            }
            var model = ProductProject.FindById(Id);
            if (model == null)
            {
                res.msg = GetResource("项目信息不存在");
                return Json(res);
            }
            var modelProductProject = ProductProject.FindByName(Name);
            if (modelProductProject != null && modelProductProject.Id != Id)
            {
                res.msg = GetResource("名称已存在");
                return Json(res);
            }
            model.Name = Name;
            model.ProjectNo = ProjectNo;
            model.ProductTypeId = ProductTypeId;
            model.Status = Status;
            model.Remark = Remark;
            if (!DataCategoryIds.IsNullOrWhiteSpace())
            {
                model.DataCategoryIds = "," + DataCategoryIds + ",";
            }
            else
            {
                model.DataCategoryIds = null;
            }
            model.Update();

            var logs = new ProductProjectLog()
            {
                ProductProjectId = model.Id,
                DType = 2,
                Content = "编辑了项目",
            };
            logs.Insert();

            res.success = true;
            res.msg = GetResource("编辑成功");
            return Json(res);
        }

        /// <summary>
        /// 修改产品项目启用状态
        /// </summary>
        /// <param name="Id">项目id</param>
        /// <param name="Status">启用状态</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("修改状态")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult ModifyState(Int32 Id, Boolean Status)
        {
            var result = new DResult();

            var model = ProductProject.FindById(Id);
            if (model == null)
            {
                result.msg = GetResource("状态调整出错");
                return Json(result);
            }

            model.Status = Status;
            model.Update();

            var logs = new ProductProjectLog()
            {
                ProductProjectId = model.Id,
                DType = 2,
                Content = "调整了项目启用状态",
            };
            logs.Insert();

            result.success = true;
            result.msg = GetResource("状态调整成功");

            return Json(result);
        }

        /// <summary>
        /// 删除产品项目
        /// </summary>
        /// <param name="Id">项目id</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("删除")]
        [EntityAuthorize(PermissionFlags.Delete)]
        public IActionResult Delete(Int32 Id)
        {
            var res = new DResult();

            var count = ProductProjectData.FindCount(ProductProjectData._.ProductProjectId == Id);
            if (count > 0)
            {
                res.msg = GetResource("产品项目中已上传资料 不能删除");
                return Json(res);
            }

            ProductProject.Delete(ProductProject._.Id == Id);

            var logs = new ProductProjectLog()
            {
                ProductProjectId = Id,
                DType = 3,
                Content = "删除了项目",
            };
            logs.Insert();
            res.success = true;
            res.msg = GetResource("删除成功");
            return Json(res);

        }

        /// <summary>
        /// 配置
        /// </summary>
        /// <returns></returns>
        [DisplayName("产品项目配置项")]
        [EntityAuthorize((PermissionFlags)16)]
        public IActionResult Config(Int32 ProjectId)
        {
            var project = ProductProject.FindById(ProjectId);
            if (project == null)
            {
                return Content(GetResource("产品项目不存在"));
            }
            return View(project);
        }

        /// <summary>
        /// 配置
        /// </summary>
        /// <returns></returns>
        [DisplayName("产品项目配置项")]
        [EntityAuthorize((PermissionFlags)16)]
        public IActionResult ConfigList(Int32 ProjectId, Int32 page, Int32 limit)
        {
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "Id",
                Desc = true,
            };

            List<ProductDataCategory> data = new();

            var project = ProductProject.FindById(ProjectId);
            if(project != null && !project.DataCategoryIds.IsNullOrWhiteSpace())
            {
                foreach (var item in project.DataCategoryIds.SplitAsInt())
                {
                    var model = ProductDataCategory.FindById(item);
                    if (model == null) continue;
                    model.ParentName = ProductDataCategory.FindById(model.ParentId)?.Name;
                    data.Add(model);
                }
            }

            return Json(new { code = 0, msg = "success", count = data.Count, data = data.Skip((page - 1) * limit).Take(limit) });
        }

        /// <summary>
        /// 搜索产品资料分类
        /// </summary>
        /// <param name="keyword">关键字</param>
        /// <param name="page">页码</param>
        /// <param name="ProjectId">产品项目id</param>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult SearchDataCategory1(String keyword, Int32 page, Int32 ProjectId)
        {
            var res = new DResult();

            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = 10,
                RetrieveTotalCount = true,
                Sort = ProductDataCategory._.Id,
            };

            var exp = new WhereExpression();

            exp &= ProductDataCategory._.ParentId > 0 & ProductDataCategory._.Status == true;

            var project = ProductProject.FindById(ProjectId);
            if (project != null && !project.DataCategoryIds.IsNullOrWhiteSpace())
            {
                exp &= ProductDataCategory._.Id.NotIn(project.DataCategoryIds.Trim(','));
            }

            res.data = ProductDataCategory.FindAll(exp, pages).Select(e =>
            {
                var p = ProductDataCategory.FindById(e.ParentId);
                return new
                {
                    name = e.Name + $"({p?.Name})",
                    value = e.Id,
                };
            });

            res.extdata = new { pages.PageCount };
            res.success = true;
            return Json(res);
        }

        /// <summary>
        /// 搜索产品资料分类
        /// </summary>
        /// <param name="keyword">关键字</param>
        /// <param name="page">页码</param>
        /// <param name="ProjectId">产品项目id</param>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult SearchDataCategory2(String keyword, Int32 page, Int32 ProjectId)
        {
            var res = new DResult();

            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = 10,
                RetrieveTotalCount = true,
                Sort = ProductDataCategory._.Id,
            };

            var exp = new WhereExpression();

            exp &= ProductDataCategory._.ParentId > 0 & ProductDataCategory._.Status == true;

            var project = ProductProject.FindById(ProjectId);
            if (project != null && !project.DataCategoryIds.IsNullOrWhiteSpace())
            {
                exp &= ProductDataCategory._.Id.In(project.DataCategoryIds.Trim(','));
                res.data = ProductDataCategory.FindAll(exp, pages).Select(e =>
                {
                    var p = ProductDataCategory.FindById(e.ParentId);
                    return new
                    {
                        name = e.Name + $"({p?.Name})",
                        value = e.Id,
                    };
                });
            }
            else
            {
                res.data = new List<object>();
            }

            res.extdata = new { pages.PageCount };
            res.success = true;
            return Json(res);
        }

        /// <summary>
        /// 搜索产品资料
        /// </summary>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult SearchProductData(String keyword, Int32 page, Int32 DataCategoryId)
        {
            var res = new DResult();

            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = 10,
                RetrieveTotalCount = true,
                Sort = ProductProjectData._.Id,
            };

            res.data = ProductData.Search(-1, -1, 0, DataCategoryId, DateTime.MinValue, DateTime.MinValue, "", pages).Select(e =>
            {
                var ptype = e.Ptype == 1 ? GetResource("产测固件") : e.Ptype == 2 ? GetResource("出货固件") : e.Ptype == 1 ? GetResource("标签模板") : GetResource("普通");
                return new
                {
                    name = e.FileName+$"({ptype})",
                    value = e.Id,
                };
            });

            res.extdata = new { pages.PageCount };
            res.success = true;
            return Json(res);
        }

        /// <summary>
        /// 上传资料
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("上传资料")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult UploadFiles(IFormFile file)
        {
            var res = new DResult();

            try
            {
                if (file == null)
                {
                    XTrace.WriteLine("获取到的文件为空");

                    res.msg = GetResource("资料上传有误");
                    return Json(res);
                }

                var OrignfileName = file.FileName;

                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(OrignfileName)}";
                //var filename = $"{OrignfileName}";
                var filepath = DHSetting.Current.UploadPath.CombinePath($"ProductData/{filename}");
                var saveFileName = filepath.GetFullPath();
                var f = saveFileName.AsFile();
                if (f.Exists)
                {
                    f.Delete();
                }
                saveFileName.EnsureDirectory();
                file.SaveAs(saveFileName);
                res.msg = GetResource("资料上传成功");
                res.success = true;
                res.data = new { OriginFileName = OrignfileName, FilePath = filepath.Replace("\\", "/") };
                return Json(res);
            }
            catch (Exception ex)
            {
                res.msg = GetResource("资料上传异常");
                XTrace.WriteLine($"资料上传异常：{ex.ToString()}");
                return Json(res);
            }
        }

        /// <summary>
        /// 添加配置项
        /// </summary>
        /// <param name="ProjectId"></param>
        /// <returns></returns>
        [DisplayName("添加配置项")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult ConfigAdd(Int32 ProjectId)
        {
            var project = ProductProject.FindById(ProjectId);
            if (project == null)
            {
                return Content(GetResource("产品项目不存在"));
            }
            return View(project);
        }

        /// <summary>
        /// 添加配置项
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("添加配置项")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult ConfigAdd(Int32 ProjectId,String DataCategoryId)
        {
            DResult res = new();

            if(DataCategoryId.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("配置项不能为空");
                return Json(res);
            }
            if (ProjectId <= 0)
            {
                res.msg = GetResource("产品项目不能为空");
                return Json(res);
            }
            var project = ProductProject.FindById(ProjectId);
            if (project == null)
            {
                res.msg = GetResource("产品项目不存在");
                return Json(res);
            }

            if (project.DataCategoryIds.IsNullOrWhiteSpace())
            {
                project.DataCategoryIds = "," + DataCategoryId + ",";
            }
            else
            {
                project.DataCategoryIds  = project.DataCategoryIds + DataCategoryId + ",";
            }
            project.Update();
            var listProductDataCategory = ProductDataCategory.FindAllByIds(DataCategoryId);
            var logs = new ProductProjectLog()
            {
                ProductProjectId = project.Id,
                DType = 1,
                Content = $"添加了项目配置项：{String.Join(",", listProductDataCategory.Select(e => e.Name))}",
            };
            logs.Insert();
            res.success = true;
            res.msg = GetResource("添加配置项成功");
            return Json(res);
        }

        /// <summary>
        /// 删除配置项
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("删除配置项")]
        [EntityAuthorize(PermissionFlags.Delete)]
        public IActionResult ConfigDelete(Int32 ProjectId,Int32 DataCategoryId)
        {
            DResult res = new();

            var project = ProductProject.FindById(ProjectId);
            if(project == null)
            {
                res.msg = GetResource("产品项目不存在");
                return Json(res);
            }

            var model = ProductDataCategory.FindById(DataCategoryId);
            if(model == null)
            {
                res.msg = GetResource("配置项不存在");
                return Json(res);
            }

            var modelProductProjectData = ProductProjectData.FindByProductProjectIdAndDataCategoryId(ProjectId, DataCategoryId);
            if(modelProductProjectData != null)
            {
                res.msg = GetResource("配置项已上传资料 无法删除");
                return Json(res);
            }

            if (project.DataCategoryIds.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("产品项目没有配置项");
                return Json(res);
            }

            if(project.DataCategoryIds == $",{DataCategoryId},")
            {
                project.DataCategoryIds = "";
            }
            else
            {
                project.DataCategoryIds = project.DataCategoryIds.Replace($",{DataCategoryId},", ",");
            }        
            project.Update();
            var logs = new ProductProjectLog()
            {
                ProductProjectId = project.Id,
                DType = 3,
                Content = $"删除了项目配置项：{model.Name}",
            };
            logs.Insert();
            res.success = true;
            res.msg = GetResource("删除配置项成功");
            return Json(res);
        }

        /// <summary>
        /// 产品项目资料
        /// </summary>
        /// <param name="ProjectId"></param>
        /// <returns></returns>
        [DisplayName("产品项目资料")]
        [EntityAuthorize((PermissionFlags)32)]
        public IActionResult DataInfo(Int32 ProjectId)
        {
            var project = ProductProject.FindById(ProjectId);
            if (project == null)
            {
                return Content(GetResource("产品项目不存在"));
            }
            return View(project);
        }

        /// <summary>
        /// 产品项目资料
        /// </summary>
        /// <returns></returns>
        [DisplayName("产品项目资料")]
        [EntityAuthorize((PermissionFlags)32)]
        public IActionResult DataInfoList(Int32 DataCategoryId, Int32 ProjectId,Int32 Status = -1)
        {
            var pages = new PageParameter
            {
                RetrieveTotalCount = true,
                Sort = "Id",
                Desc = true,
            };

            var data = ProductProjectData.Search(DataCategoryId,ProjectId, 0, Status, DateTime.MinValue, DateTime.MinValue, "", pages).Select(e => 
            {
                var modelProductData = ProductData.FindById(e.ProductDataId);
                var ext = Path.GetExtension(modelProductData?.FilePath) ?? "";
                return new
                {
                    e.Id,
                    ProductDataCategoryName1 = ProductDataCategory.FindById(modelProductData?.ProductDataCategoryId1 ?? 0)?.Name,
                    ProductDataCategoryName2 = ProductDataCategory.FindById(modelProductData?.ProductDataCategoryId2 ?? 0)?.Name,
                    FileName = (modelProductData?.FileName ?? "").Contains(ext) ? modelProductData?.FileName : modelProductData?.FileName + ext,
                    FilePath = modelProductData?.FilePath,
                    Version = modelProductData?.Version,
                    Ptype = modelProductData?.Ptype,
                    e.Status,
                    e.CreateUser,
                    e.CreateTime,
                    e.Remark,
                    e.ReAudit,
                    e.ReAuditRemark,
                };
            }).OrderBy(e=>e.ProductDataCategoryName2);

            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
        }

        /// <summary>
        /// 添加产品项目资料
        /// </summary>
        /// <param name="ProjectId"></param>
        /// <returns></returns>
        [DisplayName("添加产品项目资料")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult AddDataInfo(Int32 ProjectId)
        {
            var project = ProductProject.FindById(ProjectId);
            if (project == null)
            {
                return Content(GetResource("产品项目不存在"));
            }
            return View(project);
        }

        /// <summary>
        /// 添加产品项目资料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("添加产品项目资料")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult AddDataInfo(Int32 ProjectId,Int32 ProductDataId)
        {
            DResult res = new();

            if(ProjectId <= 0)
            {
                res.msg = GetResource("产品项目不能为空");
                return Json(res);
            }
            if (ProductDataId <= 0)
            {
                res.msg = GetResource("产品资料不能为空");
                return Json(res);
            }

            var project = ProductProject.FindById(ProjectId);
            if (project == null)
            {
                res.msg = GetResource("产品项目不存在");
                return Json(res);
            }

            var modelProductData = ProductData.FindById(ProductDataId);
            if (modelProductData == null)
            {
                res.msg = GetResource("产品资料不存在");
                return Json(res);
            }

            if (ProductProjectData.FindCount(project.Id, modelProductData.ProductDataCategoryId2, modelProductData.Ptype) >= 1)
            {
                res.msg = GetResource("项目中已存在同类别同类型资料");
                return Json(res);
            }

            var model = new ProductProjectData()
            {
                ProductProjectId = project.Id,
                ProductDataId = modelProductData.Id,
                Status = 0,
            };
            model.Insert();
            var modelProductDataCategory = ProductDataCategory.FindById(modelProductData.ProductDataCategoryId2);
            var logs = new ProductProjectLog()
            {
                ProductProjectId = project.Id,
                DType = 1,
                Content = $"新增了项目资料：{modelProductDataCategory?.Name}",
            };
            logs.Insert();
            res.success = true;
            res.msg = GetResource("添加产品项目资料成功");
            return Json(res);
        }

        /// <summary>
        /// 添加产品资料
        /// </summary>
        /// <returns></returns>
        [DisplayName("添加产品项目资料")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult AddProductData(Int32 ProjectId)
        {
            var project = ProductProject.FindById(ProjectId);
            if (project == null)
            {
                return Content(GetResource("产品项目不存在"));
            }
            return View(project);
        }

        /// <summary>
        /// 添加产品资料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("添加产品项目资料")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult AddProductData(String FilePath, Int32 ProductDataCategoryId, String Remark, Int32 Ptype, String Version, String FileName,Int32 ProductProjectId)
        {
            DResult res = new();
            if (ProductDataCategoryId <= 0)
            {
                res.msg = GetResource("产品资料类别不能为空");
                return Json(res);
            }
            if (FilePath.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("产品资料文件不能为空");
                return Json(res);
            }
            var modelProductDataCategory = ProductDataCategory.FindById(ProductDataCategoryId);
            if (modelProductDataCategory == null)
            {
                res.msg = GetResource("产品资料类别信息不存在");
                return Json(res);
            }
            if(ProductProjectData.FindCount(ProductProjectId, ProductDataCategoryId, Ptype) >= 1)
            {
                res.msg = GetResource("项目中已存在同类别同类型资料");
                return Json(res);
            }
            var model = new ProductData()
            {
                FilePath = FilePath,
                ProductDataCategoryId1 = modelProductDataCategory.ParentId,
                ProductDataCategoryId2 = modelProductDataCategory.Id,
                Ptype = Ptype,
                Remark = Remark,
                Version = Version,
                FileName = FileName,
            };
            model.Insert();
            var modelProductProjectData = new ProductProjectData()
            {
                ProductProjectId = ProductProjectId,
                ProductDataId = model.Id,
                Status = 0,
            };
            modelProductProjectData.Insert();
            var logs = new ProductProjectLog()
            {
                ProductProjectId = ProductProjectId,
                DType = 1,
                Content = $"新增了项目资料：{modelProductDataCategory.Name}",
            };
            logs.Insert();
            res.success = true;
            res.msg = GetResource("添加成功");
            return Json(res);
        }

        /// <summary>
        /// 审核产品项目资料
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [DisplayName("审核产品项目资料")]
        [EntityAuthorize((PermissionFlags)64)]
        public IActionResult AuditDataInfo(Int32 Id)
        {
            var model = ProductProjectData.FindById(Id);
            if(model == null)
            {
                return Content(GetResource("产品项目资料不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 审核产品项目资料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("审核产品项目资料")]
        [EntityAuthorize((PermissionFlags)64)]
        public IActionResult AuditDataInfo(Int32 Id, Int32 Status, String Remark)
        {
            var res = new DResult();

            var model = ProductProjectData.FindById(Id);
            if (model == null)
            {
                res.msg = GetResource("产品项目资料不存在");
                return Json(res);
            }

            var project = ProductProject.FindById(model.ProductProjectId);
            if(project == null)
            {
                res.msg = GetResource("产品项目不存在");
                return Json(res);
            }

            if(project.AuditStatus == 0)
            {
                project.AuditStatus = 1;
                project.Update();
            }

            model.Status = Status;
            model.Remark = Remark;
            model.Update();

            var modelProductDataCategory = ProductDataCategory.FindById(model.ProductData?.ProductDataCategoryId2 ?? 0);

            var state = Status == 2 ? "审核通过" : "审核失败";

            var logs = new ProductProjectLog()
            {
                ProductProjectId = project.Id,
                DType = 4,
                Content = $"{project.Name}的 {modelProductDataCategory} 资料已完成审核。审核结果：{state}。审核备注：{Remark}。",
            };
            logs.Insert();

            var modelMessage = new Message()
            {
                Title = "项目资料审核结果",
                Content = $"{project.Name}的 {modelProductDataCategory} 资料已完成审核。审核结果：{state}。审核备注：{Remark}。",
                ReceiveUserID = model.CreateUserID,
                Status = false,
            };
            modelMessage.Insert();

            res.success = true;
            res.msg = GetResource("审核完成");
            return Json(res);
        }

        /// <summary>
        /// 删除产品项目资料
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("删除产品项目资料")]
        [EntityAuthorize(PermissionFlags.Delete)]
        public IActionResult DeleteDataInfo(Int32 Id)
        {
            DResult res = new();
            var model = ProductProjectData.FindById(Id);
            if (model == null)
            {
                res.msg = GetResource("产品项目资料不存在");
                return Json(res);
            }
            if(model.Status >= 2 && model.ProductProject?.ReAudit < 2)
            {
                res.msg = GetResource("非法操作");
                return Json(res);
            }

            model.Delete();

            var modelProductDataCategory = ProductDataCategory.FindById(model.ProductData?.ProductDataCategoryId2 ?? 0);

            var logs = new ProductProjectLog()
            {
                ProductProjectId = model.ProductProjectId,
                DType = 3,
                Content = $"删除了项目资料：{modelProductDataCategory?.Name}，文件名：{model.ProductData?.FileName}",
            };
            logs.Insert();


            res.success = true;
            res.msg = GetResource("删除成功");
            return Json(res);
        }

        /// <summary>
        /// 提交推送
        /// </summary>
        /// <param name="ProjectId"></param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("提交推送")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult SubmitPush(Int32 ProjectId)
        {
            DResult res = new();

            var model = ProductProject.FindById(ProjectId);
            if(model == null)
            {
                res.msg = GetResource("产品项目不存在");
                return Json(res);
            }
            if (model.DataCategoryIds.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("产品项目配置项为空");
                return Json(res);
            }
            if(model.AuditStatus >= 2)
            {
                res.msg = GetResource("产品项目已审核 不可重复提交");
                return Json(res);
            }

            var list = ProductProjectData.FindAllByProductProjectId(ProjectId);

            XTrace.WriteLine($"111：{list.ToJson()}");

            foreach (var item in model.DataCategoryIds.Split(","))
            {
                XTrace.WriteLine($"222：{item}");
                var category = ProductDataCategory.FindById(item.ToInt());
                if(category != null)
                {
                    XTrace.WriteLine($"333：{category.Name}");
                    var count = list.Count(e => e.ProductData?.ProductDataCategoryId2 == category.Id);
                    XTrace.WriteLine($"444：{count}");
                    if (count == 0)
                    {
                        res.msg = GetResource($"{category.Name}的资料未提交");
                        return Json(res);
                    }
                }
            }

            model.ReAudit = 0;
            if (model.SubmitStatus == 0)
            {
                model.SubmitStatus = 1;
                model.Update();
                res.msg = GetResource("提交审核成功");

                AddMessage("项目资料提交审核", $"{model.Name}的资料已提交审核，请尽快处理。", 128);

                var logs = new ProductProjectLog()
                {
                    ProductProjectId = model.Id,
                    DType = 4,
                    Content = $"提交项目资料审核",
                };
                logs.Insert();
            }
            else
            {
                model.SubmitStatus = 0;
                model.Update();
                res.msg = GetResource("反提交成功");

                AddMessage("反提交项目资料", $"{model.Name}的资料操作了反提交，请知悉。", 128);

                var logs = new ProductProjectLog()
                {
                    ProductProjectId = model.Id,
                    DType = 4,
                    Content = $"反提交项目资料",
                };
                logs.Insert();
            }

            res.success = true;
            return Json(res);
        }

        /// <summary>
        /// 项目审核
        /// </summary>
        /// <param name="ProjectId"></param>
        /// <returns></returns>
        [DisplayName("项目审核")]
        [EntityAuthorize((PermissionFlags)128)]
        public IActionResult Audit(Int32 ProjectId)
        {
            var project = ProductProject.FindById(ProjectId);
            if (project == null)
            {
                return Content(GetResource("产品项目不存在"));
            }
            return View(project);
        }

        /// <summary>
        /// 项目审核
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("项目审核")]
        [EntityAuthorize((PermissionFlags)128)]
        public IActionResult Audit(Int32 ProjectId,Int32 AuditStatus)
        {
            DResult res = new();

            var model = ProductProject.FindById(ProjectId);
            if (model == null)
            {
                res.msg = GetResource("产品项目不存在");
                return Json(res);
            }
            if (model.DataCategoryIds.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("产品项目配置项为空");
                return Json(res);
            }
            if(model.AuditStatus >= 2)
            {
                res.msg = GetResource("产品项目已审核");
                return Json(res);
            }

            var list = ProductProjectData.FindAllByProductProjectId(ProjectId);

            XTrace.WriteLine($"111：{list.ToJson()}");

            foreach (var item in model.DataCategoryIds.Split(","))
            {
                XTrace.WriteLine($"222：{item}");
                var category = ProductDataCategory.FindById(item.ToInt());
                if (category != null)
                {
                    XTrace.WriteLine($"333：{category.Name}");
                    var count = list.Count(e => e.ProductData?.ProductDataCategoryId2 == category.Id);
                    XTrace.WriteLine($"444：{count}");
                    if (count == 0)
                    {
                        res.msg = GetResource($"{category.Name}的资料未提交");
                        return Json(res);
                    }
                }
            }

            var num = list.Count(e => e.Status < 2);
            if(num > 0)
            {
                res.msg = GetResource($"项目中存在未审核的资料");
                return Json(res);
            }
     
            model.AuditStatus = AuditStatus;
            model.ReAudit = 0;
            model.Update();

            var state = AuditStatus == 2 ? "审核通过" : "审核失败";

            var logs = new ProductProjectLog()
            {
                ProductProjectId = model.Id,
                DType = 4,
                Content = $"项目终审。审核结果：{state}。",
            };
            logs.Insert();

            var modelMessage = new Message()
            {
                Title = "项目资料审核结果",
                Content = $"{model.Name}的所有资料已完成审核。审核结果：{state}。",
                ReceiveUserID = model.CreateUserID,
                Status = false,
            };
            modelMessage.Insert();

            res.success = true;
            res.msg = GetResource("提交审核成功");
            return Json(res);
        }

        /// <summary>
        /// 项目申请反审
        /// </summary>
        /// <param name="ProjectId"></param>
        /// <returns></returns>
        [DisplayName("申请反审")]
        [EntityAuthorize((PermissionFlags)256)]
        public IActionResult ApplyReAudit(Int32 ProjectId)
        {
            var project = ProductProject.FindById(ProjectId);
            if (project == null)
            {
                return Content(GetResource("产品项目不存在"));
            }
            return View(project);
        }

        /// <summary>
        /// 项目申请反审
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("申请反审")]
        [EntityAuthorize((PermissionFlags)256)]
        public IActionResult ApplyReAudit(Int32 ProjectId,String Remark) 
        {
            DResult res = new();
            var model = ProductProject.FindById(ProjectId);
            if (model == null)
            {
                res.msg = GetResource("产品项目不存在");
                return Json(res);
            }
            if (model.AuditStatus == 0)
            {
                res.msg = GetResource("产品项目未审核或已反审");
                return Json(res);
            }

            //邮件通知

            model.ReAudit = 1;
            model.ReAuditRemark = Remark;
            model.Update();

            AddMessage("项目资料申请反申", $"{model.Name}的资料申请反申，反申原因：{Remark}。请尽快处理。", 512);

            var logs = new ProductProjectLog()
            {
                ProductProjectId = model.Id,
                DType = 4,
                Content = $"项目资料申请反申，反申原因：{Remark}",
            };
            logs.Insert();

            res.success = true;
            res.msg = GetResource("申请反审成功");
            return Json(res);
        }

        /// <summary>
        /// 资料申请反审
        /// </summary>
        /// <returns></returns>
        [DisplayName("申请反审")]
        [EntityAuthorize((PermissionFlags)256)]
        public IActionResult DataApplyReAudit(Int32 ProjectDataId)
        {
            var model = ProductProjectData.FindById(ProjectDataId);
            if (model == null)
            {
                return Content(GetResource("项目资料不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 资料申请反审
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("申请反审")]
        [EntityAuthorize((PermissionFlags)256)]
        public IActionResult DataApplyReAudit(Int32 ProjectDataId, String Remark)
        {
            DResult res = new();
            var model = ProductProjectData.FindById(ProjectDataId);
            if (model == null)
            {
                res.msg = GetResource("项目资料不存在");
                return Json(res);
            }
            if (model.Status != 2)
            {
                res.msg = GetResource("项目资料未审核");
                return Json(res);
            }

            //邮件通知

            model.ReAudit = 1;
            model.ReAuditRemark = Remark;
            model.Update();

            var modelProductDataCategory = ProductDataCategory.FindById(model.ProductData?.ProductDataCategoryId2 ?? 0);

            AddMessage("项目资料申请反申", $"{model.ProductProject?.Name}的 {modelProductDataCategory?.Name} 资料申请反申，反申原因：{Remark}。请尽快处理。", 512);

            var logs = new ProductProjectLog()
            {
                ProductProjectId = model.ProductProjectId,
                DType = 4,
                Content = $"项目资料 {modelProductDataCategory?.Name} 申请反申，反申原因：{Remark}",
            };
            logs.Insert();

            res.success = true;
            res.msg = GetResource("申请反审成功");
            return Json(res);
        }

        /// <summary>
        /// 项目确认反审
        /// </summary>
        /// <param name="ProjectId"></param>
        /// <returns></returns>
        [DisplayName("确认反审")]
        [EntityAuthorize((PermissionFlags)512)]
        public IActionResult ConfirmReAudit(Int32 ProjectId)
        {
            var project = ProductProject.FindById(ProjectId);
            if (project == null)
            {
                return Content(GetResource("产品项目不存在"));
            }
            return View(project);
        }

        /// <summary>
        /// 项目确认反审
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("确认反审")]
        [EntityAuthorize((PermissionFlags)512)]
        public IActionResult ConfirmReAudit(Int32 ProjectId, Int32 ReAudit)
        {
            DResult res = new();
            var model = ProductProject.FindById(ProjectId);
            if (model == null)
            {
                res.msg = GetResource("产品项目不存在");
                return Json(res);
            }
            if (model.AuditStatus == 0 || model.ReAudit != 1)
            {
                res.msg = GetResource("产品项目未审核或未申请反审");
                return Json(res);
            }

            if (ReAudit == 1)
            {
                model.AuditStatus = 1;
                model.SubmitStatus = 0;
                model.ReAudit = 2;
                model.ReAuditRemark = "";
            }
            else
            {
                model.ReAudit = 0;
            }
            model.Update();

            var logs = new ProductProjectLog()
            {
                ProductProjectId = model.Id,
                DType = 4,
                Content = $"项目资料反申申请已确认。",
            };
            logs.Insert();

            var modelMessage = new Message()
            {
                Title = "项目资料申请反申结果",
                Content = $"{model.Name}的资料反申申请已确认，请尽快完善项目资料。",
                ReceiveUserID = model.CreateUserID,
                Status = false,
            };
            modelMessage.Insert();

            res.success = true;
            res.msg = GetResource("确认反审成功");
            return Json(res);
        }

        /// <summary>
        /// 资料确认反审
        /// </summary>
        /// <returns></returns>
        [DisplayName("确认反审")]
        [EntityAuthorize((PermissionFlags)512)]
        public IActionResult DataConfirmReAudit(Int32 ProjectDataId)
        {
            var model = ProductProjectData.FindById(ProjectDataId);
            if (model == null)
            {
                return Content(GetResource("项目资料不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 资料确认反审
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("确认反审")]
        [EntityAuthorize((PermissionFlags)512)]
        public IActionResult DataConfirmReAudit(Int32 ProjectDataId, Int32 ReAudit)
        {
            DResult res = new();
            var model = ProductProjectData.FindById(ProjectDataId);
            if (model == null)
            {
                res.msg = GetResource("项目资料不存在");
                return Json(res);
            }
            if (model.Status != 2 || model.ReAudit != 1)
            {
                res.msg = GetResource("产品项目未审核或未申请反审");
                return Json(res);
            }

            var modelProductProject = ProductProject.FindById(model.ProductProjectId);
            if (modelProductProject == null)
            {
                res.msg = GetResource("产品项目不存在");
                return Json(res);
            }

            if (ReAudit == 1)
            {

                modelProductProject.AuditStatus = 1;
                modelProductProject.SubmitStatus = 0;

                modelProductProject.Update();

                model.Status = 0;
                model.ReAudit = 2;
                model.ReAuditRemark = "";
            }
            else
            {
                model.ReAudit = 0;
            }
            model.Update();

            var modelProductDataCategory = ProductDataCategory.FindById(model.ProductData?.ProductDataCategoryId2 ?? 0);

            var logs = new ProductProjectLog()
            {
                ProductProjectId = model.ProductProjectId,
                DType = 4,
                Content = $"项目 {modelProductDataCategory?.Name} 资料反申申请已确认。",
            };
            logs.Insert();

            var modelMessage = new Message()
            {
                Title = "项目资料申请反申结果",
                Content = $"{modelProductProject.Name}的 {modelProductDataCategory?.Name} 资料反申申请已确认，请尽快完善。",
                ReceiveUserID = model.CreateUserID,
                Status = false,
            };
            modelMessage.Insert();

            res.success = true;
            res.msg = GetResource("确认反审成功");
            return Json(res);
        }

        /// <summary>
        /// 配置导入
        /// </summary>
        /// <param name="ProjectId"></param>
        /// <returns></returns>
        [DisplayName("导入")]
        [EntityAuthorize((PermissionFlags)1024)]
        public IActionResult ConfigImport(Int32 ProjectId)
        {
            var project = ProductProject.FindById(ProjectId);
            if (project == null)
            {
                return Content(GetResource("产品项目不存在"));
            }
            ViewBag.ProjectList = ProductProject.FindAll(ProductProject._.Id != project.Id);
            return View(project);
        }

        /// <summary>
        /// 配置导入
        /// </summary>
        [HttpPost]
        [DisplayName("导入")]
        [EntityAuthorize((PermissionFlags)1024)]
        public IActionResult ConfigImport(Int32 ProjectId, Int32 SelectId)
        {
            DResult res = new();
            var model = ProductProject.FindById(ProjectId);
            if (model == null)
            {
                res.msg = GetResource("产品项目不存在");
                return Json(res);
            }
            var modelSelect = ProductProject.FindById(SelectId);
            if (modelSelect == null)
            {
                res.msg = GetResource("选择的产品项目不存在");
                return Json(res);
            }
            if (modelSelect.DataCategoryIds.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("选择的产品项目没有配置项");
                return Json(res);
            }
            List<String> names = new();
            foreach (var item in modelSelect.DataCategoryIds.SplitAsInt())
            {
                var modelDataCategory = ProductDataCategory.FindById(item);
                if (modelDataCategory == null) continue;
                if (model.DataCategoryIds.IsNullOrWhiteSpace())
                {
                    model.DataCategoryIds = "," + modelDataCategory.Id + ",";
                    names.Add(modelDataCategory.Name);
                }
                else if (!model.DataCategoryIds.Contains($",{modelDataCategory.Id},"))
                {
                    model.DataCategoryIds = model.DataCategoryIds + modelDataCategory.Id + ",";
                    names.Add(modelDataCategory.Name);
                }
            }
            model.Update();

            if(names.Count > 0)
            {
                var logs = new ProductProjectLog()
                {
                    ProductProjectId = model.Id,
                    DType = 0,
                    Content = $"导入了项目配置项：{String.Join(",", names)}",
                };
                logs.Insert();
            }
            res.success = true;
            res.msg = GetResource("导入成功");
            return Json(res);
        }

        /// <summary>
        /// 资料导入
        /// </summary>
        /// <param name="ProjectId"></param>
        /// <returns></returns>
        [DisplayName("导入")]
        [EntityAuthorize((PermissionFlags)1024)]
        public IActionResult DataInfoImport(Int32 ProjectId)
        {
            var project = ProductProject.FindById(ProjectId);
            if (project == null)
            {
                return Content(GetResource("产品项目不存在"));
            }
            return View(project);
        }

        /// <summary>
        /// 搜索产品项目
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="page"></param>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult SearchProduct(String keyword, Int32 page)
        {
            var res = new DResult();

            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = 10,
                RetrieveTotalCount = true,
                Sort = ProductProject._.Id,
            };

            res.data = ProductProject.Search(-1, null, DateTime.MinValue, DateTime.MinValue, keyword, pages).Select(e =>
            {
                return new
                {
                    name = e.Name,
                    value = e.Id,
                };
            });

            res.extdata = new { pages.PageCount };
            res.success = true;
            return Json(res);
        }

        /// <summary>
        /// 资料导入
        /// </summary>
        [HttpPost]
        [DisplayName("导入")]
        [EntityAuthorize((PermissionFlags)1024)]
        public IActionResult DataInfoImport(Int32 ProjectId, Int32 SelectId)
        {
            DResult res = new();
            var model = ProductProject.FindById(ProjectId);
            if (model == null)
            {
                res.msg = GetResource("产品项目不存在");
                return Json(res);
            }
            var modelSelect = ProductProject.FindById(SelectId);
            if (modelSelect == null)
            {
                res.msg = GetResource("选择的产品项目不存在");
                return Json(res);
            }
            var listSelectData = ProductProjectData.FindAllByProductProjectId(modelSelect.Id);
            if (listSelectData.Count == 0)
            {
                res.msg = GetResource("选择的产品项目没有资料");
                return Json(res);
            }
            if (model.DataCategoryIds.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("请先添加配置项");
                return Json(res);
            }
            foreach (var item in listSelectData)
            {
                if(item.ProductData != null)
                {
                    var count = ProductProjectData.FindCount(model.Id, item.ProductData.ProductDataCategoryId2, item.ProductData.Ptype);
                    if(count == 0 && model.DataCategoryIds.Contains($",{item.ProductData.ProductDataCategoryId2},"))
                    {
                        var modelNew = new ProductProjectData()
                        { 
                            ProductProjectId = model.Id,
                            ProductDataId = item.ProductDataId,
                            Status = 0,
                        };
                        modelNew.Insert();
                    }
                }
            }
            res.success = true;
            res.msg = GetResource("导入成功");
            return Json(res);
        }

        /// <summary>
        /// 产品项目日志
        /// </summary>
        /// <returns></returns>
        [DisplayName("产品项目日志")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult Logs(Int32 ProjectId)
        {
            var project = ProductProject.FindById(ProjectId);
            if (project == null)
            {
                return Content(GetResource("产品项目不存在"));
            }
            return View(project);
        }

        /// <summary>
        /// 产品项目日志
        /// </summary>
        /// <returns></returns>
        [DisplayName("产品项目日志")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult LogsList(Int32 ProjectId, Int32 page,Int32 limit)
        {
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "Id",
                Desc = true,
            };
            var data = ProductProjectLog.Search(ProjectId, DateTime.MinValue, DateTime.MinValue, "", pages);
            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
        }

        /// <summary>
        /// 给有指定权限的用户推送消息
        /// </summary>
        /// <param name="Title"></param>
        /// <param name="Content"></param>
        /// <param name="flags"></param>
        private void AddMessage(String Title,String Content,Int32 flags)
        {
            var modelMenu = ManageProvider.Menu.FindByFullName("HlktechIoT.Areas.Production.Controllers.ProductProjectController");
            var listRole = Role.FindAll(Role._.Permission.Contains($"{modelMenu.ID}#"));
            PermissionFlags needPermissionFlags = (PermissionFlags)flags;
            //所有要推送的用户
            List<Int32> UIds = new();
            foreach (var item in listRole)
            {
                if (!item.Permission.IsNullOrWhiteSpace())
                {
                    var listPermission = item.Permission.Split(",");
                    var modelPermission = listPermission.FirstOrDefault(e => e.Contains($"{modelMenu.ID}#"));
                    if (modelPermission != null)
                    {
                        var perm = modelPermission.Split("#")[1].ToInt();
                        if (perm == -1)
                        {
                            var ids = UserEx.FindAllByRoleID(item.ID).Select(e => e.ID).ToList();
                            UIds.AddRange(ids);
                        }
                        else if (perm > 0)
                        {
                            var permissionFlags = (PermissionFlags)perm;
                            var result = permissionFlags.HasFlag(needPermissionFlags);
                            if (result)
                            {
                                var ids = UserEx.FindAllByRoleID(item.ID).Select(e => e.ID).ToList();
                                UIds.AddRange(ids);
                            }
                        }
                    }
                }
            }

            try
            {
                foreach (var item in UIds)
                {
                    var modelMessage = new Message()
                    {
                        Title = Title,
                        Content = Content,
                        ReceiveUserID = item,
                        Status = false,
                    };
                    modelMessage.Insert();
                }
            }
            catch (Exception ex)
            {
                XTrace.WriteLine($"消息推送异常：{ex.SafeString()}");
            }
        }
    }
}


