﻿.textcenter {
    text-align: center;
}

.divider,
.divider-vertical {
    margin: 0 8px;
    display: inline-block;
    height: .9em;
    width: 1px;
    vertical-align: middle;
    position: relative;
    top: -.06em;
}

.divider {
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1", Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #515a6e;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    list-style: none;
    background: #e8eaec;
}

.dg-form {
    background-color: #fff;
    padding: 10px 10px 1px 10px;
    margin-bottom: 10px;
}

.dgtable-body {
    background-color: #fff;
    margin-top: 10px;
    padding: 0 10px;
    /*border: 1px solid #e6e6e6;*/
}

.dg-form .layui-input,
.dg-form .layui-select,
.dg-form .layui-textarea {
    height: 30px;
    line-height: 1.3;
    line-height: 30px\9;
    border-width: 1px;
    border-style: solid;
    background-color: #fff;
    border-radius: 2px;
}

.dg-form .layui-form-label {
    float: left;
    display: block;
    padding: 5px 2px;
    width: 70px;
    font-weight: 400;
    line-height: 20px;
    text-align: right;
}

.layui-nav>.manage {
    margin-right: 10px;
}

/* 右侧面板 */
.layui-layer-adminRight {
    /* top: 50px !important; */
    top: 0px !important;
    bottom: 0;
    box-shadow: 1px 1px 10px rgba(0, 0, 0, .1);
    border-radius: 0;
    overflow: auto;
}

.layadmin-iframe {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
}

.layui-layer-title {
    background-color: #F8F8F8;
}

.layui-table-tool {
    margin-bottom: 10px;
    border-width: 0 0 1px !important;
    box-shadow: 0 2px 8px rgb(0 0 0 / 8%) !important;
}

/* 全局所有滚动条加宽 */
.layui-table-view ::-webkit-scrollbar {
    /* width: 16px; */
    /* 纵向滚动条宽度 */
    height: 10px;
    /* 横向滚动条高度 */
}

.layui-table-view ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 8px;
}

.layui-table-view ::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 8px;
}

/* 兼容 Firefox */
.layui-table-view {
    scrollbar-width: auto;
    scrollbar-color: #c1c1c1 #f0f0f0;
}

/* 全局所有滚动条加宽 */