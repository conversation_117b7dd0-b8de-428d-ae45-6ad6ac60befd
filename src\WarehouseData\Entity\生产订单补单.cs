﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>生产订单补单</summary>
[Serializable]
[DataObject]
[Description("生产订单补单")]
[BindIndex("IX_DH_ProductSupplementalOrders_OrderId", false, "OrderId")]
[BindIndex("IX_DH_ProductSupplementalOrders_ProductTypeId", false, "ProductTypeId")]
[BindIndex("IX_DH_ProductSupplementalOrders_Status", false, "Status")]
[BindTable("DH_ProductSupplementalOrders", Description = "生产订单补单", ConnName = "DH", DbType = DatabaseType.None)]
public partial class ProductSupplementalOrders : IProductSupplementalOrders, IEntity<IProductSupplementalOrders>
{
    #region 属性
    private Int64 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, false, false, 0)]
    [BindColumn("Id", "编号", "", DataScale = "time")]
    public Int64 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String _OrderId = null!;
    /// <summary>订单号</summary>
    [DisplayName("订单号")]
    [Description("订单号")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("OrderId", "订单号", "", Master = true)]
    public String OrderId { get => _OrderId; set { if (OnPropertyChanging("OrderId", value)) { _OrderId = value; OnPropertyChanged("OrderId"); } } }

    private Int32 _ProductTypeId;
    /// <summary>产品型号编号</summary>
    [DisplayName("产品型号编号")]
    [Description("产品型号编号")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ProductTypeId", "产品型号编号", "")]
    public Int32 ProductTypeId { get => _ProductTypeId; set { if (OnPropertyChanging("ProductTypeId", value)) { _ProductTypeId = value; OnPropertyChanged("ProductTypeId"); } } }

    private Int32 _Quantity;
    /// <summary>数量</summary>
    [DisplayName("数量")]
    [Description("数量")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Quantity", "数量", "")]
    public Int32 Quantity { get => _Quantity; set { if (OnPropertyChanging("Quantity", value)) { _Quantity = value; OnPropertyChanged("Quantity"); } } }

    private String? _Reason;
    /// <summary>补单原因</summary>
    [DisplayName("补单原因")]
    [Description("补单原因")]
    [DataObjectField(false, false, true, 500)]
    [BindColumn("Reason", "补单原因", "")]
    public String? Reason { get => _Reason; set { if (OnPropertyChanging("Reason", value)) { _Reason = value; OnPropertyChanged("Reason"); } } }

    private Int32 _Status;
    /// <summary>审核状态 0待审核 1审核中 2已审核 3审核失败</summary>
    [DisplayName("审核状态0待审核1审核中2已审核3审核失败")]
    [Description("审核状态 0待审核 1审核中 2已审核 3审核失败")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Status", "审核状态 0待审核 1审核中 2已审核 3审核失败", "")]
    public Int32 Status { get => _Status; set { if (OnPropertyChanging("Status", value)) { _Status = value; OnPropertyChanged("Status"); } } }

    private String? _Remark;
    /// <summary>审核备注</summary>
    [DisplayName("审核备注")]
    [Description("审核备注")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("Remark", "审核备注", "")]
    public String? Remark { get => _Remark; set { if (OnPropertyChanging("Remark", value)) { _Remark = value; OnPropertyChanged("Remark"); } } }

    private DateTime _AuditTime;
    /// <summary>审核时间</summary>
    [DisplayName("审核时间")]
    [Description("审核时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("AuditTime", "审核时间", "")]
    public DateTime AuditTime { get => _AuditTime; set { if (OnPropertyChanging("AuditTime", value)) { _AuditTime = value; OnPropertyChanged("AuditTime"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductSupplementalOrders model)
    {
        Id = model.Id;
        OrderId = model.OrderId;
        ProductTypeId = model.ProductTypeId;
        Quantity = model.Quantity;
        Reason = model.Reason;
        Status = model.Status;
        Remark = model.Remark;
        AuditTime = model.AuditTime;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "OrderId" => _OrderId,
            "ProductTypeId" => _ProductTypeId,
            "Quantity" => _Quantity,
            "Reason" => _Reason,
            "Status" => _Status,
            "Remark" => _Remark,
            "AuditTime" => _AuditTime,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToLong(); break;
                case "OrderId": _OrderId = Convert.ToString(value); break;
                case "ProductTypeId": _ProductTypeId = value.ToInt(); break;
                case "Quantity": _Quantity = value.ToInt(); break;
                case "Reason": _Reason = Convert.ToString(value); break;
                case "Status": _Status = value.ToInt(); break;
                case "Remark": _Remark = Convert.ToString(value); break;
                case "AuditTime": _AuditTime = value.ToDateTime(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static ProductSupplementalOrders? FindById(Int64 id)
    {
        if (id < 0) return null;

        return Find(_.Id == id);
    }

    /// <summary>根据订单号查找</summary>
    /// <param name="orderId">订单号</param>
    /// <returns>实体列表</returns>
    public static IList<ProductSupplementalOrders> FindAllByOrderId(String orderId)
    {
        if (orderId.IsNullOrEmpty()) return [];

        return FindAll(_.OrderId == orderId);
    }

    /// <summary>根据产品型号编号查找</summary>
    /// <param name="productTypeId">产品型号编号</param>
    /// <returns>实体列表</returns>
    public static IList<ProductSupplementalOrders> FindAllByProductTypeId(Int32 productTypeId)
    {
        if (productTypeId < 0) return [];

        return FindAll(_.ProductTypeId == productTypeId);
    }

    /// <summary>根据审核状态0待审核1审核中2已审核3审核失败查找</summary>
    /// <param name="status">审核状态0待审核1审核中2已审核3审核失败</param>
    /// <returns>实体列表</returns>
    public static IList<ProductSupplementalOrders> FindAllByStatus(Int32 status)
    {
        if (status < 0) return [];

        return FindAll(_.Status == status);
    }
    #endregion

    #region 数据清理
    /// <summary>清理指定时间段内的数据</summary>
    /// <param name="start">开始时间。未指定时清理小于指定时间的所有数据</param>
    /// <param name="end">结束时间</param>
    /// <returns>清理行数</returns>
    public static Int32 DeleteWith(DateTime start, DateTime end)
    {
        return Delete(_.Id.Between(start, end, Meta.Factory.Snow));
    }
    #endregion

    #region 字段名
    /// <summary>取得生产订单补单字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>订单号</summary>
        public static readonly Field OrderId = FindByName("OrderId");

        /// <summary>产品型号编号</summary>
        public static readonly Field ProductTypeId = FindByName("ProductTypeId");

        /// <summary>数量</summary>
        public static readonly Field Quantity = FindByName("Quantity");

        /// <summary>补单原因</summary>
        public static readonly Field Reason = FindByName("Reason");

        /// <summary>审核状态 0待审核 1审核中 2已审核 3审核失败</summary>
        public static readonly Field Status = FindByName("Status");

        /// <summary>审核备注</summary>
        public static readonly Field Remark = FindByName("Remark");

        /// <summary>审核时间</summary>
        public static readonly Field AuditTime = FindByName("AuditTime");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得生产订单补单字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>订单号</summary>
        public const String OrderId = "OrderId";

        /// <summary>产品型号编号</summary>
        public const String ProductTypeId = "ProductTypeId";

        /// <summary>数量</summary>
        public const String Quantity = "Quantity";

        /// <summary>补单原因</summary>
        public const String Reason = "Reason";

        /// <summary>审核状态 0待审核 1审核中 2已审核 3审核失败</summary>
        public const String Status = "Status";

        /// <summary>审核备注</summary>
        public const String Remark = "Remark";

        /// <summary>审核时间</summary>
        public const String AuditTime = "AuditTime";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
