let options = {
    elem: '#taskTable',
    id:'taskTable',
    title:'全部待生产数据',
    cols: [[
       
      { field: 'OrderId', title: '订单编号', width: '12%' }
      , { field: 'OrderingUser', title: '打单人', width: '8%' }
      , {
          title: '打单时间', minWidth: '8%', templet: (d) => {
              if (d.OrderingTime != undefined && d.OrderingTime[0] != 0) {
                  return `<div>${d.OrderingTime}</div>`
              }
              return `<div></div>`
          }
      }
      , { field: 'IsDuplicate', title: '重复打单', width: '4%',align:'center'}
      , { field: 'PickingUser', title: '领料人', width: '8%' }
      , {
          title: '领料时间', minWidth: '8%', templet: (d) => {
              if (d.PickingTime != undefined && d.PickingTime[0] != 0) {
                  return `<div>${d.PickingTime}</div>`
              }
              return `<div></div>`
          }
      }
      , { field: 'ProductionUser', title: '生产者', width: '5%'}
      , {
          title: '生产时间', minWidth: '8%', templet: (d) => {
              if (d.ProductionTime != undefined && d.ProductionTime[0] != 0) {
                  return `<div>${d.ProductionTime}</div>`
              }
              return `<div></div>`
          }
      }
      , { field: 'AuditingUser', title: '审核人', width: '5%'}
      , {
          title: '审核时间', minWidth: '8%', templet: (d) => {
              if (d.AuditingTime != undefined && d.AuditingTime[0] != 0) {
                  return `<div>${d.AuditingTime}</div>`
              }
              return `<div></div>`
          }
      }
      , { field: 'PackUser', title: '打包人', width: '5%' }
      , { title: '打包时间', minWidth: '8%',templet:(d)=>{
          if (d.PackTime != undefined && d.PackTime[0] != 0) {
              return `<div>${d.PackTime}</div>`
          }
          return `<div></div>`
      } }
    ]],
    api: 'ToBeProduced', //接口
    url:'/Api/V1/Order/ToBeProduced',
    headers: {
      "content-type":"application/x-www-form-urlencoded",
      Lng: '1dsadasjio',
      Id: Date.now(),
    },
    method:'post',
    data: [{OrderId: '无数据',}]
    , page: true //开启分页
    ,cellMinWidth: 80
    ,skin:'line'
    ,
    totalRow:true,
    limit: 20, // 每页默认显示的数量
    height: (()=>{
      let tableHeight = layui.$('div.item_center').height()
      // console.log('打印',tableHeight);
      return ((tableHeight)  + 'px')
    })(),
    error: function(e, msg) {
        console.log('调用错误：',e, msg)
        window.location.reload();
    },
    }
  // 导出js
  export default options
  