﻿using NewLife.Configuration;

using System.ComponentModel;

namespace HlktechIoT.Common;

/// <summary>设置</summary>
[DisplayName("设置")]
[Config("Pek")]
public class PekSettings : Config<PekSettings> {
    /// <summary>
    /// 允许获取SN和MAC地址。主要用于控制是否允许获取设备的序列号(SN)和MAC地址。
    /// </summary>
    [Description("允许获取SN和MAC地址。主要用于控制是否允许获取设备的序列号(SN)和MAC地址。")]
    public Boolean AllowGetSNMac { get; set; } = true;

    /// <summary>
    /// SN回收超时时间（分钟）。用于控制SN被消费后未验证的回收阈值。
    /// </summary>
    [Description("SN回收超时时间（分钟）。用于控制SN被消费后未验证的回收阈值。")]
    public Int32 SNRecycleTimeoutMinutes { get; set; } = 10;
}
