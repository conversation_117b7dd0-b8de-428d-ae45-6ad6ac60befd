﻿namespace HlktechIoT.Dto;

public class DTreeDto {
    public DTreeStatus status { get; set; } = new DTreeStatus();

    public IList<DTreeItem> data { get; set; } = new List<DTreeItem>();
}

public class DTreeStatus {
    public Int32 code { get; set; }

    public String? message { get; set; }
}

public class DTreeItem {
    public Int32 id { get; set; }

    public String? title { get; set; }

    public Boolean last { get; set; }

    public Int32 parentId { get; set; }

    public IList<DTreeItem>? children { get; set; } = new List<DTreeItem>();
}