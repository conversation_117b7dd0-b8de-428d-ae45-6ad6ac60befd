﻿@{
    Html.AppendTitleParts(T("添加工具版本").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
</style>
<div class="containers">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("工具标识")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Code" placeholder="@T("请输入工具标识")" autocomplete="off" class="layui-input" value="">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("版本号")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Version" placeholder="@T("请输入版本号")" autocomplete="off" class="layui-input" value="">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("文件名")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="FileName" id="FileName" placeholder="@T("请输入文件名")" autocomplete="off" class="layui-input" value="">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width">
                <span>*</span>@T("文件")
            </label>
            <div class="layui-input-inline">
                <div class="layui-upload-drag" id="upload" style="width:258px">
                    <i class="layui-icon"></i>
                    <p>@T("点击上传，或将文件拖拽到此处")</p>
                    <div class="layui-hide" id="uploadDemoView">
                        <hr>
                        <label id="excel" class="layui-form-label-left" style="word-break: break-all;white-space: normal; display: block;"></label>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label-width"><span>*</span>@T("升级内容")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <textarea placeholder="@T("请输入内容")" name="Content" id="" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">@T("是否强制升级")</label>
                <div class="layui-input-inline">
                    <input type="checkbox" name="IsQiangZhi" lay-filter="" lay-skin="switch">
                </div>
             </div>
        </div>
        <div class="layui-form-item btn">
            <input hidden name="FilePath" id="FilePath"/>
            <input hidden name="FileSize" id="FileSize"/>
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var upload = layui.upload;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        //拖拽阈值表上传
        upload.render({
            elem: '#upload'
            , url: '@Url.Action("UploadFiles")' //改成您自己的上传接口
            , done: function (res) {
                if (!res.success) { //失败打印
                    os.warning(res.msg);
                    return;
                }
                os.success('@T("上传成功")');

                layui.$('#uploadDemoView').removeClass('layui-hide');
                $("#excel").text(res.data.OriginFileName);
                $("#FilePath").val(res.data.FilePath);
                $("#FileSize").val(res.data.Size);
            },
            before: function () {

            }
            , accept: 'file' //允许上传的文件类型

        });

        form.on('submit(Submit)', function (data) {

            var waitIndex = parent.layer.load(2);

            data.field.IsQiangZhi = data.field.IsQiangZhi == 'on';

            abp.ajax({
                url: "@Url.Action("Add")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>