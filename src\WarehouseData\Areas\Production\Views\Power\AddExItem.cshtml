﻿@{
    Html.AppendTitleParts(T("新增型号配置").Text);
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }
</style>
<script asp-location="Head">
    var closeThis = '@T("关 闭 当 前")';
    var closeOther = '@T("关 闭 其 他")';
    var closeAll = '@T("关 闭 全 部")';

    var jsMenuStyle = "@T("菜单风格")";
    var jsTopStyle = "@T("顶部风格")";
    var jsMenu = "@T("菜单")";
    var jsView = "@T("视图")";
    var jsBanner = "@T("通栏")";
    var jsThroughColor = "@T("通色")";
    var jsFooter = "@T("页脚")";
    var jsMoreSettings = "@T("更多设置")";
    var jsOpen = "@T("开")";
    var jsClose = "@T("关")";
    var jsThemeColor = "@T("主题配色")";
    var layuiNoData = '@T("无数据")';
    var layuiAsc = "@T("升序")";
    var layuiDesc = "@T("降序")";

    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
</script>
<form class="layui-form" lay-filter="organization-form" style="padding: 10px 0 0 0;">
    <input type="hidden" name="Id" value="@ViewBag.Id" />
    <div class="layui-form-item">
        <label class="layui-form-label">@T("名称")</label>
        <div class="layui-input-block">
            <input type="text" name="Name" lay-verify="required" lay-verType="tips" placeholder="@T("请输入名称")" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">@T("编号")</label>
        <div class="layui-input-block">
            <input type="text" name="Code" lay-verify="required" lay-verType="tips" placeholder="@T("请输入编号")" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">@T("单位")</label>
        <div class="layui-input-block">
            <input type="text" name="Unit" lay-verType="tips" placeholder="@T("请输入单位")" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">@T("备注")</label>
        <div class="layui-input-block">
            <input type="text" name="Remark" lay-verType="tips" placeholder="@T("请输入备注")" autocomplete="off" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item layui-hide">
        <div class="layui-input-block">
            <button class="layui-btn layui-hide" lay-submit lay-filter="organization-submit" id="organization-submit">@T("提交")</button>
        </div>
    </div>
</form>
<script asp-location="Footer">
    layui.use(['abp', 'form'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        window.submitForm = function () {
            $("#organization-submit").click();
        }

        form.on('submit(organization-submit)', function (data) {
            var field = data.field;

            var waitIndex = parent.layer.load(2);
            abp.ajax({
                url: "@Url.Action("AddExItem")",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }

                data.index = index;

                parent.saveCallback(data);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });
    });
</script>