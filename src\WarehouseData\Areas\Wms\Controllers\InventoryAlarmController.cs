﻿using System.ComponentModel;

using DG.Web.Framework;

using DH.Models;

using HlktechIoT.Dto.Export;
using HlktechIoT.Entity;

using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;

using NewLife;
using NewLife.Data;

using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

using Pek.Models;

using XCode.Membership;

using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Wms.Controllers;

/// <summary>库存告警</summary>
[DisplayName("库存告警")]
[Description("库存告警管理")]
[WmsArea]
[DHMenu(85,ParentMenuName = "OrdersManager", ParentMenuDisplayName = "订单管理", ParentMenuUrl = "", ParentMenuOrder = 50, ParentIcon = "layui-icon-component", CurrentMenuUrl = "~/{area}/InventoryAlarm", CurrentMenuName = "InventoryAlarmList", LastUpdate = "20241120")]
public class InventoryAlarmController : BaseAdminControllerX
{
    /// <summary>菜单顺序。扫描时会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 85;

    private readonly IMemoryCache _memoryCache;

    public InventoryAlarmController(IMemoryCache memoryCache)
    {
        _memoryCache = memoryCache;
    }

    /// <summary>
    /// 库存告警列表视图
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("库存告警列表")]
    public IActionResult Index()
    {
        return View();
    }

    [HttpPost]
    [DisplayName("导入库存告警")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Import(IFormFile file)
    {
        if (file == null || file.Length == 0)
        {
            return Json(new DResult { success = false, msg = "请选择要导入的文件" });
        }

        var list = new List<InventoryAlarmImport>();

        try
        {
            using (var stream = new MemoryStream())
            {
                file.CopyTo(stream);
                stream.Position = 0;
                IWorkbook workbook;

                if (Path.GetExtension(file.FileName).Equals(".xls", StringComparison.OrdinalIgnoreCase))
                {
                    workbook = new HSSFWorkbook(stream);
                }
                else
                {
                    workbook = new XSSFWorkbook(stream);
                }

                ISheet sheet = workbook.GetSheetAt(0);
                if (sheet == null)
                {
                    return Json(new DResult { success = false, msg = "文件内容为空" });
                }

                // 获取表头
                var headerRow = sheet.GetRow(0);
                int cellCount = headerRow.LastCellNum;

                // 建立列名与索引的映射
                var headerDict = new Dictionary<string, int>();
                for (int i = 0; i < cellCount; i++)
                {
                    var cell = headerRow.GetCell(i);
                    if (cell != null && !string.IsNullOrWhiteSpace(cell.ToString()))
                    {
                        headerDict[cell.ToString().Trim()] = i;
                    }
                }

                // 读取数据行
                for (int i = 1; i <= sheet.LastRowNum; i++)
                {
                    var row = sheet.GetRow(i);
                    if (row == null) continue;

                    var item = new InventoryAlarmImport();
                    try
                    {
                        item.MaterialNumber = row.GetCell(headerDict["物料编号"])?.ToString();
                        item.MaterialName = row.GetCell(headerDict["物料名称"])?.ToString();
                        item.Category = row.GetCell(headerDict["种类"])?.ToString();
                        item.SpecificationModel = row.GetCell(headerDict["规格型号"])?.ToString();
                        item.Unit = row.GetCell(headerDict["单位"])?.ToString();
                        item.WarehouseNumber = row.GetCell(headerDict["仓库编号"])?.ToString();
                        item.WarehouseName = row.GetCell(headerDict["仓库名称"])?.ToString();
                        item.AvailableQuantity = int.Parse(row.GetCell(headerDict["可用数"])?.ToString() ?? "0");

                        list.Add(item);
                    }
                    catch (Exception ex)
                    {
                        return Json(new DResult { success = false, msg = $"第{i + 1}行数据解析错误：{ex.Message}" });
                    }
                }
            }
        }
        catch (Exception ex)
        {
            return Json(new DResult { success = false, msg = $"导入失败：{ex.Message}" });
        }

        if (list.Count == 0)
        {
            return Json(new DResult { success = false, msg = "导入失败，文件内容有错误或为空" });
        }
        
        foreach (var item in list)
        {
            var availableQuantity = item.AvailableQuantity;

            var entity = InventoryAlarm.FindByMaterialNumberAndSpecificationModelAndWarehouseNumber(
                item.MaterialNumber,
                item.SpecificationModel,
                item.WarehouseNumber
            );

            if (entity == null)
            {
                entity = new InventoryAlarm
                {
                    MaterialNumber = item.MaterialNumber,
                    MaterialName = item.MaterialName,
                    Category = item.Category,
                    SpecificationModel = item.SpecificationModel,
                    Unit = item.Unit,
                    WarehouseNumber = item.WarehouseNumber,
                    WarehouseName = item.WarehouseName,
                    AlarmQuantity = 0,
                };
            }
            else
            {
                entity.MaterialName = item.MaterialName;
                entity.Category = item.Category;
                entity.Unit = item.Unit;
                entity.WarehouseName = item.WarehouseName;
            }

            entity.Save();

            _memoryCache.Set($"AvailableQuantity:{entity.Id}", availableQuantity, TimeSpan.FromDays(1));
        }

        var data = InventoryAlarm.FindAllByKeysOrIds("", "", "", "", DateTime.MinValue, DateTime.MaxValue);

        var showList = data
            .Where(e =>
            {
                var availableQuantityKey = $"AvailableQuantity:{e.Id}";
                var isKeyExists = _memoryCache.TryGetValue(availableQuantityKey, out int availableQuantity);
                return isKeyExists && (availableQuantity - e.AlarmQuantity <= 0);
            })
            .Select(e => new InventoryAlarmExport()
            {
                MaterialNumber = e.MaterialNumber,
                MaterialName = e.MaterialName,
                Category = e.Category,
                SpecificationModel = e.SpecificationModel,
                Unit = e.Unit,
                WarehouseNumber = e.WarehouseNumber,
                WarehouseName = e.WarehouseName,
                AlarmQuantity = e.AlarmQuantity,
                AvailableQuantity = _memoryCache.Get<int>($"AvailableQuantity:{e.Id}"),
                CreateTime = e.CreateTime,
                UpdateTime = e.UpdateTime
            }).ToList();

        return Json(new DResult { success = true, msg = "导入成功", data = showList });
    }

    /// <summary>
    /// 获取库存告警列表数据
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("获取库存告警列表")]
    public IActionResult GetList(String materialNumber, string specificationModel, string warehouseNumber, DateTime start, DateTime end, Int32 page = 1, Int32 limit = 10)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = InventoryAlarm._.Id,
        };

        var list = InventoryAlarm.Search(materialNumber, specificationModel, warehouseNumber, start, end, "", pages);

        var data = list.Select(x => new
        {
            x.Id,
            x.MaterialNumber,
            x.MaterialName,
            x.Category,
            x.SpecificationModel,
            x.Unit,
            x.WarehouseNumber,
            x.WarehouseName,
            x.AlarmQuantity,
            x.CreateTime,
            x.UpdateTime
        });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 编辑库存告警信息
    /// </summary>
    /// <param name="id">库存告警Id</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("编辑库存告警")]
    public IActionResult Edit(Int32 id = 0)
    {
        var model = InventoryAlarm.FindById(id);
        if (model == null)
        {
            return NotFound("库存告警不存在");
        }

        return View(model);
    }

    /// <summary>
    /// 保存库存告警信息
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("保存库存告警")]
    public IActionResult Edit(Int32 id, Int32 AlarmQuantity)
    {
        var entity = InventoryAlarm.FindById(id);
        if (entity == null) return Json(new DResult()
        {
            success = false,
            msg = GetResource("数据不存在")
        });

        entity.AlarmQuantity = AlarmQuantity;

        entity.Save();

        return Json(new DResult() { success = true, msg = GetResource("设置成功") });
    }

    /// <summary>
    /// 删除库存告警
    /// </summary>
    /// <param name="id">库存告警Id</param>
    /// <returns></returns>
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除库存告警")]
    public IActionResult Delete(Int32 id)
    {
        var model = InventoryAlarm.FindById(id);
        if (model == null)
        {
            return Json(new DResult() { success = true, msg = GetResource("库存告警不存在") });
        }

        model.Delete();

        return Json(new DResult() { success = true, msg = GetResource("删除成功") });
    }

    /// <summary>
    /// 批量删除库存告警
    /// </summary>
    /// <param name="ids">库存告警Id列表</param>
    /// <returns></returns>
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("批量删除库存告警")]
    public IActionResult DeleteMultiple(string idss)
    {
        var ids = idss.SplitAsInt(",");
        if (ids == null || !ids.Any())
        {
            return Json(new DResult { success = false, msg = GetResource("请选择要删除的库存告警") });
        }

        foreach (var id in ids)
        {
            var model = InventoryAlarm.FindById(id);
            if (model != null)
            {
                model.Delete();
            }
        }

        return Json(new DResult { success = true, msg = GetResource("批量删除成功") });
    }

    /// <summary>
    /// 导出库存告警列表
    /// </summary>
    /// <returns></returns>
    [DisplayName("导出库存告警")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public async Task<IActionResult> ExportAll(String materialNumber, string specificationModel, string warehouseNumber, String ids, DateTime start, DateTime end)
    {
        IExporter exporter = new ExcelExporter();
        List<InventoryAlarmExport>? list = null;

        var data = InventoryAlarm.FindAllByKeysOrIds(materialNumber, specificationModel, warehouseNumber, ids, start, end);

        var allKeysMissing = data.All(e => !_memoryCache.TryGetValue($"AvailableQuantity:{e.Id}", out _));

        if (allKeysMissing)
        {
            return Json(new DResult { success = false, msg = GetResource("库存数据没有缓存，请重新导入") });
        }

        list = data
            .Where(e =>
            {
                var availableQuantityKey = $"AvailableQuantity:{e.Id}";
                var isKeyExists = _memoryCache.TryGetValue(availableQuantityKey, out int availableQuantity);
                return isKeyExists && (availableQuantity - e.AlarmQuantity <= 0);
            })
            .Select(e => new InventoryAlarmExport()
            {
                MaterialNumber = e.MaterialNumber,
                MaterialName = e.MaterialName,
                Category = e.Category,
                SpecificationModel = e.SpecificationModel,
                Unit = e.Unit,
                WarehouseNumber = e.WarehouseNumber,
                WarehouseName = e.WarehouseName,
                AlarmQuantity = e.AlarmQuantity,
                AvailableQuantity = _memoryCache.Get<int>($"AvailableQuantity:{e.Id}"),
                CreateTime = e.CreateTime,
                UpdateTime = e.UpdateTime
            }).ToList();

        var result = await exporter.ExportAsByteArray(list);
        return File(result, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"InventoryAlarm_{DateTime.Now:yyyyMMddHHmm}.xlsx");
    }
}
