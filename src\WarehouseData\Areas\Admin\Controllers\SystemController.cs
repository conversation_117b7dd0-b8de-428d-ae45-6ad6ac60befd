﻿using DG.Web.Framework;

using DH.Core.Domain.Localization;
using DH.Core.Infrastructure;
using DH.Entity;
using DH.Models;

using Microsoft.AspNetCore.Mvc;
using Pek.Models;
using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>我的设置</summary>
[DisplayName("我的设置")]
[Description("个人基本数据的管理")]
[AdminArea]
[DHMenu(40,ParentMenuName = "System", ParentMenuDisplayName = "控制面板", ParentMenuUrl = "", ParentMenuOrder = 10, ParentIcon = "layui-icon-set-fill", CurrentMenuUrl = "", CurrentMenuName = "SystemSetting", LastUpdate = "20240124")]
public class SystemController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 40;
}

/// <summary>基本资料</summary>
[DisplayName("基本资料")]
[Description("个人基本资料修改")]
[AdminArea]
[DHMenu(100,ParentMenuName = "SystemSetting", ParentMenuDisplayName = "我的设置", CurrentMenuUrl = "~/{area}/UserInfo", CurrentMenuName = "UserInfo", LastUpdate = "20240124")]
public class UserInfoController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 100;

    /// <summary>
    /// 基本资料编辑
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("基本资料")]
    [HttpGet]
    public IActionResult Index()
    {
        var model = ManageProvider.User;

        var RoleName = model.RoleName;

        var localizationSettings = LocalizationSettings.Current;

        if (localizationSettings.IsEnable)
        {
            var RoleLanInfo = RoleLan.FindByRIdAndLId(model.RoleID, WorkingLanguage.Id, true);
            RoleName = RoleLanInfo.Name;
        }

        //var list = Role.FindAllWithCache().OrderBy(x => x.ID);
        //ViewBag.RoleList = list.Select(item => new SelectListItem { Value = item.ID.ToString(), Text = item.Name, Selected = item.ID == model.RoleID });

        ViewBag.RoleName = RoleName;

        return View(model);
    }

    /// <summary>
    /// 基本资料提交修改
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("基本资料")]
    [HttpPost]
    public IActionResult UpdateInfo(String DisplayName, Int32 Sex, String Mobile, String Mail)
    {
        var model = ManageProvider.User;
        model.DisplayName = DisplayName;
        model.Sex = Sex == 0 ? SexKinds.未知 : (Sex == 1 ? SexKinds.男 : SexKinds.女);

        var m = UserE.FindByMobile(Mobile);
        if (m != null && m.ID != model.ID)
        {
            return Json(new DResult { success = false, msg = GetResource("手机号码已被使用") });
        }
        model.Mobile = Mobile;

        var ma = UserE.FindByMail(Mail);
        if (ma != null && ma.ID != model.ID)
        {
            return Json(new DResult { success = false, msg = GetResource("邮箱已被使用") });
        }
        model.Mail = Mail;
        model.Save();

        return Json(new DResult { success = true, msg = GetResource("修改成功") });
    }
}