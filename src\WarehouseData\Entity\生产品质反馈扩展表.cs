﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>生产品质反馈扩展表</summary>
[Serializable]
[DataObject]
[Description("生产品质反馈扩展表")]
[BindIndex("IX_DH_ProductionQualityFeedbackEx_FeedbackId", false, "FeedbackId")]
[BindIndex("IX_DH_ProductionQualityFeedbackEx_DType", false, "DType")]
[BindTable("DH_ProductionQualityFeedbackEx", Description = "生产品质反馈扩展表", ConnName = "DH", DbType = DatabaseType.None)]
public partial class ProductionQualityFeedbackEx : IProductionQualityFeedbackEx, IEntity<IProductionQualityFeedbackEx>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int32 _FeedbackId;
    /// <summary>品质反馈编号</summary>
    [DisplayName("品质反馈编号")]
    [Description("品质反馈编号")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("FeedbackId", "品质反馈编号", "")]
    public Int32 FeedbackId { get => _FeedbackId; set { if (OnPropertyChanging("FeedbackId", value)) { _FeedbackId = value; OnPropertyChanged("FeedbackId"); } } }

    private Int32 _DType;
    /// <summary>类型 0补充 1处理</summary>
    [DisplayName("类型0补充1处理")]
    [Description("类型 0补充 1处理")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("DType", "类型 0补充 1处理", "")]
    public Int32 DType { get => _DType; set { if (OnPropertyChanging("DType", value)) { _DType = value; OnPropertyChanged("DType"); } } }

    private String? _Content;
    /// <summary>内容</summary>
    [DisplayName("内容")]
    [Description("内容")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("Content", "内容", "")]
    public String? Content { get => _Content; set { if (OnPropertyChanging("Content", value)) { _Content = value; OnPropertyChanged("Content"); } } }

    private String? _PicPath;
    /// <summary>图片。多个图片用,分隔</summary>
    [DisplayName("图片")]
    [Description("图片。多个图片用,分隔")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("PicPath", "图片。多个图片用,分隔", "")]
    public String? PicPath { get => _PicPath; set { if (OnPropertyChanging("PicPath", value)) { _PicPath = value; OnPropertyChanged("PicPath"); } } }

    private String? _VideoPath;
    /// <summary>视频。多个视频用,分隔</summary>
    [DisplayName("视频")]
    [Description("视频。多个视频用,分隔")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("VideoPath", "视频。多个视频用,分隔", "")]
    public String? VideoPath { get => _VideoPath; set { if (OnPropertyChanging("VideoPath", value)) { _VideoPath = value; OnPropertyChanged("VideoPath"); } } }

    private String? _FilePath;
    /// <summary>文件。多个文件用,分隔</summary>
    [DisplayName("文件")]
    [Description("文件。多个文件用,分隔")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("FilePath", "文件。多个文件用,分隔", "")]
    public String? FilePath { get => _FilePath; set { if (OnPropertyChanging("FilePath", value)) { _FilePath = value; OnPropertyChanged("FilePath"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductionQualityFeedbackEx model)
    {
        Id = model.Id;
        FeedbackId = model.FeedbackId;
        DType = model.DType;
        Content = model.Content;
        PicPath = model.PicPath;
        VideoPath = model.VideoPath;
        FilePath = model.FilePath;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "FeedbackId" => _FeedbackId,
            "DType" => _DType,
            "Content" => _Content,
            "PicPath" => _PicPath,
            "VideoPath" => _VideoPath,
            "FilePath" => _FilePath,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "FeedbackId": _FeedbackId = value.ToInt(); break;
                case "DType": _DType = value.ToInt(); break;
                case "Content": _Content = Convert.ToString(value); break;
                case "PicPath": _PicPath = Convert.ToString(value); break;
                case "VideoPath": _VideoPath = Convert.ToString(value); break;
                case "FilePath": _FilePath = Convert.ToString(value); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static ProductionQualityFeedbackEx? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据品质反馈编号查找</summary>
    /// <param name="feedbackId">品质反馈编号</param>
    /// <returns>实体列表</returns>
    public static IList<ProductionQualityFeedbackEx> FindAllByFeedbackId(Int32 feedbackId)
    {
        if (feedbackId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.FeedbackId == feedbackId);

        return FindAll(_.FeedbackId == feedbackId);
    }

    /// <summary>根据类型0补充1处理查找</summary>
    /// <param name="dType">类型0补充1处理</param>
    /// <returns>实体列表</returns>
    public static IList<ProductionQualityFeedbackEx> FindAllByDType(Int32 dType)
    {
        if (dType < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.DType == dType);

        return FindAll(_.DType == dType);
    }
    #endregion

    #region 字段名
    /// <summary>取得生产品质反馈扩展表字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>品质反馈编号</summary>
        public static readonly Field FeedbackId = FindByName("FeedbackId");

        /// <summary>类型 0补充 1处理</summary>
        public static readonly Field DType = FindByName("DType");

        /// <summary>内容</summary>
        public static readonly Field Content = FindByName("Content");

        /// <summary>图片。多个图片用,分隔</summary>
        public static readonly Field PicPath = FindByName("PicPath");

        /// <summary>视频。多个视频用,分隔</summary>
        public static readonly Field VideoPath = FindByName("VideoPath");

        /// <summary>文件。多个文件用,分隔</summary>
        public static readonly Field FilePath = FindByName("FilePath");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得生产品质反馈扩展表字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>品质反馈编号</summary>
        public const String FeedbackId = "FeedbackId";

        /// <summary>类型 0补充 1处理</summary>
        public const String DType = "DType";

        /// <summary>内容</summary>
        public const String Content = "Content";

        /// <summary>图片。多个图片用,分隔</summary>
        public const String PicPath = "PicPath";

        /// <summary>视频。多个视频用,分隔</summary>
        public const String VideoPath = "VideoPath";

        /// <summary>文件。多个文件用,分隔</summary>
        public const String FilePath = "FilePath";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";
    }
    #endregion
}
