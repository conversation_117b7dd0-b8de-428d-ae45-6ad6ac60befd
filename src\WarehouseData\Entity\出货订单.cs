﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>出货订单</summary>
[Serializable]
[DataObject]
[Description("出货订单")]
[BindIndex("IU_DH_WmsOrder_OrderId", true, "OrderId")]
[BindIndex("IX_DH_WmsOrder_OrderingID", false, "OrderingID")]
[BindIndex("IX_DH_WmsOrder_PickingID", false, "PickingID")]
[BindIndex("IX_DH_WmsOrder_ProductionID", false, "ProductionID")]
[BindIndex("IX_DH_WmsOrder_AuditingID", false, "AuditingID")]
[BindIndex("IX_DH_WmsOrder_PackID", false, "PackID")]
[BindIndex("IX_DH_WmsOrder_PackTime", false, "PackTime")]
[BindIndex("IX_DH_WmsOrder_OrderingTime", false, "OrderingTime")]
[BindIndex("IX_DH_WmsOrder_IsDuplicate", false, "IsDuplicate")]
[BindIndex("IX_DH_WmsOrder_IsEnd", false, "IsEnd")]
[BindIndex("IX_DH_WmsOrder_Status", false, "Status")]
[BindIndex("IX_DH_WmsOrder_HasRemark", false, "HasRemark")]
[BindTable("DH_WmsOrder", Description = "出货订单", ConnName = "DH", DbType = DatabaseType.None)]
public partial class WmsOrder : IWmsOrder, IEntity<IWmsOrder>
{
    #region 属性
    private Int64 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, false, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int64 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String _OrderId = null!;
    /// <summary>ERP订单编号</summary>
    [DisplayName("ERP订单编号")]
    [Description("ERP订单编号")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("OrderId", "ERP订单编号", "", Master = true)]
    public String OrderId { get => _OrderId; set { if (OnPropertyChanging("OrderId", value)) { _OrderId = value; OnPropertyChanged("OrderId"); } } }

    private Int32 _OrderingID;
    /// <summary>打单人</summary>
    [DisplayName("打单人")]
    [Description("打单人")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("OrderingID", "打单人", "")]
    public Int32 OrderingID { get => _OrderingID; set { if (OnPropertyChanging("OrderingID", value)) { _OrderingID = value; OnPropertyChanged("OrderingID"); } } }

    private DateTime _OrderingTime;
    /// <summary>打单时间</summary>
    [DisplayName("打单时间")]
    [Description("打单时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("OrderingTime", "打单时间", "", DataScale = "timeShard:yy")]
    public DateTime OrderingTime { get => _OrderingTime; set { if (OnPropertyChanging("OrderingTime", value)) { _OrderingTime = value; OnPropertyChanged("OrderingTime"); } } }

    private Int32 _PickingID;
    /// <summary>领料人</summary>
    [DisplayName("领料人")]
    [Description("领料人")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("PickingID", "领料人", "")]
    public Int32 PickingID { get => _PickingID; set { if (OnPropertyChanging("PickingID", value)) { _PickingID = value; OnPropertyChanged("PickingID"); } } }

    private DateTime _PickingTime;
    /// <summary>领料时间</summary>
    [DisplayName("领料时间")]
    [Description("领料时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("PickingTime", "领料时间", "")]
    public DateTime PickingTime { get => _PickingTime; set { if (OnPropertyChanging("PickingTime", value)) { _PickingTime = value; OnPropertyChanged("PickingTime"); } } }

    private Int32 _ProductionID;
    /// <summary>生产人</summary>
    [DisplayName("生产人")]
    [Description("生产人")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ProductionID", "生产人", "")]
    public Int32 ProductionID { get => _ProductionID; set { if (OnPropertyChanging("ProductionID", value)) { _ProductionID = value; OnPropertyChanged("ProductionID"); } } }

    private DateTime _ProductionTime;
    /// <summary>生产时间</summary>
    [DisplayName("生产时间")]
    [Description("生产时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("ProductionTime", "生产时间", "")]
    public DateTime ProductionTime { get => _ProductionTime; set { if (OnPropertyChanging("ProductionTime", value)) { _ProductionTime = value; OnPropertyChanged("ProductionTime"); } } }

    private Int32 _AuditingID;
    /// <summary>审核人</summary>
    [DisplayName("审核人")]
    [Description("审核人")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("AuditingID", "审核人", "")]
    public Int32 AuditingID { get => _AuditingID; set { if (OnPropertyChanging("AuditingID", value)) { _AuditingID = value; OnPropertyChanged("AuditingID"); } } }

    private DateTime _AuditingTime;
    /// <summary>审核时间</summary>
    [DisplayName("审核时间")]
    [Description("审核时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("AuditingTime", "审核时间", "")]
    public DateTime AuditingTime { get => _AuditingTime; set { if (OnPropertyChanging("AuditingTime", value)) { _AuditingTime = value; OnPropertyChanged("AuditingTime"); } } }

    private Int32 _PackID;
    /// <summary>出货人</summary>
    [DisplayName("出货人")]
    [Description("出货人")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("PackID", "出货人", "")]
    public Int32 PackID { get => _PackID; set { if (OnPropertyChanging("PackID", value)) { _PackID = value; OnPropertyChanged("PackID"); } } }

    private DateTime _PackTime;
    /// <summary>出货时间</summary>
    [DisplayName("出货时间")]
    [Description("出货时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("PackTime", "出货时间", "")]
    public DateTime PackTime { get => _PackTime; set { if (OnPropertyChanging("PackTime", value)) { _PackTime = value; OnPropertyChanged("PackTime"); } } }

    private Int32 _ShippingID;
    /// <summary>打包人</summary>
    [DisplayName("打包人")]
    [Description("打包人")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ShippingID", "打包人", "")]
    public Int32 ShippingID { get => _ShippingID; set { if (OnPropertyChanging("ShippingID", value)) { _ShippingID = value; OnPropertyChanged("ShippingID"); } } }

    private DateTime _ShippingTime;
    /// <summary>打包时间</summary>
    [DisplayName("打包时间")]
    [Description("打包时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("ShippingTime", "打包时间", "")]
    public DateTime ShippingTime { get => _ShippingTime; set { if (OnPropertyChanging("ShippingTime", value)) { _ShippingTime = value; OnPropertyChanged("ShippingTime"); } } }

    private Int32 _CancelID;
    /// <summary>取消人</summary>
    [DisplayName("取消人")]
    [Description("取消人")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CancelID", "取消人", "")]
    public Int32 CancelID { get => _CancelID; set { if (OnPropertyChanging("CancelID", value)) { _CancelID = value; OnPropertyChanged("CancelID"); } } }

    private DateTime _CancelTime;
    /// <summary>取消时间</summary>
    [DisplayName("取消时间")]
    [Description("取消时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CancelTime", "取消时间", "")]
    public DateTime CancelTime { get => _CancelTime; set { if (OnPropertyChanging("CancelTime", value)) { _CancelTime = value; OnPropertyChanged("CancelTime"); } } }

    private Boolean _IsDuplicate;
    /// <summary>重复打单</summary>
    [DisplayName("重复打单")]
    [Description("重复打单")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("IsDuplicate", "重复打单", "")]
    public Boolean IsDuplicate { get => _IsDuplicate; set { if (OnPropertyChanging("IsDuplicate", value)) { _IsDuplicate = value; OnPropertyChanged("IsDuplicate"); } } }

    private Int16 _Status;
    /// <summary>状态。0为正常，1为取消</summary>
    [DisplayName("状态")]
    [Description("状态。0为正常，1为取消")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Status", "状态。0为正常，1为取消", "", DefaultValue = "0")]
    public Int16 Status { get => _Status; set { if (OnPropertyChanging("Status", value)) { _Status = value; OnPropertyChanged("Status"); } } }

    private Boolean _IsEnd;
    /// <summary>结束完成</summary>
    [DisplayName("结束完成")]
    [Description("结束完成")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("IsEnd", "结束完成", "")]
    public Boolean IsEnd { get => _IsEnd; set { if (OnPropertyChanging("IsEnd", value)) { _IsEnd = value; OnPropertyChanged("IsEnd"); } } }

    private Boolean _HasRemark;
    /// <summary>是否有备注</summary>
    [DisplayName("是否有备注")]
    [Description("是否有备注")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("HasRemark", "是否有备注", "")]
    public Boolean HasRemark { get => _HasRemark; set { if (OnPropertyChanging("HasRemark", value)) { _HasRemark = value; OnPropertyChanged("HasRemark"); } } }

    private String? _Remark;
    /// <summary>备注</summary>
    [DisplayName("备注")]
    [Description("备注")]
    [DataObjectField(false, false, true, 512)]
    [BindColumn("Remark", "备注", "")]
    public String? Remark { get => _Remark; set { if (OnPropertyChanging("Remark", value)) { _Remark = value; OnPropertyChanged("Remark"); } } }

    private DateTime _RemarkTime;
    /// <summary>备注时间</summary>
    [DisplayName("备注时间")]
    [Description("备注时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("RemarkTime", "备注时间", "")]
    public DateTime RemarkTime { get => _RemarkTime; set { if (OnPropertyChanging("RemarkTime", value)) { _RemarkTime = value; OnPropertyChanged("RemarkTime"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IWmsOrder model)
    {
        Id = model.Id;
        OrderId = model.OrderId;
        OrderingID = model.OrderingID;
        OrderingTime = model.OrderingTime;
        PickingID = model.PickingID;
        PickingTime = model.PickingTime;
        ProductionID = model.ProductionID;
        ProductionTime = model.ProductionTime;
        AuditingID = model.AuditingID;
        AuditingTime = model.AuditingTime;
        PackID = model.PackID;
        PackTime = model.PackTime;
        ShippingID = model.ShippingID;
        ShippingTime = model.ShippingTime;
        CancelID = model.CancelID;
        CancelTime = model.CancelTime;
        IsDuplicate = model.IsDuplicate;
        Status = model.Status;
        IsEnd = model.IsEnd;
        HasRemark = model.HasRemark;
        Remark = model.Remark;
        RemarkTime = model.RemarkTime;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "OrderId" => _OrderId,
            "OrderingID" => _OrderingID,
            "OrderingTime" => _OrderingTime,
            "PickingID" => _PickingID,
            "PickingTime" => _PickingTime,
            "ProductionID" => _ProductionID,
            "ProductionTime" => _ProductionTime,
            "AuditingID" => _AuditingID,
            "AuditingTime" => _AuditingTime,
            "PackID" => _PackID,
            "PackTime" => _PackTime,
            "ShippingID" => _ShippingID,
            "ShippingTime" => _ShippingTime,
            "CancelID" => _CancelID,
            "CancelTime" => _CancelTime,
            "IsDuplicate" => _IsDuplicate,
            "Status" => _Status,
            "IsEnd" => _IsEnd,
            "HasRemark" => _HasRemark,
            "Remark" => _Remark,
            "RemarkTime" => _RemarkTime,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToLong(); break;
                case "OrderId": _OrderId = Convert.ToString(value); break;
                case "OrderingID": _OrderingID = value.ToInt(); break;
                case "OrderingTime": _OrderingTime = value.ToDateTime(); break;
                case "PickingID": _PickingID = value.ToInt(); break;
                case "PickingTime": _PickingTime = value.ToDateTime(); break;
                case "ProductionID": _ProductionID = value.ToInt(); break;
                case "ProductionTime": _ProductionTime = value.ToDateTime(); break;
                case "AuditingID": _AuditingID = value.ToInt(); break;
                case "AuditingTime": _AuditingTime = value.ToDateTime(); break;
                case "PackID": _PackID = value.ToInt(); break;
                case "PackTime": _PackTime = value.ToDateTime(); break;
                case "ShippingID": _ShippingID = value.ToInt(); break;
                case "ShippingTime": _ShippingTime = value.ToDateTime(); break;
                case "CancelID": _CancelID = value.ToInt(); break;
                case "CancelTime": _CancelTime = value.ToDateTime(); break;
                case "IsDuplicate": _IsDuplicate = value.ToBoolean(); break;
                case "Status": _Status = Convert.ToInt16(value); break;
                case "IsEnd": _IsEnd = value.ToBoolean(); break;
                case "HasRemark": _HasRemark = value.ToBoolean(); break;
                case "Remark": _Remark = Convert.ToString(value); break;
                case "RemarkTime": _RemarkTime = value.ToDateTime(); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static WmsOrder? FindById(Int64 id)
    {
        if (id < 0) return null;

        return Find(_.Id == id);
    }

    /// <summary>根据ERP订单编号查找</summary>
    /// <param name="orderId">ERP订单编号</param>
    /// <returns>实体对象</returns>
    public static WmsOrder? FindByOrderId(String orderId)
    {
        if (orderId.IsNullOrEmpty()) return null;

        return Find(_.OrderId == orderId);
    }
    #endregion

    #region 数据清理
    /// <summary>清理指定时间段内的数据</summary>
    /// <param name="start">开始时间。未指定时清理小于指定时间的所有数据</param>
    /// <param name="end">结束时间</param>
    /// <returns>清理行数</returns>
    public static Int32 DeleteWith(DateTime start, DateTime end)
    {
        if (start == end) return Delete(_.OrderingTime == start);

        return Delete(_.OrderingTime.Between(start, end));
    }

    /// <summary>删除指定时间段内的数据表</summary>
    /// <param name="start">开始时间</param>
    /// <param name="end">结束时间</param>
    /// <returns>清理行数</returns>
    public static Int32 DropWith(DateTime start, DateTime end)
    {
        return Meta.AutoShard(start, end, session =>
        {
            try
            {
                return session.Execute($"Drop Table {session.FormatedTableName}");
            }
            catch (Exception ex)
            {
                XTrace.WriteException(ex);
                return 0;
            }
        }
        ).Sum();
    }
    #endregion

    #region 字段名
    /// <summary>取得出货订单字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>ERP订单编号</summary>
        public static readonly Field OrderId = FindByName("OrderId");

        /// <summary>打单人</summary>
        public static readonly Field OrderingID = FindByName("OrderingID");

        /// <summary>打单时间</summary>
        public static readonly Field OrderingTime = FindByName("OrderingTime");

        /// <summary>领料人</summary>
        public static readonly Field PickingID = FindByName("PickingID");

        /// <summary>领料时间</summary>
        public static readonly Field PickingTime = FindByName("PickingTime");

        /// <summary>生产人</summary>
        public static readonly Field ProductionID = FindByName("ProductionID");

        /// <summary>生产时间</summary>
        public static readonly Field ProductionTime = FindByName("ProductionTime");

        /// <summary>审核人</summary>
        public static readonly Field AuditingID = FindByName("AuditingID");

        /// <summary>审核时间</summary>
        public static readonly Field AuditingTime = FindByName("AuditingTime");

        /// <summary>出货人</summary>
        public static readonly Field PackID = FindByName("PackID");

        /// <summary>出货时间</summary>
        public static readonly Field PackTime = FindByName("PackTime");

        /// <summary>打包人</summary>
        public static readonly Field ShippingID = FindByName("ShippingID");

        /// <summary>打包时间</summary>
        public static readonly Field ShippingTime = FindByName("ShippingTime");

        /// <summary>取消人</summary>
        public static readonly Field CancelID = FindByName("CancelID");

        /// <summary>取消时间</summary>
        public static readonly Field CancelTime = FindByName("CancelTime");

        /// <summary>重复打单</summary>
        public static readonly Field IsDuplicate = FindByName("IsDuplicate");

        /// <summary>状态。0为正常，1为取消</summary>
        public static readonly Field Status = FindByName("Status");

        /// <summary>结束完成</summary>
        public static readonly Field IsEnd = FindByName("IsEnd");

        /// <summary>是否有备注</summary>
        public static readonly Field HasRemark = FindByName("HasRemark");

        /// <summary>备注</summary>
        public static readonly Field Remark = FindByName("Remark");

        /// <summary>备注时间</summary>
        public static readonly Field RemarkTime = FindByName("RemarkTime");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得出货订单字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>ERP订单编号</summary>
        public const String OrderId = "OrderId";

        /// <summary>打单人</summary>
        public const String OrderingID = "OrderingID";

        /// <summary>打单时间</summary>
        public const String OrderingTime = "OrderingTime";

        /// <summary>领料人</summary>
        public const String PickingID = "PickingID";

        /// <summary>领料时间</summary>
        public const String PickingTime = "PickingTime";

        /// <summary>生产人</summary>
        public const String ProductionID = "ProductionID";

        /// <summary>生产时间</summary>
        public const String ProductionTime = "ProductionTime";

        /// <summary>审核人</summary>
        public const String AuditingID = "AuditingID";

        /// <summary>审核时间</summary>
        public const String AuditingTime = "AuditingTime";

        /// <summary>出货人</summary>
        public const String PackID = "PackID";

        /// <summary>出货时间</summary>
        public const String PackTime = "PackTime";

        /// <summary>打包人</summary>
        public const String ShippingID = "ShippingID";

        /// <summary>打包时间</summary>
        public const String ShippingTime = "ShippingTime";

        /// <summary>取消人</summary>
        public const String CancelID = "CancelID";

        /// <summary>取消时间</summary>
        public const String CancelTime = "CancelTime";

        /// <summary>重复打单</summary>
        public const String IsDuplicate = "IsDuplicate";

        /// <summary>状态。0为正常，1为取消</summary>
        public const String Status = "Status";

        /// <summary>结束完成</summary>
        public const String IsEnd = "IsEnd";

        /// <summary>是否有备注</summary>
        public const String HasRemark = "HasRemark";

        /// <summary>备注</summary>
        public const String Remark = "Remark";

        /// <summary>备注时间</summary>
        public const String RemarkTime = "RemarkTime";
    }
    #endregion
}
