﻿@{
    Html.AppendTitleParts(T("选择型号扩展").Text);

    Int32 Id = ViewBag.Id;

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .xm-option-content {
        padding: 0 5px 0 15px;
    }
</style>
<script asp-location="Head">
    var closeThis = '@T("关 闭 当 前")';
    var closeOther = '@T("关 闭 其 他")';
    var closeAll = '@T("关 闭 全 部")';

    var jsMenuStyle = "@T("菜单风格")";
    var jsTopStyle = "@T("顶部风格")";
    var jsMenu = "@T("菜单")";
    var jsView = "@T("视图")";
    var jsBanner = "@T("通栏")";
    var jsThroughColor = "@T("通色")";
    var jsFooter = "@T("页脚")";
    var jsMoreSettings = "@T("更多设置")";
    var jsOpen = "@T("开")";
    var jsClose = "@T("关")";
    var jsThemeColor = "@T("主题配色")";
    var layuiNoData = '@T("无数据")';
    var layuiAsc = "@T("升序")";
    var layuiDesc = "@T("降序")";

    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
</script>
<form class="layui-form" lay-filter="organization-form" style="padding: 15px 0 0 0; height: 300px;">
    <div class="layui-form-item">
        <label class="layui-form-label">@T("配置选择")</label>
        <div class="layui-input-inline">
            <div id="demo1" style=" width: 100%;"></div>
        </div>
        <label class="layui-form-label" style="padding:1px;">
            <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add" id="add">
                <i class="layui-icon layui-icon-add-1"></i>
                @T("新增")
            </button>
        </label>
    </div>
    <div class="layui-form-item layui-hide">
        <div class="layui-input-block">
            <button class="layui-btn layui-hide" lay-submit lay-filter="organization-submit" id="organization-submit">@T("提交")</button>
        </div>
    </div>
</form>
<script asp-location="Footer">
    layui.use(['abp', 'form'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        window.submitForm = function () {
            $("#organization-submit").click();
        }

        var demo1 = xmSelect.render({
            el: '#demo1',
            //radio: true,
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            multiple: false, // 多选
            pageRemote: true,  // 分页
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];

                // 接口数据
                $.post('@Url.Action("SearchItem")', { keyword: val, page: pageIndex }, function (res) {
                    if (res.success) {
                        if (res.data != null) {
                            cb(res.data, res.extdata);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择
                console.log(data);
                if (data.arr.length > 0) {
                    var a = "";
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {
                            a = data.arr[i].value;
                        }
                        else {
                            a += "," + data.arr[i].value;
                        }
                    }
                }
            }
        });

        form.on('submit(organization-submit)', function (data) {
            var field = data.field;

            var waitIndex = parent.layer.load(2);
            abp.ajax({
                url: "@Url.Action("SelectItems", new { Id = Id })",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: field,
                abpHandleError: false
            }).done(function (data) {
                if (data.success) {
                    data.index = index;

                    parent.saveCallback(data);
                } else {
                    parent.warning(data.msg);
                }
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });


        $("#add").on("click", function () {
            parent.layer.close(index);
            parent.add();
        })
    });
</script>