﻿namespace HlktechIoT.Dto;

/// <summary>设备类型</summary>
public enum HardwareType {
    /// <summary>
    /// 扫码枪
    /// </summary>
    扫码枪 = 0,

    /// <summary>
    /// 高拍仪
    /// </summary>
    高拍仪 = 1,
}

/// <summary>设备所在工序</summary>
public enum HardwareStatus {
    /// <summary>
    /// 打单
    /// </summary>
    打单 = 1,

    /// <summary>
    /// 领料
    /// </summary>
    领料 = 2,

    /// <summary>
    /// 生产
    /// </summary>
    生产 = 3,

    /// <summary>
    /// 生产审核
    /// </summary>
    生产审核 = 4,

    /// <summary>
    /// 出货/入库
    /// </summary>
    出货或入库 = 5,

    /// <summary>
    /// 人为结束
    /// </summary>
    人为结束 = 6,

    /// <summary>
    /// 取消订单
    /// </summary>
    取消订单 = 7,

    /// <summary>
    /// 设置
    /// </summary>
    设置 = 8,

    /// <summary>
    /// 打包
    /// </summary>
    打包 = 9,
}