/* 自定义css */
:root{
    --color:rgb(1, 196, 247);
    --font-size: 2vh;
    --h1-font-size: 1.7vh;
    --h2-font-size: 1.6vh;
}
.transparent{
    background-color: transparent !important;
}
/* 自定义css结束 */
html {
scroll-behavior: smooth;
}


body{
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    background-color: rgb(3, 16, 81);
    /* border: 1px solid gray; */
}
#app{
    width: 100%;
    height: 100%;
    /* border: 1px solid ; */
}
/* 顶边盒子边框线 */
.title{
    width: 100%;
    padding: 16px 0;
    margin-bottom: 2vh;
    height: 5vh;
    text-align: center;
    font-size: 3.5vh;
    letter-spacing: 0.2vw;
    color: #01c4f7;
    text-shadow: 0vw 0vw 0.1vw #ccc;
    letter-spacing: 1px;
    font-weight: 500;
    position: relative;
    background-image: url('../images/head.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    /* border: 2px solid red; */
}
/* 搜索-盒子 */
.searchBox{
    position: relative;

    left: 5%;
    width: 19%;
    height: 3.5vh;
    min-height: 40px;
    /* border: 1px solid white; */
    /* max-width: 600px; */
    display: flex;
    place-items: center;
    background-image: url('../images/search_boder.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
}
input{
    outline: none !important;
    border: none !important;
    color: white;
}

/* 搜索-输入框 */
.layui-input{
    width: 90%;
    max-width: 85%;
    height: 100%;
    color: var(--color);
    font-size: 1.6vh;
    text-indent: 1vw;
    background-color: #01c4f7;
    /* border: 2px solid red !important; */
}
.layui-input::placeholder{
    color: var(--color);
    font-weight: bold;
    line-height: 3.5vh;
    font-size: 1.8vh;
    /* line-height: 3vh !important; */
}
.form{
    margin-left: 0.3vw;
    width: 15%;
    height: 100%;
    /* border: 2px solid green; */
    display:flex;
    justify-content: center;
}
/* 搜索-按钮 */
.layui-btn{
    width: 100% ;
    height: 100% ;
    background-color: var(--color);
    line-height: 4vh;
    color: white;
    /* border: 2px solid red; */
    display:flex;
    justify-content: center;
    place-items: center;
}
.layui-btn>img{
    min-width: 1.5vw !important;
    width: 70%;
    height: 70%;
    /* border: 2px solid red; */
}
/* 主要区域 */
.boxContent{
    width: 100%;
    height: calc(100% - 15vh);
    display: flex;
    justify-content: space-around;
    /* border: 2px solid red; */
}
/* 把页面切成三分  /item_left/item_center/item_right  三个盒子上下都顶点间隙出来*/
.boxContent>div{
    margin-top: 2vh;
    flex: 1;
    max-width: 33vw;
    max-height: 100%;
    background-color: #031051;
    /* border: 2px solid red; */
}
/* 左边 */
.item_left{
    display: flex;
    flex-direction: column;
    /* gap: 2vh 0; 这里浏览器不支持*/ 
    /* border: 2px solid red; */
}

/* 左边盒子边框线 -每个左边盒子 * 4 */
.item_left_box{
    max-width: 90%;
    margin-left: 5%;
    /* height: 50%; */
    /* border: 2px solid red; */
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
    background-image: url('../images/left_border.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    /* overflow: hidden; */
}
.item_left_box:nth-child(n+2){
    margin-top: 2vh;
}

/* 左边盒子标题 */
.leftTile{
    width: 52%;
    margin: 1vh 0px 0.5vh 2vh ;
    font-size: var(--font-size);
    color: white;
    font-weight: bold;
    text-indent: 10px;
    border-left: 4px solid dodgerblue;
    
}
.item_center{
    position: relative;
    /* border: 2px solid red; */
    max-width: 90%;
    /* height: ; */
    /* background-image: url('../images/center_border.png'); */
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.item_center1{
    position: relative;
    margin-left: 4.5%;
    width: 90%;
    height: 42vh;
    align-self: flex-start;
    /* border: 2px solid red; */
}

.centerTile{
    position: absolute;
    top: 0px;
    left: 50%;
    padding: 0.5vh 2.5vw;
    transform: translate(-50%, -50%);
    font-size: var(--font-size);
    white-space: nowrap;
    font-weight: 600;
    color: #02d9fd!important;
    background-image: url('../images/border2.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
}
#feedViewBox{
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    background-image: url('../images/left_border.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

#feedViewBox .task[data-index="01"] {
    background-image: url('../images/01.png');
}

#feedViewBox>.task[data-index="02"] {
    background-image: url('../images/02.png');
}

#feedViewBox>.task[data-index="03"] {
    background-image: url('../images/03.png');
}

#feedViewBox>.task[data-index="04"] {
    background-image: url('../images/04.png');
}

#feedViewBox>.task[data-index="05"] {
    background-image: url('../images/05.png');
}

#feedViewBox>.task[data-index="06"] {
    background-image: url('../images/06.png');
}
#feedViewBox>.task[data-index="07"] {
    background-image: url('../images/07.png');
}

.item_center2 {
    position: relative;
    margin-top: auto;
    margin-left: 4.5%;
    width: 90%;
    height: 36vh;
}

.centerTile2 {
    position: absolute;
    top: 0px;
    left: 50%;
    padding: 0.5vh 2.5vw;
    transform: translate(-50%, -50%);
    font-size: var(--font-size);
    white-space: nowrap;
    font-weight: 600;
    color: #02d9fd!important;
    background-image: url('../images/border2.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
}
/* 中间盒子标题 */
.item_center_title{
    margin-top: 1vh;
    position: absolute;
    top: -1vh;
    left: 50%;
    padding: 0.7vh 2.2vw;
    transform: translate(-50%, -50%);
    text-align: center;
    font-size: var(--font-size);
    color: white;
    cursor: pointer;
    background-image: url('../images/border2.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    font-weight: bold;
}

.item_center_title_span{
    color: white;
}
.item_center_title_span[data-selected="true"]{
    color: rgb(1, 196, 247);
}

/* 右边 */
.item_right{
    /* margin-top: .5vh; */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    /* border: 2px solid red; */
}
.item_right1{
    position: relative;
    margin-left: 4.5%;
    width: 90%;
    height: 42vh;
    align-self: flex-start;
    /* border: 2px solid red; */
}
#taskViewBox{
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    background-image: url('../images/left_border.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    /* border: 2px solid red; */
}


.item_right2{
    position: relative;
    margin-top: auto;
    margin-left: 4.5%;
    width: 90%;
    height: 36vh;
    /* border: 2px solid red; */
}
.rightTile2{
    position: absolute;
    top: 0px;
    left: 50%;
    padding: 0.5vh 2.5vw;
    transform: translate(-50%, -50%);
    font-size: var(--font-size);
    white-space: nowrap;
    font-weight: 600;
    color: #02d9fd!important;
    background-image: url('../images/border2.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
}
/* 每个任务 */
.task{
    /* margin-top: 2%; */
    margin-left: 5%;
    width: 42.5%;
    height: 19%;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    flex-wrap: wrap;
    place-items: center;
    justify-content: center;
    overflow: hidden;
    cursor: pointer;
    /* border: 2px solid red; */
}
/* 每个任务 -- 文字和数字 */
.task>div{
    width: 100%;
    height: 50%;
    /* border: 1px solid red; */
    overflow: hidden;
    color: white;
    text-align: center;
    display: table-cell;
    vertical-align: bottom;
    text-indent: 1.5vw; 
    font-size: 1.7vh;

}


#taskViewBox .task[data-index="01"] {
    background-image: url('../images/01.png');
}

#taskViewBox>.task[data-index="02"] {
    background-image: url('../images/02.png');
}

#taskViewBox>.task[data-index="03"] {
    background-image: url('../images/03.png');
}

#taskViewBox>.task[data-index="04"] {
    background-image: url('../images/04.png');
}

#taskViewBox>.task[data-index="05"] {
    background-image: url('../images/05.png');
}

#taskViewBox>.task[data-index="06"] {
    background-image: url('../images/06.png');
}
#taskViewBox>.task[data-index="07"] {
    background-image: url('../images/07.png');
}

.taskTableBox{
    position: relative;
    top: 0%;
    width: 94%;
    height: 90%;
    background-color: #031051;
    border: 2px solid white;
    /* background-image: url('../images/center_border.png');
    background-size: 100% 100%;
    background-repeat: no-repeat; */
}


.echarts{
    position: relative;
    margin:2vh 5% 0px 5%;
    padding-left: 5%;
    max-height: 50vh;
    /* display: flex; */
    flex: 2;
    background-image: url('../images/left_border.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    /* border: 2px solid red; */
}
#pieBox{
    margin-top: 3vh;
    margin-left: 1%;
    width: 94%;
    height: 40%;
    /* border: 2px solid red; */
}
#barBox{
    margin-top: 0vh;
    margin-left: 1%;
    width: 94%;
    height: 45%;
    /* border: 2px solid red; */
}

/* 每个图表的高度 */
.echarts>div>div{
    width: 100%;
    height: 99%;
    /* border: 2px solid red; */
}
/* 每个图表的标题 */
.rightTile{
    position: absolute;
    top: 0px;
    left: 50%;
    padding: 0.5vh 2.5vw;
    transform: translate(-50%, -50%);
    font-size: var(--font-size);
    white-space: nowrap;
    font-weight: 600;
    color: #02d9fd!important;
    background-image: url('../images/border2.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
}


.item_center_title {
    white-space: nowrap;
    width: fit-content;
}
.mask{
    transition: all 1s;
    position: fixed;
    top: 0%;
    left: 0%;
    width: 0%;
    height: 0vh;
    display: flex;
    justify-content: center;
    place-items: center;
 
    background-color: rgba(0,0,0,0.4);

}
.searchResponse{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50vw;
    min-width: 720px;
    /* min-width: 30vh;
    min-height: 20vh; */
    background-color: #031051;
    background-image: url('../images/left_border.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
    border-radius: 15px;
    display: none;
    z-index: 100;
}
.searchResponse_title{
    position: absolute;
    top: -0px;
    left: 50%;
    padding: 0.7vh 2vw;
    transform: translate(-50%, -50%);
    text-align: center;
    font-size: var(--font-size);
    color: white;
    cursor: pointer;
    font-size: 1.7vh;
    background-image: url('../images/border2.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    font-weight: bold;
    letter-spacing: 1px;
}
.searchResponse_data{
    margin-top: 3%;
    margin-bottom: 2%;
    margin-left: 5%;
    width: 95%;
    padding: 1vh 0px;
    display: flex;
    flex-wrap: wrap;
    /* justify-content: center;
    align-items: center; */
    overflow: hidden;
    font-size: var(--h1-font-size);
    white-space: nowrap;
}
.searchResponse_data_label{
    padding: 0.8vh 1vh;
    margin: 0.5vh 1vh;
    color:#02d9fd;
    background-color: #022371;
    width: 8%;
    height: 2vh;
    min-width: 60px !important;
    /* border: 2px solid dodgerblue; */
}
.searchResponse_data_text{
    padding: 0.7vh 1vh;
    margin: 0.5vh 0.5vh;
    width: calc(40% - 8vh);
    min-width: 170px !important;
    color: white;
    height: 2vh;
    border: 1px solid dodgerblue;
}
.close{
    position: absolute;
    top: 1vh;
    right: calc(2%);
    height: 2vh;
    margin-top: 6px;
    z-index: 1000000;
    cursor: pointer;
}
/* 功能盒子 */
.function{
    margin-left: 2%;
    width: 90%;
    padding: .2vh 0;
    color: white;
    font-size: 1.5vh;
    /* border: 2px solid white; */
    display: flex;
}
/* 每个功能 */
.function>.function_item{
    margin-left: 2%;
    width: 15%;
    max-height: 3vh;
    line-height: 3vh;
    display: flex;
    place-items:center;
    font-size: 1.7vh;
    /* border: 2px solid white; */
}
input{
    background-color: rgb(13, 47, 119);
    text-indent: .5vw;
    height: 3vh;
    line-height: 3vh;
    width: 100%;
}
.taskLabel{
    width: fit-content;
    white-space: nowrap;
    margin-right: .5vw;
}
/* 任务的 input类似盒子 */
.taskKey{
    width: 70%;
    display: flex;
}
#demo1{
    display: flex;
    align-items: center;
    height: 100%;

}
/* 超时订单 --查看按钮 */
.reader_btn{
    max-height: 30px !important;
    background-color: #01c4f7;
    border-radius: 0px !important;
}
/* layui 页码 */
.layui-laypage .layui-laypage-curr .layui-laypage-em{
    background-color: white;
    color: black;
}
/* layui 页码 */
em{
    color: dodgerblue !important;
}
/* layui 分页码 */
a[data-page]{
    color: white !important;
}
/* layui 日期 - 星期 */
.layui-laydate-content th{
    background-color: white !important;
}

/* layui 或其它 -样式 */
thead{
    background-color: #022371;
    color: #02cef5;
}
/* 每个表格的单元格间距 */
.layui-table-cell{
    padding: 0.1vh 1vh !important;
    margin: 0 !important;
    height: auto !important;
}
/* 每个表格的单元格间距 */
#taskTableBox .layui-table-cell{
    padding: .5vh .5vh !important;
    margin: 0.5vh 0vh !important;
    height: auto !important;
}
/* 字体行高 */
.layui-table td, .layui-table th {
    line-height: 10vh; /* 设置行高为2 */
}
/* 自定义表格行的 hover 样式 */
.layui-table-hover:hover {
    background-color: #022371; /* 更改背景颜色 */
    /* color: white; 更改文字颜色 */
}

/* 表格可视数据的宽度 */
.layui-table-view{
    width: calc(100% - 0px);
    /* border: 2px solid red;  */
}
/* 禁止出现滚动条 */
.layui-table-body{
    overflow: auto;
    scrollbar-width: none;
    -ms-overflow-style: none; 
}
/* 表格-自定义样式 */
.layui-card{
    display: block;
    background-color: transparent !important;
    color: white !important;
    max-width: 92%;
    padding: 1%;
    /* width: 90%; */
    margin-left:3%;
    max-height: 91.2%;
    /* border: 2px solid red; */
    overflow: hidden;
    transition: all 1s; 
}
/* 表格-layui */
.layui-table{
    background-color: transparent !important;
    color: white;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
    /* text-shadow: 0px 0px 1px white; */
}
/*设置不可编辑时字体颜色*/
.layui-disabled, .layui-disabled:hover {
    color: #101010 !important;
    background-color: #eee;
}
/*设置表格单元格线样式*/
.item_center .layui-table td, .layui-table th, .layui-table-col-set, .layui-table-fixed-r, .layui-table-grid-down, .layui-table-header, .layui-table-page, .layui-table-tips-main, .layui-table-tool, .layui-table-total, .layui-table-view, .layui-table[lay-skin=line], .layui-table[lay-skin=row] {
    border-color: transparent; /*#bbb*/
    font-size:1.5vh;
    white-space: nowrap; /* 不换行 */
    overflow: hidden; /* 超出内容隐藏 */
    text-overflow: ellipsis; /* 显示省略号 */
}
.item_left .layui-table td, .layui-table th, .layui-table-col-set, .layui-table-fixed-r, .layui-table-grid-down, .layui-table-header, .layui-table-page, .layui-table-tips-main, .layui-table-tool, .layui-table-total, .layui-table-view, .layui-table[lay-skin=line], .layui-table[lay-skin=row] {
    border-color: transparent; /*#bbb*/
    font-size:1.5vh;
    white-space: nowrap; /* 不换行 */
    overflow: hidden; /* 超出内容隐藏 */
    text-overflow: ellipsis; /* 显示省略号 */
}
.layui-table-total >.layui-table td, .layui-table th, .layui-table-col-set, .layui-table-fixed-r, .layui-table-grid-down, .layui-table-header, .layui-table-page, .layui-table-tips-main, .layui-table-tool, .layui-table-total, .layui-table-view, .layui-table[lay-skin=line], .layui-table[lay-skin=row] {
    border-color: transparent; /*#bbb*/
}
/* 任务表格 的单元格背景颜色 */
#taskTableBox .layui-table td{
    border-bottom-color: rgb(8, 72, 192) !important;
    border-bottom-width: 2px;
    border-right-width: 0;
} 
/* 表格的最后一根线 */
.layui-table-total >table > tbody>tr>td{
    border: none !important;
 }
.layui-table-total{
    height: 0vh !important;
}
/* 表格分页 */
.layui-table-column{
    margin-top: 17px !important;
}
.layui-icon{
    color: rgb(8, 72, 192);
}
.layui-laypage-count{
    color: white !important;
}
.layerMsg{
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
}
@media screen and (min-width: 3000px) {
    .layui-table-cell{
        padding: 2vh !important;
        font-size: 2vh !important;
        /* border: 2px solid red; */
    }
    /* 每个任务 -- 文字 */
    .task>div:nth-child(1){
        font-size: 2vh !important;
    }
    :root{
        --font-size: 2vh !important;
    }
    .layui-input{
        font-size: 2vh !important;
        height: 80% !important;
    }
    .searchResponse_data_label{
        width: 10%;
    }
}
@media screen and (max-width: 1500px){
    :root{
        --font-size: 1.6vh !important;
    }
}
.layui-table-click, .layui-table-hover, .layui-table[lay-even] tbody tr:nth-child(even){
    background-color:#022371 !important;
}
.blue{
    background-color: #031051 !important;
    color: white !important;
}
.layui-layer-title{
    background-color: rgb(21, 90, 160) !important;
    color: white !important;
}
#remark{
    margin: 10px;
    padding:10px;
    letter-spacing: 1px;
    font-size: var(--font-size);
}
/* .layui-layer-content{
    background-color: #02cef5 !important;
    color: white !important;
} */