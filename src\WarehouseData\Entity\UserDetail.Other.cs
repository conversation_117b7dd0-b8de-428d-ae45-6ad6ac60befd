﻿using DH.Entity;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Membership;

namespace HlktechIoT.Entity
{
    public class UserDetailEx : UserDetail
    {
        /// <summary>
        /// 根据公司ID查找
        /// </summary>
        /// <returns></returns>
        public static IList<UserDetail> FindAllByCompanyId(Int32 CompanyId)
        {
            if (CompanyId <= 0) return [];
            if (Meta.Session.Count < 1000)
            {
                return Meta.Cache.FindAll(e => e.CompanyId == CompanyId);
            }
            return FindAll(_.CompanyId == CompanyId);
        }

    }
}
