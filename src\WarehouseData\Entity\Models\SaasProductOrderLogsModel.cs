﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>生产厂家生产记录</summary>
public partial class SaasProductOrderLogsModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int64 Id { get; set; }

    /// <summary>订单号</summary>
    public String OrderId { get; set; } = null!;

    /// <summary>第三方对接</summary>
    public Int32 OpenPlatformId { get; set; }

    /// <summary>产品名称/型号</summary>
    public String Name { get; set; } = null!;

    /// <summary>设备Mac地址</summary>
    public String Mac { get; set; } = null!;

    /// <summary>文件路径。七牛云文件存储链接</summary>
    public String? FileUrl { get; set; }

    /// <summary>产线测试点序号，一个功能点测试算一个节点，如果只有一道测试工序，即为 1</summary>
    public Int32 Node { get; set; }

    /// <summary>测试结果， 1 测试通过 ，0 测试失败</summary>
    public Int32 Status { get; set; }

    /// <summary>测试结果的描述，最长不超过 1024 字节</summary>
    public String? Msg { get; set; }

    /// <summary>固件版本号</summary>
    public String? Version { get; set; }

    /// <summary>测试工号，如果一个测试工号负责多个同样测试点测试，可以按 xxxx-01 来区分</summary>
    public String? WorkId { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ISaasProductOrderLogs model)
    {
        Id = model.Id;
        OrderId = model.OrderId;
        OpenPlatformId = model.OpenPlatformId;
        Name = model.Name;
        Mac = model.Mac;
        FileUrl = model.FileUrl;
        Node = model.Node;
        Status = model.Status;
        Msg = model.Msg;
        Version = model.Version;
        WorkId = model.WorkId;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
