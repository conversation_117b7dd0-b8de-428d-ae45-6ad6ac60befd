﻿@{
    Html.AppendTitleParts(T("添加消息").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
</style>
<div class="containers">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("消息标题")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="title" placeholder="@T("请输入消息标题")" autocomplete="off" class="layui-input" value="">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("接收人")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <div id="demo1" style=" width: 100%;"></div>
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label-width"><span>*</span>@T("消息内容")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <textarea placeholder="@T("请输入内容")" name="content" id="" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item btn">
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var upload = layui.upload;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        var demo1 = xmSelect.render({
            el: '#demo1',
            radio: true, //设置单选
            name: 'receiveUserID',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            disabled: false, // 设置禁用
            remoteMethod: function (val, cb, show, pageIndex) 
            {  // 远程方法
                var obj = [];
                // 接口数据
                $.post('@Url.Action("SearchUser")', { keyword: val, page: pageIndex }, function (res) 
                {
                    // console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                            cb(res.data, res.extdata.PageCount);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });
            },
            on: function (data) 
            {  
                // 监听选择
            }
        });

        form.on('submit(Submit)', function (data) {

            var waitIndex = parent.layer.load(2);
            abp.ajax({
                url: "@Url.Action("Add")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>