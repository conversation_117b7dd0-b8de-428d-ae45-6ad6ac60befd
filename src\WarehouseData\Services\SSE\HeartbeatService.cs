﻿using DH.ServerSentEvents;

namespace HlktechIoT.Services.SSE;

internal class HeartbeatService : BackgroundService {
    #region Fields
    private const string HEARTBEAT_MESSAGE_FORMAT = "DaLiBattery.ServerSentEvents Heartbeat ({0} UTC)";

    private readonly IServerSentEventsService _serverSentEventsService;
    #endregion

    #region Constructor
    public HeartbeatService(IServerSentEventsService serverSentEventsService)
    {
        _serverSentEventsService = serverSentEventsService;
    }
    #endregion

    #region Methods
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            await _serverSentEventsService.SendEventAsync(String.Format(HEARTBEAT_MESSAGE_FORMAT, DateTime.UtcNow));

            await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
        }
    }
    #endregion
}