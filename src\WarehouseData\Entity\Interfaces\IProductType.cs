﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>产品型号</summary>
public partial interface IProductType
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>型号名称</summary>
    String Name { get; set; }

    /// <summary>软件工程师</summary>
    Int32 SoftwareId { get; set; }

    /// <summary>硬件工程师</summary>
    Int32 HardwareId { get; set; }

    /// <summary>状态</summary>
    Boolean Status { get; set; }

    /// <summary>物料编号</summary>
    String? Material { get; set; }

    /// <summary>备注</summary>
    String? Remark { get; set; }

    /// <summary>产品类型。0为无，1为电源</summary>
    Int16 PType { get; set; }

    /// <summary>生成Sn</summary>
    Boolean NeedSn { get; set; }

    /// <summary>生成主Mac</summary>
    Boolean NeedMac { get; set; }

    /// <summary>生成副Mac</summary>
    Boolean NeedMac1 { get; set; }

    /// <summary>PCB周期</summary>
    Boolean PCBCycle { get; set; }

    /// <summary>屏蔽罩周期</summary>
    Boolean ShieldCycle { get; set; }

    /// <summary>主芯片周期</summary>
    Boolean MainChipCycle { get; set; }

    /// <summary>测试项</summary>
    String? TestItems { get; set; }

    /// <summary>配置项</summary>
    String? ExpansionItems { get; set; }

    /// <summary>模组类型(0:无,1:ACDC,2:DCDC)</summary>
    Int32 ModuleType { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
