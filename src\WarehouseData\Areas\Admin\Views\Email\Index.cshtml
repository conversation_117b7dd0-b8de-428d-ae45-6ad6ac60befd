﻿@using Microsoft.Extensions.Options
@{
    Html.AppendTitleParts(T("邮箱设置").Text);

    var SiteSettings = DH.Entity.SiteSettingInfo.SiteSettings;
    var optionEmail = EngineContext.Current.Resolve<IOptionsMonitor<DH.Mail.Email>>().CurrentValue;
}
<style asp-location="true">
    .layui-form-label {
        width: 100px;
    }
</style>
<div class="layui-card">
    <div class="layui-card-header">@T("邮箱设置")</div>
    <div class="layui-card-body" pad15>
        <form class="layui-form" lay-filter="">
            <div class="layui-form-item">
                <label class="layui-form-label">@T("SMTP服务器")</label>
                <div class="layui-input-inline">
                    <input type="text" id="email_host" name="email_host" lay-verify="pass" placeholder="@T("SMTP服务器")" value="@optionEmail.Host" autocomplete="off" class="layui-input">
                </div>
                <div class="layui-form-mid layui-word-aux">@T("设置 SMTP 服务器的地址，如 smtp.exmail.qq.com")</div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("SMTP协议")</label>
                <div class="layui-input-inline" style="width:190px;">
                    <select id="email_secure" name="email_secure" lay-filter="email_secure">
                        <!option value="false" @(!optionEmail.IsSSL ? "selected" : "")>@T("非SSL协议")</!option>
                        <!option value="true" @(optionEmail.IsSSL ? "selected" : "")>@T("SSL协议")</!option>
                    </select>
                </div>
                <div class="layui-form-mid layui-word-aux">@T("设置 SMTP 服务器的地址，如 smtp.exmail.qq.com")</div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("SMTP端口")</label>
                <div class="layui-input-inline">
                    <input type="text" id="email_port" name="email_port" lay-verify="pass" placeholder="@T("SMTP端口")" autocomplete="off" value="@optionEmail.Port" class="layui-input">
                </div>
                <div class="layui-form-mid layui-word-aux">@T("设置 SMTP 服务器的端口，非SSL协议默认为 25，SSL协议默认为465")</div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("发信人邮件地址")</label>
                <div class="layui-input-inline">
                    <input type="text" name="email_addr" id="email_addr" placeholder="@T("发信人邮件地址")" value="@optionEmail.From" autocomplete="off" class="layui-input">
                </div>
                <div class="layui-form-mid layui-word-aux">@T("使用SMTP协议发送的邮件地址，如 <EMAIL>")</div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("身份验证用户名")</label>
                <div class="layui-input-inline">
                    <input type="text" name="email_id" id="email_id" value="@optionEmail.UserName" autocomplete="off" class="layui-input">
                </div>
                <div class="layui-form-mid layui-word-aux">@T("如***************")</div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("身份验证密码")</label>
                <div class="layui-input-inline">
                    <input type="password" name="email_pass" id="email_pass" value="@optionEmail.Password" autocomplete="off" class="layui-input">
                </div>
                <div class="layui-form-mid layui-word-aux">@T("********************邮件的密码，如 123456")</div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("发送邮箱昵称")</label>
                <div class="layui-input-inline">
                    <input type="text" name="FromName" id="FromName" value="@optionEmail.FromName" autocomplete="off" class="layui-input">
                </div>
                <div class="layui-form-mid layui-word-aux">@T("如海凌科")</div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("测试邮件地址")</label>
                <div class="layui-input-inline">
                    <input type="text" name="email_test" id="email_test" value="" autocomplete="off" class="layui-input">
                </div>
                <div class="layui-form-mid layui-word-aux" style="padding-top: 0px !important;">
                    <a class="layui-btn " id="send_test_email">@T("测试")</a>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block pad">
                    <button type="button" class="layui-btn" lay-submit="" lay-filter="demo1">@T("立即提交")</button>
                </div>
            </div>

        </form>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;

        //自定义验证
        form.verify({

        });

        $('#send_test_email').click(function () {
            $.ajax({
                type: 'POST',
                url: "@Url.Action("SendMailTest", "Send", new { area = "" })",
                data: {
                    'email_host': $('#email_host').val(),
                    'email_secure': $('#email_secure option:selected').val(),
                    'email_port': $('#email_port').val(),
                    'email_addr': $('#email_addr').val(),
                    'email_id': $('#email_id').val(),
                    'email_pass': $('#email_pass').val(),
                    'email_test': $('#email_test').val(),
                    'fromname': $('#FromName').val()
                },
                error: function (html) {
                    layer.alert(html.msg);
                },
                success: function (html) {
                    layer.alert(html.msg);
                },
                dataType: 'json'
            });
        });

        form.on('submit(demo1)', function (data) {
            let loading = layer.load();
            $.post("@Url.Action("UpdateEmailSetting")", data.field, function (result) {
                layer.close(loading);
                if (result.success) {
                    abp.notify.success(result.msg);
                } else {
                    abp.notify.error(result.msg);
                }
            });
        });
    });
</script>