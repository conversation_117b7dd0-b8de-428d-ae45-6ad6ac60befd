﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>电源生产数据</summary>
public partial interface IPowerProduction
{
    #region 属性
    /// <summary>编号</summary>
    Int64 Id { get; set; }

    /// <summary>订单号</summary>
    String OrderId { get; set; }

    /// <summary>模块物料编号</summary>
    String? Material { get; set; }

    /// <summary>产品型号编号</summary>
    Int32 ProductTypeId { get; set; }

    /// <summary>电源型号配置编号</summary>
    Int32 PowerTypeItemId { get; set; }

    /// <summary>SN</summary>
    String? Sn { get; set; }

    /// <summary>测试时间</summary>
    DateTime TestTime { get; set; }

    /// <summary>备注</summary>
    String? Remak { get; set; }

    /// <summary>测试结果。使用Json存储</summary>
    String? Content { get; set; }

    /// <summary>测试结果。成功或者失败</summary>
    Boolean TestResult { get; set; }

    /// <summary>检测工厂编号</summary>
    String? CompanyId { get; set; }

    /// <summary>检测工厂名称</summary>
    String? CompanyName { get; set; }

    /// <summary>测试工位</summary>
    String? TestStation { get; set; }

    /// <summary>批次字段1</summary>
    String? BatchField1 { get; set; }

    /// <summary>批次字段2</summary>
    String? BatchField2 { get; set; }

    /// <summary>批次字段3</summary>
    String? BatchField3 { get; set; }

    /// <summary>批次字段4</summary>
    String? BatchField4 { get; set; }

    /// <summary>批次字段5</summary>
    String? BatchField5 { get; set; }

    /// <summary>批次字段6</summary>
    String? BatchField6 { get; set; }

    /// <summary>批次字段7</summary>
    String? BatchField7 { get; set; }

    /// <summary>批次字段8</summary>
    String? BatchField8 { get; set; }

    /// <summary>批次字段9</summary>
    String? BatchField9 { get; set; }

    /// <summary>批次字段10</summary>
    String? BatchField10 { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }
    #endregion
}
