﻿using DH;

namespace HlktechIoT.Common;

public class SingleArticleSetting : CacheObject {
    static SingleArticleSetting()
    {
        Init();
    }

    private static void Init()
    {
        var list = cdb.FindAll<SingleArticleSetting>();
        if (list.Count == 0)
        {
            var model = new SingleArticleSetting();
            model.Name = "Ex1";
            model.DisplayName = "版本号";
            model.IsEnabled = false;
            cdb.Insert(model);
        }
    }

    public static List<SingleArticleSetting> GetAll()
    {
        return cdb.FindAll<SingleArticleSetting>();
    }

    /// <summary>
    /// 是否启用
    /// </summary>
    public Boolean IsEnabled { get; set; }

    /// <summary>
    /// 显示名称
    /// </summary>
    public String? DisplayName { get; set; }
}