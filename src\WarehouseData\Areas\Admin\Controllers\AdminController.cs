﻿using DG.Web.Framework;

using DH.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek.Exceptions;
using Pek.Helpers;
using Pek.Models;

using System.ComponentModel;
using System.Dynamic;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>
/// 管理员列表
/// </summary>
[DisplayName("管理员管理")]
[Description("管理员的管理")]
[AdminArea]
[DHMenu(80,ParentMenuName = "System", CurrentMenuUrl = "~/{area}/Admin", CurrentMenuName = "Manager", LastUpdate = "20240124")]
public class AdminController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 80;

    /// <summary>
    /// 管理员列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("管理员管理")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 根据手机号 和用户名分页查询管理员数据
    /// </summary>
    /// <param name="name"></param>
    /// <param name="mobile"></param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("管理员管理")]
    public IActionResult GetpageAdmin(String name, String mobile, Int32 page = 1, Int32 limit = 10)
    {
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true
        };

        var provider = ManageProvider.Provider;
        if (provider?.Current.ID == 1)
        {
            var list = UserE.Searchs(true, true, pages, mobile, name).Select(item =>
            {
                var (Name, Remark) = RoleLan.FindByRIdAndLId(item.RoleID, WorkingLanguage.Id, true);
                return new { item.ID, item.Name, item.LastLoginIP, item.RegisterTime, Sex = GetResource(item.Sex.ToString()), RoleName = Name, item.Mobile };
            }).OrderBy(e => e.ID);

            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data = list });
        }
        else
        {
            var list = UserE.Searchs(true, true, pages, mobile, name).Where(item => item.ID != 1).Select(item =>
            {
                var (Name, Remark) = RoleLan.FindByRIdAndLId(item.RoleID, WorkingLanguage.Id, true);
                return new { item.ID, item.Name, item.LastLoginIP, item.RegisterTime, Sex = GetResource(item.Sex.ToString()), RoleName = Name, item.Mobile };
            }).OrderBy(e => e.ID);

            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data = list });
        }
    }

    /// <summary>
    /// 查看修改用户页面
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [DisplayName("进入修改/创建用户页面")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult EditUser(Int32 Id)
    {
        dynamic viewModel = new ExpandoObject();
        var Roles = Role.FindAllWithCache().Where(x => x.ID != 1 && x.IsSystem == true).Select(x => new Role { ID = x.ID, Name = RoleLan.FindByRIdAndLId(x.ID, WorkingLanguage.Id, true).Name }).OrderBy(e => e.ID).ToList();
        viewModel.Roles = Roles;

        if (Id != 0)
        {
            viewModel.UserModel = UserE.FindByID(Id);
            return View(viewModel);
        }
        else
        {
            viewModel.UserModel = new UserE();
            return View(viewModel);
        }
    }

    /// <summary>
    /// 修改管理员信息
    /// </summary>
    /// <returns></returns>
    [DisplayName("修改管理员信息")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult UpdateUserPwd(String PassWord, Int32 Id, String RoleID, String OtherPermissions, String Mobile, String RealName, String Enabled)
    {
        try
        {
            if (!PassWord.IsNullOrEmpty())
            {
                if (PassWord.Length < 8)
                {
                    throw new DHException(GetResource("密码长度不可少于8位"));
                }
                if (PassWord.Length > 32)
                {
                    throw new DHException(GetResource("密码长度不可超过32位"));
                }
            }
            if (!Mobile.IsNullOrWhiteSpace())
            {
                if (!ValidateHelper.IsMobile(Mobile))
                {
                    throw new DHException(GetResource("手机号码格式不对"));
                }

                var m = UserE.FindByMobile(Mobile);
                if (m != null && m.ID != Id)
                {
                    throw new DHException(GetResource("手机号码已被使用"));
                }
            }

            var UserModel = UserE.FindByID(Id);
            if (!PassWord.IsNullOrWhiteSpace())
            {
                UserModel.Password = ManageProvider.Provider?.PasswordProvider.Hash(PassWord.Length == 32 ? PassWord : PassWord.MD5());
            }
            if (!RoleID.IsNullOrWhiteSpace())
            {
                UserModel.RoleID = RoleID.ToInt();
            }
            UserModel.Mobile = Mobile;
            UserModel.Enable = Enabled == "on";
            UserModel.Update();

            var modelUserDetail = UserDetail.FindById(Id);
            if (modelUserDetail != null)
            {
                modelUserDetail.OtherPermissions = OtherPermissions;
                modelUserDetail.TrueName = RealName;
                modelUserDetail.Update();
            }
            else
            {
                modelUserDetail = new UserDetail();
                modelUserDetail.Id = Id;
                modelUserDetail.OtherPermissions = OtherPermissions;
                modelUserDetail.TrueName = RealName;
                modelUserDetail.Insert();
            }
        }
        catch (DHException ex)
        {
            return Json(new DResult { success = false, msg = ex.Message });
        }
        return Json(new DResult { success = true, msg = GetResource("修改成功") });
    }

    /// <summary>
    /// 新增管理员
    /// </summary>
    /// <returns></returns>
    [DisplayName("创建管理员")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult CreateUser(string name, string PassWord, string RoleID, String OtherPermissions, String Mobile, String RealName, String Enabled)
    {
        try
        {
            if (name.IsNullOrWhiteSpace())
            {
                throw new DHException(GetResource("账户名不可为空"));
            }
            if (name.Length <= 2)
            {
                throw new DHException(GetResource("账户名长度不可少于2位"));
            }
            if (PassWord.IsNullOrWhiteSpace())
            {
                throw new DHException(GetResource("密码不可为空"));
            }
            if (PassWord.Length < 8)
            {
                throw new DHException(GetResource("密码长度不可少于8位"));
            }
            if (PassWord.Length > 32)
            {
                throw new DHException(GetResource("密码长度不可超过32位"));
            }

            if (!Mobile.IsNullOrWhiteSpace())
            {
                if (!ValidateHelper.IsMobile(Mobile))
                {
                    throw new DHException(GetResource("手机号码格式不对"));
                }

                var m = UserE.FindByMobile(Mobile);
                if (m != null)
                    throw new DHException(GetResource("手机号码已被其他人使用"));
            }

            var Model = UserE.FindByName(name);
            if (Model != null)
            {
                throw new DHException(GetResource("用户名已存在 请重新输入！"));
            }
            var UserModel = new UserE();
            UserModel.Name = name;
            UserModel.Password = ManageProvider.Provider?.PasswordProvider.Hash(PassWord.Length == 32 ? PassWord : PassWord.MD5());
            UserModel.Ex1 = 1;
            UserModel.Sex = SexKinds.未知;
            UserModel.Enable = true;
            UserModel.RegisterTime = DateTime.Now;
            UserModel.RegisterIP =Pek.Helpers.DHWeb.IP;
            UserModel.RoleID = RoleID.ToInt();  // 默认超管
            UserModel.Mobile = Mobile;
            if (ValidateHelper.IsEmail(name))
            {
                UserModel.Mail = name;
            }
            else if (ValidateHelper.IsMobile(name))
            {
                UserModel.Mobile = name;
            }
            UserModel.Enable = Enabled == "on";
            UserModel.Insert();

            var modelUserDetail = new UserDetail();
            modelUserDetail.Id = UserModel.ID;
            modelUserDetail.OtherPermissions = OtherPermissions;
            modelUserDetail.TrueName = RealName;
            modelUserDetail.Insert();
        }
        catch (DHException ex)
        {
            return Json(new DResult { success = false, msg = ex.Message });
        }
        return Json(new DResult { success = true, msg = GetResource("创建成功") });
    }

    /// <summary>删除用户</summary>
    /// <returns></returns>
    [DisplayName("删除用户")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult MemberDelete(Int32 Id)
    {
        var model = UserE.FindByID(Id.ToInt());
        if (model.RoleID == 1)
        {
            return Json(new DResult() { success = false, msg = GetResource("超级管理员不允许被删除！") });
        }

        UserE.Delete(UserE._.ID == Id);
        UserDetail.Delete(UserDetail._.Id == Id);

        UserE.Meta.Cache.Clear("", true);
        UserDetail.Meta.Cache.Clear("", true);

        return Json(new DResult() { success = true, msg = GetResource("删除成功") });
    }
}