﻿@{
    Html.AppendTitleParts(T("服务器信息").Text);
    var dgPage = (Pek.Webs.HttpContext.Current.Request.RouteValues["controller"] + "_" + Pek.Webs.HttpContext.Current.Request.RouteValues["action"]).ToLower();
}
@using System.Diagnostics;
@using System.Runtime;
@using System.Runtime.Versioning;
@using System.Reflection;
@using DG.Web.Framework
@using DG.Webs
@using DH.Core.Webs
@using Microsoft.AspNetCore.Hosting
@using NewLife
@using NewLife.Reflection
@using Pek.Webs
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor
@inject IWebHostEnvironment Env
@{
    var asm = Assembly.GetExecutingAssembly();
    var att = asm.GetCustomAttribute<TargetFrameworkAttribute>();
    var ver = att?.FrameworkDisplayName ?? att?.FrameworkName;

    var httpContext = HttpContextAccessor.HttpContext;
    var req = httpContext.Request;
    var conn = httpContext.Connection;

    var mi = MachineInfo.Current ?? new MachineInfo();

    // GC设置
    var gc = $"IsServerGC={GCSettings.IsServerGC},LatencyMode={GCSettings.LatencyMode}";
}
<style asp-location="true">
        form.layui-form.dg-form {
            padding: 10px;
        }
        td {
            text-align: left;
            padding-left: 10px;
            padding-right: 0px;
            width: auto;
            border: 1px solid #ddd;
        }
        a {
            color: #0D93BF
        }
        #layui-layer-iframe1 {
            height: 1000px !important;
        }
        #layui-layer-iframe2 {
            height: 1000px !important;
        }
        #layui-layer-iframe3 {
            height: 1000px !important;
        }
        .hiMallDatagrid-cell {
            word-break: break-all;
            word-wrap: break-word;
            white-space: pre-wrap;
        }
    </style>
<div class="layui-fluid">
    <form class="layui-form dg-form">
        <div class="layui-form-item" style="margin-bottom: 10px;">
            <div class="layui-form">
                <table class="layui-table">

                    <thead>
                        <tr>
                            <th colspan="4">
                                @T("服务器信息")
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="name">
                                @T("应用系统：")
                            </td>
                            <td class="value">
                                &nbsp;
                                @if (this.Has((PermissionFlags)16))
                                {
                                    <a data-href="@Url.Action("Restart")" href="javascript:;" id="Restart">@T("重启应用系统")</a>
                                }
                                &nbsp;&nbsp;&nbsp;&nbsp;@req.GetRawUrl()
                            </td>
                            <td class="name">
                                @T("目录：")
                            </td>
                            <td class="value">
                                @Env.ContentRootPath
                            </td>
                        </tr>
                        <tr>
                            <td class="name">
                                @T("域名地址：")
                            </td>
                            <td class="value">
                                @{
                                    var addrLocal = conn.LocalIpAddress;
                                    var addrRemote = conn.RemoteIpAddress;
                                    if (addrLocal != null && addrLocal.IsIPv4MappedToIPv6) addrLocal = addrLocal.MapToIPv4();
                                    if (addrRemote != null && addrRemote.IsIPv4MappedToIPv6) addrRemote = addrRemote.MapToIPv4();
                                    var userHost = httpContext.GetUserHost();
                                }
                                <span title="@T("主机")">@req.Headers["Host"]</span>，
                                <span title="@T("本地")">@addrLocal:@conn.LocalPort</span>
                                &nbsp;<span title="@T("远程")">[@addrRemote:@conn.RemotePort]</span>
                                @if (addrRemote + "" != userHost)
                                {
                                    <span title="@T("真实")">&nbsp;[@httpContext.GetUserHost()]</span>
                                }
                            </td>
                            <td class="name">
                                @T("应用程序：")
                            </td>
                            <td class="value">
                                <span title="@Environment.CommandLine">@Process.GetCurrentProcess().ProcessName</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="name">
                                @T("应用域：")
                            </td>
                            <td class="value">
                                @AppDomain.CurrentDomain.FriendlyName
                                <a id="Assembly" href="javascript:;" title="@T("点击打开进程程序集列表")">@T("程序集列表")</a>
                                <a id="ProcessModules" href="javascript:;" title="@T("点击打开进程模块列表")">@T("模块列表")</a>
                                <a id="ServerVar" href="javascript:;" title="@T("点击打开服务器变量列表")">@T("服务器变量列表")</a>
                            </td>
                            <td class="name">
                                @T(".Net 版本：")
                            </td>
                            <td class="value">
                                @Environment.Version &nbsp;@ver
                            </td>
                        </tr>
                        <tr>
                            <td class="name">
                                @T("操作系统：")
                            </td>
                            <td class="value" title="@mi.Guid">
                                @mi.OSName @mi.OSVersion
                            </td>
                            <td class="name">
                                @T("机器用户：")
                            </td>
                            <td class="value" title="@mi.UUID">
                                @if (!mi.Product.IsNullOrEmpty())
                                {
                                    <apan>@mi.Product，</apan>
                                }
                                @Environment.UserName/@Environment.MachineName
                            </td>
                        </tr>
                        <tr>
                            <td class="name">
                                @T("处理器：")
                            </td>
                            <td class="value">
                                @mi.Processor，
                                @Environment.ProcessorCount
                                核心，@mi.CpuRate.ToString("p0")
                                @if (mi.Temperature > 0)
                                {
                                    <span>，@mi.Temperature ℃</span>
                                }
                            </td>
                            <td class="name">
                                @T("时间：")
                            </td>
                            @{
                                var uptime = TimeSpan.FromMilliseconds(Environment.TickCount64);
                            }
                            <td class="value" width="150px" title="@T("这里使用了服务器默认的时间格式！后面是开机时间。")">
                                @DateTimeOffset.Now，@T("开机")@(uptime.ToString(@"dd\.hh\:mm\:ss"))
                            </td>
                        </tr>
                        <tr>
                            @{ var process = Process.GetCurrentProcess();}
                            <td class="name">
                                @T("内存：")
                            </td>
                            <td class="value">
                                @T("物理：")@((mi.AvailableMemory / 1024 / 1024).ToString("n0"))M / @((mi.Memory / 1024 / 1024).ToString("n0"))M，
                                @T("工作/提交:") @((process.WorkingSet64 / 1024 / 1024).ToString("n0"))M/@((process.PrivateMemorySize64 / 1024 / 1024).ToString("n0"))M
                                <br />
                                GC: @((GC.GetTotalMemory(false) / 1024 / 1024).ToString("n0"))M
                                <a href="@Url.Action("MemoryFree")" title="@T("点击释放进程内存")">@T("释放内存")</a>
                            </td>
                            <td class="name">
                                @T("进程时间：")
                            </td>
                            <td class="value">
                                @process.TotalProcessorTime.TotalSeconds.ToString("N2")@T("秒 启动于") @process.StartTime.ToLocalTime().ToFullString()
                            </td>
                        </tr>
                        <tr>
                            <td class="name">
                                @T("Session：")
                            </td>
                            <td class="value">
                                @httpContext.Session.Keys.Count() 个
                                <a id="Session" href="javascript:;" title="@T("点击打开Session列表")">@T("Session列表")</a>
                                ，@gc
                            </td>
                            <td class="name">
                                @T("应用启动：")
                            </td>
                            <td class="value">
                                @T("启动于") @DHSetting.Current.StartTime.ToLocalTime().ToFullString()
                            </td>
                        </tr>
                    </tbody>
                </table>

                <table lay-filter="parse-table-demo">
                    <thead>
                        <tr>
                            <th style="text-align:center;padding-left:0px;padding-right:0px;width:150px;border:1px solid #ddd;">@T("名称")</th>
                            <th style="text-align:center;padding-left:0px;padding-right:0px;width:150px;border:1px solid #ddd;">@T("标题")</th>
                            <th style="text-align:center;padding-left:0px;padding-right:0px;width:135px;border:1px solid #ddd;">@T("文件版本")</th>
                            <th style="text-align:center;padding-left:0px;padding-right:0px;width:130px;border:1px solid #ddd;">@T("内部版本")</th>
                            <th style="text-align:center;padding-left:0px;padding-right:0px;width:80px;border:1px solid #ddd;">@T("编译时间")</th>
                            <th style="text-align:center;padding-left:0px;padding-right:0px;width:auto;border:1px solid #ddd;">@T("描述")</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (AssemblyX item in ViewBag.MyAsms)
                        {
                            <tr>
                                <td style="width:auto;"><div class="hiMallDatagrid-cell ">@item.Name</div></td>
                                <td style="width:auto;"><div class="hiMallDatagrid-cell ">@item.Title</div></td>
                                <td style="width:auto;"><div class="hiMallDatagrid-cell ">@item.FileVersion</div></td>
                                <td style="width:auto;"><div class="hiMallDatagrid-cell ">@item.Version</div></td>
                                <td style="width:auto;"><div class="hiMallDatagrid-cell " style="width: 80px;">@(item.Compile.Year <= 2000 ? "" : item.Compile.ToFullString())</div></td>
                                <td style="width:auto;"><div class="hiMallDatagrid-cell ">@item.Description</div></td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
       
    </form>
    <div class="table-body">

    </div>
</div>
<script asp-location="Footer">
     $(function () {
        $('#Restart').click(function () {
            //$.dialog.confirm('仅重启本应用进程，而不是操作系统！<br/>确认重启？', function () {
            //    var loading = showLoading();
            //    $.get("./Restart", {}, function (result) {
            //        if (result.code == 0) {
            //            loading.close();
            //            window.setTimeout(function () {
            //                location.reload()
            //            }, result.time * 1000)
            //        }
            //    })
            //});

            var mymessage = confirm("@T("仅重启本应用进程，而不是操作系统！确认重启？")");
            if (mymessage == true) {
                var loading = showLoading();
                $.get("@Url.Action("Restart")", {}, function (result) {
                    if (result.code == 0) {
                        loading.close();
                        window.setTimeout(function () {
                            location.reload()
                        }, result.time * 1000)
                    }
                })
            }


        })

        $("#Assembly").click(function () {
            //弹出即全屏
            var index = layer.open({
                title: "@T("程序集列表")",
                type: 2,
                content: '@Url.Action("Index", new{ id = "Assembly" })',
                area: ['320px', '195px'],
                //maxmin: true
            });
            layer.full(index);
        })

        $("#ProcessModules").click(function () {
            //弹出即全屏
            var index = layer.open({
                title: "@T("模块列表")",
                type: 2,
                content: '@Url.Action("Index", new{ id = "ProcessModules" })',
                area: ['320px', '195px'],
                //maxmin: true
            });
            layer.full(index);
        })

        $("#ServerVar").click(function () {
            //弹出即全屏
            var index = layer.open({
                title: "@T("服务器变量列表")",
                type: 2,
                content: '@Url.Action("Index", new{ id = "ServerVar" })',
                area: ['320px', '195px'],
                //maxmin: true
            });
            layer.full(index);
        })

        $("#Session").click(function () {
            //弹出即全屏
            var index = layer.open({
                title: "@T("Session列表")",
                type: 2,
                content: '@Url.Action("Index", new{ id = "Session" })',
                //area: ['320px', '195px'],
                maxmin: true
            });
            layer.full(index);
        })

        function showLoading(msg, delay) {
            /// <param name="msg" type="String">待显示的文本,非必填</param>
            /// <param name="delay" type="Int">延时显示的毫秒数，默认100毫秒显示,非必填</param>
            if (!delay)
                delay = 100;
            var loading = $('<div class="ajax-loading" style="display:none"><table height="100%" width="100%"><tr><td align="center"><p>' + (msg ? msg : '') + '</p></td></tr></table></div>');
            loading.appendTo('body');
            var s = setTimeout(function () {
                if ($(".ajax-loading").length > 0) {
                    loading.show();
                    $('.container,.login-box').addClass('blur');
                }
            }, delay);
            return {
                close: function ()
                {
                    clearTimeout(s);
                    loading.remove();
                    $('.container,.login-box').removeClass('blur');
                }
            }

        }
    })

</script>