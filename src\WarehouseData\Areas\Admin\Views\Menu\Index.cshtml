﻿@{
    Html.AppendTitleParts(T("菜单管理").Text);
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }
</style>

<script asp-location="Head">
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<div class="layui-card">
    <div class="layui-card-header">@T("菜单管理")</div>
    <div class="layui-card-body">
        <table class="layui-hide" id="power-table" lay-filter="power-table"></table>
    </div>
</div>

<script type="text/html" id="icon">
    <i class="{{d.icon}}"></i>
</script>

<script type="text/html" id="power-toolbar">
    @* <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
            <i class="layui-icon layui-icon-add-1"></i>
            @T("新增")
        </button> *@
    @if (this.Has((PermissionFlags)8)){
    <button class="pear-btn pear-btn-danger pear-btn-md" lay-event="batchRemove">
        <i class="layui-icon layui-icon-delete"></i>
        @T("删除")
    </button>
    }
    <button class="pear-btn pear-btn-success pear-btn-md" lay-event="expandAll">
        <i class="layui-icon layui-icon-spread-left"></i>
        @T("展开")
    </button>
    <button class="pear-btn pear-btn-success pear-btn-md" lay-event="foldAll">
        <i class="layui-icon layui-icon-shrink-right"></i>
        @T("折叠")
    </button>
    <button class="pear-btn pear-btn-success pear-btn-md" lay-event="reload">
        <i class="layui-icon layui-icon-refresh"></i>
        @T("重载")
    </button>
</script>

<script type="text/html" id="power-type">
    {{#if (d.powerType == 0) { }}
    <span>@T("目录")</span>
    {{# }else if(d.powerType == 1){ }}
    <span>@T("菜单")</span>
    {{# }else if(d.powerType == 2){ }}
    <span>@T("按钮")</span>
    {{# } }}
</script>

<script type="text/html" id="power-enable">
    @if (this.Has((PermissionFlags)4)){
    <input type="checkbox" name="enable" data-id="{{d.id}}" lay-skin="switch" lay-text="@T("是")|@T("否")" lay-filter="statusedit" {{ d.enable == true ? 'checked' : ''}}>
    }else{
    <input type="checkbox" name="enable" data-id="{{d.id}}" lay-skin="switch" lay-text="@T("是")|@T("否")" lay-filter="statusedit" {{ d.enable == true ? 'checked' : ''}} disabled>
    }
</script>

<script type="text/html" id="power-bar">
    @if (this.Has((PermissionFlags)4)){
    <button class="pear-btn pear-btn-primary pear-btn-sm" lay-event="edit"><i class="layui-icon layui-icon-edit"></i></button>
    }
    @if (this.Has((PermissionFlags)8)){
    <button class="pear-btn pear-btn-danger pear-btn-sm" lay-event="remove"><i class="layui-icon layui-icon-delete"></i></button>
    }
</script>

<script asp-location="Footer">
    layui.use(['table', 'abp', 'dg', 'form', 'treetable', "common"], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let treetable = layui.treetable;
        var abp = layui.abp;
        var dg = layui.dg;
        var os = layui.common;

        treetable.render({
            treeColIndex: 1,
            treeSpid: 0,
            treeIdName: 'id',
            treePidName: 'parentId',
            skin: 'line',
            treeDefaultClose: true,
            toolbar: '#power-toolbar',
            elem: '#power-table',
            url: '@Url.Action("GetMenuList")',
            page: false,
            cols: [
                [
                    { type: 'checkbox' },
                    { field: 'name', minWidth: 200, title: '@T("权限名称")' },
                    { field: 'icon', title: '@T("图标")', templet: '#icon' },
                    { field: 'powerType', title: '@T("权限类型")', templet: '#power-type' },
                    { field: 'enable', title: '@T("是否可见")', templet: '#power-enable' },
                    { field: 'sort', title: '@T("排序")' },
                    { title: '@T("操作")', templet: '#power-bar', width: 150, align: 'center' }
                ]
            ]
        });

        table.on('toolbar(power-table)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                window.refresh();
            } else if (obj.event === 'batchRemove') {
                window.batchRemove(obj);
            } else if (obj.event === 'expandAll') {
                treetable.expandAll("#power-table");
            } else if (obj.event === 'foldAll') {
                treetable.foldAll("#power-table");
            } else if (obj.event === 'reload') {
                treetable.reload("#power-table");
            }
        });

        table.on('tool(power-table)', function (obj) {
            if (obj.event === 'remove') {
                window.remove(obj);
            } else if (obj.event === 'edit') {
                window.edit(obj);
            }
        })

        //监听开关事件
        form.on('switch(statusedit)', function (data) {
            //开关是否开启，true或者false
            var checked = data.elem.checked;
            //获取所属属性值
            var id = data.elem.attributes['data-id'].nodeValue;
            $.post('@Url.Action("ModifyState")', { id: id, status: checked }, function (res) {
                if (res.success) {
                    abp.notify.success('状态调整成功!');
                }
                else {
                    abp.notify.error(res.msg);
                }
            });
        });

        window.saveCallback = function (data) {
            parent.layer.close(data.index);
            abp.notify.success(data.msg);
            treetable.reload("#power-table");
        }

        window.edit = function (obj) {
            var data = obj.data;
            parent.layer.open({
                type: 2,
                title: "@T("编辑菜单")",
                content: "@Url.Action("EditMenu")" + abp.utils.formatString("?id={0}", data.id),
                area: ["510px", "436px"],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    parent.window['layui-layer-iframe' + index].submitForm();
                }
            });
        }

        window.remove = function (obj) {
            var data = obj.data;
            parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.post('@Url.Action("Delete")', { Id: data.id }, function (data) {
                    layer.close(loading);
                    if (data.success) {
                        abp.notify.success(data.msg);
                        treetable.reload("#power-table");
                    } else {
                        abp.notify.warn(data.msg);
                    }
                });
                parent.layer.close(index);
            });
        }

    });
</script>