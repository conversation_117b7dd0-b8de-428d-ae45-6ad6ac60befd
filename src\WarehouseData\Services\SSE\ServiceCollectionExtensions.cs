﻿namespace HlktechIoT.Services.SSE;

internal static class ServiceCollectionExtensions {
    #region Fields
    private const string NOTIFICATIONS_SERVICE_TYPE_CONFIGURATION_KEY = "NotificationsService";
    private const string NOTIFICATIONS_SERVICE_TYPE_LOCAL = "Local";
    private const string NOTIFICATIONS_SERVICE_TYPE_REDIS = "Redis";
    #endregion

    #region Methods
    public static IServiceCollection AddNotificationsService(this IServiceCollection services, IConfiguration configuration)
    {
        var notificationsServiceType = configuration.GetValue(NOTIFICATIONS_SERVICE_TYPE_CONFIGURATION_KEY, NOTIFICATIONS_SERVICE_TYPE_LOCAL);

        if (notificationsServiceType?.Equals(NOTIFICATIONS_SERVICE_TYPE_LOCAL, StringComparison.InvariantCultureIgnoreCase) == true)
        {
            services.AddTransient<INotificationsService, LocalNotificationsService>();
        }
        else if (notificationsServiceType?.Equals(NOTIFICATIONS_SERVICE_TYPE_REDIS, StringComparison.InvariantCultureIgnoreCase) == true)
        {
            services.AddSingleton<INotificationsService, RedisNotificationsService>();
        }
        else
        {
            throw new NotSupportedException($"Not supported {nameof(INotificationsService)} type.");
        }

        return services;
    }
    #endregion
}