﻿@{


    Html.AppendTitleParts(T("编辑合作公司").Text);

    Html.AppendCssFileParts(ResourceLocation.Head, "~/libs/pear/css/module/dtree/dtree.css");
    Html.AppendCssFileParts(ResourceLocation.Head, "~/libs/pear/css/module/dtree/font/dtreefont.css");
}

<style asp-location="true">
    .sex {
        min-width: 30px;
        /* margin-top:10px; */
        margin: 10px 0px 0px -20px;
    }
</style>

<div class="layui-tab layui-tab-brief" lay-filter="user-tab">
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show">
            <form class="layui-form" lay-filter="user-form" style="padding: 10px 0 0 0;" autocomplete="off">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">@T("消息标题")</label>
                        <div class="layui-input-inline">
                            <input type="text"  lay-verType="tips" autocomplete="off" class="layui-input" style="width:150%" value="@Model.Title" disabled>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">@T("发送者")</label>
                        <div class="layui-input-inline">
                            <input type="text" lay-verType="tips" autocomplete="off" class="layui-input" style="width:150%" value="@Model.CreateUser" disabled>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">@T("发送时间")</label>
                        <div class="layui-input-inline">
                            <input type="text" lay-verType="tips" autocomplete="off" class="layui-input" style="width:150%" value="@Model.CreateTime" disabled>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">@T("消息内容")</label>
                        <div class="layui-input-inline">
                            <textarea type="text" autocomplete="off" class="layui-input" style="width:150%;height:150px" disabled>@Model.Content</textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>