﻿using DG.Web.Framework;

using DH.Core.Infrastructure;
using DH.Entity;

using HlktechIoT.Dto;
using HlktechIoT.Dto.Export;
using HlktechIoT.Entity;
using HlktechIoT.Services.SSE;

using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

using Microsoft.AspNetCore.Mvc;

using MiniExcelLibs;

using NewLife;
using NewLife.Caching;
using NewLife.Data;
using NewLife.Serialization;

using Pek;
using Pek.Helpers;
using Pek.Iot;
using Pek.Models;

using System.Collections.Concurrent;
using System.ComponentModel;
using System.Data;
using System.Text.Json;
using System.Text.RegularExpressions;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Production.Controllers;

/// <summary>生产订单</summary>
[DisplayName("生产订单")]
[Description("生产订单")]
[ProductionArea]
[DHMenu(75, ParentMenuName = "ProductionsManager", ParentMenuDisplayName = "生产管理", ParentMenuUrl = "", ParentMenuOrder = 30, ParentIcon = "layui-icon-component", CurrentMenuUrl = "~/{area}/ManufacturerOrder", CurrentMenuName = "ManufacturerOrderList", LastUpdate = "20250521")]
public class ManufacturerOrderController : BaseAdminControllerX {

    /// <summary>
    /// 生产订单列表
    /// </summary>
    /// <returns></returns>
    [DisplayName("列表")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 生产订单列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="limit">条数</param>
    /// <param name="orderId">订单号</param>
    /// <param name="companyId">合作公司id</param>
    /// <param name="productTypeId">产品型号id</param>
    /// <param name="start">开始时间</param>
    /// <param name="end">结束时间</param>
    /// <param name="status">审核状态</param>
    /// <returns></returns>
    [DisplayName("列表")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult GetList(Int32 page, Int32 limit, String orderId, Int32 companyId, Int32 productTypeId, DateTime start, DateTime end, Int32 status = -1)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true,
        };

        var UId = -1;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",001,")))
        {
            UId = ManageProvider.User?.ID ?? 0;
        }

        var data = ProductOrders.Search(orderId, companyId, productTypeId, start, end, "", status, UId, pages).Select(e => new
        {
            Id = e.Id.SafeString(),
            e.OrderId,
            e.StartTime,
            e.EndTime,
            e.Status,
            e.Quantity,
            Company = e.Company?.Name,
            ProductType = ProductType.FindById(e.ProductTypeId)?.Name,
            e.ProductSupplementalQuantity,
            e.FirmwareName,
            ProductProjectName = e.ProductProject?.Name,
            e.CreateTime
        });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });

    }

    /// <summary>
    /// 搜索合作公司
    /// </summary>
    /// <param name="keyword">关键字</param>
    /// <param name="page">页码</param>
    /// <param name="Id">合作公司id</param>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("搜索公司")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult SearchCompany(String keyword, Int32 page, Int32 Id)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = Company._.Id,
            Desc = true,
        };

        res.data = Company.Search(keyword, true, pages).Select(e =>
        {
            return new Xmselect<Int32>
            {
                name = e.Name,
                value = e.Id
            };
        });

        res.success = true;

        var model = Company.FindById(Id);

        if (model == null)
        {
            res.extdata = new { pages.PageCount };
        }
        else
        {
            res.extdata = new { pages.PageCount, data = new List<NameValueL<Int32?>>() { new() { name = model.Name, value = model.Id } } };
        }

        return Json(res);
    }

    /// <summary>
    /// 搜索产品型号
    /// </summary>
    /// <param name="keyword">关键字</param>
    /// <param name="page">页码</param>
    /// <param name="Id">产品型号id</param>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("搜索产品型号")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult SearchProductType(String keyword, Int32 page, Int32 Id)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = ProductType._.Id,
            Desc = true,
        };

        res.data = ProductType.Search(0, keyword, true, DateTime.MinValue, DateTime.MinValue, "", pages).Select(e =>
        {
            return new
            {
                name = e.Name,
                value = e.Id,
                e.NeedSn,
                e.NeedMac,
                e.NeedMac1,
                e.PCBCycle,
                e.ShieldCycle,
                e.MainChipCycle,
            };
        });

        res.success = true;

        var model = ProductType.FindById(Id);

        if (model == null)
        {
            res.extdata = new { pages.PageCount };
        }
        else
        {
            res.extdata = new { pages.PageCount, data = new List<object>() { new { name = model.Name, value = model.Id, NeedSn = model.NeedSn, NeedMac = model.NeedMac, model.NeedMac1,model.PCBCycle,model.ShieldCycle,model.MainChipCycle } } };
        }

        return Json(res);
    }

    /// <summary>
    /// 搜索产品项目
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("搜索产品项目")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult SearchProductProject(String keyword, Int32 page, Int32 Id)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = ProductType._.Id,
            Desc = true,
        };

        res.data = ProductProject.Search(2, null, DateTime.MinValue, DateTime.MinValue, keyword, pages).Select(e =>
        {
            return new
            {
                name = e.ProjectNo.IsNullOrWhiteSpace() ? e.Name : e.Name+$"({e.ProjectNo})",
                value = e.Id,
                NeedSn = e.ProductType?.NeedSn,
                NeedMac = e.ProductType?.NeedMac,
                NeedMac1 = e.ProductType?.NeedMac1,
                PCBCycle = e.ProductType?.PCBCycle,
                ShieldCycle = e.ProductType?.ShieldCycle,
                MainChipCycle = e.ProductType?.MainChipCycle,
            };
        });

        res.success = true;

        var model = ProductProject.FindById(Id);

        if (model == null)
        {
            res.extdata = new { pages.PageCount };
        }
        else
        {
            res.extdata = new { pages.PageCount, data = new List<object>() { new { name = model.Name, value = model.Id, NeedSn = model.ProductType?.NeedSn, NeedMac = model.ProductType?.NeedMac, NeedMac1 = model.ProductType?.NeedMac1, PCBCycle = model.ProductType?.PCBCycle, ShieldCycle = model.ProductType?.ShieldCycle, MainChipCycle = model.ProductType?.MainChipCycle } } };
        }

        return Json(res);
    }

    /// <summary>
    /// 添加生产订单
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult Add()
    {
        return View();
    }

    /// <summary>
    /// 添加生产订单
    /// </summary>
    /// <param name="OrderId">订单号</param>
    /// <param name="CompanyId">合作公司id</param>
    /// <param name="Quantity">数量</param>
    /// <param name="ProductProjectId">产品项目id</param>
    /// <param name="Macranges">Mac地址集合</param>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("新增")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult Add(String OrderId, Int32 CompanyId, Int32 Quantity, Int32 ProductProjectId, List<MacRangeModel> Macranges)
    {
        var res = new DResult();
        if (OrderId.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("订单号不能为空");
            return Json(res);
        }
        if (CompanyId <= 0)
        {
            res.msg = GetResource("所属公司不能为空");
            return Json(res);
        }
        if (ProductProjectId <= 0)
        {
            res.msg = GetResource("产品项目不能为空");
            return Json(res);
        }
        var modelProductProject = ProductProject.FindById(ProductProjectId);
        if (modelProductProject == null)
        {
            res.msg = GetResource("产品项目不存在");
            return Json(res);
        }
        var modelProductType = ProductType.FindById(modelProductProject.ProductTypeId);
        if (modelProductType == null)
        {
            res.msg = GetResource("产品项目关联的产品类型不存在");
            return Json(res);
        }
        var modelSaasProductOrder = ProductOrders.FindByOrderId(OrderId);
        if (modelSaasProductOrder != null)
        {
            res.msg = GetResource("订单号已存在");
            return Json(res);
        }
        try
        {
            long macrangeCount = 0;
            var ordermacranges = ProductOrders.GetMacList(0);//获取未审核的订单mac地址
            var _macranges = new List<MacRangeModel>();
            foreach (var item in Macranges)
            {
                if ((string.IsNullOrWhiteSpace(item.Start) && !string.IsNullOrWhiteSpace(item.End)) ||
                    (!string.IsNullOrWhiteSpace(item.Start) && string.IsNullOrWhiteSpace(item.End)))
                {
                    res.msg = GetResource("请填写起始和结束MAC地址");
                    return Json(res);
                }
                if (string.IsNullOrWhiteSpace(item.Start) || string.IsNullOrWhiteSpace(item.End))
                {
                    continue;
                }
                var curmacranges = ordermacranges.Where(x =>
                                (MacHelper.ParseMacToLong(x.Start) <= MacHelper.ParseMacToLong(item.Start)
                                 && MacHelper.ParseMacToLong(x.End) >= MacHelper.ParseMacToLong(item.Start))
                                 || (MacHelper.ParseMacToLong(x.Start) <= MacHelper.ParseMacToLong(item.End)
                                 && MacHelper.ParseMacToLong(x.End) >= MacHelper.ParseMacToLong(item.End))).ToList();
                if (curmacranges.Count > 0)
                {
                    res.msg = GetResource("MAC 地址范围重复");
                    return Json(res);
                }

                var list = DeviceMacs.FindAll(DeviceMacs._.Mac == item.Start);
                if (list.Any())
                {
                    res.msg = GetResource("已存在Mac地址")+"：["+item.Start+"]";
                    return Json(res);
                }
                ordermacranges.Add(item);//添加到缓存用于判断
                _macranges.Add(item);//用于添加到表
                macrangeCount += MacHelper.GetMacAddressCount(item.Start, item.End);
            }
            string macJson = JsonSerializer.Serialize(_macranges);


            var stateInfo = new
            {
                modelProductType.NeedSn,
                modelProductType.NeedMac,
                modelProductType.NeedMac1,
                modelProductType.PCBCycle,
                modelProductType.ShieldCycle,
                modelProductType.MainChipCycle,
            };

            var stateJson = JsonSerializer.Serialize(stateInfo);

            modelSaasProductOrder = new ProductOrders()
            {
                OrderId = OrderId,
                CompanyId = CompanyId,
                Quantity = Quantity,
                ProductTypeId = modelProductType.Id,
                ProductProjectId = modelProductProject.Id,
                MacRange = macJson,
                DataInfo = stateJson,
                MacRangeCount = macrangeCount
            };
            modelSaasProductOrder.Insert();

            res.success = true;
            res.msg = GetResource("添加成功");
            return Json(res);
        }
        catch (Exception ex)
        {
            res.success = false;
            res.msg = ex.Message;
            return Json(res);
        }
    }

    /// <summary>
    /// 删除生产订单
    /// </summary>
    /// <param name="Id">订单id</param>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("删除")]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult Delete(Int64 Id)
    {
        var res = new DResult();

        var model = ProductOrders.FindById(Id);

        if (model == null)
        {
            res.msg = GetResource("订单不存在");
            return Json(res);
        }

        if (model.Status > 1)
        {
            res.msg = GetResource("非法操作");
            return Json(res);
        }

        model.Delete();

        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 编辑生产订单
    /// </summary>
    /// <param name="Id">订单id</param>
    /// <returns></returns>
    [DisplayName("编辑")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Edit(Int64 Id)
    {
        var modelSaasProductOrder = ProductOrders.FindById(Id);
        if (modelSaasProductOrder == null)
        {
            return Content(GetResource("生产订单不存在"));
        }

        var stateInfo = new ProductOrderStateDto();

        if (!modelSaasProductOrder.DataInfo.IsNullOrWhiteSpace())
        {
            stateInfo = JsonSerializer.Deserialize<ProductOrderStateDto>(modelSaasProductOrder.DataInfo);
        }
        else
        {
            stateInfo = new ProductOrderStateDto
            {
                NeedSn = modelSaasProductOrder.ProductType?.NeedSn ?? false,
                NeedMac = modelSaasProductOrder.ProductType?.NeedMac ?? false,
                NeedMac1 = modelSaasProductOrder.ProductType?.NeedMac1 ?? false,
                PCBCycle = modelSaasProductOrder.ProductType?.PCBCycle ?? false,
                ShieldCycle = modelSaasProductOrder.ProductType?.ShieldCycle ?? false,
                MainChipCycle = modelSaasProductOrder.ProductType?.MainChipCycle ?? false,
            };
        }

        ViewBag.StateInfo = stateInfo;
        return View(modelSaasProductOrder);
    }

    /// <summary>
    /// 编辑生产订单
    /// </summary>
    /// <param name="Id">订单id</param>
    /// <param name="OrderId">订单号</param>
    /// <param name="CompanyId">合作公司id</param>
    /// <param name="Quantity">数量</param>
    /// <param name="ProductProjectId">产品项目id</param>
    /// <param name="Macranges">Mac地址集合</param>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("编辑")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Edit(Int64 Id, String OrderId, Int32 CompanyId, Int32 Quantity, Int32 ProductProjectId, List<MacRangeModel> Macranges)
    {
        var res = new DResult();
        if (OrderId.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("订单号不能为空");
            return Json(res);
        }
        if (CompanyId <= 0)
        {
            res.msg = GetResource("所属公司不能为空");
            return Json(res);
        }
        if (ProductProjectId <= 0)
        {
            res.msg = GetResource("产品项目不能为空");
            return Json(res);
        }
        var modelProductProject = ProductProject.FindById(ProductProjectId);
        if (modelProductProject == null)
        {
            res.msg = GetResource("产品项目不存在");
            return Json(res);
        }
        var modelProductType = ProductType.FindById(modelProductProject.ProductTypeId);
        if (modelProductType == null)
        {
            res.msg = GetResource("产品项目关联的产品类型不存在");
            return Json(res);
        }
        var modelSaasProductOrderExit = ProductOrders.FindByOrderId(OrderId);
        if (modelSaasProductOrderExit != null && modelSaasProductOrderExit.Id != Id)
        {
            res.msg = GetResource("订单号已存在");
            return Json(res);
        }
        var modelSaasProductOrder = ProductOrders.FindById(Id);
        if (modelSaasProductOrder == null)
        {
            res.msg = GetResource("生产订单不存在");
            return Json(res);
        }
        if (modelSaasProductOrder.Status > 1)
        {
            res.msg = GetResource("非法操作");
            return Json(res);
        }
        try
        {
            long macrangeCount = 0;
            var ordermacranges = ProductOrders.GetMacList(Id);//获取未审核的订单mac地址
            var _macranges = new List<MacRangeModel>();
            foreach (var item in Macranges)
            {
                if ((string.IsNullOrWhiteSpace(item.Start) && !string.IsNullOrWhiteSpace(item.End)) ||
                    (!string.IsNullOrWhiteSpace(item.Start) && string.IsNullOrWhiteSpace(item.End)))
                {
                    res.msg = GetResource("请填写起始和结束MAC地址");
                    return Json(res);
                }
                if (string.IsNullOrWhiteSpace(item.Start) || string.IsNullOrWhiteSpace(item.End))
                {
                    continue;
                }
                var curmacranges = ordermacranges.Where(x => 
                                (MacHelper.ParseMacToLong(x.Start) <= MacHelper.ParseMacToLong(item.Start)
                                 && MacHelper.ParseMacToLong(x.End) >= MacHelper.ParseMacToLong(item.Start))
                                 ||(MacHelper.ParseMacToLong(x.Start) <= MacHelper.ParseMacToLong(item.End)
                                 && MacHelper.ParseMacToLong(x.End) >= MacHelper.ParseMacToLong(item.End))).ToList();
                if (curmacranges.Count>0)
                {
                    res.msg = GetResource("MAC 地址范围重复");
                    return Json(res);
                }
                var list = DeviceMacs.FindAll(DeviceMacs._.Mac == item.Start);
                if (list.Any())
                {
                    res.msg = GetResource("已存在Mac地址")+"：["+item.Start+"]";
                    return Json(res);
                }
                ordermacranges.Add(item);//添加到缓存用于判断
                _macranges.Add(item);//用于添加到表
                macrangeCount += MacHelper.GetMacAddressCount(item.Start, item.End);
            }
            string macJson = JsonSerializer.Serialize(_macranges);

            var stateInfo = new
            {
                modelProductType.NeedSn,
                modelProductType.NeedMac,
                modelProductType.NeedMac1,
                modelProductType.PCBCycle,
                modelProductType.ShieldCycle,
                modelProductType.MainChipCycle,
            };

            var stateJson = JsonSerializer.Serialize(stateInfo);

            modelSaasProductOrder.OrderId = OrderId;
            modelSaasProductOrder.CompanyId = CompanyId;
            modelSaasProductOrder.Quantity = Quantity;
            modelSaasProductOrder.ProductTypeId = modelProductType.Id;
            modelSaasProductOrder.ProductProjectId = modelProductProject.Id;
            modelSaasProductOrder.DataInfo = stateJson;
            modelSaasProductOrder.MacRange = macJson;
            modelSaasProductOrder.MacRangeCount = macrangeCount;
            modelSaasProductOrder.Update();
            res.success = true;
            res.msg = GetResource("编辑成功");
            return Json(res);
        }
        catch (Exception ex)
        {
            res.success = false;
            res.msg = ex.Message;
            return Json(res);
        }


    }

    /// <summary>
    /// 生产订单日志
    /// </summary>
    /// <param name="Id">订单id</param>
    /// <returns></returns>
    [DisplayName("日志")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult LogIndex(Int64 Id)
    {
        var modelSaasProductOrder = ProductOrders.FindById(Id);
        if (modelSaasProductOrder == null)
        {
            return Content(GetResource("生产订单不存在"));
        }
        return View(modelSaasProductOrder);
    }

    //[DisplayName("日志")]
    //[EntityAuthorize(PermissionFlags.Detail)]
    //public IActionResult GetLogList(Int32 page, Int32 limit, String orderId, String mac, DateTime start, DateTime end)
    //{
    //    var pages = new PageParameter
    //    {
    //        PageIndex = page,
    //        PageSize = limit,
    //        RetrieveTotalCount = true,
    //        Sort = "Id",
    //        Desc = true,
    //    };

    //    var data = SaasProductOrderLogs.Search(orderId, 0, mac, DateTime.MinValue, start, end, "", pages).Select(e => new
    //    {
    //        Id = e.Id.SafeString(),
    //        e.Name,
    //        e.Mac,
    //        e.FileUrl,
    //        e.Node,
    //        Status = e.Status == 0 ? GetResource("测试失败") : GetResource("测试成功"),
    //        e.Msg,
    //        e.Version,
    //        e.WorkId,
    //        e.CreateTime
    //    });

    //    return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });

    //}

    ///// <summary>
    ///// 删除日志
    ///// </summary>
    ///// <param name="Id"></param>
    ///// <returns></returns>
    //[HttpPost]
    //[DisplayName("删除")]
    //[EntityAuthorize(PermissionFlags.Delete)]
    //public IActionResult LogDelete(Int64 Id)
    //{
    //    var res = new DResult();

    //    var modelSaasProductOrderLogs = SaasProductOrderLogs.FindById(Id);
    //    modelSaasProductOrderLogs?.Delete();

    //    res.success = true;
    //    res.msg = GetResource("删除成功");
    //    return Json(res);
    //}

    /// <summary>
    /// 审核生产订单
    /// </summary>
    /// <param name="Id">订单id</param>
    /// <returns></returns>
    [DisplayName("审核")]
    [EntityAuthorize((PermissionFlags)32)]
    public IActionResult Audit(Int64 Id)
    {
        var model = ProductOrders.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("生产订单不存在"));
        }
        return View(model);
    }

    /// <summary>
    /// 审核生产订单
    /// </summary>
    /// <param name="Id">订单id</param>
    /// <param name="Status">审核状态</param>
    /// <param name="Remark">备注</param>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("审核")]
    [EntityAuthorize((PermissionFlags)32)]
    public IActionResult Audit(Int64 Id, Int32 Status, String Remark)
    {
        var res = new DResult();

        var model = ProductOrders.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("生产订单信息不存在");
            return Json(res);
        }

        if (model.Status > 1)
        {
            res.msg = GetResource("非法操作");
            return Json(res);
        }

        if (model.ProductType?.NeedSn != true && model.ProductType?.NeedMac != true && model.ProductType?.NeedMac1 != true)
        {
            res.msg = GetResource("参数非法");
            return Json(res);
        }

        // 获取Mac地址范围内所有的Mac地址
        var listMac = new List<String>();
        var queue = new ConcurrentQueue<String>();

        if (model.ProductType?.NeedMac == true || model.ProductType?.NeedMac1 == true)
        {
            var macList = model.MacRange?.ToJsonEntity<List<MacRangeModel>>() ?? [];

            foreach (var item in macList)
            {
                if (item.Start.IsNullOrWhiteSpace() || item.End.IsNullOrWhiteSpace()) continue;
                listMac.AddRange(MacHelper.GetMacAddresses(item.Start, item.End));
            }

            var count = model.Quantity;
            if (model.ProductType?.NeedMac == true && model.ProductType?.NeedMac1 == true)
            {
                count *= 2;
            }

            if (listMac.Count < count)
            {
                res.msg = GetResource("Mac地址范围内的Mac数量不足，请检查Mac地址范围设置！");
                return Json(res);
            }

            for (var i = 0; i < count; i++)
            {
                queue.Enqueue(listMac[i]);
            }
        }

        model.Status = Status;
        model.Remark = Remark;
        model.AuditTime = DateTime.Now;
        model.StartTime = DateTime.Now;
        model.Update();

        Task.Run(() =>
        {
            var cacheProvider = EngineContext.Current.Resolve<ICacheProvider>();
            model.CreateSnMac(cacheProvider, queue);
        });

        res.success = true;
        res.msg = GetResource("审核完成");
        return Json(res);
    }

    /// <summary>
    /// 导出生产订单sn/mac/mac1
    /// </summary>
    /// <param name="ids">订单id集合</param>
    /// <returns></returns>
    [DisplayName("导出")]
    [EntityAuthorize((PermissionFlags)64)]
    public async Task<IActionResult> ExportAll(String ids)
    {
        IExporter exporter = new ExcelExporter();
        List<ManufacturerOrderExport> list = new();
        var UId = -1;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",001,")))
        {
            UId = ManageProvider.User?.ID ?? 0;
        }
        var data = ProductSnMac.FindAllByProductOrderIds(ids, UId);
        foreach (var item in data)
        {
            list.Add(new ManufacturerOrderExport
            {
                SN = item.Sn,
                Mac = item.Mac,
                Mac1 = item.Mac1,
                OrderNum = item.OrderId,
            });
        }
        var result = await exporter.ExportAsByteArray(list);
        return File(result, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"ExportOrder{DateTime.Now:yyyyMMddhhmm}.xlsx");
    }

    /// <summary>
    /// 生产订单sn列表
    /// </summary>
    /// <param name="Id">订单id</param>
    /// <returns></returns>
    [DisplayName("SN列表")]
    [EntityAuthorize((PermissionFlags)128)]
    public IActionResult SnView(Int64 Id)
    {
        var modelSaasProductOrder = ProductOrders.FindById(Id);
        var modelProductSupplementalOrders = ProductSupplementalOrders.FindById(Id);
        if (modelSaasProductOrder == null && modelProductSupplementalOrders == null)
        {
            return Content(GetResource("生产订单不存在"));
        }
        if (modelSaasProductOrder != null)
            return View(modelSaasProductOrder);
        else
            return View(modelProductSupplementalOrders);
    }

    /// <summary>
    /// 生产订单sn列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="limit">条数</param>
    /// <param name="start">开始时间</param>
    /// <param name="end">结束时间</param>
    /// <param name="orderId">订单号</param>
    /// <param name="key">关键字</param>
    /// <param name="isConsumed">是否消费</param>
    /// <param name="isConfirmConsumed">是否确认消费</param>
    /// <returns></returns>
    [DisplayName("SN列表")]
    [EntityAuthorize((PermissionFlags)128)]
    public IActionResult Snlist(Int32 page, Int32 limit, DateTime start, DateTime end, Int64 orderId, String key,Int32 isConsumed = -1, Int32 isConfirmConsumed = -1)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true,
        };

        var data = ProductSnMac.Search(orderId, "", "", "", DateTime.MinValue, start, end, key, isConsumed, isConfirmConsumed, pages).Select(e => new
        {
            Id = e.Id.SafeString(),
            e.Sn,
            e.Mac,
            e.Mac1,
            e.IsRepetition,
            e.IsRepetitionDate,
            e.IsConsumed,
            e.ConsumedTime,
            e.PCBCycle,
            e.ShieldCycle,
            e.MainChipCycle,
            e.IsConfirmConsumed,
            e.ConfirmConsumedTime,
            e.CreateTime,
            e.DataInfo,
            e.FilePath,
            e.Content,
            e.IsValidate,
            e.ShorUrl,
        });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 设置sn
    /// </summary>
    /// <param name="Id">sn列表id</param>
    /// <returns></returns>
    [DisplayName("设置SN")]
    [EntityAuthorize((PermissionFlags)256)]
    public IActionResult SettingSnRepeat(Int64 Id)
    {
        var model = ProductSnMac.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("生产SN不存在"));
        }
        return View(model);
    }

    /// <summary>
    /// 设置sn
    /// </summary>
    /// <param name="Id">sn列表id</param>
    /// <param name="IsRepetition">是否重复</param>
    /// <param name="IsRepetitionDate">重复截止时间</param>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("设置SN")]
    [EntityAuthorize((PermissionFlags)256)]
    public IActionResult SettingSnRepeat(Int64 Id, Boolean IsRepetition, DateTime IsRepetitionDate)
    {
        var res = new DResult();
        if (IsRepetition && IsRepetitionDate == DateTime.MinValue)
        {
            res.msg = GetResource("重复时间不能为空");
            return Json(res);
        }
        var model = ProductSnMac.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("生产Sn不存在");
            return Json(res);
        }
        model.IsRepetition = IsRepetition;
        model.IsRepetitionDate = IsRepetitionDate;
        model.Update();
        res.success = true;
        res.msg = GetResource("设置成功");
        return Json(res);
    }

    /// <summary>
    /// 补单列表
    /// </summary>
    /// <param name="orderId">生产订单号</param>
    /// <returns></returns>
    [DisplayName("列表")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult SupplementaryOrder(String orderId)
    {
        var model = ProductOrders.FindByOrderId(orderId);
        if (model == null)
        {
            return Content(GetResource("生产订单不存在"));
        }
        return View(model);
    }

    /// <summary>
    /// 补单列表
    /// </summary>
    /// <param name="orderId">生产订单号</param>
    /// <param name="page">页码</param>
    /// <param name="limit">条数</param>
    /// <param name="start">开始时间</param>
    /// <param name="end">结束时间</param>
    /// <param name="status">审核状态</param>
    /// <returns></returns>
    [DisplayName("列表")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult SupplementaryOrderList(String orderId, Int32 page, Int32 limit, DateTime start, DateTime end, Int32 status = -1)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true,
        };

        var data = ProductSupplementalOrders.Search(orderId, status, 0, start, end, "", pages).Select(e => new
        {
            Id = e.Id.SafeString(),
            e.OrderId,
            e.Status,
            e.Quantity,
            e.Reason,
            ProductType = ProductType.FindById(e.ProductTypeId)?.Name,
            e.CreateTime
        });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 补单添加
    /// </summary>
    /// <returns></returns>
    [DisplayName("添加")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddSupplementaryOrder(String orderId)
    {
        var modelSaasProductOrder = ProductOrders.FindByOrderId(orderId);
        if (modelSaasProductOrder == null)
        {
            return Content(GetResource("生产订单不存在"));
        }
        var stateInfo = new ProductOrderStateDto();

        if (!modelSaasProductOrder.DataInfo.IsNullOrWhiteSpace())
        {
            stateInfo = JsonSerializer.Deserialize<ProductOrderStateDto>(modelSaasProductOrder.DataInfo);
        }
        else
        {
            stateInfo = new ProductOrderStateDto
            {
                NeedSn = modelSaasProductOrder.ProductType?.NeedSn ?? false,
                NeedMac = modelSaasProductOrder.ProductType?.NeedMac ?? false,
                NeedMac1 = modelSaasProductOrder.ProductType?.NeedMac1 ?? false,
                PCBCycle = modelSaasProductOrder.ProductType?.PCBCycle ?? false,
                ShieldCycle = modelSaasProductOrder.ProductType?.ShieldCycle ?? false,
                MainChipCycle = modelSaasProductOrder.ProductType?.MainChipCycle ?? false,
            };
        }

        ViewBag.StateInfo = stateInfo;
        return View(modelSaasProductOrder);
    }

    /// <summary>
    /// 补单添加
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("添加")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddSupplementaryOrder(String OrderId, Int32 Quantity,String Reason)
    {
        var res = new DResult();
        if (OrderId.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("订单号不能为空");
            return Json(res);
        }
        if (Quantity <= 0)
        {
            res.msg = GetResource("数量不能为空");
            return Json(res);
        }
        var modelSaasProductOrder = ProductOrders.FindByOrderId(OrderId);
        if (modelSaasProductOrder == null)
        {
            res.msg = GetResource("生产订单不存在");
            return Json(res);
        }
        var modelSaasProductSupplementalOrder = new ProductSupplementalOrders()
        {
            OrderId = OrderId,
            ProductTypeId = modelSaasProductOrder.ProductTypeId,
            Quantity = Quantity,
            Reason = Reason,
            Status = 0,
        };
        modelSaasProductSupplementalOrder.Insert();

        res.success = true;
        res.msg = GetResource("添加成功");
        return Json(res);
    }

    /// <summary>
    /// 补单编辑
    /// </summary>
    /// <returns></returns>
    [DisplayName("编辑")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult UpdateSupplementaryOrder(Int64 Id)
    {
        var model = ProductSupplementalOrders.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("补单信息不存在"));
        }
        var modelSaasProductOrder = ProductOrders.FindByOrderId(model.OrderId);
        if (modelSaasProductOrder == null)
        {
            return Content(GetResource("生产订单不存在"));
        }
        var stateInfo = new ProductOrderStateDto();

        if (!modelSaasProductOrder.DataInfo.IsNullOrWhiteSpace())
        {
            stateInfo = JsonSerializer.Deserialize<ProductOrderStateDto>(modelSaasProductOrder.DataInfo);
        }
        else
        {
            stateInfo = new ProductOrderStateDto
            {
                NeedSn = modelSaasProductOrder.ProductType?.NeedSn ?? false,
                NeedMac = modelSaasProductOrder.ProductType?.NeedMac ?? false,
                NeedMac1 = modelSaasProductOrder.ProductType?.NeedMac1 ?? false,
                PCBCycle = modelSaasProductOrder.ProductType?.PCBCycle ?? false,
                ShieldCycle = modelSaasProductOrder.ProductType?.ShieldCycle ?? false,
                MainChipCycle = modelSaasProductOrder.ProductType?.MainChipCycle ?? false,
            };
        }

        ViewBag.StateInfo = stateInfo;
        return View(model);
    }

    /// <summary>
    /// 补单编辑
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("编辑")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult UpdateSupplementaryOrder(Int64 Id, Int32 Quantity,String Reason)
    {
        var res = new DResult();
        if (Quantity <= 0)
        {
            res.msg = GetResource("数量不能为空");
            return Json(res);
        }
        var model = ProductSupplementalOrders.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("补单信息不存在");
            return Json(res);
        }
        var modelProductOrders = ProductOrders.FindByOrderId(model.OrderId);
        if (modelProductOrders == null)
        {
            res.msg = GetResource("生产订单不存在");
            return Json(res);
        }

        model.Quantity = Quantity;
        model.Reason = Reason;
        model.Update();

        res.success = true;
        res.msg = GetResource("编辑成功");

        return Json(res);
    }

    /// <summary>
    /// 补单删除
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("删除")]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult DeleteSupplementaryOrder(Int64 Id)
    {
        var res = new DResult();

        var model = ProductSupplementalOrders.FindById(Id);

        if (model == null)
        {
            res.msg = GetResource("补单不存在");
            return Json(res);
        }

        if (model.Status > 1)
        {
            res.msg = GetResource("非法操作");
            return Json(res);
        }

        model.Delete();

        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 补单审核
    /// </summary>
    /// <returns></returns>
    [DisplayName("审核")]
    [EntityAuthorize((PermissionFlags)32)]
    public IActionResult AuditSupplementaryOrder(Int64 Id)
    {
        var model = ProductSupplementalOrders.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("补单不存在"));
        }
        return View(model);
    }

    /// <summary>
    /// 补单审核
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("审核")]
    [EntityAuthorize((PermissionFlags)32)]
    public IActionResult AuditSupplementaryOrder(Int64 Id, Int32 Status, String Remark)
    {
        var res = new DResult();

        var model = ProductSupplementalOrders.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("补单不存在");
            return Json(res);
        }

        if (model.Status > 1)
        {
            res.msg = GetResource("非法操作");
            return Json(res);
        }

        if (model.ProductType?.NeedSn != true && model.ProductType?.NeedMac != true && model.ProductType?.NeedMac1 != true)
        {
            res.msg = GetResource("参数非法");
            return Json(res);
        }

        var modelProductOrders = ProductOrders.FindByOrderId(model.OrderId);
        if (modelProductOrders == null)
        {
            res.msg = GetResource("主生产订单不存在");
            return Json(res);
        }

        // 获取Mac地址范围内所有未使用的Mac地址
        var listMac = new List<String>();
        var queue = new ConcurrentQueue<String>();

        if (model.ProductType?.NeedMac == true || model.ProductType?.NeedMac1 == true)
        {
            var macList = modelProductOrders.MacRange?.ToJsonEntity<List<MacRangeModel>>() ?? [];

            foreach (var item in macList)
            {
                if (item.Start.IsNullOrWhiteSpace() || item.End.IsNullOrWhiteSpace()) continue;
                listMac.AddRange(MacHelper.GetMacAddresses(item.Start, item.End));
            }

            var list = DeviceMacs.FindAllByOrderId(model.OrderId).Select(e => e.Mac);
            listMac = [.. listMac.Except(list)]; // 剩余未使用的Mac地址

            var count = model.Quantity;
            if (model.ProductType?.NeedMac == true && model.ProductType?.NeedMac1 == true)
            {
                count *= 2;
            }

            if (listMac.Count < count)
            {
                res.msg = GetResource("Mac地址范围内的Mac数量不足，请检查Mac地址范围设置！");
                return Json(res);
            }

            for (var i = 0; i < count; i++)
            {
                queue.Enqueue(listMac[i]);
            }
        }

        model.Status = Status;
        model.Remark = Remark;
        model.AuditTime = DateTime.Now;
        model.Update();

        Task.Run(() =>
        {
            var cacheProvider = EngineContext.Current.Resolve<ICacheProvider>();
            model.CreateSnMac(cacheProvider, queue);
        });

        modelProductOrders.ProductSupplementalQuantity += model.Quantity;
        modelProductOrders.Update();

        res.success = true;
        res.msg = GetResource("审核完成");
        return Json(res);
    }
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Import(long orderId)
    {
        if (orderId.IsNull())
        {
            return Json(new DResult { success = false, msg = "订单号不存在" });
        }

        var productOrders = ProductOrders.FindById(orderId);
        if (productOrders == null)
        {
            return Json(new DResult { success = false, msg = "订单不存在" });
        }

        return View(productOrders);
    }

    /// <summary>
    /// 导入sn/mac/mac1
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("导入")]
    [EntityAuthorize(PermissionFlags.Update)]
    public async Task<IActionResult> Import(IFormFile file, long orderId, int type, string msgId)
    {

        var result = new DResult();
        if (file == null || file.Length == 0)
        {
            return Json(new DResult { success = false, msg = "请选择要导入的文件" });
        }

        if (orderId.IsNull())
        {
            return Json(new DResult { success = false, msg = "订单号不存在" });
        }

        var productOrders = ProductOrders.FindById(orderId);
        if (productOrders == null)
        {
            return Json(new DResult { success = false, msg = "订单不存在" });
        }
        if (productOrders.Status != 2)
        {
            return Json(new DResult { success = false, msg = "改订单记录未审核" });
        }
        //String msgId = DateTime.Now.ToString("yyyyMMddhhmmss");
        var productSnMacs = new List<ProductSnMac>();
        var _notificationsService = EngineContext.Current.Resolve<INotificationsService>();
        int percentage = 1;  //进度条1~100

        var rows = new List<dynamic>();
        try
        {
            rows = MiniExcel.Query(file.OpenReadStream()).ToList();
        }
        catch
        {
            return Json(new DResult { success = false, msg = "导入失败：请使用办公软件另存1次xlsx文件后重试" });
        }

        //var import = MiniExcel.Query(file.OpenReadStream()).ToList();
        var datastr = new List<string>();//待导入数据
        var ordermacranges = ProductOrders.GetMacList(0);//获取未审核的订单mac地址
        int totalNum = rows.Count;     //总数量
        int processed = 0;
      
        try
        {

            for (int i = 1; i < rows.Count; i++)
            {
                var row = rows[i];
                if (row == null) continue;
                var rowDict = (IDictionary<string, object>)row;
                var rowValues = rowDict.Values.ToList();
                var value = rowValues.FirstOrDefault()?.ToString()?.Trim();
                if (value.IsNullOrEmpty()) continue;
                if (!datastr.Contains(value))
                {
                    if (type == 0)
                    {
                        //if (ProductSnMac.FindAllOtherBySn(value, productOrders.StartTime, productOrders.EndTime).Count > 0)//该记录已存在
                        //    continue;
                        if(DeviceSns.FindAll(DeviceSns._.Sn == value).Any())//该记录已存在
                            continue;
                    }
                    else
                    {
                        if (!IsValidMacAddress(value))
                            continue;
                        long maclong = MacHelper.ParseMacToLong(value);
                        var curmacranges = ordermacranges.Where(x => MacHelper.ParseMacToLong(x.Start) <= maclong && MacHelper.ParseMacToLong(x.End) >= maclong).ToList();
                        if (curmacranges.Count > 0)//mac地址存在于未审核的地址中
                            continue;
                        var list = DeviceMacs.FindAll(DeviceMacs._.Mac == value);
                        if (list.Any())//地址已存在在设备MAC表中
                            continue;
                    }

                    datastr.Add(value);
                    processed++;
                    if (processed % 10 == 0)
                    {
                        //计算百分比：
                        percentage = processed * 100 / totalNum;
                        await _notificationsService.SendNotificationAsync(new { typeId = 1, process = percentage, msgId }.ToJson(), false);  // 推送进度
                    }
                }
            }
            //获取缺少SN/MAC/MAC1的数据
            var exp = new WhereExpression();
            DateTime endtime = productOrders.EndTime<=DateTime.MinValue?DateTime.Now: productOrders.EndTime;
            exp &= ProductSnMac._.Id.Between(productOrders.StartTime, endtime, ProductSnMac.Meta.Factory.Snow) & ProductSnMac._.OrderId == productOrders.OrderId;
            switch (type)
            {
                case 0:
                    exp &= ProductSnMac._.Sn.IsNullOrEmpty();
                    break;
                case 1:
                    exp &= ProductSnMac._.Mac.IsNullOrEmpty();
                    break;
                case 2:
                    exp &= ProductSnMac._.Mac1.IsNullOrEmpty();
                    break;
            }
            var existingSnMacs = ProductSnMac.FindAll(exp);
            int existingCount = existingSnMacs.Count > datastr.Count ? datastr.Count : existingSnMacs.Count;
            for (int i = 0; i < existingCount; i++)
            {
                var snMac = existingSnMacs[i];
                if (type == 0)
                {
                    snMac.Sn = datastr[i];

                    var devsn = new DeviceSns();//添加Sn记录
                    devsn.Sn = datastr[i];
                    devsn.AssociationId = productOrders.Id;
                    devsn.OrderId = productOrders.OrderId;
                    devsn.Insert();
                }
                else
                {
                    if (type==1)
                    {
                        snMac.Mac = datastr[i];
                    }else if(type == 2)
                    {
                        snMac.Mac1 = datastr[i];
                    }
                    var devmac = new DeviceMacs();
                    devmac.Mac = datastr[i];
                    devmac.AssociationId = productOrders.Id;
                    devmac.OrderId = productOrders.OrderId;
                    devmac.Insert();
                }
                snMac.Update();

                processed++;
            }
            if (existingCount==0)
            {
                await _notificationsService.SendNotificationAsync(new { typeId = 1, process = 100, msgId }.ToJson(), false);  // 推送进度
                return Json(new DResult { success = false, msg = "导入失败：表格中未包含有效数据或数据重复" });
            }
            await _notificationsService.SendNotificationAsync(new { typeId = 1, process = 100, msgId }.ToJson(), false);  // 推送进度
            return Json(new DResult { success = true, msg = $"导入成功：共导入{existingCount}条记录" });
        }
        catch (Exception ex)
        {
            return Json(new DResult { success = true, msg = $"导入失败：异常:{ex.Message}" });
        }
        
    }

    /// <summary>
    /// 验证mac地址格式
    /// </summary>
    /// <param name="macAddress"></param>
    /// <returns></returns>
    public static bool IsValidMacAddress(string macAddress)
    {
        string pattern = @"^(?:[0-9A-Fa-f]{2}[:-]){5}[0-9A-Fa-f]{2}$|^[0-9A-Fa-f]{4}\.[0-9A-Fa-f]{4}$";
        return Regex.IsMatch(macAddress, pattern);
    }
}
