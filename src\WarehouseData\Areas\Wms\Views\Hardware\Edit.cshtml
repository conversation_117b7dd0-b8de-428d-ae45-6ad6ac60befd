@model HardwareDevices
@{
    Html.AppendTitleParts(T("编辑设备").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
        white-space:nowrap;
        min-width:110px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
    .select{
        max-width: 420px !important;
        border: 1px solid #e6e6e6;
    }
</style>
<script asp-location="Head">
    var closeThis = '@T("关 闭 当 前")';
    var closeOther = '@T("关 闭 其 他")';
    var closeAll = '@T("关 闭 全 部")';

    var jsMenuStyle = "@T("菜单风格")";
    var jsTopStyle = "@T("顶部风格")";
    var jsMenu = "@T("菜单")";
    var jsView = "@T("视图")";
    var jsBanner = "@T("通栏")";
    var jsThroughColor = "@T("通色")";
    var jsFooter = "@T("页脚")";
    var jsMoreSettings = "@T("更多设置")";
    var jsOpen = "@T("开")";
    var jsClose = "@T("关")";
    var jsThemeColor = "@T("主题配色")";
    var layuiNoData = '@T("无数据")';
    var layuiAsc = "@T("升序")";
    var layuiDesc = "@T("降序")";

    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
</script>
<div class="containers">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("使用设备Mac地址")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Mac" placeholder="@T("请输入使用设备Mac地址")" autocomplete="off" class="layui-input" lay-filter="Versions" value="@Model.Mac">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("设备号")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Code" placeholder="@T("请输入设备号")" autocomplete="off" class="layui-input" value="@Model.Code">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("设备类型")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="HType" placeholder="@T("请输入设备类型")" autocomplete="off" class="layui-input" lay-filter="Versions" value="@Model.HType">
            </div>
        </div>


        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("设备型号")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="DeviceModel" placeholder="@T("请输入设备型号")" autocomplete="off" class="layui-input" value="@Model.DeviceModel">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("备注")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Remark" placeholder="@T("请输入备注")" autocomplete="off" class="layui-input" value="@Model.Remark">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("绑定用户")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <div id="demo1" style=" width: 100%;"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("工序")</label>
            <div class="layui-input-inline" style="width: 300px;">
                <select name="Status">
                    <option value="">@T("请选择")</option>
                    <!option value="1" @((int)Model.Status == 1 ? "selected" : "")>@T("打单")</!option>
                    <!option value="2" @((int)Model.Status == 2 ? "selected" : "")>@T("领料")</!option>
                    <!option value="3" @((int)Model.Status == 3 ? "selected" : "")>@T("生产")</!option>
                    <!option value="4" @((int)Model.Status == 4 ? "selected" : "")>@T("生产审核")</!option>
                    <!option value="9" @((int)Model.Status == 9 ? "selected" : "")>@T("打包")</!option>
                    <!option value="5" @((int)Model.Status == 5 ? "selected" : "")>@T("出货")</!option>
                </select>
            </div>
        </div>

        <div class="layui-form-item btn">
            <input hidden name="Id" value="@Model.Id" /> 
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" style="margin-left:20px" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

         var demo1 = xmSelect.render({
            el: '#demo1',
            radio: true, //单选
            name: 'UId',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            disabled: false, // 设置禁用
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];

                // 接口数据
                $.post('@Url.Action("SearchUser")', { key: val, page: pageIndex, id: @Model.Id }, function (res) {
                    // console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                            if (res.extdata.data != null) {
                                demo1.setValue(res.extdata.data)// 传入一个-默认值-数组
                            }
                            cb(res.data, res.extdata.PageCount);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择
                if (data.arr.length > 0) {
                    var a = "";
                    for (var i = 0; i < data.arr.length; i++) {
                        if (i == 0) {
                            
                            a = data.arr[i].value;
                        }
                        else {
                            a += "," + data.arr[i].value;
                        }
                    }
                }


            }
        });


        form.on('submit(Submit)', function (data) {
            if (data.field.Mac.length == 0) {
                abp.notify.warn("@T("设备Mac地址不能为空")");
                return;
            }

            if (data.field.DeviceModel.length == 0) {
                abp.notify.warn("@T("设备型号不能为空")");
                return;
            }

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("Edit")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>