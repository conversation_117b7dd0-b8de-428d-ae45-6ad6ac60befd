﻿using Pek.QiNiu.Extensions;
using DG.Web.Framework;
using DH.AspNetCore.MVC.Filters;
using DH.Helpers;
using HlktechIoT.Dto;
using HlktechIoT.Entity;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using Pek;
using Pek.Configs;
using Pek.Helpers;
using Pek.Models;
using Pek.MVC;
using Pek.Swagger;
using Pek.Timing;
using System.Security.Claims;
using NPOI.SS.Formula.Functions;
using DH.Entity;

namespace HlktechIoT.Controllers.Api;

/// <summary>
/// 通用接口
/// </summary>
[Produces("application/json")]
[CustomRoute(ApiVersions.V1)]
[Authorize("jwt")]
public class Http2Controller : ApiControllerBaseX {
    /// <summary>
    /// 搜索产品型号
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Id">唯一标识</param>
    /// <param name="keyword">关键字</param>
    /// <param name="page">页码</param>
    /// <returns></returns>
    [HttpPost("SearchProductType")]
    public IActionResult SearchProductType([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromForm] String keyword, [FromForm] Int32 page)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(SearchProductType));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;
        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }
        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }
        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10003;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }
        if (TimeStamp <= 0)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }

        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10010;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10011;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10012;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = ProductType._.Id,
            Desc = true,
        };

        result.Data = ProductType.Search(0, keyword, true, DateTime.MinValue, DateTime.MinValue, "", pages).Select(e =>
        {
            return new
            {
                Name = e.Name,
                Value = e.Id,
                e.NeedSn,
                e.NeedMac,
                e.NeedMac1,
                e.PCBCycle,
                e.ShieldCycle,
                e.MainChipCycle,
            };
        });

        result.Code = StateCode.Ok;

        result.ExtData = new { pages.PageCount };

        return result;
    }

    /// <summary>
    /// 提交生产反馈问题
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Id">唯一标识</param>
    /// <param name="data">问题</param>
    /// <returns></returns>
    [HttpPost("SaveFeedback")]
    public IActionResult SaveFeedback([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromForm] FeedbackDto data)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(SaveFeedback));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;
        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }
        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }
        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10003;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }
        if (TimeStamp <= 0)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }
        if (data == null)
        {
            result.ErrCode = 10005;
            result.Message = GetResource("请求参数不能为空", Lng);
            return result;
        }
        if (data.Title.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10006;
            result.Message = GetResource("问题标题不能为空", Lng);
            return result;
        }
        if (data.Content.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10007;
            result.Message = GetResource("问题内容不能为空", Lng);
            return result;
        }
        if (data.ProductTypeId <= 0)
        {
            result.ErrCode = 10008;
            result.Message = GetResource("产品型号不能为空", Lng);
            return result;
        }
        var modelProductType = ProductType.FindById(data.ProductTypeId);
        if (modelProductType == null)
        {
            result.ErrCode = 10009;
            result.Message = GetResource("产品型号不存在", Lng);
            return result;
        }
        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10010;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10011;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10012;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var UId = DHWeb.Identity.GetValue(ClaimTypes.Sid).ToDGInt();
        var Name = DHWeb.Identity.GetValue(ClaimTypes.Name);
        var Ip = UserHost;

        if (!data.OrderId.IsNullOrWhiteSpace())
        {
            var modelOrder = ProductOrders.FindByOrderId(data.OrderId);
            if(modelOrder == null)
            {
                result.ErrCode = 10016;
                result.Message = GetResource($"生产订单不存在", Lng);
                return result;
            }
            data.ProductOrderId = modelOrder.Id;
        }

        var model = new ProductionFeedback
        {
            Name = data.Title,
            ProductTypeId = data.ProductTypeId,
            DType = data.DType,
            ProductOrderId = data.ProductOrderId,
            Content = data.Content,
            CreateUserID = UId,
            CreateUser = Name,
            CreateIP = Ip,
        };


        if (data.Images.Count > 0 || data.Videos.Count > 0)
        {
            var AccessKey = OssSetting.Current.QiNiu.AccessKey;
            var SecretKey = OssSetting.Current.QiNiu.SecretKey;
            var Bucket = OssSetting.Current.QiNiu.Bucket;
            var BasePath = OssSetting.Current.QiNiu.BasePath ?? "";

            if (AccessKey.IsNullOrWhiteSpace() || SecretKey.IsNullOrWhiteSpace() || Bucket.IsNullOrWhiteSpace())
            {
                result.ErrCode = 10013;
                result.Message = GetResource($"七牛云未初始化配置", Lng);
                return result;
            }

            List<String> Images = new();
            List<String> Videos = new();
            List<String> Files = new();
            foreach (var item in data.Images)
            {
                if(item != null)
                {
                    var fileName = (item.FileName + UnixTime.ToTimestamp()).MD5() + Path.GetExtension(item.FileName);
                    using var ms = new MemoryStream();
                    item.CopyTo(ms);
                    byte[] fileBytes = ms.ToArray();
                    var res = QiniuCloud.UploadData(fileName, fileBytes);
                    if (res.Code != 200)
                    {
                        result.ErrCode = 10014;
                        result.Message = GetResource($"图片上传失败", Lng);
                        return result;
                    }
                    var fileUrl = Path.Combine(BasePath, fileName);
                    Images.Add(fileUrl);
                }
            }
            foreach (var item in data.Videos)
            {
                if (item != null)
                {
                    var fileName = (item.FileName + UnixTime.ToTimestamp()).MD5() + Path.GetExtension(item.FileName);
                    using var ms = new MemoryStream();
                    item.CopyTo(ms);
                    byte[] fileBytes = ms.ToArray();
                    var res = QiniuCloud.UploadData(fileName, fileBytes);
                    if (res.Code != 200)
                    {
                        result.ErrCode = 10015;
                        result.Message = GetResource($"视频上传失败", Lng);
                        return result;
                    }
                    var fileUrl = Path.Combine(BasePath, fileName);
                    Videos.Add(fileUrl);
                }
            }
            foreach (var item in data.Files)
            {
                if (item != null)
                {
                    var fileName = (item.FileName + UnixTime.ToTimestamp()).MD5() + Path.GetExtension(item.FileName);
                    using var ms = new MemoryStream();
                    item.CopyTo(ms);
                    byte[] fileBytes = ms.ToArray();
                    var res = QiniuCloud.UploadData(fileName, fileBytes);
                    if (res.Code != 200)
                    {
                        result.ErrCode = 10014;
                        result.Message = GetResource($"文件上传失败", Lng);
                        return result;
                    }
                    var fileUrl = Path.Combine(BasePath, fileName);
                    Files.Add(fileUrl);
                }
            }
            if (Images.Count > 0)
            {
                model.PicPath = String.Join(",", Images);
            }
            if (Videos.Count > 0)
            {
                model.VideoPath = String.Join(",", Videos);
            }
            if (Files.Count > 0)
            {
                model.FilePath = String.Join(",", Files);
            }
        }
        model.Insert();
        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 获取生产反馈问题列表
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Id">唯一标识</param>
    /// <param name="page">页码</param>
    /// <param name="limit">条数</param>
    /// <param name="ProductTypeId">产品型号id</param>
    /// <param name="Start">开始时间</param>
    /// <param name="End">结束时间</param>
    /// <param name="Status">状态 0待处理 1处理中 2已处理 3不予处理</param>
    /// <param name="DType">类型 0待分配 1软件 2硬件</param>
    /// <param name="IsAll">是否查看所有</param>
    /// <param name="ProductOrderId">生产订单Id</param>
    /// <returns></returns>
    [HttpPost("GetFeedbackList")]
    public IActionResult GetFeedbackList([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromForm] Int32 page, [FromForm] Int32 limit, [FromForm] Int32 ProductTypeId, [FromForm] DateTime Start, [FromForm] DateTime End, [FromForm] Int32 Status, [FromForm] Int32 DType, [FromForm] Boolean IsAll, [FromForm]Int64 ProductOrderId)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(GetFeedbackList));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;
        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }
        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }
        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10003;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }
        if (TimeStamp <= 0)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }
        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10005;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10006;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10007;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var UId = IsAll ? -1: DHWeb.Identity.GetValue(ClaimTypes.Sid).ToDGInt();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true,
        };

        var list = ProductionFeedback.Search(ProductOrderId,DType,UId, Status, "", ProductTypeId, Start, End, "", pages).Select(e =>
        {
           string StatusName = "";
            switch (e.Status)
            {
                case 0:
                    StatusName = GetResource("待处理", Lng);
                    break;
                case 1:
                    StatusName = GetResource("处理中", Lng);
                    break;
                case 2:
                    StatusName = GetResource("已处理", Lng);
                    break;
                case 3:
                    StatusName = GetResource("不予处理", Lng);
                    break;
                case 4:
                    StatusName = GetResource("已完结", Lng);
                    break;
                default:
                    StatusName = GetResource("未知", Lng);
                    break;
            }
            return new
            {
                e.Id,
                e.Name,
                e.ProductTypeId,
                ProductTypeName = e.ProductType?.Name ?? "",
                e.Content,
                e.PicPath,
                e.VideoPath,
                e.Cause,
                StatusName,
                OrderId = ProductOrders.FindById(e.ProductOrderId)?.OrderId,
                DType = e.DType == 1 ? GetResource("软件", Lng) : e.DType == 2 ? GetResource("硬件", Lng) : GetResource("待分配", Lng),
                CreateTime = e.CreateTime.ToFullString(),
                e.CreateUser,
                e.CreateUserID,
            };
        }).ToList();

        result.Data = list;
        result.ExtData = new { url = OssSetting.Current.QiNiu.Domain,total = pages.TotalCount };
        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 获取生产订单列表
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Id">唯一标识</param>
    /// <param name="page">页码</param>
    /// <param name="limit">条数</param>
    /// <param name="start">开始时间</param>
    /// <param name="end">结束时间</param>
    /// <returns></returns>
    [HttpPost("GetOrderList")]
    public IActionResult GetOrderList([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromForm] Int32 page, [FromForm] Int32 limit, [FromForm]DateTime start, [FromForm] DateTime end)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(GetOrderList));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;
        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }
        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }
        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10003;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }
        if (TimeStamp <= 0)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }
        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10005;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10006;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10007;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var UId = DHWeb.Identity.GetValue(ClaimTypes.Sid).ToDGInt();

        var modelUserDetail = UserDetail.FindById(UId);

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true,
        };

        var list = ProductOrders.FindAllByCompanyId(modelUserDetail?.CompanyId ?? 0, start, end).Select(e => new
        {
            Id = e.Id.SafeString(),
            e.OrderId,
            e.ProductTypeId,
            e.CompanyName,
            e.ProductProjectName,
            e.ProductTypeName,
            e.Quantity,
            Status = e.Status == 0 ? GetResource("待审核") : e.Status == 1 ? GetResource("审核中") : e.Status == 2 ? GetResource("审核成功") : GetResource("审核失败"),
            e.CreateTime,
        });

        result.Data = list;
        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 获取生产订单资料
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Id">唯一标识</param>
    /// <param name="OrderId">订单号</param>
    /// <returns></returns>
    [HttpPost("GetOrderByData")]
    public IActionResult GetOrderByData([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromForm] String OrderId)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(GetOrderList));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;
        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }
        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }
        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10003;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }
        if (TimeStamp <= 0)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }
        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10005;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10006;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10007;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var model = ProductOrders.FindByOrderId(OrderId);
        if(model == null)
        {
            result.ErrCode = 10008;
            result.Message = GetResource($"订单不存在", Lng);
            return result;
        }

        var list = ProductProjectData.FindAllByProductProjectId(model.ProductProjectId).Select(e => new
        {
            Status = e.Status == 0 ? GetResource("待审核") : e.Status == 1 ? GetResource("审核中") : e.Status == 2 ? GetResource("审核成功") : GetResource("审核失败"),
            Catelog = ProductDataCategory.FindById(e.ProductData?.ProductDataCategoryId1 ?? 0)?.Name,
            Name = ProductDataCategory.FindById(e.ProductData?.ProductDataCategoryId2 ?? 0)?.Name,
            FileName = e.ProductData?.FileName,
            FilePath = e.ProductData?.FilePath,
            e.CreateUser,
            e.CreateTime,
        });

        result.Data = list;
        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 获取最新的工具版本
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Id">唯一标识</param>
    /// <param name="Code">工具标识</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("GetNewToolVersion")]
    public IActionResult GetNewToolVersion([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromForm] String Code)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(GetNewToolVersion));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;
        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }
        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }
        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10003;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }
        if (TimeStamp <= 0)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }
        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10005;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10006;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10007;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }
        result.Data = ToolVersion.FindAllByCode(Code).OrderByDescending(x => x.CreateTime).FirstOrDefault();
        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 获取反馈问题的补充反馈列表
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Id">唯一标识</param>
    /// <param name="FeedbackId">问题反馈Id</param>
    /// <returns></returns>
    [HttpPost("GetFeedbackExList")]
    public IActionResult GetFeedbackExList([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromForm] Int32 FeedbackId)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(GetOrderList));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;
        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }
        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }
        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10003;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }
        if (TimeStamp <= 0)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }
        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10005;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10006;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10007;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var model = ProductionFeedback.FindById(FeedbackId);
        if (model == null)
        {
            result.ErrCode = 10008;
            result.Message = GetResource($"反馈问题不存在", Lng);
            return result;
        }

        var list = ProductionFeedbackEx.FindAllByFeedbackId(model.Id);

        result.Data = list;
        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 补充生产反馈问题
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Id">唯一标识</param>
    /// <param name="data">问题</param>
    /// <returns></returns>
    [HttpPost("SaveFeedbackEx")]
    public IActionResult SaveFeedbackEx([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromForm] FeedbackDto data)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(SaveFeedback));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;
        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }
        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }
        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10003;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }
        if (TimeStamp <= 0)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }
        if (data == null)
        {
            result.ErrCode = 10005;
            result.Message = GetResource("请求参数不能为空", Lng);
            return result;
        }
        if (data.Content.IsNullOrWhiteSpace() && data.Images.Count == 0 && data.Videos.Count == 0 && data.Files.Count == 0)
        {
            result.ErrCode = 10006;
            result.Message = GetResource("请求参数不能为空", Lng);
            return result;
        }
        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10007;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10008;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10009;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var UId = DHWeb.Identity.GetValue(ClaimTypes.Sid).ToDGInt();
        var Name = DHWeb.Identity.GetValue(ClaimTypes.Name);
        var Ip = UserHost;

        var modelFeedback = ProductionFeedback.FindById(data.FeedbackId);
        if (modelFeedback == null)
        {
            result.ErrCode = 10010;
            result.Message = GetResource("反馈问题不存在", Lng);
            return result;
        }

        if (modelFeedback.Status == 4)
        {
            result.ErrCode = 10015;
            result.Message = GetResource("反馈问题已完结", Lng);
            return result;
        }

        modelFeedback.Status = 0;
        modelFeedback.Update();

        var model = new ProductionFeedbackEx
        {
            FeedbackId = modelFeedback.Id,
            DType = 0,
            Content = data.Content,
            CreateUserID = UId,
            CreateUser = Name,
            CreateIP = Ip,
        };


        if (data.Images.Count > 0 || data.Videos.Count > 0 || data.Files.Count > 0)
        {
            var AccessKey = OssSetting.Current.QiNiu.AccessKey;
            var SecretKey = OssSetting.Current.QiNiu.SecretKey;
            var Bucket = OssSetting.Current.QiNiu.Bucket;
            var BasePath = OssSetting.Current.QiNiu.BasePath ?? "";

            if (AccessKey.IsNullOrWhiteSpace() || SecretKey.IsNullOrWhiteSpace() || Bucket.IsNullOrWhiteSpace())
            {
                result.ErrCode = 10011;
                result.Message = GetResource($"七牛云未初始化配置", Lng);
                return result;
            }

            List<String> Images = new();
            List<String> Videos = new();
            List<String> Files = new();
            foreach (var item in data.Images)
            {
                if (item != null)
                {
                    var fileName = (item.FileName + UnixTime.ToTimestamp()).MD5() + Path.GetExtension(item.FileName);
                    using var ms = new MemoryStream();
                    item.CopyTo(ms);
                    byte[] fileBytes = ms.ToArray();
                    var res = QiniuCloud.UploadData(fileName, fileBytes);
                    if (res.Code != 200)
                    {
                        result.ErrCode = 10012;
                        result.Message = GetResource($"图片上传失败", Lng);
                        return result;
                    }
                    var fileUrl = Path.Combine(BasePath, fileName);
                    Images.Add(fileUrl);
                }
            }
            foreach (var item in data.Videos)
            {
                if (item != null)
                {
                    var fileName = (item.FileName + UnixTime.ToTimestamp()).MD5() + Path.GetExtension(item.FileName);
                    using var ms = new MemoryStream();
                    item.CopyTo(ms);
                    byte[] fileBytes = ms.ToArray();
                    var res = QiniuCloud.UploadData(fileName, fileBytes);
                    if (res.Code != 200)
                    {
                        result.ErrCode = 10013;
                        result.Message = GetResource($"视频上传失败", Lng);
                        return result;
                    }
                    var fileUrl = Path.Combine(BasePath, fileName);
                    Videos.Add(fileUrl);
                }
            }
            foreach (var item in data.Files)
            {
                if (item != null)
                {
                    var fileName = (item.FileName + UnixTime.ToTimestamp()).MD5() + Path.GetExtension(item.FileName);
                    using var ms = new MemoryStream();
                    item.CopyTo(ms);
                    byte[] fileBytes = ms.ToArray();
                    var res = QiniuCloud.UploadData(fileName, fileBytes);
                    if (res.Code != 200)
                    {
                        result.ErrCode = 10014;
                        result.Message = GetResource($"文件上传失败", Lng);
                        return result;
                    }
                    var fileUrl = Path.Combine(BasePath, fileName);
                    Files.Add(fileUrl);
                }
            }

            if (Images.Count > 0)
            {
                model.PicPath = String.Join(",", Images);
            }
            if (Videos.Count > 0)
            {
                model.VideoPath = String.Join(",", Videos);
            }
            if (Files.Count > 0)
            {
                model.FilePath = String.Join(",", Files);
            }
        }
        model.Insert();
        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 完结产品型号问题反馈
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Id">唯一标识</param>
    /// <param name="FeedbackId">产品型号问题反馈编号</param>
    /// <returns></returns>
    [HttpPost("CompletedFeedback")]
    public IActionResult CompletedFeedback([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromForm] Int32 FeedbackId)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(GetOrderList));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;
        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }
        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }
        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10003;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }
        if (TimeStamp <= 0)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }
        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10005;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10006;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10007;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var model = ProductionFeedback.FindById(FeedbackId);
        if (model == null)
        {
            result.ErrCode = 10008;
            result.Message = GetResource($"问题反馈不存在", Lng);
            return result;
        }

        var UId = DHWeb.Identity.GetValue(ClaimTypes.Sid).ToDGInt();
        if(model.CreateUserID != UId)
        {
            result.ErrCode = 10009;
            result.Message = GetResource($"只允许创建者操作", Lng);
            return result;
        }

        if(model.Status == 4)
        {
            result.ErrCode = 10010;
            result.Message = GetResource($"反馈已完结", Lng);
            return result;
        }

        model.Status = 4; // 完结状态
        model.Update();

        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 补充问题反馈订单号/问题类型
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Id">唯一标识</param>
    /// <param name="FeedbackId">产品型号问题反馈编号</param>
    /// <param name="OrderId">生产订单号</param>
    /// <param name="DType">生产订单号</param>
    /// <returns></returns>
    [HttpPost("SupplementFeedback")]
    public IActionResult SupplementFeedback([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromForm] Int32 FeedbackId, [FromForm] String OrderId, [FromForm] Int32 DType)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(GetOrderList));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;
        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }
        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }
        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10003;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }
        if (TimeStamp <= 0)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }
        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10005;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10006;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10007;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var model = ProductionFeedback.FindById(FeedbackId);
        if (model == null)
        {
            result.ErrCode = 10008;
            result.Message = GetResource($"问题反馈不存在", Lng);
            return result;
        }

        var UId = DHWeb.Identity.GetValue(ClaimTypes.Sid).ToDGInt();
        if (model.CreateUserID != UId)
        {
            result.ErrCode = 10009;
            result.Message = GetResource($"只允许创建者操作", Lng);
            return result;
        }

        if (!OrderId.IsNullOrWhiteSpace())
        {
            var modelOrder = ProductOrders.FindByOrderId(OrderId);
            if(modelOrder == null)
            {
                result.ErrCode = 10010;
                result.Message = GetResource($"生产订单不存在", Lng);
                return result;
            }
            model.ProductOrderId = modelOrder.Id;
        }

        model.DType = DType;
        model.Update();

        result.Code = StateCode.Ok;
        return result;
    }

    ///// <summary>
    ///// 存储Sn/DeviceName生产数据
    ///// </summary>
    ///// <param name="Id"></param>
    ///// <param name="Lng"></param>
    ///// <param name="DeviceName">设备DN</param>
    ///// <param name="ProjectKey">设备项目Id</param>
    ///// <param name="Mac">设备Mac地址</param>
    ///// <returns></returns>
    ////[AllowAnonymous]
    //[HttpPost("SaveProduction")]
    //[ApiSignature]
    //public IActionResult SaveProduction([FromHeader] String Id, [FromHeader] String Lng, [FromForm] String DeviceName, [FromForm] String ProjectKey, [FromForm] String Mac)
    //{
    //    using var span = DefaultTracer.Instance?.NewSpan(nameof(GetFiveDeviceInfo));

    //    var result = new DGResult();

    //    if (Id.IsNullOrWhiteSpace())
    //    {
    //        result.Message = GetResource("请求标识不能为空", Lng);
    //        return result;
    //    }
    //    result.Id = Id;

    //    if (DeviceName.IsNullOrWhiteSpace())
    //    {
    //        result.Message = GetResource("设备DN不能为空", Lng);
    //        return result;
    //    }

    //    if (ProjectKey.IsNullOrWhiteSpace())
    //    {
    //        result.Message = GetResource("项目Id不能为空", Lng);
    //        return result;
    //    }

    //    if (Mac.IsNullOrWhiteSpace())
    //    {
    //        result.Message = GetResource("设备Mac地址不能为空", Lng);
    //        return result;
    //    }

    //    var check = SnLogs.FindByProjectIdAndSn2(ProjectKey, DeviceName);
    //    if (check != null)
    //    {
    //        result.Message = DeviceName + GetResource("设备已生产", Lng);
    //        return result;
    //    }

    //    var UId = DHWeb.Identity.GetValue(ClaimTypes.Sid).ToDGInt();
    //    var Name = DHWeb.Identity.GetValue(ClaimTypes.Name);

    //    check = new SnLogs
    //    {
    //        ProjectId = ProjectKey,
    //        Sn = DeviceName,
    //        Mac = Mac,
    //        CreateUserID = UId,
    //        UpdateUserID = UId,
    //        CreateUser = Name,
    //        UpdateUser = Name,
    //    };
    //    check.Insert();

    //    result.Code = StateCode.Ok;
    //    return result;
    //}
}
