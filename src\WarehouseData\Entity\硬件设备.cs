﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>硬件设备</summary>
[Serializable]
[DataObject]
[Description("硬件设备")]
[BindIndex("IU_DH_HardwareDevices_Mac", true, "Mac")]
[BindIndex("IX_DH_HardwareDevices_Status", false, "Status")]
[BindTable("DH_HardwareDevices", Description = "硬件设备", ConnName = "DH", DbType = DatabaseType.None)]
public partial class HardwareDevices : IHardwareDevices, IEntity<IHardwareDevices>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String _Mac = null!;
    /// <summary>设备Mac地址</summary>
    [DisplayName("设备Mac地址")]
    [Description("设备Mac地址")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("Mac", "设备Mac地址", "", Master = true)]
    public String Mac { get => _Mac; set { if (OnPropertyChanging("Mac", value)) { _Mac = value; OnPropertyChanged("Mac"); } } }

    private String? _Code;
    /// <summary>设备号</summary>
    [DisplayName("设备号")]
    [Description("设备号")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Code", "设备号", "")]
    public String? Code { get => _Code; set { if (OnPropertyChanging("Code", value)) { _Code = value; OnPropertyChanged("Code"); } } }

    private Dto.HardwareType _HType;
    /// <summary>设备类型。0为扫码枪</summary>
    [DisplayName("设备类型")]
    [Description("设备类型。0为扫码枪")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("HType", "设备类型。0为扫码枪", "")]
    public Dto.HardwareType HType { get => _HType; set { if (OnPropertyChanging("HType", value)) { _HType = value; OnPropertyChanged("HType"); } } }

    private String _DeviceModel = null!;
    /// <summary>设备型号</summary>
    [DisplayName("设备型号")]
    [Description("设备型号")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("DeviceModel", "设备型号", "")]
    public String DeviceModel { get => _DeviceModel; set { if (OnPropertyChanging("DeviceModel", value)) { _DeviceModel = value; OnPropertyChanged("DeviceModel"); } } }

    private String? _Remark;
    /// <summary>备注</summary>
    [DisplayName("备注")]
    [Description("备注")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Remark", "备注", "")]
    public String? Remark { get => _Remark; set { if (OnPropertyChanging("Remark", value)) { _Remark = value; OnPropertyChanged("Remark"); } } }

    private Int32 _BindUserID;
    /// <summary>绑定用户ID</summary>
    [DisplayName("绑定用户ID")]
    [Description("绑定用户ID")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("BindUserID", "绑定用户ID", "")]
    public Int32 BindUserID { get => _BindUserID; set { if (OnPropertyChanging("BindUserID", value)) { _BindUserID = value; OnPropertyChanged("BindUserID"); } } }

    private String? _BindUserRealName;
    /// <summary>绑定用户姓名。冗余字段</summary>
    [DisplayName("绑定用户姓名")]
    [Description("绑定用户姓名。冗余字段")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("BindUserRealName", "绑定用户姓名。冗余字段", "")]
    public String? BindUserRealName { get => _BindUserRealName; set { if (OnPropertyChanging("BindUserRealName", value)) { _BindUserRealName = value; OnPropertyChanged("BindUserRealName"); } } }

    private String? _BindUser;
    /// <summary>绑定用户名</summary>
    [DisplayName("绑定用户名")]
    [Description("绑定用户名")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("BindUser", "绑定用户名", "")]
    public String? BindUser { get => _BindUser; set { if (OnPropertyChanging("BindUser", value)) { _BindUser = value; OnPropertyChanged("BindUser"); } } }

    private Dto.HardwareStatus _Status;
    /// <summary>设备所在工序。1为打单，2为领料，3为生产，4为生产审核，5为出货/入库，6为人为结束，7为取消订单，8为设置，9为打包</summary>
    [DisplayName("设备所在工序")]
    [Description("设备所在工序。1为打单，2为领料，3为生产，4为生产审核，5为出货/入库，6为人为结束，7为取消订单，8为设置，9为打包")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Status", "设备所在工序。1为打单，2为领料，3为生产，4为生产审核，5为出货/入库，6为人为结束，7为取消订单，8为设置，9为打包", "")]
    public Dto.HardwareStatus Status { get => _Status; set { if (OnPropertyChanging("Status", value)) { _Status = value; OnPropertyChanged("Status"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IHardwareDevices model)
    {
        Id = model.Id;
        Mac = model.Mac;
        Code = model.Code;
        HType = model.HType;
        DeviceModel = model.DeviceModel;
        Remark = model.Remark;
        BindUserID = model.BindUserID;
        BindUserRealName = model.BindUserRealName;
        BindUser = model.BindUser;
        Status = model.Status;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "Mac" => _Mac,
            "Code" => _Code,
            "HType" => _HType,
            "DeviceModel" => _DeviceModel,
            "Remark" => _Remark,
            "BindUserID" => _BindUserID,
            "BindUserRealName" => _BindUserRealName,
            "BindUser" => _BindUser,
            "Status" => _Status,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "Mac": _Mac = Convert.ToString(value); break;
                case "Code": _Code = Convert.ToString(value); break;
                case "HType": _HType = (Dto.HardwareType)value.ToInt(); break;
                case "DeviceModel": _DeviceModel = Convert.ToString(value); break;
                case "Remark": _Remark = Convert.ToString(value); break;
                case "BindUserID": _BindUserID = value.ToInt(); break;
                case "BindUserRealName": _BindUserRealName = Convert.ToString(value); break;
                case "BindUser": _BindUser = Convert.ToString(value); break;
                case "Status": _Status = (Dto.HardwareStatus)value.ToInt(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static HardwareDevices? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据设备Mac地址查找</summary>
    /// <param name="mac">设备Mac地址</param>
    /// <returns>实体对象</returns>
    public static HardwareDevices? FindByMac(String mac)
    {
        if (mac.IsNullOrEmpty()) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Mac.EqualIgnoreCase(mac));

        // 单对象缓存
        return Meta.SingleCache.GetItemWithSlaveKey(mac) as HardwareDevices;

        //return Find(_.Mac == mac);
    }

    /// <summary>根据设备所在工序查找</summary>
    /// <param name="status">设备所在工序</param>
    /// <returns>实体列表</returns>
    public static IList<HardwareDevices> FindAllByStatus(Dto.HardwareStatus status)
    {
        if (status < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Status == status);

        return FindAll(_.Status == status);
    }
    #endregion

    #region 字段名
    /// <summary>取得硬件设备字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>设备Mac地址</summary>
        public static readonly Field Mac = FindByName("Mac");

        /// <summary>设备号</summary>
        public static readonly Field Code = FindByName("Code");

        /// <summary>设备类型。0为扫码枪</summary>
        public static readonly Field HType = FindByName("HType");

        /// <summary>设备型号</summary>
        public static readonly Field DeviceModel = FindByName("DeviceModel");

        /// <summary>备注</summary>
        public static readonly Field Remark = FindByName("Remark");

        /// <summary>绑定用户ID</summary>
        public static readonly Field BindUserID = FindByName("BindUserID");

        /// <summary>绑定用户姓名。冗余字段</summary>
        public static readonly Field BindUserRealName = FindByName("BindUserRealName");

        /// <summary>绑定用户名</summary>
        public static readonly Field BindUser = FindByName("BindUser");

        /// <summary>设备所在工序。1为打单，2为领料，3为生产，4为生产审核，5为出货/入库，6为人为结束，7为取消订单，8为设置，9为打包</summary>
        public static readonly Field Status = FindByName("Status");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得硬件设备字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>设备Mac地址</summary>
        public const String Mac = "Mac";

        /// <summary>设备号</summary>
        public const String Code = "Code";

        /// <summary>设备类型。0为扫码枪</summary>
        public const String HType = "HType";

        /// <summary>设备型号</summary>
        public const String DeviceModel = "DeviceModel";

        /// <summary>备注</summary>
        public const String Remark = "Remark";

        /// <summary>绑定用户ID</summary>
        public const String BindUserID = "BindUserID";

        /// <summary>绑定用户姓名。冗余字段</summary>
        public const String BindUserRealName = "BindUserRealName";

        /// <summary>绑定用户名</summary>
        public const String BindUser = "BindUser";

        /// <summary>设备所在工序。1为打单，2为领料，3为生产，4为生产审核，5为出货/入库，6为人为结束，7为取消订单，8为设置，9为打包</summary>
        public const String Status = "Status";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
