﻿@{
    Html.AppendTitleParts(T("编辑产品资料").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
</style>
<div class="containers">
    <form class="layui-form">

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("产品资料类别")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <div id="demo3" style=" width: 100%;"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("类型")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <select name="Ptype" lay-filter="Ptype">
                    <!option value="0" @(Model.Ptype == 0 ? "selected":"")>@T("普通")</!option>
                    <!option value="1" @(Model.Ptype == 1 ? "selected":"")>@T("产测固件")</!option>
                    <!option value="2" @(Model.Ptype == 2 ? "selected":"")>@T("出货固件")</!option>
                    <!option value="3" @(Model.Ptype == 3 ? "selected":"")>@T("标签模板")</!option>
                </select>
            </div>
        </div>

        <div class="layui-form-item" id="Version">
            <label class="layui-form-label label-width">@T("版本号")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Version" placeholder="@T("请输入版本号")" autocomplete="off" class="layui-input" value="@Model.Version">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("文件名")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="FileName" id="FileName" placeholder="@T("请输入文件名")" autocomplete="off" class="layui-input" value="@Model.FileName">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">
                @T("文件")
            </label>
            <div class="layui-input-inline">
                <div class="layui-upload-drag" id="upload" style="width:258px">
                    <i class="layui-icon"></i>
                    <p>@T("点击上传，或将文件拖拽到此处")</p>
                    <div class="" id="uploadDemoView">
                        <hr>
                        <label id="excel" class="layui-form-label-left" style="word-break: break-all;white-space: normal; display: block;">@Model.FilePath</label>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label-width">@T("备注")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <textarea placeholder="@T("请输入内容")" name="Remark" id="Remark" class="layui-textarea">@Model.Remark</textarea>
            </div>
        </div>

        <div class="layui-form-item btn">
            <input hidden name="FilePath" id="FilePath"  value="@Model.FilePath"/>
            <input hidden name="Id" id="" value="@Model.Id" />
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var upload = layui.upload;

        var v = '@Model.Ptype';
        if(v === '0' || v === '3'){
            $("#Version").hide();
        }else{
            $("#Version").show();
        }

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);
        
        //拖拽阈值表上传
        upload.render({
            elem: '#upload'
            , url: '@Url.Action("UploadFiles")' //改成您自己的上传接口
            , done: function (res) {
                if (!res.success) { //失败打印
                    os.warning(res.msg);
                    return;
                }
                os.success('@T("上传成功")');

                layui.$('#uploadDemoView').removeClass('layui-hide');
                $("#excel").text(res.data.OriginFileName);
                var filename = $("#FileName").val();
                console.log("filename=>",filename)
                if(!filename){
                    $("#FileName").val(res.data.OriginFileName);
                }
                $("#FilePath").val(res.data.FilePath);
            },
            before: function () {

            }
            , accept: 'file' //允许上传的文件类型

        });

         var demo3 = xmSelect.render({
            el: '#demo3',
            radio: true, //设置单选
            name: 'ProductDataCategoryId',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            disabled: false, // 设置禁用
            remoteMethod: function (val, cb, show, pageIndex) 
            {  // 远程方法
                var obj = [];
                // 接口数据
                $.post('@Url.Action("SearchDataCategory")', { keyword: val, page: pageIndex,Id:'@Model.ProductDataCategoryId2' }, function (res) 
                {
                    // console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                            if (res.extdata.data != null) {
                                demo3.setValue(res.extdata.data)// 传入一个-默认值-数组
                            }
                            cb(res.data, res.extdata.PageCount);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });
            },
            on: function (data) 
            {  
                // 监听选择
            }
        });

        form.on('select(Ptype)', function (data) {
            if(data.value === '1' || data.value === '2'){
                $("#Version").show();
            }else{
                $("#Version").hide();
            }
        });

        form.on('submit(Submit)', function (data) {

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("Update")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>