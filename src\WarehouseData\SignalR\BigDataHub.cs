﻿using DH;
using DH.Core.Infrastructure;
using DH.Helpers;
using DH.SignalR;
using DH.SignalR.Dtos;

using Microsoft.AspNetCore.SignalR;

using NewLife;
using NewLife.Caching;
using NewLife.Log;

using Pek.Configs;
using Pek.Helpers;

namespace HlktechIoT.SignalR;

public class BigDataHub : Hub<IClientNotifyHub>, IServerNotifyHub {
    /// <summary>
    /// 缓存
    /// </summary>
    private readonly ICache? _cache;

    public BigDataHub(ICache cache)
    {
        if (RedisSetting.Current.RedisEnabled)
        {
            _cache = EngineContext.Current.Resolve<FullRedis>();
            if (_cache == null)
            {
                XTrace.WriteLine($"Redis缓存对象为空，请检查是否注入FullRedis");
            }
        }
        else
        {
            _cache = cache;
        }
    }

    public override async Task OnConnectedAsync()
    {
        var dgpage = Context.GetHttpContext()?.Request.Query["dgpage"].FirstOrDefault();
        var userId = Context.GetHttpContext()?.Request.Query["sid"].FirstOrDefault().ToDGLong();

#if DEBUG
        XTrace.WriteLine($"OnConnectedAsync----userId:{userId},dgpage:{dgpage},connectionId:{Context.ConnectionId}");
#endif

        if (userId != 0)
        {
            _cache?.Increment($"{SignalRSetting.Current.SignalRPrefixUser}{RedisSetting.Current.CacheKeyPrefix}{userId}Count", 1);
            await JoinToGroup(userId, Context.ConnectionId, dgpage);
            await DealOnLineNotify(userId, Context.ConnectionId);
        }

        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var dgpage = Context.GetHttpContext()?.Request.Query["dgpage"].FirstOrDefault();
        var userId = Context.GetHttpContext()?.Request.Query["sid"].FirstOrDefault().ToDGLong();

#if DEBUG
        XTrace.WriteLine($"OnDisconnectedAsync----userId:{userId},dgpage:{dgpage},connectionId:{Context.ConnectionId}");
#endif

        if (userId != 0)
        {
            _cache?.Decrement($"{SignalRSetting.Current.SignalRPrefixUser}{RedisSetting.Current.CacheKeyPrefix}{userId}Count", 1);
            await DealOffLineNotify(userId, Context.ConnectionId);
        }

        await LeaveFromGroup(Context.ConnectionId, dgpage);
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// 处理上线通知(只有用户第一个连接才通知)
    /// </summary>
    /// <param name="userId">用户Id</param>
    /// <param name="connectionId">连接Id</param>
    /// <returns></returns>
    private async Task DealOnLineNotify(Int64? userId, string connectionId)
    {
        var userConnectCount = _cache?.Get<Int64?>($"{SignalRSetting.Current.SignalRPrefixUser}{RedisSetting.Current.CacheKeyPrefix}{userId}Count");
        await Clients.All.OnLine(new OnLineData
        {
            UserId = userId ?? 0,
            ConnectionId = connectionId,
            IsFirst = userConnectCount == 1
        });
    }

    /// <summary>
    /// 处理下线通知(只有当用户一个连接都没了 才算下线)
    /// </summary>
    /// <param name="userId">用户Id</param>
    /// <param name="connectionId">连接Id</param>
    /// <returns></returns>
    private async Task DealOffLineNotify(Int64? userId, string connectionId)
    {
        var userConnectCount = _cache?.Get<Int32>($"{SignalRSetting.Current.SignalRPrefixUser}{RedisSetting.Current.CacheKeyPrefix}{userId}Count");
        await Clients.All.OffLine(new OffLineData
        {
            UserId = userId ?? 0,
            ConnectionId = connectionId,
            IsLast = userConnectCount == 0
        });
    }

    /// <summary>
    /// 加入组
    /// </summary>
    /// <param name="userId">用户Id</param>
    /// <param name="connectionId">连接Id</param>
    /// <param name="groups">组</param>
    /// <returns></returns>
    private async Task JoinToGroup(Int64? userId, String connectionId, params String?[] groups)
    {
        if (userId > 0 && groups != null && groups.Length > 0)
        {
            foreach (var group in groups)
            {
                if (!group.IsNullOrWhiteSpace())
                {
                    await Groups.AddToGroupAsync(connectionId, group);

                    var dic = _cache?.GetDictionary<Int64?>($"{SignalRSetting.Current.SignalRPrefixGroup}{RedisSetting.Current.CacheKeyPrefix}{group}");
                    dic?.Add(connectionId, userId);
                }
            }
        }
    }

    /// <summary>
    /// 从组中移除
    /// </summary>
    /// <param name="connectionId">连接Id</param>
    /// <param name="groups">组</param>
    /// <returns></returns>
    private async Task LeaveFromGroup(String connectionId, params String?[] groups)
    {
        if (groups != null && groups.Length > 0)
        {
            foreach (var group in groups)
            {
                if (!group.IsNullOrWhiteSpace())
                {
                    await Groups.RemoveFromGroupAsync(connectionId, group);

                    var dic = _cache?.GetDictionary<Int64?>($"{SignalRSetting.Current.SignalRPrefixGroup}{RedisSetting.Current.CacheKeyPrefix}{group}");
                    dic?.Remove(connectionId);
                }
            }
        }
    }

    public async Task SendAll(Object data)
    {
        await Clients.All.OnNotify(data);
    }

    public async Task Send(Object data, String Group)
    {
        await Clients.Group(Group).OnNotify(data);
    }

}
