﻿using DG.Web.Framework.Routing;

using DH;
using DH.Core.Domain.Localization;
using DH.Core.Infrastructure;
using DH.Entity;

namespace HlktechIoT.Common.Routing;

public class RouteProvider : IRouteProvider {
    /// <summary>
    /// 注册路由
    /// </summary>
    /// <param name="endpointRouteBuilder">路由构造器</param>
    public void RegisterRoutes(IEndpointRouteBuilder endpointRouteBuilder)
    {
        var UrlSuffix = DG.Setting.Current.IsAllowUrlSuffix ? DG.Setting.Current.UrlSuffix : "";

        var localizationSettings = LocalizationSettings.Current;

        var pattern = string.Empty;
        if (DHSetting.Current.IsInstalled)
        {
            if (localizationSettings.SeoFriendlyUrlsForLanguagesEnabled && localizationSettings.IsEnable)
            {
                var languages = Language.FindByDefault();
                pattern = "{language:lang=" + languages.UniqueSeoCode + "}/";
            }
        }

        //endpointRouteBuilder.MapControllerRoute("UserAgreement", $"UserAgreement{UrlSuffix}",
        //    new { controller = "UserAgreement", action = "Index" });

        //if (Setting.Current.LanguageConfig.IsEnable) // 是否启用多语言
        //{
        //    endpointRouteBuilder.MapControllerRoute("UserAgreement", $"{pattern}UserAgreement{UrlSuffix}",
        //    new { controller = "UserAgreement", action = "Index" });
        //}
    }

    /// <summary>
    /// 获取路由提供者的优先级
    /// </summary>
    public int Priority => -1;
}