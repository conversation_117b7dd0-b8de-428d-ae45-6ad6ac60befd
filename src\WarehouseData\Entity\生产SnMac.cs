﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>生产SnMac</summary>
[Serializable]
[DataObject]
[Description("生产SnMac")]
[BindIndex("IX_DH_ProductSnMac_ProductOrdersId", false, "ProductOrdersId")]
[BindIndex("IX_DH_ProductSnMac_OrderId", false, "OrderId")]
[BindIndex("IX_DH_ProductSnMac_Sn", false, "Sn")]
[BindIndex("IX_DH_ProductSnMac_Mac", false, "Mac")]
[BindIndex("IX_DH_ProductSnMac_Mac1", false, "Mac1")]
[BindIndex("IX_DH_ProductSnMac_IsRepetition", false, "IsRepetition")]
[BindIndex("IX_DH_ProductSnMac_IsValidate", false, "IsValidate")]
[BindIndex("IX_DH_ProductSnMac_IsConsumed", false, "IsConsumed")]
[BindIndex("IX_DH_ProductSnMac_ConsumedTime", false, "ConsumedTime")]
[BindIndex("IX_DH_ProductSnMac_CreateTime", false, "CreateTime")]
[BindTable("DH_ProductSnMac", Description = "生产SnMac", ConnName = "DH", DbType = DatabaseType.None)]
public partial class ProductSnMac : IProductSnMac, IEntity<IProductSnMac>
{
    #region 属性
    private Int64 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, false, false, 0)]
    [BindColumn("Id", "编号", "", DataScale = "timeShard:yy")]
    public Int64 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int64 _ProductOrdersId;
    /// <summary>生产订单编号</summary>
    [DisplayName("生产订单编号")]
    [Description("生产订单编号")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ProductOrdersId", "生产订单编号", "")]
    public Int64 ProductOrdersId { get => _ProductOrdersId; set { if (OnPropertyChanging("ProductOrdersId", value)) { _ProductOrdersId = value; OnPropertyChanged("ProductOrdersId"); } } }

    private String _OrderId = null!;
    /// <summary>订单号。冗余字段</summary>
    [DisplayName("订单号")]
    [Description("订单号。冗余字段")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("OrderId", "订单号。冗余字段", "", Master = true)]
    public String OrderId { get => _OrderId; set { if (OnPropertyChanging("OrderId", value)) { _OrderId = value; OnPropertyChanged("OrderId"); } } }

    private String? _Sn;
    /// <summary>设备Sn/DeviceName</summary>
    [DisplayName("设备Sn_DeviceName")]
    [Description("设备Sn/DeviceName")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Sn", "设备Sn/DeviceName", "")]
    public String? Sn { get => _Sn; set { if (OnPropertyChanging("Sn", value)) { _Sn = value; OnPropertyChanged("Sn"); } } }

    private String? _Mac;
    /// <summary>设备Mac地址</summary>
    [DisplayName("设备Mac地址")]
    [Description("设备Mac地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Mac", "设备Mac地址", "")]
    public String? Mac { get => _Mac; set { if (OnPropertyChanging("Mac", value)) { _Mac = value; OnPropertyChanged("Mac"); } } }

    private String? _Mac1;
    /// <summary>设备Mac地址</summary>
    [DisplayName("设备Mac地址")]
    [Description("设备Mac地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Mac1", "设备Mac地址", "")]
    public String? Mac1 { get => _Mac1; set { if (OnPropertyChanging("Mac1", value)) { _Mac1 = value; OnPropertyChanged("Mac1"); } } }

    private String? _ShorUrl;
    /// <summary>对应的短网址</summary>
    [DisplayName("对应的短网址")]
    [Description("对应的短网址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("ShorUrl", "对应的短网址", "")]
    public String? ShorUrl { get => _ShorUrl; set { if (OnPropertyChanging("ShorUrl", value)) { _ShorUrl = value; OnPropertyChanged("ShorUrl"); } } }

    private String? _FilePath;
    /// <summary>文件路径</summary>
    [DisplayName("文件路径")]
    [Description("文件路径")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("FilePath", "文件路径", "")]
    public String? FilePath { get => _FilePath; set { if (OnPropertyChanging("FilePath", value)) { _FilePath = value; OnPropertyChanged("FilePath"); } } }

    private String? _Content;
    /// <summary>测试结果。以Json的方式存储，比如某一项为key,成功与否为value，方便后续解析json根据key和value来渲染到页面上</summary>
    [DisplayName("测试结果")]
    [Description("测试结果。以Json的方式存储，比如某一项为key,成功与否为value，方便后续解析json根据key和value来渲染到页面上")]
    [DataObjectField(false, false, true, 1024)]
    [BindColumn("Content", "测试结果。以Json的方式存储，比如某一项为key,成功与否为value，方便后续解析json根据key和value来渲染到页面上", "")]
    public String? Content { get => _Content; set { if (OnPropertyChanging("Content", value)) { _Content = value; OnPropertyChanged("Content"); } } }

    private String? _DataInfo;
    /// <summary>用户上传数据</summary>
    [DisplayName("用户上传数据")]
    [Description("用户上传数据")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("DataInfo", "用户上传数据", "")]
    public String? DataInfo { get => _DataInfo; set { if (OnPropertyChanging("DataInfo", value)) { _DataInfo = value; OnPropertyChanged("DataInfo"); } } }

    private Boolean _IsRepetition;
    /// <summary>是否允许重复</summary>
    [DisplayName("是否允许重复")]
    [Description("是否允许重复")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("IsRepetition", "是否允许重复", "")]
    public Boolean IsRepetition { get => _IsRepetition; set { if (OnPropertyChanging("IsRepetition", value)) { _IsRepetition = value; OnPropertyChanged("IsRepetition"); } } }

    private DateTime _IsRepetitionDate;
    /// <summary>是否允许重复时间段</summary>
    [DisplayName("是否允许重复时间段")]
    [Description("是否允许重复时间段")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("IsRepetitionDate", "是否允许重复时间段", "")]
    public DateTime IsRepetitionDate { get => _IsRepetitionDate; set { if (OnPropertyChanging("IsRepetitionDate", value)) { _IsRepetitionDate = value; OnPropertyChanged("IsRepetitionDate"); } } }

    private Boolean _IsValidate;
    /// <summary>是否生产校验</summary>
    [DisplayName("是否生产校验")]
    [Description("是否生产校验")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("IsValidate", "是否生产校验", "")]
    public Boolean IsValidate { get => _IsValidate; set { if (OnPropertyChanging("IsValidate", value)) { _IsValidate = value; OnPropertyChanged("IsValidate"); } } }

    private Boolean _IsConsumed;
    /// <summary>是否已被消费</summary>
    [DisplayName("是否已被消费")]
    [Description("是否已被消费")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("IsConsumed", "是否已被消费", "")]
    public Boolean IsConsumed { get => _IsConsumed; set { if (OnPropertyChanging("IsConsumed", value)) { _IsConsumed = value; OnPropertyChanged("IsConsumed"); } } }

    private DateTime _ConsumedTime;
    /// <summary>消费时间</summary>
    [DisplayName("消费时间")]
    [Description("消费时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("ConsumedTime", "消费时间", "")]
    public DateTime ConsumedTime { get => _ConsumedTime; set { if (OnPropertyChanging("ConsumedTime", value)) { _ConsumedTime = value; OnPropertyChanged("ConsumedTime"); } } }

    private Boolean _IsConfirmConsumed;
    /// <summary>是否确认消费</summary>
    [DisplayName("是否确认消费")]
    [Description("是否确认消费")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("IsConfirmConsumed", "是否确认消费", "")]
    public Boolean IsConfirmConsumed { get => _IsConfirmConsumed; set { if (OnPropertyChanging("IsConfirmConsumed", value)) { _IsConfirmConsumed = value; OnPropertyChanged("IsConfirmConsumed"); } } }

    private DateTime _ConfirmConsumedTime;
    /// <summary>确认消费时间</summary>
    [DisplayName("确认消费时间")]
    [Description("确认消费时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("ConfirmConsumedTime", "确认消费时间", "")]
    public DateTime ConfirmConsumedTime { get => _ConfirmConsumedTime; set { if (OnPropertyChanging("ConfirmConsumedTime", value)) { _ConfirmConsumedTime = value; OnPropertyChanged("ConfirmConsumedTime"); } } }

    private DateTime _Double;
    /// <summary>消费时间</summary>
    [DisplayName("消费时间")]
    [Description("消费时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("Double", "消费时间", "")]
    public DateTime Double { get => _Double; set { if (OnPropertyChanging("Double", value)) { _Double = value; OnPropertyChanged("Double"); } } }

    private String? _PCBCycle;
    /// <summary>PCB周期</summary>
    [DisplayName("PCB周期")]
    [Description("PCB周期")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("PCBCycle", "PCB周期", "")]
    public String? PCBCycle { get => _PCBCycle; set { if (OnPropertyChanging("PCBCycle", value)) { _PCBCycle = value; OnPropertyChanged("PCBCycle"); } } }

    private String? _ShieldCycle;
    /// <summary>屏蔽罩周期</summary>
    [DisplayName("屏蔽罩周期")]
    [Description("屏蔽罩周期")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("ShieldCycle", "屏蔽罩周期", "")]
    public String? ShieldCycle { get => _ShieldCycle; set { if (OnPropertyChanging("ShieldCycle", value)) { _ShieldCycle = value; OnPropertyChanged("ShieldCycle"); } } }

    private String? _MainChipCycle;
    /// <summary>主芯片周期</summary>
    [DisplayName("主芯片周期")]
    [Description("主芯片周期")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("MainChipCycle", "主芯片周期", "")]
    public String? MainChipCycle { get => _MainChipCycle; set { if (OnPropertyChanging("MainChipCycle", value)) { _MainChipCycle = value; OnPropertyChanged("MainChipCycle"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductSnMac model)
    {
        Id = model.Id;
        ProductOrdersId = model.ProductOrdersId;
        OrderId = model.OrderId;
        Sn = model.Sn;
        Mac = model.Mac;
        Mac1 = model.Mac1;
        ShorUrl = model.ShorUrl;
        FilePath = model.FilePath;
        Content = model.Content;
        DataInfo = model.DataInfo;
        IsRepetition = model.IsRepetition;
        IsRepetitionDate = model.IsRepetitionDate;
        IsValidate = model.IsValidate;
        IsConsumed = model.IsConsumed;
        ConsumedTime = model.ConsumedTime;
        IsConfirmConsumed = model.IsConfirmConsumed;
        ConfirmConsumedTime = model.ConfirmConsumedTime;
        Double = model.Double;
        PCBCycle = model.PCBCycle;
        ShieldCycle = model.ShieldCycle;
        MainChipCycle = model.MainChipCycle;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "ProductOrdersId" => _ProductOrdersId,
            "OrderId" => _OrderId,
            "Sn" => _Sn,
            "Mac" => _Mac,
            "Mac1" => _Mac1,
            "ShorUrl" => _ShorUrl,
            "FilePath" => _FilePath,
            "Content" => _Content,
            "DataInfo" => _DataInfo,
            "IsRepetition" => _IsRepetition,
            "IsRepetitionDate" => _IsRepetitionDate,
            "IsValidate" => _IsValidate,
            "IsConsumed" => _IsConsumed,
            "ConsumedTime" => _ConsumedTime,
            "IsConfirmConsumed" => _IsConfirmConsumed,
            "ConfirmConsumedTime" => _ConfirmConsumedTime,
            "Double" => _Double,
            "PCBCycle" => _PCBCycle,
            "ShieldCycle" => _ShieldCycle,
            "MainChipCycle" => _MainChipCycle,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToLong(); break;
                case "ProductOrdersId": _ProductOrdersId = value.ToLong(); break;
                case "OrderId": _OrderId = Convert.ToString(value); break;
                case "Sn": _Sn = Convert.ToString(value); break;
                case "Mac": _Mac = Convert.ToString(value); break;
                case "Mac1": _Mac1 = Convert.ToString(value); break;
                case "ShorUrl": _ShorUrl = Convert.ToString(value); break;
                case "FilePath": _FilePath = Convert.ToString(value); break;
                case "Content": _Content = Convert.ToString(value); break;
                case "DataInfo": _DataInfo = Convert.ToString(value); break;
                case "IsRepetition": _IsRepetition = value.ToBoolean(); break;
                case "IsRepetitionDate": _IsRepetitionDate = value.ToDateTime(); break;
                case "IsValidate": _IsValidate = value.ToBoolean(); break;
                case "IsConsumed": _IsConsumed = value.ToBoolean(); break;
                case "ConsumedTime": _ConsumedTime = value.ToDateTime(); break;
                case "IsConfirmConsumed": _IsConfirmConsumed = value.ToBoolean(); break;
                case "ConfirmConsumedTime": _ConfirmConsumedTime = value.ToDateTime(); break;
                case "Double": _Double = value.ToDateTime(); break;
                case "PCBCycle": _PCBCycle = Convert.ToString(value); break;
                case "ShieldCycle": _ShieldCycle = Convert.ToString(value); break;
                case "MainChipCycle": _MainChipCycle = Convert.ToString(value); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static ProductSnMac? FindById(Int64 id)
    {
        if (id < 0) return null;

        return Find(_.Id == id);
    }

    /// <summary>根据生产订单编号查找</summary>
    /// <param name="productOrdersId">生产订单编号</param>
    /// <returns>实体列表</returns>
    public static IList<ProductSnMac> FindAllByProductOrdersId(Int64 productOrdersId)
    {
        if (productOrdersId < 0) return [];

        return FindAll(_.ProductOrdersId == productOrdersId);
    }

    /// <summary>根据订单号查找</summary>
    /// <param name="orderId">订单号</param>
    /// <returns>实体列表</returns>
    public static IList<ProductSnMac> FindAllByOrderId(String orderId)
    {
        if (orderId.IsNullOrEmpty()) return [];

        return FindAll(_.OrderId == orderId);
    }

    /// <summary>根据设备Sn_DeviceName查找</summary>
    /// <param name="sn">设备Sn_DeviceName</param>
    /// <returns>实体列表</returns>
    public static IList<ProductSnMac> FindAllBySn(String? sn)
    {
        if (sn == null) return [];

        return FindAll(_.Sn == sn);
    }

    /// <summary>根据设备Mac地址查找</summary>
    /// <param name="mac">设备Mac地址</param>
    /// <returns>实体列表</returns>
    public static IList<ProductSnMac> FindAllByMac(String? mac)
    {
        if (mac == null) return [];

        return FindAll(_.Mac == mac);
    }

    /// <summary>根据设备Mac地址查找</summary>
    /// <param name="mac1">设备Mac地址</param>
    /// <returns>实体列表</returns>
    public static IList<ProductSnMac> FindAllByMac1(String? mac1)
    {
        if (mac1 == null) return [];

        return FindAll(_.Mac1 == mac1);
    }
    #endregion

    #region 数据清理
    /// <summary>清理指定时间段内的数据</summary>
    /// <param name="start">开始时间。未指定时清理小于指定时间的所有数据</param>
    /// <param name="end">结束时间</param>
    /// <returns>清理行数</returns>
    public static Int32 DeleteWith(DateTime start, DateTime end)
    {
        return Delete(_.Id.Between(start, end, Meta.Factory.Snow));
    }

    /// <summary>删除指定时间段内的数据表</summary>
    /// <param name="start">开始时间</param>
    /// <param name="end">结束时间</param>
    /// <returns>清理行数</returns>
    public static Int32 DropWith(DateTime start, DateTime end)
    {
        return Meta.AutoShard(start, end, session =>
        {
            try
            {
                return session.Execute($"Drop Table {session.FormatedTableName}");
            }
            catch (Exception ex)
            {
                XTrace.WriteException(ex);
                return 0;
            }
        }
        ).Sum();
    }
    #endregion

    #region 字段名
    /// <summary>取得生产SnMac字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>生产订单编号</summary>
        public static readonly Field ProductOrdersId = FindByName("ProductOrdersId");

        /// <summary>订单号。冗余字段</summary>
        public static readonly Field OrderId = FindByName("OrderId");

        /// <summary>设备Sn/DeviceName</summary>
        public static readonly Field Sn = FindByName("Sn");

        /// <summary>设备Mac地址</summary>
        public static readonly Field Mac = FindByName("Mac");

        /// <summary>设备Mac地址</summary>
        public static readonly Field Mac1 = FindByName("Mac1");

        /// <summary>对应的短网址</summary>
        public static readonly Field ShorUrl = FindByName("ShorUrl");

        /// <summary>文件路径</summary>
        public static readonly Field FilePath = FindByName("FilePath");

        /// <summary>测试结果。以Json的方式存储，比如某一项为key,成功与否为value，方便后续解析json根据key和value来渲染到页面上</summary>
        public static readonly Field Content = FindByName("Content");

        /// <summary>用户上传数据</summary>
        public static readonly Field DataInfo = FindByName("DataInfo");

        /// <summary>是否允许重复</summary>
        public static readonly Field IsRepetition = FindByName("IsRepetition");

        /// <summary>是否允许重复时间段</summary>
        public static readonly Field IsRepetitionDate = FindByName("IsRepetitionDate");

        /// <summary>是否生产校验</summary>
        public static readonly Field IsValidate = FindByName("IsValidate");

        /// <summary>是否已被消费</summary>
        public static readonly Field IsConsumed = FindByName("IsConsumed");

        /// <summary>消费时间</summary>
        public static readonly Field ConsumedTime = FindByName("ConsumedTime");

        /// <summary>是否确认消费</summary>
        public static readonly Field IsConfirmConsumed = FindByName("IsConfirmConsumed");

        /// <summary>确认消费时间</summary>
        public static readonly Field ConfirmConsumedTime = FindByName("ConfirmConsumedTime");

        /// <summary>消费时间</summary>
        public static readonly Field Double = FindByName("Double");

        /// <summary>PCB周期</summary>
        public static readonly Field PCBCycle = FindByName("PCBCycle");

        /// <summary>屏蔽罩周期</summary>
        public static readonly Field ShieldCycle = FindByName("ShieldCycle");

        /// <summary>主芯片周期</summary>
        public static readonly Field MainChipCycle = FindByName("MainChipCycle");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得生产SnMac字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>生产订单编号</summary>
        public const String ProductOrdersId = "ProductOrdersId";

        /// <summary>订单号。冗余字段</summary>
        public const String OrderId = "OrderId";

        /// <summary>设备Sn/DeviceName</summary>
        public const String Sn = "Sn";

        /// <summary>设备Mac地址</summary>
        public const String Mac = "Mac";

        /// <summary>设备Mac地址</summary>
        public const String Mac1 = "Mac1";

        /// <summary>对应的短网址</summary>
        public const String ShorUrl = "ShorUrl";

        /// <summary>文件路径</summary>
        public const String FilePath = "FilePath";

        /// <summary>测试结果。以Json的方式存储，比如某一项为key,成功与否为value，方便后续解析json根据key和value来渲染到页面上</summary>
        public const String Content = "Content";

        /// <summary>用户上传数据</summary>
        public const String DataInfo = "DataInfo";

        /// <summary>是否允许重复</summary>
        public const String IsRepetition = "IsRepetition";

        /// <summary>是否允许重复时间段</summary>
        public const String IsRepetitionDate = "IsRepetitionDate";

        /// <summary>是否生产校验</summary>
        public const String IsValidate = "IsValidate";

        /// <summary>是否已被消费</summary>
        public const String IsConsumed = "IsConsumed";

        /// <summary>消费时间</summary>
        public const String ConsumedTime = "ConsumedTime";

        /// <summary>是否确认消费</summary>
        public const String IsConfirmConsumed = "IsConfirmConsumed";

        /// <summary>确认消费时间</summary>
        public const String ConfirmConsumedTime = "ConfirmConsumedTime";

        /// <summary>消费时间</summary>
        public const String Double = "Double";

        /// <summary>PCB周期</summary>
        public const String PCBCycle = "PCBCycle";

        /// <summary>屏蔽罩周期</summary>
        public const String ShieldCycle = "ShieldCycle";

        /// <summary>主芯片周期</summary>
        public const String MainChipCycle = "MainChipCycle";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
