﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>设备Mac地址</summary>
[Serializable]
[DataObject]
[Description("设备Mac地址")]
[BindIndex("IU_DH_DeviceMacs_Mac", true, "Mac")]
[BindIndex("IX_DH_DeviceMacs_ProductOrdersId", false, "ProductOrdersId")]
[BindTable("DH_DeviceMacs", Description = "设备Mac地址", ConnName = "DH", DbType = DatabaseType.None)]
public partial class DeviceMacs : IDeviceMacs, IEntity<IDeviceMacs>
{
    #region 属性
    private Int64 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, false, false, 0)]
    [BindColumn("Id", "编号", "", DataScale = "time")]
    public Int64 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String _Mac = null!;
    /// <summary>Mac地址</summary>
    [DisplayName("Mac地址")]
    [Description("Mac地址")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("Mac", "Mac地址", "", Master = true)]
    public String Mac { get => _Mac; set { if (OnPropertyChanging("Mac", value)) { _Mac = value; OnPropertyChanged("Mac"); } } }

    private Int64 _ProductOrdersId;
    /// <summary>订单编号</summary>
    [DisplayName("订单编号")]
    [Description("订单编号")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ProductOrdersId", "订单编号", "")]
    public Int64 ProductOrdersId { get => _ProductOrdersId; set { if (OnPropertyChanging("ProductOrdersId", value)) { _ProductOrdersId = value; OnPropertyChanged("ProductOrdersId"); } } }

    private Int64 _AssociationId;
    /// <summary>关联Id</summary>
    [DisplayName("关联Id")]
    [Description("关联Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("AssociationId", "关联Id", "")]
    public Int64 AssociationId { get => _AssociationId; set { if (OnPropertyChanging("AssociationId", value)) { _AssociationId = value; OnPropertyChanged("AssociationId"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IDeviceMacs model)
    {
        Id = model.Id;
        Mac = model.Mac;
        ProductOrdersId = model.ProductOrdersId;
        AssociationId = model.AssociationId;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "Mac" => _Mac,
            "ProductOrdersId" => _ProductOrdersId,
            "AssociationId" => _AssociationId,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToLong(); break;
                case "Mac": _Mac = Convert.ToString(value); break;
                case "ProductOrdersId": _ProductOrdersId = value.ToLong(); break;
                case "AssociationId": _AssociationId = value.ToLong(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static DeviceMacs? FindById(Int64 id)
    {
        if (id < 0) return null;

        return Find(_.Id == id);
    }

    /// <summary>根据Mac地址查找</summary>
    /// <param name="mac">Mac地址</param>
    /// <returns>实体对象</returns>
    public static DeviceMacs? FindByMac(String mac)
    {
        if (mac.IsNullOrEmpty()) return null;

        return Find(_.Mac == mac);
    }

    /// <summary>根据订单编号查找</summary>
    /// <param name="productOrdersId">订单编号</param>
    /// <returns>实体列表</returns>
    public static IList<DeviceMacs> FindAllByProductOrdersId(Int64 productOrdersId)
    {
        if (productOrdersId < 0) return [];

        return FindAll(_.ProductOrdersId == productOrdersId);
    }
    #endregion

    #region 数据清理
    /// <summary>清理指定时间段内的数据</summary>
    /// <param name="start">开始时间。未指定时清理小于指定时间的所有数据</param>
    /// <param name="end">结束时间</param>
    /// <returns>清理行数</returns>
    public static Int32 DeleteWith(DateTime start, DateTime end)
    {
        return Delete(_.Id.Between(start, end, Meta.Factory.Snow));
    }
    #endregion

    #region 字段名
    /// <summary>取得设备Mac地址字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>Mac地址</summary>
        public static readonly Field Mac = FindByName("Mac");

        /// <summary>订单编号</summary>
        public static readonly Field ProductOrdersId = FindByName("ProductOrdersId");

        /// <summary>关联Id</summary>
        public static readonly Field AssociationId = FindByName("AssociationId");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得设备Mac地址字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>Mac地址</summary>
        public const String Mac = "Mac";

        /// <summary>订单编号</summary>
        public const String ProductOrdersId = "ProductOrdersId";

        /// <summary>关联Id</summary>
        public const String AssociationId = "AssociationId";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";
    }
    #endregion
}
