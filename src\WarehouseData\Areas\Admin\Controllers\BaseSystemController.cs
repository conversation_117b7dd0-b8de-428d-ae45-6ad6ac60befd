﻿using DG.Web.Framework;

using DH;
using DH.Core.Domain.Localization;
using DH.Entity;

using HlktechIoT.Common;

using Microsoft.AspNetCore.Mvc;

using NewLife.Common;

using Pek;
using Pek.Configs;
using Pek.Models;

using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>基础数据管理</summary>
[DisplayName("基础数据管理")]
[Description("系统基础数据的管理")]
[AdminArea]
[DHMenu(100,ParentMenuName = "System", CurrentMenuUrl = "", CurrentMenuName = "BaseSystem", LastUpdate = "20240124")]
public class BaseSystemController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 100;
}

/// <summary>系统配置</summary>
[DisplayName("系统配置")]
[Description("系统参数配置")]
[AdminArea]
[DHMenu(100,ParentMenuName = "BaseSystem", CurrentMenuUrl = "~/{area}/SystemInfo", CurrentMenuName = "SystemInfo", LastUpdate = "20240124")]
public class SystemInfoController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 100;

    /// <summary>
    /// 系统配置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("系统配置")]
    public IActionResult Index()
    {
        ViewBag.Model = SiteInfo.FindDefault();

        ViewBag.RoleList = Role.Meta.Cache.Entities;

        return View();
    }

    /// <summary>
    /// 基本资料提交修改
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("系统配置")]
    public IActionResult UpdateInfo(String registration, String IsEnableLanguage, String IsAllowSwagger, String IsCheckApiSignature, String IsAllowMarkupMin, String ServerToken, String CurDomainUrl, String Company, String Copyright, String SslEnabled, String AllowRequestParams, Int32 SessionTimeout, String IsOnlyManager, String ExcludeUrl, String PaswordStrength, Int32 IoTSessionTimeout, String DefaultRole, String AutoRegister, String AllowManageRegister, String EnableOnlineStatistics, Int32 MaxOnlineCount, Int32 OnlineCountExpire, Int32 UpdateOnlineTimeSpan, Int32 OnlineUserExpire, String BanAccessTime, String BanAccessIP, String AllowAccessIP, String StarWeb, String SeoFriendlyUrlsForLanguagesEnabled)
    {
        SysConfig.Current.Company = Company;
        SysConfig.Current.Save();

        DHSetting.Current.CurDomainUrl = CurDomainUrl.TrimEnd('/');

        var Model = SiteInfo.FindDefault();
        Model.Registration = registration;
        Model.SiteCopyright = Copyright.SafeString().Trim();
        Model.Update();

        var localizationSettings = LocalizationSettings.Current;

        localizationSettings.IsEnable = IsEnableLanguage.SafeString() == "on";
        localizationSettings.SeoFriendlyUrlsForLanguagesEnabled = SeoFriendlyUrlsForLanguagesEnabled.SafeString() == "on";

        ConfigFileHelper.AddOrUpdateAppSetting("SwaggerOption:Enabled", IsAllowSwagger.SafeString() == "on", @"Settings/Swagger.json");

        DG.Setting.Current.IsAllowMarkupMin = IsAllowMarkupMin.SafeString() == "on";
        DG.Setting.Current.IsOnlyManager = IsOnlyManager.SafeString() == "on";
        DG.Setting.Current.AllowManageRegister = AllowManageRegister == "on";
        DG.Setting.Current.Save();

        switch (SslEnabled)
        {
            default:
            case "0":
                DHSetting.Current.SslEnabled = 0;
                DHSetting.Current.AllSslEnabled = false;
                break;

            case "1":
                DHSetting.Current.SslEnabled = 1;
                DHSetting.Current.AllSslEnabled = false;
                break;

            case "2":
                DHSetting.Current.SslEnabled = 2;
                DHSetting.Current.AllSslEnabled = false;
                break;

            case "99":
                DHSetting.Current.SslEnabled = 1;
                DHSetting.Current.AllSslEnabled = true;
                break;
        }

        PekSysSetting.Current.AllowRequestParams = AllowRequestParams.SafeString() == "on";
        PekSysSetting.Current.ExcludeUrl = ExcludeUrl;
        PekSysSetting.Current.Save();

        DHSetting.Current.EnableOnlineStatistics = EnableOnlineStatistics.SafeString() == "on";
        DHSetting.Current.MaxOnlineCount = MaxOnlineCount;
        DHSetting.Current.OnlineCountExpire = OnlineCountExpire;
        DHSetting.Current.UpdateOnlineTimeSpan = UpdateOnlineTimeSpan;
        DHSetting.Current.OnlineUserExpire = OnlineUserExpire;
        DHSetting.Current.BanAccessTime = BanAccessTime;
        DHSetting.Current.BanAccessIP = BanAccessIP;
        DHSetting.Current.AllowAccessIP = AllowAccessIP;
        DHSetting.Current.StarWeb = StarWeb;
        DHSetting.Current.ServerToken = ServerToken;
        DHSetting.Current.SessionTimeout = SessionTimeout;
        DHSetting.Current.PaswordStrength = PaswordStrength;
        DHSetting.Current.DefaultRole = DefaultRole;
        DHSetting.Current.Save();

        IoTSetting.Current.SessionTimeout = IoTSessionTimeout;
        IoTSetting.Current.AutoRegister = AutoRegister.SafeString() == "on";
        IoTSetting.Current.Save();

        return Json(new DResult { success = true, msg = GetResource("编辑成功") });
    }
}