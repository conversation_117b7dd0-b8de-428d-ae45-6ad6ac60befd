﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>验证码</summary>
public partial interface IVerifyCode
{
    #region 属性
    /// <summary>验证码唯一键</summary>
    String? Key { get; set; }

    /// <summary>验证码</summary>
    String? Code { get; set; }

    /// <summary>过期时间</summary>
    DateTime EndTime { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }
    #endregion
}
