.pear-btn {
	display: inline-block;
	line-height: 38px;
	white-space: nowrap;
	cursor: pointer;
	text-align: center;
	box-sizing: border-box;
	outline: none;
	transition: 0.1s;
	font-weight: 500;
	padding: 0 18px;
	height: 38px;
	font-size: 14px;
	background-color: white;
	border: 1px solid #dcdfe6;
	border-radius: 2px;
}

.pear-btn i {
	font-size: 13px;
}

.pear-btn:hover {
	opacity: .8;
	filter: alpha(opacity=80);
	color: #409eff;
	background-color: #ECF5FF;
}

.pear-btn-danger,
.pear-btn-warming,
.pear-btn-success,
.pear-btn-primary {
	height: 37px;
	line-height: 37px;
	color: #fff !important
}

/** Button 主题 */
.pear-btn-primary {
	border: 1px solid #2D8CF0;
	background-color: #2D8CF0 !important;
}
.pear-btn-danger {
	border: 1px solid #f56c6c;
	background-color: #f56c6c !important;
}
.pear-btn-warming {
	border: 1px solid #f6ad55;
	background-color: #f6ad55 !important;
}
.pear-btn-success {
	border: 1px solid #36b368;
	background-color: #36b368 !important;
}

.pear-btn[round] {
	border-radius: 50px;
}

.pear-btn-primary[plain] {
	color: #409eff !important;
	background: #ecf5ff 10% !important;
}

.pear-btn-primary[plain]:hover {
	color: #fff !important;
	background-color: #2d8cf0!important
}

.pear-btn-success[plain] {
	color: #36b368 !important;
	background: #f0f9eb !important;
}

.pear-btn-success[plain]:hover {
	color: white !important;
	background-color: #36b368 !important
}

.pear-btn-warming[plain] {
	color: #e6a23c !important;
	background: #fdf6ec !important;
}

.pear-btn-warming[plain]:hover {
	color: white !important;
	background-color: #e6a23c !important
}

.pear-btn-danger[plain] {
	color: #f56c6c !important;
	background: #fef0f0 !important;
}

.pear-btn-danger[plain]:hover {
	color: white !important;
	background-color: #f56c6c !important
}

/** Button Group */
.pear-btn-group {
	display: inline-block;
	vertical-align: middle;
}

.pear-btn-group .pear-btn {
	float: left;
	position: relative;
	border-radius: 0px;
	margin-left: 1px;
	margin-right: 1px;
}

.pear-btn-md {
	height: 34px;
	line-height: 34px;
	padding: 0 10px;
	font-size: 12.5px;
}

.pear-btn-group .pear-btn:first-child {
	border-top-left-radius: 4px !important;
	border-bottom-left-radius: 4px !important;
}

.pear-btn-group .pear-btn:last-child {
	border-top-right-radius: 4px !important;
	border-bottom-right-radius: 4px !important;
}

.pear-btn-group .pear-btn[round]:first-child {
	border-top-left-radius: 50px !important;
	border-bottom-left-radius: 50px !important;
}

.pear-btn-group .pear-btn[round]:last-child {
	border-top-right-radius: 50px !important;
	border-bottom-right-radius: 50px !important;
}

/** Button Size*/
.pear-btn-sm {
	height: 32px;
	line-height: 32px;
	padding: 0 10px;
	font-size: 12px;
}

.pear-btn-xs {
	height: 28px;
	line-height: 28px;
	padding: 0 8px;
	font-size: 12px;
}

.pear-btn-md {
	height: 34px;
	line-height: 34px;
	padding: 0 10px;
	font-size: 12.5px;
}

.pear-btn-lg {
	height: 44px;
	line-height: 44px;
	padding: 0 25px;
	font-size: 16px;
}
