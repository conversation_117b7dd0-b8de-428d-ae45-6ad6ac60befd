﻿using DG;
using DG.Utils.Models.EventModel;
using DG.Web.Framework;
using DG.Web.Framework.Controllers;

using DH.Core.Infrastructure;
using DH.Core.Webs;
using DH.Entity;
using DH.Models;
using DH.Permissions.Identity.JwtBearer;
using DH.Services.Membership;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Caching;
using NewLife.Common;
using NewLife.Log;

using Pek.Events;
using Pek.Exceptions;
using Pek.Helpers;
using Pek.Models;
using Pek.Security;
using Pek.Timing;
using Pek.Webs;

using System.ComponentModel;
using System.Security.Claims;
using System.Web;

using XCode.Membership;

namespace HlktechIoT.Controllers;

/// <summary>
/// 登录控制器
/// </summary>
public class UserLoginController : ControllerBaseX {
    /// <summary>
    /// Jwt令牌构建器
    /// </summary>
    public IJsonWebTokenBuilder TokenBuilder { get; }

    /// <summary>
    /// Jwt令牌存储器
    /// </summary>
    public IJsonWebTokenStore TokenStore { get; }

    /// <summary>用于防爆破登录。即使内存缓存，也有一定用处，最糟糕就是每分钟重试次数等于集群节点数的倍数</summary>
    private static readonly ICache _cache = Cache.Default ?? new MemoryCache();

    private readonly IManageProvider _provider;

    /// <summary>
    /// 初始化一个<see cref="UserLoginController"/>类型的实例
    /// </summary>
    /// <param name="tokenBuilder">Jwt令牌构建器</param>
    /// <param name="tokenStore">Jwt令牌存储器</param>
    /// <param name="provider">管理提供者</param>
    public UserLoginController(IJsonWebTokenBuilder tokenBuilder, IJsonWebTokenStore tokenStore, IManageProvider provider)
    {
        TokenBuilder = tokenBuilder;
        TokenStore = tokenStore;
        _provider = provider;
    }

    /// <summary>
    /// 登录
    /// </summary>
    /// <returns></returns>
    [DisplayName("登录")]
    public IActionResult Index()
    {
        var returnUrl = GetRequest("r");
        if (returnUrl.IsNullOrEmpty()) returnUrl = GetRequest("ReturnUrl");

        if (DG.Setting.Current.LoginUrl != "~/UserLogin")
        {
            return Redirect($"{DG.Setting.Current.LoginUrl.AppendReturn(returnUrl)}");
        }

        var user = _provider.TryLogin(HttpContext);

        // 如果已登录，直接跳转
        if (ManageProvider.User != null)
        {
            if (ManageProvider.User.Enable)
            {
                var userDetail = UserDetail.FindById(ManageProvider.User.ID);
                if (userDetail == null)
                {
                    userDetail = new UserDetail();
                    userDetail.Id = ManageProvider.User.ID;
                }
                userDetail.SId = Sid;
                userDetail.Save();

                if (Url.IsLocalUrl(returnUrl))
                    return Redirect(returnUrl);
                else
                {
                    //return Redirect($"{DG.Setting.Current.UserCenterUrl.AppendReturn(returnUrl, "page")}");
                    return Redirect(Url.Action("Index", "BigData", new { area = "" }) ?? $"{DG.Setting.Current.UserCenterUrl.AppendReturn(returnUrl, "page")}");
                }
            }
        }

        // 是否已完成第三方登录
        var logId = Session["Cube_OAuthId"].ToLong();

        // 如果禁用本地登录，且只有一个第三方登录，直接跳转，构成单点登录
        var ms = OAuthConfig.GetValids(GrantTypes.AuthorizationCode);
        if (ms != null && !DG.Setting.Current.AllowLogin)
        {
            if (ms.Count == 0) throw new Exception(GetResource("禁用了本地密码登录，且没有配置第三方登录"));
            if (logId > 0) throw new Exception("已完成第三方登录，但无法绑定本地用户且没有开启自动注册，建议开启OAuth应用的自动注册");

            // 只有一个，跳转
            if (ms.Count == 1)
            {
                var url = $"~/Sso/Login?name={HttpUtility.UrlEncode(ms[0].Name)}";

                if (ReferrerId > 0)
                {
                    url += $"&ReferrerId={HttpContext.Items["ReferrerId"]}";
                }
                if (UtId > 0)
                {
                    url += $"&UtId={HttpContext.Items["UtId"]}";
                }

                if (!returnUrl.IsNullOrEmpty()) url += "&r=" + HttpUtility.UrlEncode(returnUrl);

                return Redirect(url);
            }
        }

        // 部分提供支持应用内免登录，直接跳转
        if (ms != null && ms.Count > 0 && logId == 0 && GetRequest("autologin") != "0")
        {
            var agent = DHWeb.UserAgent;
            if (!agent.IsNullOrWhiteSpace())
            {
                foreach (var item in ms)
                {
                    var client = OAuthClient.Create(item.Name);
                    if (client != null && client.Support(agent))
                    {
                        var logId1 = GetRequest("logId").ToLong();
                        if (logId1 > 0)
                        {
                            var modelAppLog = AppLog.FindById(logId1);
                            if (modelAppLog != null)
                            {
                                modelAppLog.Provider = item.Name;
                                modelAppLog.Update();
                            }
                        }

                        var url = $"~/Sso/Login?name={item.Name}";
                        if (ReferrerId > 0)
                        {
                            url += $"&ReferrerId={HttpContext.Items["ReferrerId"]}";
                        }
                        if (UtId > 0)
                        {
                            url += $"&UtId={HttpContext.Items["UtId"]}";
                        }

                        if (!returnUrl.IsNullOrEmpty()) url += "&r=" + HttpUtility.UrlEncode(returnUrl);

                        return Redirect(url);
                    }
                }
            }
        }

        var model = GetViewModel(returnUrl);
        model.OAuthItems = ms?.Where(e => e.Visible).ToList();

        return View(model);
    }

    private LoginViewModel GetViewModel(String returnUrl)
    {
        var set = DG.Setting.Current;
        var sys = SysConfig.Current;
        var model = new LoginViewModel
        {
            DisplayName = sys.DisplayName,

            AllowLogin = set.AllowLogin,
            AllowRegister = set.AllowRegister,

            LoginTip = set.LoginTip,
            ResourceUrl = CDNOptionSetting.Current.Url,
            ReturnUrl = returnUrl,

            //OAuthItems = ms,
        };

        if (model.ResourceUrl.IsNullOrEmpty()) model.ResourceUrl = "/Content";
        model.ResourceUrl = model.ResourceUrl.TrimEnd('/');

        // 是否使用Sso登录
        var appId = GetRequest("ssoAppId").ToInt();
        var app = App.FindById(appId);
        if (app != null)
        {
            model.DisplayName = app + "";
            model.Logo = app.Logo;
        }

        return model;
    }

    /// <summary>密码登录</summary>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <param name="keeplogin">是否记住密码</param>
    /// <param name="checkCode">验证码</param>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("登录")]
    public async Task<IActionResult> Login(String username, String password, String checkCode, Boolean keeplogin = false)
    {
        // 连续错误校验
        var key = $"Login:{username}";
        var errors = _cache.Get<Int32>(key);
        var ipKey = $"Login:{UserHost}";
        var ipErrors = _cache.Get<Int32>(ipKey);
        var errorKey = $"Login:ErrorTime";
        var errorTime = _cache.Get<DateTime>(errorKey);

        var set = DG.Setting.Current;
        var returnUrl = GetRequest("r");
        if (returnUrl.IsNullOrEmpty()) returnUrl = GetRequest("ReturnUrl");

        try
        {
            // 检查输入合法性
            CheckInput(username, password);

            // 检查验证码
            CheckVCode(checkCode);

            // 指定时间延时登录
            LoginDelay(username);

            var rresult = new DResult();

            var provider = ManageProvider.Provider;
            if (ModelState.IsValid && provider?.Login(username, password, keeplogin) != null)
            {
                // 登录成功，清空错误数
                if (errors > 0) _cache.Remove(key);
                if (ipErrors > 0) _cache.Remove(ipKey);
                if (errorTime > DateTime.MinValue) _cache.Remove(errorKey);

                if (ManageProvider.User?.Enable == false)
                {
                    throw new DHException(GetResource("用户被禁用"));
                }

                //if (IsJsonRequest)
                //{
                //    return Json(0, "ok", new { provider.Current.ID });
                //}

                if (!Request.IsAjaxRequest())
                {
                    if (Url.IsLocalUrl(returnUrl)) return Redirect(returnUrl);
                }

                var userDetail = UserDetail.FindById(ManageProvider.User?.ID ?? -1);
                if (userDetail == null)
                {
                    userDetail = new UserDetail();
                    userDetail.Id = ManageProvider.User?.ID ?? -1;
                }
                userDetail.SId = Sid;
                userDetail.Save();

                // 消费登录之后的自定义逻辑
                var _eventPublisher = EngineContext.Current.Resolve<IEventPublisher>();
                _eventPublisher.Publish(new LoginEvent(1, ManageProvider.User, userDetail));

                // 登录后自动绑定
                var logId = Session["Cube_OAuthId"].ToLong();
                if (logId > 0)
                {
                    Session["Cube_OAuthId"] = null;
                    var log = SsoController.Provider.BindAfterLogin(logId);
                    if (log != null && log.Success && !log.RedirectUri.IsNullOrEmpty()) return Redirect(log.RedirectUri);
                }

                var payload = new Dictionary<string, string>
                {
                    ["clientId"] = ManageProvider.User!.ID.ToString(),
                    [ClaimTypes.Sid] = ManageProvider.User.ID.ToString(),
                    [ClaimTypes.NameIdentifier] = ManageProvider.User.Name,
                    ["From"] = "Web",
                };

                JsonWebToken result;
                if (keeplogin)
                {
                    result = TokenBuilder.Create(payload, (Double)365 * 24 * 60);
                }
                else
                {
                    result = TokenBuilder.Create(payload);
                }

                if (IsJsonRequest)
                {
                    rresult.extdata = HttpContext.Items["jwtToken"];
                }

                rresult.success = true;
                rresult.locate = DG.Setting.Current.UserCenterUrl.Replace("~/", "").AppendReturn(returnUrl);
                rresult.data = result;

                var model = GetViewModel(returnUrl);
                model.OAuthItems = OAuthConfig.GetVisibles();

                rresult.extdata = model;
                return Json(rresult);
            }
            else
            {
                throw new DHException(GetResource("用户名和密码不匹配"));
            }
        }
        catch (Exception ex)
        {
            // 登录失败比较重要，记录一下
            var action = ex is InvalidOperationException ? "风控" : "登录";
            LogProvider.Provider.WriteLog(typeof(User), action, false, ex.Message, 0, username, UserHost);
            XTrace.WriteLine("[{0}]登录失败！{1}", username, ex.Message);
            XTrace.WriteException(ex);

            // 累加错误数，首次出错时设置过期时间
            var userErrors = _cache.Increment(key, 1);
            var ipErros = _cache.Increment(ipKey, 1);
            var time = 300;
            if (set.LoginForbiddenTime > 0) time = set.LoginForbiddenTime;
            if (errors <= 0) _cache.SetExpire(key, TimeSpan.FromSeconds(time));
            if (ipErrors <= 0) _cache.SetExpire(ipKey, TimeSpan.FromSeconds(time));
            if (errorTime < DateTime.MinValue)
            {
                _cache.Set(errorKey, DateTime.Now);
                _cache.SetExpire(errorKey, TimeSpan.FromSeconds(time));
            }

            var errorTimes = userErrors > ipErros ? userErrors : ipErros;
            var showerrmsg = ex.Message;

            return Json(new { success = false, msg = showerrmsg, errorTimes, minTimesWithoutCheckCode = DG.Setting.Current.MaxLoginError, code = 0 });
        }
    }

    private void CheckInput(String username, String password)
    {
        if (string.IsNullOrWhiteSpace(username))
            throw new DHException(GetResource("用户名不能为空！"));

        if (string.IsNullOrWhiteSpace(password))
            throw new DHException(GetResource("密码不能为空！"));

    }

    private void CheckVCode(String checkCode)
    {
        if (checkCode.IsNullOrWhiteSpace())
            throw new DHException(string.Format(GetResource("登录需要提供验证码"), DG.Setting.Current.LoginFailMinute, DG.Setting.Current.MaxLoginError));

        var systemCheckCode = HttpContext.Session.GetString("ybbcode");
        if (systemCheckCode.IsNullOrWhiteSpace())
        {
            throw new DHException(GetResource("验证码有误，请联系管理员！"));
        }

        if (!systemCheckCode.EqualIgnoreCase(checkCode))
            throw new DHException(GetResource("验证码错误"));

        // 生成随机验证码，强制使验证码过期（一交提交必须更改验证码）
        HttpContext.Session.SetString("ybbcode", Guid.NewGuid().ToString());
    }

    private void LoginDelay(String username)
    {
        var set = DG.Setting.Current;
        // 连续错误校验
        var key = $"Login:{username}";
        var errors = _cache.Get<Int32>(key);
        var ipKey = $"Login:{UserHost}";
        var ipErrors = _cache.Get<Int32>(ipKey);
        var errorKey = $"Login:ErrorTime";
        var errorTime = _cache.Get<DateTime>(errorKey);

        if (errorTime > DateTime.MinValue && errorTime < DateTime.MaxValue)
        {
            var dt = DateTimeUtil.DateDiff("second", DateTime.Now, errorTime.AddSeconds(DG.Setting.Current.LoginForbiddenTime));

            if (errors >= set.MaxLoginError && set.MaxLoginError > 0) throw new InvalidOperationException($"[{username}]登录错误过多，请在{dt}秒后再试！");
            if (ipErrors >= set.MaxLoginError && set.MaxLoginError > 0) throw new InvalidOperationException($"IP地址[{UserHost}]登录错误过多，请在{dt}秒后再试！");
        }
    }
}
