﻿@using Pek.Configs
@{
    Html.AppendTitleParts(T("系统配置").Text);

    var site = SiteInfo.FindDefault();

    var configuration = EngineContext.Current.Resolve<IConfiguration>();

    var localizationSettings = LocalizationSettings.Current;

    var RoleList = ViewBag.RoleList as IList<Role>;
}
<style asp-location="true">
    .layui-form-label {
        width: 150px;
    }

    .layui-form-item.btn {
        margin-top: 20px;
        text-align: center;
    }
</style>
<div class="layui-card">
    <div class="layui-card-header">@T("系统配置")</div>
    <div class="layui-card-body" pad15>
        <form class="layui-form" lay-filter="">
            <div class="layui-form-item">
                <label class="layui-form-label">@T("系统地址")</label>
                <div class="layui-input-block">
                    <input type="text" name="CurDomainUrl" value="@DHSetting.Current.CurDomainUrl" class="layui-input" style="width: 250px !important;">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("公司名称")</label>
                <div class="layui-input-block">
                    <input type="text" name="Company" value="@NewLife.Common.SysConfig.Current.Company" class="layui-input" style="width: 250px !important;">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("版权")</label>
                <div class="layui-input-block">
                    <input type="text" name="Copyright" value="@ViewBag.Model.SiteCopyright" class="layui-input" style="width: 250px !important;">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("签名校验Token")</label>
                <div class="layui-input-block">
                    <input type="text" name="ServerToken" value="@DHSetting.Current.ServerToken" class="layui-input" style="width: 250px !important;">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">ICP @T("备案号")</label>
                <div class="layui-input-block">
                    <input type="text" name="registration" value="@site.Registration" class="layui-input" style="width: 250px !important;">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("会话超时")</label>
                <div class="layui-input-block">
                    <input type="text" name="SessionTimeout" value="@DHSetting.Current.SessionTimeout" class="layui-input" style="width: 250px !important; display: inline;"><span style="margin-left: 10px;">@T("单点登录后会话超时时间，该时间内可借助Cookie登录，默认0s。")</span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("启用多语言")</label>
                <div class="layui-input-inline">
                    <input type="checkbox" lay-filter="switch" name="IsEnableLanguage" lay-skin="switch" lay-text="@T("是")|@T("否")" @(localizationSettings.IsEnable ? Html.Raw("checked") : Html.Raw(""))>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("启用多语言SEO友好URL")</label>
                <div class="layui-input-inline">
                    <input type="checkbox" lay-filter="switch" name="SeoFriendlyUrlsForLanguagesEnabled" lay-skin="switch" lay-text="@T("是")|@T("否")" @(localizationSettings.SeoFriendlyUrlsForLanguagesEnabled ? Html.Raw("checked") : Html.Raw(""))>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("是否压缩页面")</label>
                <div class="layui-input-inline">
                    <input type="checkbox" lay-filter="switch" name="IsAllowMarkupMin" lay-skin="switch" lay-text="@T("是")|@T("否")" @(DG.Setting.Current.IsAllowMarkupMin ? Html.Raw("checked") : Html.Raw(""))>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("启用接口调试")</label>
                <div class="layui-input-inline">
                    <input type="checkbox" lay-filter="switch" name="IsAllowSwagger" lay-skin="switch" lay-text="@T("是")|@T("否")" @(configuration.GetValue<bool>("SwaggerOption:Enabled", false) ? Html.Raw("checked") : Html.Raw(""))>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("启用接口校验")</label>
                <div class="layui-input-inline">
                    <input type="checkbox" lay-filter="switch" name="IsCheckApiSignature" lay-skin="switch" lay-text="@T("是")|@T("否")" @(DHSetting.Current.IsCheckApiSignature ? Html.Raw("checked") : Html.Raw(""))>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("密码强度检验")</label>
                <div class="layui-input-block">
                    <input type="text" name="PaswordStrength" value="@DHSetting.Current.PaswordStrength" class="layui-input" style="width: 250px !important;">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("默认进入后台管理")</label>
                <div class="layui-input-inline">
                    <input type="checkbox" lay-filter="switch" name="IsOnlyManager" lay-skin="switch" lay-text="@T("是")|@T("否")" @(DG.Setting.Current.IsOnlyManager ? Html.Raw("checked") : Html.Raw(""))>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("开启管理系统注册")</label>
                <div class="layui-input-inline">
                    <input type="checkbox" lay-filter="switch" name="AllowManageRegister" lay-skin="switch" lay-text="@T("是")|@T("否")" @(DG.Setting.Current.AllowManageRegister ? Html.Raw("checked") : Html.Raw(""))>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("允许获取请求日志")</label>
                <div class="layui-input-inline" style="width: 470px;">
                    <input type="checkbox" lay-filter="switch" name="AllowRequestParams" lay-skin="switch" lay-text="@T("是")|@T("否")" @(PekSysSetting.Current.AllowRequestParams ? Html.Raw("checked") : Html.Raw(""))><div style="margin-left: 10px; display: inline-block; vertical-align: middle; margin-top: 8px;">@T("开启对性能有影响，需要调试请求数据时打开，用完及时关闭。")</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("过滤接口")</label>
                <div class="layui-input-block">
                    <input type="text" name="ExcludeUrl" value="@PekSysSetting.Current.ExcludeUrl" class="layui-input" style="width: 250px !important; display: inline;"><span style="margin-left: 10px;">@T("部分接口在开启日志获取时会影响实际数据，需要在此过滤,以英文逗号分隔")</span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("SSL启用")</label>
                <div class="layui-input-inline">
                    <select name="SslEnabled" id="SslEnabled">
                        <!option value="0" @(DHSetting.Current.SslEnabled == 0 && !DHSetting.Current.AllSslEnabled ? "selected" : "")>@T("不处理")</!option>
                        <!option value="1" @(DHSetting.Current.SslEnabled == 1 ? "selected" : "")>@T("局部重定向（永久）到页面的HTTPS版本")</!option>
                        <!option value="2" @(DHSetting.Current.SslEnabled == 2 ? "selected" : "")>@T("局部（永久）重定向到页面的HTTP版本")</!option>
                        <!option value="99" @(DHSetting.Current.AllSslEnabled ? "selected" : "")>@T("全站全局启用")</!option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("设备离线超时")</label>
                <div class="layui-input-block">
                    <input type="text" name="IoTSessionTimeout" value="@HlktechIoT.Common.IoTSetting.Current.SessionTimeout" class="layui-input" style="width: 250px !important; display: inline;"><span style="margin-left: 10px;">@T("设备在指定时间内没有收到相应的心跳则超时提示。单位秒")</span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("允许设备端自动注册")</label>
                <div class="layui-input-block">
                    <div class="layui-input-inline" style="width: 470px;">
                        <input type="checkbox" lay-filter="switch" name="AutoRegister" lay-skin="switch" lay-text="@T("是")|@T("否")" @(HlktechIoT.Common.IoTSetting.Current.AutoRegister ? Html.Raw("checked") : Html.Raw(""))><div style="margin-left: 10px; display: inline-block; vertical-align: middle; margin-top: 8px;">@T("开启后后台没有这个设备会自动创建设备。")</div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("用户默认角色")</label>
                <div class="layui-input-inline">
                    <select name="DefaultRole" id="DefaultRole">
                        @foreach (var item in RoleList!)
                        {
                            var modelRolenLan = RoleLan.FindByRIdAndLId(item.ID, language.Id, true);
                            <!option value="@item.Name" @(DHSetting.Current.DefaultRole == item.Name ? "selected" : "")>@modelRolenLan.Name</!option>
                        }
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("启用在线数统计")</label>
                <div class="layui-input-block">
                    <div class="layui-input-inline" style="width: 470px;">
                        <input type="checkbox" lay-filter="switch" name="EnableOnlineStatistics" lay-skin="switch" lay-text="@T("是")|@T("否")" @(DHSetting.Current.EnableOnlineStatistics ? Html.Raw("checked") : Html.Raw(""))><div style="margin-left: 10px; display: inline-block; vertical-align: middle; margin-top: 8px;">@T("开启后后台可以看到当前在线的用户以及用户在线日志。")</div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("最大在线人数")</label>
                <div class="layui-input-block">
                    <input type="text" name="MaxOnlineCount" value="@DHSetting.Current.MaxOnlineCount" class="layui-input" style="width: 250px !important; display: inline;"><span style="margin-left: 10px;">@T("平台访问的最大在线人数")</span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("在线人数缓存时间")</label>
                <div class="layui-input-block">
                    <input type="text" name="OnlineCountExpire" value="@DHSetting.Current.OnlineCountExpire" class="layui-input" style="width: 250px !important; display: inline;"><span style="margin-left: 10px;">@T("单位为分钟,0代表即时数量")</span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("更新用户在线时间间隔")</label>
                <div class="layui-input-block">
                    <input type="text" name="UpdateOnlineTimeSpan" value="@DHSetting.Current.UpdateOnlineTimeSpan" class="layui-input" style="width: 250px !important; display: inline;"><span style="margin-left: 10px;">@T("单位为分钟,0代表不更新")</span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("在线用户过期时间")</label>
                <div class="layui-input-block">
                    <input type="text" name="OnlineUserExpire" value="@DHSetting.Current.OnlineUserExpire" class="layui-input" style="width: 250px !important; display: inline;"><span style="margin-left: 10px;">@T("单位为分钟,指定时间内未操作则会被清除")</span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("禁止访问时间")</label>
                <div class="layui-input-block">
                    <textarea name="BanAccessTime" class="layui-textarea" style="width: 200px; display: inline;">@DHSetting.Current.BanAccessTime</textarea><span style="margin-left: 10px;">@T("每行一个时间段，格式为2023/09/08-2023/10/08")</span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("禁止IP访问")</label>
                <div class="layui-input-block">
                    <textarea name="BanAccessIP" class="layui-textarea" style="width: 200px; display: inline;">@DHSetting.Current.BanAccessIP</textarea><span style="margin-left: 10px;">@T("每行一个IP")</span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("允许IP访问")</label>
                <div class="layui-input-block">
                    <textarea name="AllowAccessIP" class="layui-textarea" style="width: 200px; display: inline;">@DHSetting.Current.AllowAccessIP</textarea><span style="margin-left: 10px;">@T("每行一个IP，允许IP访问优先级高，可以设置*为全局")</span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">@T("Trace跟踪路径")</label>
                <div class="layui-input-block">
                    <input type="text" name="StarWeb" value="@DHSetting.Current.StarWeb" class="layui-input" style="width: 200px; display: inline;"><span style="margin-left: 10px;">@T("用于排查问题")</span>
                </div>
            </div>
            <div class="layui-form-item btn">
                <div class="layui-input-block">
                    <button type="button" class="pear-btn pear-btn-primary pear-btn-normal" lay-submit lay-filter="set_website">@T("确认保存")</button>
                </div>
            </div>
        </form>
    </div>
</div>
<script asp-location="Footer">
    layui.use(['abp', 'form'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;

        //自定义验证
        form.verify({

        });

        form.on('submit(set_website)', function (data) {
            var waitIndex = layer.load(2);

            $.post("@Url.Action("UpdateInfo")", data.field, function (result) {
                layer.close(waitIndex);
                if (result.success) {
                    abp.notify.success(result.msg);
                } else {
                    abp.notify.error(result.msg);
                }
            });
        });
    });
</script>