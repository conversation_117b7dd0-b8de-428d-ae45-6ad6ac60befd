﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>生产测试数据</summary>
[Serializable]
[DataObject]
[Description("生产测试数据")]
[BindIndex("IX_device_log_DId", false, "DId")]
[BindIndex("IX_device_log_ModuleType_DeviceMac", false, "ModuleType,DeviceMac")]
[BindIndex("IX_device_log_ModuleType_DeduplicateInfo", false, "ModuleType,DeduplicateInfo")]
[BindIndex("IX_device_log_ReturnFactory", false, "ReturnFactory")]
[BindIndex("IX_device_log_CreateTime", false, "CreateTime")]
[BindTable("device_log", Description = "生产测试数据", ConnName = "TestLogs", DbType = DatabaseType.None)]
public partial class DeviceTestLogs : IDeviceTestLogs, IEntity<IDeviceTestLogs>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int64 _DId;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("DId", "编号", "", DataScale = "time")]
    public Int64 DId { get => _DId; set { if (OnPropertyChanging("DId", value)) { _DId = value; OnPropertyChanged("DId"); } } }

    private Int32 _BatchNum;
    /// <summary>批次号，建议每一批或定一个数字号</summary>
    [DisplayName("批次号")]
    [Description("批次号，建议每一批或定一个数字号")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("batch_num", "批次号，建议每一批或定一个数字号", "")]
    public Int32 BatchNum { get => _BatchNum; set { if (OnPropertyChanging("BatchNum", value)) { _BatchNum = value; OnPropertyChanged("BatchNum"); } } }

    private String? _ModuleType;
    /// <summary>模块型号</summary>
    [DisplayName("模块型号")]
    [Description("模块型号")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("module_type", "模块型号", "")]
    public String? ModuleType { get => _ModuleType; set { if (OnPropertyChanging("ModuleType", value)) { _ModuleType = value; OnPropertyChanged("ModuleType"); } } }

    private String? _ModuleId;
    /// <summary>模块id,允许重复</summary>
    [DisplayName("模块id")]
    [Description("模块id,允许重复")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("module_id", "模块id,允许重复", "")]
    public String? ModuleId { get => _ModuleId; set { if (OnPropertyChanging("ModuleId", value)) { _ModuleId = value; OnPropertyChanged("ModuleId"); } } }

    private String? _SdkVersion;
    /// <summary>固件版本</summary>
    [DisplayName("固件版本")]
    [Description("固件版本")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("sdk_version", "固件版本", "")]
    public String? SdkVersion { get => _SdkVersion; set { if (OnPropertyChanging("SdkVersion", value)) { _SdkVersion = value; OnPropertyChanged("SdkVersion"); } } }

    private String _DeviceMac = null!;
    /// <summary>mac地址，允许重复</summary>
    [DisplayName("mac地址")]
    [Description("mac地址，允许重复")]
    [DataObjectField(false, false, false, 100)]
    [BindColumn("device_mac", "mac地址，允许重复", "")]
    public String DeviceMac { get => _DeviceMac; set { if (OnPropertyChanging("DeviceMac", value)) { _DeviceMac = value; OnPropertyChanged("DeviceMac"); } } }

    private String? _DeduplicateInfo;
    /// <summary>去重信息</summary>
    [DisplayName("去重信息")]
    [Description("去重信息")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("DeduplicateInfo", "去重信息", "")]
    public String? DeduplicateInfo { get => _DeduplicateInfo; set { if (OnPropertyChanging("DeduplicateInfo", value)) { _DeduplicateInfo = value; OnPropertyChanged("DeduplicateInfo"); } } }

    private String? _TestTime;
    /// <summary>检测时间</summary>
    [DisplayName("检测时间")]
    [Description("检测时间")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("test_time", "检测时间", "")]
    public String? TestTime { get => _TestTime; set { if (OnPropertyChanging("TestTime", value)) { _TestTime = value; OnPropertyChanged("TestTime"); } } }

    private String? _TestFactory;
    /// <summary>检测工厂</summary>
    [DisplayName("检测工厂")]
    [Description("检测工厂")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("test_factory", "检测工厂", "")]
    public String? TestFactory { get => _TestFactory; set { if (OnPropertyChanging("TestFactory", value)) { _TestFactory = value; OnPropertyChanged("TestFactory"); } } }

    private String? _TestPosition;
    /// <summary>检测工位</summary>
    [DisplayName("检测工位")]
    [Description("检测工位")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("test_position", "检测工位", "")]
    public String? TestPosition { get => _TestPosition; set { if (OnPropertyChanging("TestPosition", value)) { _TestPosition = value; OnPropertyChanged("TestPosition"); } } }

    private String? _TestData;
    /// <summary>监测数据</summary>
    [DisplayName("监测数据")]
    [Description("监测数据")]
    [DataObjectField(false, false, true, 2048)]
    [BindColumn("test_data", "监测数据", "")]
    public String? TestData { get => _TestData; set { if (OnPropertyChanging("TestData", value)) { _TestData = value; OnPropertyChanged("TestData"); } } }

    private String? _SaleInfo;
    /// <summary>销售信息</summary>
    [DisplayName("销售信息")]
    [Description("销售信息")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("sale_info", "销售信息", "")]
    public String? SaleInfo { get => _SaleInfo; set { if (OnPropertyChanging("SaleInfo", value)) { _SaleInfo = value; OnPropertyChanged("SaleInfo"); } } }

    private String? _CustomerInfo;
    /// <summary>客户信息</summary>
    [DisplayName("客户信息")]
    [Description("客户信息")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("customer_info", "客户信息", "")]
    public String? CustomerInfo { get => _CustomerInfo; set { if (OnPropertyChanging("CustomerInfo", value)) { _CustomerInfo = value; OnPropertyChanged("CustomerInfo"); } } }

    private String? _Reserve1;
    /// <summary>预留字段1</summary>
    [DisplayName("预留字段1")]
    [Description("预留字段1")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("Reserve1", "预留字段1", "varchar(100)")]
    public String? Reserve1 { get => _Reserve1; set { if (OnPropertyChanging("Reserve1", value)) { _Reserve1 = value; OnPropertyChanged("Reserve1"); } } }

    private String? _Reserve2;
    /// <summary>预留自段2</summary>
    [DisplayName("预留自段2")]
    [Description("预留自段2")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("Reserve2", "预留自段2", "varchar(100)")]
    public String? Reserve2 { get => _Reserve2; set { if (OnPropertyChanging("Reserve2", value)) { _Reserve2 = value; OnPropertyChanged("Reserve2"); } } }

    private Boolean _ReturnFactory;
    /// <summary>是否返厂</summary>
    [DisplayName("是否返厂")]
    [Description("是否返厂")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ReturnFactory", "是否返厂", "")]
    public Boolean ReturnFactory { get => _ReturnFactory; set { if (OnPropertyChanging("ReturnFactory", value)) { _ReturnFactory = value; OnPropertyChanged("ReturnFactory"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("create_time", "创建时间", "", DataScale = "timeShard:yyyy")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IDeviceTestLogs model)
    {
        Id = model.Id;
        DId = model.DId;
        BatchNum = model.BatchNum;
        ModuleType = model.ModuleType;
        ModuleId = model.ModuleId;
        SdkVersion = model.SdkVersion;
        DeviceMac = model.DeviceMac;
        DeduplicateInfo = model.DeduplicateInfo;
        TestTime = model.TestTime;
        TestFactory = model.TestFactory;
        TestPosition = model.TestPosition;
        TestData = model.TestData;
        SaleInfo = model.SaleInfo;
        CustomerInfo = model.CustomerInfo;
        Reserve1 = model.Reserve1;
        Reserve2 = model.Reserve2;
        ReturnFactory = model.ReturnFactory;
        CreateTime = model.CreateTime;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "DId" => _DId,
            "BatchNum" => _BatchNum,
            "ModuleType" => _ModuleType,
            "ModuleId" => _ModuleId,
            "SdkVersion" => _SdkVersion,
            "DeviceMac" => _DeviceMac,
            "DeduplicateInfo" => _DeduplicateInfo,
            "TestTime" => _TestTime,
            "TestFactory" => _TestFactory,
            "TestPosition" => _TestPosition,
            "TestData" => _TestData,
            "SaleInfo" => _SaleInfo,
            "CustomerInfo" => _CustomerInfo,
            "Reserve1" => _Reserve1,
            "Reserve2" => _Reserve2,
            "ReturnFactory" => _ReturnFactory,
            "CreateTime" => _CreateTime,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "DId": _DId = value.ToLong(); break;
                case "BatchNum": _BatchNum = value.ToInt(); break;
                case "ModuleType": _ModuleType = Convert.ToString(value); break;
                case "ModuleId": _ModuleId = Convert.ToString(value); break;
                case "SdkVersion": _SdkVersion = Convert.ToString(value); break;
                case "DeviceMac": _DeviceMac = Convert.ToString(value); break;
                case "DeduplicateInfo": _DeduplicateInfo = Convert.ToString(value); break;
                case "TestTime": _TestTime = Convert.ToString(value); break;
                case "TestFactory": _TestFactory = Convert.ToString(value); break;
                case "TestPosition": _TestPosition = Convert.ToString(value); break;
                case "TestData": _TestData = Convert.ToString(value); break;
                case "SaleInfo": _SaleInfo = Convert.ToString(value); break;
                case "CustomerInfo": _CustomerInfo = Convert.ToString(value); break;
                case "Reserve1": _Reserve1 = Convert.ToString(value); break;
                case "Reserve2": _Reserve2 = Convert.ToString(value); break;
                case "ReturnFactory": _ReturnFactory = value.ToBoolean(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static DeviceTestLogs? FindById(Int32 id)
    {
        if (id < 0) return null;

        return Find(_.Id == id);
    }

    /// <summary>根据编号查找</summary>
    /// <param name="dId">编号</param>
    /// <returns>实体列表</returns>
    public static IList<DeviceTestLogs> FindAllByDId(Int64 dId)
    {
        if (dId < 0) return [];

        return FindAll(_.DId == dId);
    }

    /// <summary>根据模块型号、mac地址查找</summary>
    /// <param name="moduleType">模块型号</param>
    /// <param name="deviceMac">mac地址</param>
    /// <returns>实体列表</returns>
    public static IList<DeviceTestLogs> FindAllByModuleTypeAndDeviceMac(String? moduleType, String deviceMac)
    {
        if (moduleType == null) return [];
        if (deviceMac.IsNullOrEmpty()) return [];

        return FindAll(_.ModuleType == moduleType & _.DeviceMac == deviceMac);
    }

    /// <summary>根据模块型号、去重信息查找</summary>
    /// <param name="moduleType">模块型号</param>
    /// <param name="deduplicateInfo">去重信息</param>
    /// <returns>实体列表</returns>
    public static IList<DeviceTestLogs> FindAllByModuleTypeAndDeduplicateInfo(String? moduleType, String? deduplicateInfo)
    {
        if (moduleType == null) return [];
        if (deduplicateInfo == null) return [];

        return FindAll(_.ModuleType == moduleType & _.DeduplicateInfo == deduplicateInfo);
    }

    /// <summary>根据创建时间查找</summary>
    /// <param name="createTime">创建时间</param>
    /// <returns>实体列表</returns>
    public static IList<DeviceTestLogs> FindAllByCreateTime(DateTime createTime)
    {
        if (createTime.Year < 1000) return [];

        return FindAll(_.CreateTime == createTime);
    }

    /// <summary>根据模块型号查找</summary>
    /// <param name="moduleType">模块型号</param>
    /// <returns>实体列表</returns>
    public static IList<DeviceTestLogs> FindAllByModuleType(String? moduleType)
    {
        if (moduleType == null) return [];

        return FindAll(_.ModuleType == moduleType);
    }
    #endregion

    #region 数据清理
    /// <summary>清理指定时间段内的数据</summary>
    /// <param name="start">开始时间。未指定时清理小于指定时间的所有数据</param>
    /// <param name="end">结束时间</param>
    /// <returns>清理行数</returns>
    public static Int32 DeleteWith(DateTime start, DateTime end)
    {
        return Delete(_.DId.Between(start, end, Meta.Factory.Snow));
    }
    #endregion

    #region 字段名
    /// <summary>取得生产测试数据字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>编号</summary>
        public static readonly Field DId = FindByName("DId");

        /// <summary>批次号，建议每一批或定一个数字号</summary>
        public static readonly Field BatchNum = FindByName("BatchNum");

        /// <summary>模块型号</summary>
        public static readonly Field ModuleType = FindByName("ModuleType");

        /// <summary>模块id,允许重复</summary>
        public static readonly Field ModuleId = FindByName("ModuleId");

        /// <summary>固件版本</summary>
        public static readonly Field SdkVersion = FindByName("SdkVersion");

        /// <summary>mac地址，允许重复</summary>
        public static readonly Field DeviceMac = FindByName("DeviceMac");

        /// <summary>去重信息</summary>
        public static readonly Field DeduplicateInfo = FindByName("DeduplicateInfo");

        /// <summary>检测时间</summary>
        public static readonly Field TestTime = FindByName("TestTime");

        /// <summary>检测工厂</summary>
        public static readonly Field TestFactory = FindByName("TestFactory");

        /// <summary>检测工位</summary>
        public static readonly Field TestPosition = FindByName("TestPosition");

        /// <summary>监测数据</summary>
        public static readonly Field TestData = FindByName("TestData");

        /// <summary>销售信息</summary>
        public static readonly Field SaleInfo = FindByName("SaleInfo");

        /// <summary>客户信息</summary>
        public static readonly Field CustomerInfo = FindByName("CustomerInfo");

        /// <summary>预留字段1</summary>
        public static readonly Field Reserve1 = FindByName("Reserve1");

        /// <summary>预留自段2</summary>
        public static readonly Field Reserve2 = FindByName("Reserve2");

        /// <summary>是否返厂</summary>
        public static readonly Field ReturnFactory = FindByName("ReturnFactory");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得生产测试数据字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>编号</summary>
        public const String DId = "DId";

        /// <summary>批次号，建议每一批或定一个数字号</summary>
        public const String BatchNum = "BatchNum";

        /// <summary>模块型号</summary>
        public const String ModuleType = "ModuleType";

        /// <summary>模块id,允许重复</summary>
        public const String ModuleId = "ModuleId";

        /// <summary>固件版本</summary>
        public const String SdkVersion = "SdkVersion";

        /// <summary>mac地址，允许重复</summary>
        public const String DeviceMac = "DeviceMac";

        /// <summary>去重信息</summary>
        public const String DeduplicateInfo = "DeduplicateInfo";

        /// <summary>检测时间</summary>
        public const String TestTime = "TestTime";

        /// <summary>检测工厂</summary>
        public const String TestFactory = "TestFactory";

        /// <summary>检测工位</summary>
        public const String TestPosition = "TestPosition";

        /// <summary>监测数据</summary>
        public const String TestData = "TestData";

        /// <summary>销售信息</summary>
        public const String SaleInfo = "SaleInfo";

        /// <summary>客户信息</summary>
        public const String CustomerInfo = "CustomerInfo";

        /// <summary>预留字段1</summary>
        public const String Reserve1 = "Reserve1";

        /// <summary>预留自段2</summary>
        public const String Reserve2 = "Reserve2";

        /// <summary>是否返厂</summary>
        public const String ReturnFactory = "ReturnFactory";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
