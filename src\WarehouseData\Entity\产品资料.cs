﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>产品资料</summary>
[Serializable]
[DataObject]
[Description("产品资料")]
[BindIndex("IX_DH_ProductData_ProductDataCategoryId1", false, "ProductDataCategoryId1")]
[BindIndex("IX_DH_ProductData_ProductDataCategoryId2", false, "ProductDataCategoryId2")]
[BindTable("DH_ProductData", Description = "产品资料", ConnName = "DH", DbType = DatabaseType.None)]
public partial class ProductData : IProductData, IEntity<IProductData>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int32 _Ptype;
    /// <summary>类型 0普通 1产测固件 2出货固件 3标签模板</summary>
    [DisplayName("类型0普通1产测固件2出货固件3标签模板")]
    [Description("类型 0普通 1产测固件 2出货固件 3标签模板")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Ptype", "类型 0普通 1产测固件 2出货固件 3标签模板", "")]
    public Int32 Ptype { get => _Ptype; set { if (OnPropertyChanging("Ptype", value)) { _Ptype = value; OnPropertyChanged("Ptype"); } } }

    private Int32 _ProductDataCategoryId1;
    /// <summary>资料类别Id1</summary>
    [DisplayName("资料类别Id1")]
    [Description("资料类别Id1")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ProductDataCategoryId1", "资料类别Id1", "")]
    public Int32 ProductDataCategoryId1 { get => _ProductDataCategoryId1; set { if (OnPropertyChanging("ProductDataCategoryId1", value)) { _ProductDataCategoryId1 = value; OnPropertyChanged("ProductDataCategoryId1"); } } }

    private Int32 _ProductDataCategoryId2;
    /// <summary>资料类别Id2</summary>
    [DisplayName("资料类别Id2")]
    [Description("资料类别Id2")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ProductDataCategoryId2", "资料类别Id2", "")]
    public Int32 ProductDataCategoryId2 { get => _ProductDataCategoryId2; set { if (OnPropertyChanging("ProductDataCategoryId2", value)) { _ProductDataCategoryId2 = value; OnPropertyChanged("ProductDataCategoryId2"); } } }

    private String? _FileName;
    /// <summary>文件名</summary>
    [DisplayName("文件名")]
    [Description("文件名")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("FileName", "文件名", "")]
    public String? FileName { get => _FileName; set { if (OnPropertyChanging("FileName", value)) { _FileName = value; OnPropertyChanged("FileName"); } } }

    private String? _FilePath;
    /// <summary>资料路径</summary>
    [DisplayName("资料路径")]
    [Description("资料路径")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("FilePath", "资料路径", "")]
    public String? FilePath { get => _FilePath; set { if (OnPropertyChanging("FilePath", value)) { _FilePath = value; OnPropertyChanged("FilePath"); } } }

    private String? _Version;
    /// <summary>版本号</summary>
    [DisplayName("版本号")]
    [Description("版本号")]
    [DataObjectField(false, false, true, 255)]
    [BindColumn("Version", "版本号", "")]
    public String? Version { get => _Version; set { if (OnPropertyChanging("Version", value)) { _Version = value; OnPropertyChanged("Version"); } } }

    private String? _Remark;
    /// <summary>备注</summary>
    [DisplayName("备注")]
    [Description("备注")]
    [DataObjectField(false, false, true, 255)]
    [BindColumn("Remark", "备注", "")]
    public String? Remark { get => _Remark; set { if (OnPropertyChanging("Remark", value)) { _Remark = value; OnPropertyChanged("Remark"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductData model)
    {
        Id = model.Id;
        Ptype = model.Ptype;
        ProductDataCategoryId1 = model.ProductDataCategoryId1;
        ProductDataCategoryId2 = model.ProductDataCategoryId2;
        FileName = model.FileName;
        FilePath = model.FilePath;
        Version = model.Version;
        Remark = model.Remark;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "Ptype" => _Ptype,
            "ProductDataCategoryId1" => _ProductDataCategoryId1,
            "ProductDataCategoryId2" => _ProductDataCategoryId2,
            "FileName" => _FileName,
            "FilePath" => _FilePath,
            "Version" => _Version,
            "Remark" => _Remark,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "Ptype": _Ptype = value.ToInt(); break;
                case "ProductDataCategoryId1": _ProductDataCategoryId1 = value.ToInt(); break;
                case "ProductDataCategoryId2": _ProductDataCategoryId2 = value.ToInt(); break;
                case "FileName": _FileName = Convert.ToString(value); break;
                case "FilePath": _FilePath = Convert.ToString(value); break;
                case "Version": _Version = Convert.ToString(value); break;
                case "Remark": _Remark = Convert.ToString(value); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static ProductData? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据资料类别Id1查找</summary>
    /// <param name="productDataCategoryId1">资料类别Id1</param>
    /// <returns>实体列表</returns>
    public static IList<ProductData> FindAllByProductDataCategoryId1(Int32 productDataCategoryId1)
    {
        if (productDataCategoryId1 < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.ProductDataCategoryId1 == productDataCategoryId1);

        return FindAll(_.ProductDataCategoryId1 == productDataCategoryId1);
    }

    /// <summary>根据资料类别Id2查找</summary>
    /// <param name="productDataCategoryId2">资料类别Id2</param>
    /// <returns>实体列表</returns>
    public static IList<ProductData> FindAllByProductDataCategoryId2(Int32 productDataCategoryId2)
    {
        if (productDataCategoryId2 < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.ProductDataCategoryId2 == productDataCategoryId2);

        return FindAll(_.ProductDataCategoryId2 == productDataCategoryId2);
    }
    #endregion

    #region 字段名
    /// <summary>取得产品资料字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>类型 0普通 1产测固件 2出货固件 3标签模板</summary>
        public static readonly Field Ptype = FindByName("Ptype");

        /// <summary>资料类别Id1</summary>
        public static readonly Field ProductDataCategoryId1 = FindByName("ProductDataCategoryId1");

        /// <summary>资料类别Id2</summary>
        public static readonly Field ProductDataCategoryId2 = FindByName("ProductDataCategoryId2");

        /// <summary>文件名</summary>
        public static readonly Field FileName = FindByName("FileName");

        /// <summary>资料路径</summary>
        public static readonly Field FilePath = FindByName("FilePath");

        /// <summary>版本号</summary>
        public static readonly Field Version = FindByName("Version");

        /// <summary>备注</summary>
        public static readonly Field Remark = FindByName("Remark");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得产品资料字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>类型 0普通 1产测固件 2出货固件 3标签模板</summary>
        public const String Ptype = "Ptype";

        /// <summary>资料类别Id1</summary>
        public const String ProductDataCategoryId1 = "ProductDataCategoryId1";

        /// <summary>资料类别Id2</summary>
        public const String ProductDataCategoryId2 = "ProductDataCategoryId2";

        /// <summary>文件名</summary>
        public const String FileName = "FileName";

        /// <summary>资料路径</summary>
        public const String FilePath = "FilePath";

        /// <summary>版本号</summary>
        public const String Version = "Version";

        /// <summary>备注</summary>
        public const String Remark = "Remark";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
