﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>第三方对接</summary>
public partial class OpenPlatformModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>商户号</summary>
    public String AccessId { get; set; } = null!;

    /// <summary>商户密钥</summary>
    public String? AccessKey { get; set; }

    /// <summary>是否启用</summary>
    public Boolean Enabled { get; set; }

    /// <summary>IP白名单</summary>
    public String? IPWhite { get; set; }

    /// <summary>项目名称</summary>
    public String ProjectName { get; set; } = null!;

    /// <summary>备注</summary>
    public String? Remark { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IOpenPlatform model)
    {
        Id = model.Id;
        AccessId = model.AccessId;
        AccessKey = model.AccessKey;
        Enabled = model.Enabled;
        IPWhite = model.IPWhite;
        ProjectName = model.ProjectName;
        Remark = model.Remark;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
