.loader-main {
	position: fixed;
	width: 100%;
	height: 100%;
	background-color: whitesmoke;
	z-index: 9999999;
}

.loader {
	width: 50px;
	height: 50px;
	margin: 30px auto 40px;
	margin-top: 20%;
	position: relative;
	z-index: 999999;
	background-color: whitesmoke;
}

	.loader:before {
		content: "";
		width: 50px;
		height: 7px;
		border-radius: 50%;
		background: #000;
		opacity: 0.1;
		position: absolute;
		top: 59px;
		left: 0;
		animation: shadow .5s linear infinite;
	}

	.loader:after {
		content: "";
		width: 50px;
		height: 50px;
		border-radius: 3px;
		background-color: #5FB878;
		position: absolute;
		top: 0;
		left: 0;
		animation: loading .5s linear infinite;
	}

@-webkit-keyframes loading {
	17% {
		border-bottom-right-radius: 3px;
	}

	25% {
		transform: translateY(9px) rotate(22.5deg);
	}

	50% {
		transform: translateY(18px) scale(1, 0.9) rotate(45deg);
		border-bottom-right-radius: 40px;
	}

	75% {
		transform: translateY(9px) rotate(67.5deg);
	}

	100% {
		transform: translateY(0) rotate(90deg);
	}
}

@keyframes loading {
	17% {
		border-bottom-right-radius: 3px;
	}

	25% {
		transform: translateY(9px) rotate(22.5deg);
	}

	50% {
		transform: translateY(18px) scale(1, 0.9) rotate(45deg);
		border-bottom-right-radius: 40px;
	}

	75% {
		transform: translateY(9px) rotate(67.5deg);
	}

	100% {
		transform: translateY(0) rotate(90deg);
	}
}

@-webkit-keyframes shadow {

	0%, 100% {
		transform: scale(1, 1);
	}

	50% {
		transform: scale(1.2, 1);
	}
}

@keyframes shadow {

	0%, 100% {
		transform: scale(1, 1);
	}

	50% {
		transform: scale(1.2, 1);
	}
}
