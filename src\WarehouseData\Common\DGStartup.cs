﻿using DG;

using DH;
using DH.Core.Infrastructure;
using DH.Entity;
using DH.Helpers;
using DH.ServerSentEvents;
using DH.SignalR;
using DH.SignalR.Dtos;
using DH.SLazyCaptcha;
using DH.SLazyCaptcha.Generator;

using HlktechIoT.Areas.Admin.Controllers;
using HlktechIoT.Areas.Production;
using HlktechIoT.Areas.Production.Controllers;
using HlktechIoT.Areas.Wms;
using HlktechIoT.Areas.Wms.Controllers;
using HlktechIoT.Common;
using HlktechIoT.Entity;
using HlktechIoT.Services.SSE;
using HlktechIoT.SignalR;

using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.AspNetCore.SignalR;

using NewLife;
using NewLife.Caching;
using NewLife.Caching.Services;
using NewLife.Common;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Security;
using NewLife.Serialization;

using Pek;
using Pek.Configs;
using Pek.Infrastructure;
using Pek.VirtualFileSystem;

using System.Data;
using System.Text.Json;
using XCode;
using XCode.DataAccessLayer;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

using IHostedService = Microsoft.Extensions.Hosting.IHostedService;

namespace HlktechIoT;

public class DGStartup : IPekStartup {
    public void Configure(IApplicationBuilder application)
    {
        var set = SysConfig.Current;
        if (set.IsNew || set.Name == "NewLife.Cube" || set.DisplayName == "HlktechIoT" || set.DisplayName == "创楚平台")
        {
            set.DisplayName = "海凌科WMS系统";
            set.Save();
        }

        var site = SiteInfo.FindDefault();
        if (site != null)
        {
            site.SiteName = "海凌科WMS系统";
            site.SeoTitle = "海凌科WMS系统";
            site.SeoKey = "海凌科WMS系统";
            site.SeoDescribe = "海凌科WMS系统";
            site.Update();
        }
        else
        {
            site = new SiteInfo();
            site.SiteName = "海凌科WMS系统";
            site.SeoTitle = "海凌科WMS系统";
            site.SeoKey = "海凌科WMS系统";
            site.SeoDescribe = "海凌科WMS系统";
            site.Insert();
        }

        var model = User.FindByName("admin");
        if (model != null && model.Password == "21232F297A57A5A743894A0E4A801FC3")
        {
            var PasswordProvider = new SaltPasswordProvider();

            model.RoleID = 1;
            model.Enable = true;
            model.Password = PasswordProvider.Hash("giciskynet".MD5());
            model.RegisterTime = DateTime.Now;
            model.RegisterIP = Pek.Helpers.DHWeb.IP;
            model.Ex1 = 1; //1是系统管理员用户
            model.Save();
        }

        var _cacheProvider = EngineContext.Current.Resolve<ICacheProvider>();
        var _source = new CancellationTokenSource();

        _ = Task.Run(() => ConsumeMessage(_cacheProvider, _source));  // 处理其他端传过来的数据

        #region 初始化数据

        if (OtherMsgTpl.Meta.Cache.Entities.Count == 0)
        {
            if (XTrace.Debug) XTrace.WriteLine("开始初始化OtherMsgTpl[其他消息模板]数据……");

            var list1 = new List<OtherMsgTpl>();

            var entity = new OtherMsgTpl();
            entity.MName = "<strong>[用户]</strong>重置密码通知";
            entity.MTitle = "找回密码通知 - {site_name}";
            entity.MCode = "FindPassword";
            entity.MContent = "【海凌科】您正在申请找回登录密码，动态码：{code}，该验证码5分钟内有效，请勿泄露于他人";
            list1.Add(entity);

            entity = new OtherMsgTpl();
            entity.MName = "<strong>[用户]</strong>注册账户通知";
            entity.MTitle = "注册账户通知 - {site_name}";
            entity.MCode = "RegisteredCode";
            entity.MContent = "【海凌科】您正在申请注册会员，动态码：{code}，该验证码5分钟内有效，请勿泄露于他人";
            list1.Add(entity);

            list1.Insert();

            if (XTrace.Debug) XTrace.WriteLine("完成初始化OtherMsgTpl[其他消息模板]数据！");
        }

        #endregion

        LocaleStringResource.InitInsert("保存成功", "保存成功", "保存成功", "Successfully saved");
        LocaleStringResource.InitInsert("没有登录或登录超时！", "没有登录或登录超时！", "沒有登錄或登錄超時！", "No logins or login timeouts!");
        LocaleStringResource.InitInsert("海凌科IOT云", "海凌科IOT云", "海淩科IOT雲", "Hi-Link IOT Cloud", true);
        LocaleStringResource.InitInsert("一 万 年 太 久，只 争 朝 夕", "一 万 年 太 久，只 争 朝 夕", "一 萬 年 太 久，只 爭 朝 夕", "Ten thousand years is too long, just to seize the day.");
        LocaleStringResource.InitInsert("工作空间", "工作空间", "工作空間", "Workspace");

        var sitemap = CronJob.FindByName("Sitemap");
        if (sitemap != null)
        {
            sitemap.Enable = false;
            sitemap.Update();
        }
    }

    /// <summary>
    /// 将区域路由写入数据库
    /// </summary>
    public void ConfigureArea()
    {
        AreaBase.SetRoute<MainController>(AdminArea.AreaName);
        AreaBase.SetRoute<HardwareController>(WmsArea.AreaName);
        AreaBase.SetRoute<ProductionsController>(ProductionArea.AreaName);
    }

    public void ConfigureServices(IServiceCollection services, IConfiguration configuration, IWebHostEnvironment webHostEnvironment)
    {
        // 系统设置
        var set = IoTSetting.Current;
        services.AddSingleton(set);

        //// MQTT服务
        //services.AddMqtt();

        if (RedisSetting.Current.RedisEnabled)
        {
            var fulleRedis = new FullRedis(RedisSetting.Current.RedisConnectionString, RedisSetting.Current.RedisPassWord!, RedisSetting.Current.RedisDatabaseId);
            ObjectContainer.Current.AddSingleton(fulleRedis); // 全局注入Redis连接
            services.AddSingleton(fulleRedis);

            //CacheFields.QueueRedis = new FullRedis(UtilSetting.Current.RedisConnectionString, UtilSetting.Current.RedisPassWord, UtilSetting.Current.RedisQueueDatabaseId);

            //CacheFields.FullRedis = new FullRedis(UtilSetting.Current.RedisConnectionString, UtilSetting.Current.RedisPassWord, UtilSetting.Current.RedisOtherDatabaseId);
        }

        // 验证码
        // 内存缓存
        //services.AddCaptcha(configuration);
        services.AddCaptcha(configuration, option =>
        {
            option.CaptchaType = CaptchaType.WORD_NUMBER_UPPER; // 验证码类型
            option.CodeLength = 4; // 验证码长度, 要放在CaptchaType设置后.  当类型为算术表达式时，长度代表操作的个数
            option.ExpirySeconds = 60; // 验证码过期时间
            option.IgnoreCase = true; // 比较时是否忽略大小写
            option.StoreageKeyPrefix = ""; // 存储键前缀

            option.ImageOption.Animation = true; // 是否启用动画
            option.ImageOption.FrameDelay = 120; // 每帧延迟,Animation=true时有效, 默认30

            option.ImageOption.Width = 150; // 验证码宽度
            option.ImageOption.Height = 50; // 验证码高度
            option.ImageOption.BackgroundColor = SkiaSharp.SKColors.White; // 验证码背景色

            option.ImageOption.BubbleCount = 2; // 气泡数量
            option.ImageOption.BubbleMinRadius = 5; // 气泡最小半径
            option.ImageOption.BubbleMaxRadius = 15; // 气泡最大半径
            option.ImageOption.BubbleThickness = 1; // 气泡边沿厚度

            option.ImageOption.InterferenceLineCount = 2; // 干扰线数量

            option.ImageOption.FontSize = 36; // 字体大小
            option.ImageOption.FontFamily = DefaultFontFamilys.Instance.Actionj; // 字体

            /* 
             * 中文使用kaiti，其他字符可根据喜好设置（可能部分转字符会出现绘制不出的情况）。
             * 当验证码类型为“ARITHMETIC”时，不要使用“Ransom”字体。（运算符和等号绘制不出来）
             */

            option.ImageOption.TextBold = true;// 粗体，该配置2.0.3新增
        });

        #region SSE

        // 注册默认的服务器发送事件服务。
        services.AddServerSentEvents();

        // 注册将由第二个中间件使用的自定义ServerSentEventsService，否则它们最终将共享连接的用户。
        services.AddServerSentEvents<INotificationsServerSentEventsService, NotificationsServerSentEventsService>(options =>
        {
            options.ReconnectInterval = 5000;
        });

        // 为服务器发送的事件注册基于 Cookie 的客户端标识符提供程序
        // services.AddServerSentEventsClientIdProvider<CookieBasedServerSentEventsClientIdProvider>();

        // 注册IServerSentEventsNoReconnectClientsIdsStorage由内存存储支持。
        // services.AddInMemoryServerSentEventsNoReconnectClientsIdsStore();

        services.AddSingleton<IHostedService, HeartbeatService>();
        services.AddNotificationsService(configuration);

        services.AddResponseCompression(options =>
        {
            options.MimeTypes = ResponseCompressionDefaults.MimeTypes.Concat(new[] { "text/event-stream" });
        });

        #endregion
    }

    /// <summary>
    /// 调整菜单
    /// </summary>
    public void ChangeMenu()
    {

    }

    /// <summary>
    /// 注册虚拟文件路径
    /// </summary>
    /// <param name="options"></param>
    public void ConfigureVirtualFileSystem(DHVirtualFileSystemOptions options)
    {
    }

    /// <summary>
    /// 注册路由
    /// </summary>
    /// <param name="endpoints">路由生成器</param>
    public void UseDHEndpoints(IEndpointRouteBuilder endpoints)
    {
        //endpoints.MapGrpcService<GreeterService>().RequireCors(CubeService.corsPolicy).EnableGrpcWeb();

        // 设置第一个服务器发送的事件终结点。
        endpoints.MapServerSentEvents("/see-heartbeat");

        // 设置第二个（分隔的）服务器发送事件终结点。
        endpoints.MapServerSentEvents<NotificationsServerSentEventsService>("/sse-notifications");

        endpoints.MapHub<BigDataHub>("/bigdata-hub");
    }

    /// <summary>
    /// 升级更新逻辑
    /// </summary>
    public void Update()
    {
        Task.Run(() =>
        {
            var updateinfo = DHSetting.Current.UpdateInfo.Split("_");
            XTrace.WriteLine("当前版本号：" + DHSetting.Current.UpdateInfo);

            if (new Version(DHSetting.Current.CurrentVersion) == new Version("0.2.0"))
            {
                #region 0.2.0
                if (new Version(updateinfo[0]) < new Version("0.2.0") || (updateinfo[0] == "0.2.0" && updateinfo[1] == "0"))
                {
                    var dal = DAL.Create("TestLogs");

                    var tables = dal.Query($"select table_name as tableName from information_schema.tables where table_schema ='device_log' AND table_name LIKE '%device_log%'");

                    var snowflake = new Snowflake();

                    foreach (var item in tables)
                    {
                        var table = item[0].SafeString();
                        XTrace.WriteLine($"{table}表写入雪花ID");
                        var data = dal.Session.Query($"select id, create_time, DId from {table}");

                        foreach (DataRow row in data.Tables[0].Rows)
                        {
                            var DId = row["DId"].ToLong();
                            if (DId <= 0)
                            {
                                DId = snowflake.NewId((DateTime)row["create_time"], row["id"].ToInt());
                                dal.Execute($"Update {table} set DId = {DId} where Id = {row["id"]}");
                            }
                        }
                    }

                    DHSetting.Current.UpdateInfo = $"0.2.0_1";
                    DHSetting.Current.Save();
                }
                #endregion
            }
            else if (new Version(DHSetting.Current.CurrentVersion) == new Version("0.2.1"))
            {
                if (updateinfo[0] != "0.2.1" || (updateinfo[0] == "0.2.1" && updateinfo[1] == "0"))
                {
                    var list = ProductOrders.FindAll();
                    foreach (var item in list)
                    {
                        var modelProductType = ProductType.FindById(item.ProductTypeId);
                        if (modelProductType != null)
                        {
                            var stateInfo = new
                            {
                                modelProductType.NeedSn,
                                modelProductType.NeedMac,
                                modelProductType.NeedMac1,
                                modelProductType.PCBCycle,
                                modelProductType.ShieldCycle,
                                modelProductType.MainChipCycle,
                            };
                            var stateJson = JsonSerializer.Serialize(stateInfo);
                            item.DataInfo = stateJson;
                            item.Update();
                        }
                    }

                    DHSetting.Current.UpdateInfo = $"0.2.1_1";
                    DHSetting.Current.Save();
                }
                else if (updateinfo[0] != "0.2.1" || (updateinfo[0] == "0.2.1" && updateinfo[1] == "1"))
                {
                    var list = ProductSnMac.FindAll(ProductSnMac._.Id.Between(Convert.ToDateTime("2025-01-01 00:00:00"),Convert.ToDateTime("2026-01-01 00:00:00"), ProductSnMac.Meta.Factory.Snow));
                    List<string> snList = new List<string>();   
                    foreach (var item in list)
                    {
                        if (snList.Contains(item.Sn))
                        {
                            continue;
                        }
                        snList.Add(item.Sn);
                        var modelSns = new DeviceSns
                        {
                            Sn = item.Sn,
                            AssociationId = item.Id,
                            OrderId = item.OrderId,
                        };
                        modelSns.Insert();
                    }

                    DHSetting.Current.UpdateInfo = $"0.2.1_2";
                    DHSetting.Current.Save();
                }
                else if (updateinfo[0] != "0.2.1" || (updateinfo[0] == "0.2.1" && updateinfo[1] == "2"))
                {

                    var listProductType = ProductType.FindAll();

                    foreach (var item in listProductType)
                    {
                        if (item.PType.IsNull())
                        {
                            item.PType = 0; // 默认无
                            item.Update();
                        }
                    }

                    DHSetting.Current.UpdateInfo = $"0.2.1_3";
                    DHSetting.Current.Save();
                }
                else if (updateinfo[0] != "0.2.1" || (updateinfo[0] == "0.2.1" && updateinfo[1] == "3"))
                {

                    var listProductType = ProductType.FindAll();

                    foreach (var item in listProductType)
                    {
                        if (item.PType != 1)
                        {
                            item.PType = 0; // 默认无
                            item.Update();
                        }
                    }

                    DHSetting.Current.UpdateInfo = $"0.2.1_4";
                    DHSetting.Current.Save();
                }
            }
        });
    }

    private async Task ConsumeMessage(ICacheProvider redis, CancellationTokenSource source)
    {
        XTrace.WriteLine($"启动了吗？");

        var cancellationToken = source.Token;

        var ITracer = EngineContext.Current.Resolve<ITracer>();

        var topic = $"{RedisSetting.Current.CacheKeyPrefix}:device";
        if (redis is RedisCacheProvider cacheProvider)
        {
            var _notifyHub = EngineContext.Current.Resolve<IHubContext<BigDataHub, IClientNotifyHub>>();

            var queue = cacheProvider.GetQueue<String>(topic);
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    ISpan? span = null;
                    var mqMsg = await queue.TakeOneAsync(30);
                    if (mqMsg != null)
                    {
                        // 埋点
                        span = ITracer?.NewSpan($"{RedisSetting.Current.CacheKeyPrefix}:device", mqMsg);

                        XTrace.WriteLine($"接收队列数据：{mqMsg}");

                        // 解码
                        var modelNotify = mqMsg.ToJsonEntity<NotifyConnectsData>();

                        if (modelNotify != null)
                        {
                            await _notifyHub.Clients.Group("bigdata_index").OnNotify(modelNotify);
                        }
                    }
                }
            }
            catch (TaskCanceledException) { }
            catch (Exception ex)
            {
                XTrace.WriteException(ex);
            }
            finally
            {
                source.Cancel();
            }
        }
    }

    public void AfterAuth(IApplicationBuilder application)
    {
    }

    public void BeforeRouting(IApplicationBuilder application)
    {
    }

    public void ConfigureMiddleware(IApplicationBuilder application)
    {
    }

    /// <summary>
    /// 处理数据
    /// </summary>
    public void ProcessData()
    {
    }

    /// <summary>
    /// 获取此启动配置实现的顺序
    /// </summary>
    public int StartupOrder => 999; //常见服务应在错误处理程序之后加载

    /// <summary>
    /// 获取此启动配置实现的顺序。主要针对ConfigureMiddleware、UseRouting前执行的数据、UseAuthentication或者UseAuthorization后面 Endpoints前执行的数据
    /// </summary>
    public Int32 ConfigureOrder => 100;
}
