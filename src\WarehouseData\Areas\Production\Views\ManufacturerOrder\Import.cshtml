﻿@{
    Html.AppendTitleParts(T("导入").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
    Html.AppendCssFileParts(ResourceLocation.Head, "~/libs/pear/css/module/dtree/dtree.css");
    Html.AppendCssFileParts(ResourceLocation.Head, "~/libs/pear/css/module/dtree/font/dtreefont.css");
}
<style asp-location="true">
    html {
        height: 100%;
        margin: 0;
        padding: 0;
        overflow: hidden;
        background-color: white;
    }

    body {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
        background-color: white;
    }

    .layui-input-block {
        margin-right: 40px;
    }

    .layui-form-label {
        /* width: 100px; */
    }

    .uploder-box {
        width: 100%;
        /* height: 40%; */
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: white;
    }

        .uploder-box .tip {
            text-align: center;
            color: #555;
            font-weight: bold;
            font-size: 1em;
            margin-top: 30px;
            opacity: .7;
        }

    .dgselect {
        margin-top: 20px;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: white;
    }
</style>
<div class="dgselect">
    <form class="layui-form">
        <div class="layui-form-item">
                <label class="layui-form-label">@T("上传类型")：</label>
                <div class="layui-input-block">
                    <select name="uploadType">
                        <option value="0">SN</option>
                        <option value="1">MAC</option>
                        <option value="2">MAC1</option>
                    </select>
                </div>
        </div>
    </form>
</div>

<div class="uploder-box">
    <div style="text-align: center;">
        <div class="layui-upload-list">
            <div class="layui-upload-drag" id="upload" width="230px">
                <i class="layui-icon"></i>
                <p>@T("点击上传，或将文件拖拽到此处")</p>
                <div class="layui-hide" id="uploadDemoView">
                    <hr>
                    <label id="excel" class="layui-form-label-left"></label>
                </div>
            </div>
            <!-- 进度条 -->
            <div class="import-info">@T("导入处理进度：")</div>
            <div class="layui-progress" lay-showpercent="true" lay-filter="demo-filter-progress">
                <div class="layui-progress-bar" lay-percent="0%"></div>
            </div>
        </div>
        <p class="tip" style="color:red;margin-top: -10px;">@T("说明：导入xlsx表格，第一行标题选填，第二行起为导入内容，格式如下图。")</p>
    </div>
</div>
<div style="text-align:center;">
    <img src="~/images/uploadtip.png" />
</div>



<script asp-location="Footer">
    var msgId;
    layui.use(['laydate', "form", "table", "jquery", "common",
    "layer", 'upload','dg', "dgcommon", 'element'], function () {
        var t = layui.form;
        var upload = layui.upload;
        var $ = layui.jquery;
        var os = layui.common;
        var dgcommon = layui.dgcommon;
        var laydate = layui.laydate;
        var element = layui.element;
        var dg = layui.dg;


        var parentList = window.parent
        var parentPage = null;
        // 第二步：拿到对应层
        for (let i = 0; i < parentList.length; i++) {
            if (parentList[i].name === 'SnView') { //这里的name自己去对应层DIY
                parentPage = parentList[i]
                break;
            }
        }
        // 第三步：操作对应层
        var parent_window = parentPage.window  //获取父层的window层
        var parent_layer = parentPage.layer //获取父层的layer
        var parent_notify = parentPage.layui.abp.notify //获取父层的layui.notify --消息通知
        var parent_layui  = parentPage.layui  //获取父层的layui
        var currentPageCloseIndex = parent_window.importPageIndex //当前层的关闭index下标

        t.render();
        //拖拽阈值表上传
        const renderUpload = () => {
            upload.render({
            elem: '#upload'
            , url: '@Url.Action("Import")'
            , before: function (obj) {
                    msgId = Date.now().toString();;
                    var updatetype = $("select[name='uploadType']").val();
                    this.data = { 'orderId': '@Model.Id','type': updatetype,'msgId':msgId };
                    element.progress('filter-demo', '0%'); // 进度条复位
                    layer.msg('上传中', {icon: 16, time: 0});
                }
                , done: function (res) {
                    if (!res.success) { //失败打印
                        element.progress('demo-filter-progress', '0%');
                        active.refreshProgress();
                        layer.msg(res.msg,{icon:0});
                        // dgcommon.warning(res.msg);
                        return;
                    }
                    // 关闭当前编辑页面
                    parent.layer.close(currentPageCloseIndex);
                    parent_notify.success(res.msg);
                    parentPage.active.reload();
                }
                , accept: 'file' //允许上传的文件类型
                , exts: 'xls|xlsx' //只允许上传txt文件
            });
        }
        renderUpload();
        //刷新滚动条件
        window.active = {
            refreshProgress: function () {
                element.render('progress', 'demo-filter-progress');
            }
        };
    });

        function establishSSEConnection() {
            var eventSource = new EventSource("/sse-notifications");

            eventSource.onopen = function (event) {
                console.log("SSE connection established.");
            };

            eventSource.onmessage = function (event) {
                // console.log("Received message:", event.data);
                // console.log(event);
                const data = JSON.parse(event.data)

                if (data.typeId == 1) {
                    if (data.msgId != msgId) return;

                    showNotification(data);
                }
            };

            eventSource.onerror = function (event) {
                // console.error("SSE error:", event);
                // Handle error, e.g., reconnect here
                setTimeout(establishSSEConnection, 2000); // Reconnect after 2 seconds
            };

            eventSource.addEventListener('alert', function (event) {
                alert(event.data);
            });
        }

        establishSSEConnection();

        var showNotification = function (data) {
            // 动态插入进度条元素
            layui.element.progress('demo-filter-progress', data.process + '%');
            // 渲染进度条组件
            active.refreshProgress();
        };

        
</script>