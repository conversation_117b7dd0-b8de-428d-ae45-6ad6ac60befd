﻿namespace HlktechIoT.Dto;

public class BaseSaasModel {
    /// <summary>
    /// 平台商户号
    /// </summary>
    public String? AccessId { get; set; }

    /// <summary>
    /// 随机字符串
    /// </summary>
    public String? NonceStr { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public Int64? TimeStamp { get; set; }

    /// <summary>
    /// 签名
    /// </summary>
    public String? Sign { get; set; }

    /// <summary>
    /// 语言项
    /// </summary>
    public String? Lng { get; set; }
}

/// <summary>
/// 检查Mac存在性
/// </summary>
public class SaasProductOrderLogsMac : BaseSaasModel {
    /// <summary>
    /// 设备Mac地址
    /// </summary>
    public String? Mac { get; set; }

    /// <summary>
    /// 订单号
    /// </summary>
    public String? OrderId { get; set; }

    /// <summary>
    /// 产品名称/型号
    /// </summary>
    public String? Name { get; set; }
}