﻿using DG.Web.Framework;

using HlktechIoT.Dto.Export;
using HlktechIoT.Entity;

using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;
using NewLife.Log;

using Pek;
using Pek.Models;

using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Wms.Controllers;

/// <summary>快递单号列表</summary>
[DisplayName("快递单号列表")]
[Description("快递单号的管理")]
[WmsArea]
[DHMenu(90, ParentMenuName = "OrdersManager", ParentMenuDisplayName = "订单管理", ParentMenuUrl = "", ParentMenuOrder = 60, ParentIcon = "layui-icon-component", CurrentMenuUrl = "~/{area}/BarCode", CurrentMenuName = "BarCodeList", LastUpdate = "20240420")]
public class BarCodeController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 90;

    /// <summary>
    /// 快递单号列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("快递单号列表")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <param name="code">快递单号数据</param>
    /// <param name="types">快递类型</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("快递单号列表")]
    public IActionResult GetList(String code, String types,DateTime start, DateTime end, Int32 page = 1, Int32 limit = 10)
    {
        if (!code.IsNullOrWhiteSpace()) code = code.SafeString().Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsOrder._.Id,
            Desc = true,
        };

        if (start <= DateTime.MinValue)
        {
            start = (DateTime.Now.Year + "-1" + "-1").ToDateTime();
        }

        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now.AddDays(1).ToShortDateString().ToDateTime();
        }

        code = code.SafeString().Trim();
        var list = ExpressLogs.Search(code, types,start, end, String.Empty, pages);

        var data = list.Select(x => new
        {
            Id = x.Id.ToString(),
            x.KDTypes,
            x.Code,
            x.CreateTime
        });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }
    /// <summary>删除</summary>
    /// <param name="Id">id</param>
    /// <returns></returns>
    [DisplayName("删除")]
    [EntityAuthorize(PermissionFlags.Delete)]
    [HttpPost]
    public IActionResult Delete(Int64 Id)
    {
        var res = new DResult();
        ExpressLogs.Delete(ExpressLogs._.Id == Id);
        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>导出</summary>
    /// <param name="Ids">勾选中的ID</param>
    /// <param name="types">快递类型</param>
    /// <param name="code">快递单号数据</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [DisplayName("导出")]
    [EntityAuthorize((PermissionFlags)32)]
    public async Task<IActionResult> ExportAll(String Ids, String code,string types, DateTime start, DateTime end, Int32 page = 1, Int32 limit = 0)
    {
        IExporter exporter = new ExcelExporter();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsOrder._.Id,
            Desc = true,
        };

        List<BarCodeExport>? list = null;
        if (string.IsNullOrWhiteSpace(Ids))
        {
            var data = ExpressLogs.Search(code, types, start, end, String.Empty, pages);
            list = [.. data.Select(e => new BarCodeExport()
            {
                Id = e.Id,
                Types = e.KDTypes,
                Code = e.Code,
                CreateTime = e.CreateTime
            })];
        }
        else
        {
            var things = ExpressLogs.FindAllByIds(Ids);
            list = [.. things.Select(e => new BarCodeExport()
            {
                Id = e.Id,
                Types = e.KDTypes,
                Code = e.Code,
                CreateTime = e.CreateTime
            })];
        }
        var result = await exporter.ExportAsByteArray(list);
        return File(result, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"ExportBarCode{DateTime.Now:yyyyMMddhhmm}.xlsx");
    }
}
