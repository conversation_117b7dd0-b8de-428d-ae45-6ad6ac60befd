﻿@{
    Html.AppendTitleParts(T("短信设置").Text);
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }
</style>

<script asp-location="Head">
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("服务商")：</label>
            <div class="layui-input-inline">
                <input type="text" name="name" id="name" placeholder="@T("请输入服务商名")" autocomplete="off" class="layui-input" lay-filter="name">
            </div>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetList")'
            , page: false //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 120
            , cols: [[
                { field: 'DisplayName', width: 80, title: '@T("服务商")' }
                , { field: 'SmsType', width: 120, title: '@T("类型")', templet: '#smsType' }
                , { field: 'PassKey', width: 80, title: '@T("签名")', width: 92 }
                , { field: 'AccessKey', title: '@T("AccessKey")', minWidth: 120 }
                , { field: 'AccessSecret', title: '@T("AccessSecret")', minWidth: 150 }
                , { field: 'IsEnabled', title: '@T("启用")', templet: '#switchTpl', minWidth: 120 }
                , { field: 'SmsRegister', title: '@T("手机注册")', templet: '#smsRegister', minWidth: 80 }
                , { field: 'SmsLogin', title: '@T("手机登录")', templet: '#smsLogin', minWidth: 80 }
                , { field: 'SmsPassword', title: '@T("找回密码")', templet: '#smsPassword', minWidth: 80 }
                , { fixed: 'right', title: '@T("操作")', templet: '#tool', width: 100 }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'tables'
        });

        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        where: {
                            name: $("#name").val(),
                        }
                    });
            }
        }

        $("#name").on("input", function (e) {
            active.reload();
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'detail') {
                layer.msg('ID：' + data.ID + ' @T("的查看操作")');
            } else if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.ID }, function (data) {
                        if (data.success) {
                            abp.notify.success('@T("删除成功")');
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'edit') {
                top.layui.dg.popupRight({
                    id: 'SmsEdit'
                    , title: ' @T("编辑短信配置")'
                    , closeBtn: 1
                    , area: ['780px']
                    , success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Edit")' + abp.utils.formatString("?id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                    }
                });
            }
        });

        //监听开关事件
        form.on('switch(statusedit)', function (data) {
            //开关是否开启，true或者false
            var checked = data.elem.checked;
            //获取所属属性值
            var id = data.elem.attributes['data-id'].nodeValue;
            $.post('@Url.Action("ModifyState")', { id: id, status: checked }, function (res) {
                if (res.success) {
                    abp.notify.success(res.msg);
                    active.reload();
                }
                else {
                    abp.notify.error(res.msg);
                }
            });
        });

    });
</script>

<script type="text/html" id="tool">
    @if (this.Has((PermissionFlags)4))
    {
      <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="edit">@T("编辑")</a>
    }
    @if (this.Has((PermissionFlags)8))
    {
        <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="del">@T("删除")</a>
    }
</script>

<script type="text/html" id="smsType">
    {{# if(d.SmsType == 0) { }}
    @T("国内通知")
    {{# } else if(d.SmsType == 1) { }}
    @T("国际通知")
    {{# } else if(d.SmsType == 2) { }}
    @T("国内营销")
    {{# } else if(d.SmsType == 3) { }}
    @T("国际营销")
    {{# } }}
</script>

<script type="text/html" id="switchTpl">
    @if (this.Has((PermissionFlags)4)){
    <input type="checkbox" name="status" data-id="{{d.Id}}" lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" {{ d.IsEnabled ? 'checked' : ''}}>
    }else{
    <input type="checkbox" name="status" data-id="{{d.Id}}" lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" {{ d.IsEnabled ? 'checked' : ''}} disabled>
    }
</script>

<script type="text/html" id="smsRegister">
    {{# if(d.SmsRegister) { }}
    @T("是")
    {{# } else { }}
    @T("否")
    {{# } }}
</script>
<script type="text/html" id="smsLogin">
    {{# if(d.SmsLogin) { }}
    @T("是")
    {{# } else { }}
    @T("否")
    {{# } }}
</script>
<script type="text/html" id="smsPassword">
    {{# if(d.SmsPassword) { }}
    @T("是")
    {{# } else { }}
    @T("否")
    {{# } }}
</script>