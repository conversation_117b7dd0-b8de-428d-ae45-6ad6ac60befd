using DH.Entity;

using HlktechIoT.Dto;

using NewLife;
using NewLife.Data;
using NewLife.Log;

using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

using XCode;
using XCode.Membership;

namespace HlktechIoT.Entity;

public partial class HardwareDevices : CubeEntityBase<HardwareDevices>
{
    #region 对象操作
    static HardwareDevices()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(CreateUserID));

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add(new UserModule { AllowEmpty = false });
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add(new IPModule { AllowEmpty = false });

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (method == DataMethod.Insert && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
            if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
        }*/
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
        //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;

        // 检查唯一索引
        // CheckExist(method == DataMethod.Insert, nameof(Mac), nameof(HType));

        return true;
    }

    /// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    [EditorBrowsable(EditorBrowsableState.Never)]
    protected override void InitData()
    {
        // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
        if (Meta.Session.Count > 0) return;

        if (XTrace.Debug) XTrace.WriteLine("开始初始化HardwareDevices[硬件设备]数据……");

        var entity = new HardwareDevices();
        entity.Mac = "DD246A0A";
        entity.Code = "**********";
        entity.HType = 0;
        entity.DeviceModel = "逊镭Z2S（WIFI）";
        entity.Remark = "";
        entity.Insert();

        entity = new HardwareDevices();
        entity.Mac = "95EEF208";
        entity.Code = "6946230103";
        entity.HType = 0;
        entity.DeviceModel = "逊镭Z2S（WIFI）";
        entity.Remark = "";
        entity.Insert();

        entity = new HardwareDevices();
        entity.Mac = "0C1F4BD2";
        entity.Code = "6920230269";
        entity.HType = 0;
        entity.DeviceModel = "逊镭Z2S（WIFI）";
        entity.Remark = "";
        entity.Insert();

        entity = new HardwareDevices();
        entity.Mac = "B338AE5D";
        entity.Code = "6946230115";
        entity.HType = 0;
        entity.DeviceModel = "逊镭Z2S（WIFI）";
        entity.Remark = "";
        entity.Insert();

        entity = new HardwareDevices();
        entity.Mac = "9930F549";
        entity.Code = "6946230105";
        entity.HType = 0;
        entity.DeviceModel = "逊镭Z2S（WIFI）";
        entity.Remark = "";
        entity.Insert();

        entity = new HardwareDevices();
        entity.Mac = "C7C8179E";
        entity.Code = "6946230116";
        entity.HType = 0;
        entity.DeviceModel = "逊镭Z2S（WIFI）";
        entity.Remark = "";
        entity.Insert();

        entity = new HardwareDevices();
        entity.Mac = "BB8E6A25";
        entity.Code = "6920230278";
        entity.HType = 0;
        entity.DeviceModel = "逊镭Z2S（WIFI）";
        entity.Remark = "";
        entity.Insert();

        entity = new HardwareDevices();
        entity.Mac = "6664BF3D";
        entity.Code = "6920230265";
        entity.HType = 0;
        entity.DeviceModel = "逊镭Z2S（WIFI）";
        entity.Remark = "";
        entity.Insert();

        entity = new HardwareDevices();
        entity.Mac = "A16DE430";
        entity.Code = "6920230259";
        entity.HType = 0;
        entity.DeviceModel = "逊镭Z2S（WIFI）";
        entity.Remark = "";
        entity.Insert();

        entity = new HardwareDevices();
        entity.Mac = "56CC5E16";
        entity.Code = "6920230277";
        entity.HType = 0;
        entity.DeviceModel = "逊镭Z2S（WIFI）";
        entity.Remark = "";
        entity.Insert();

        entity = new HardwareDevices();
        entity.Mac = "0EE5FB19";
        entity.Code = "6946230109";
        entity.HType = 0;
        entity.DeviceModel = "逊镭Z2S（WIFI）";
        entity.Remark = "";
        entity.Insert();

        entity = new HardwareDevices();
        entity.Mac = "3705AE70";
        entity.Code = "N401W50020";
        entity.HType = 0;
        entity.DeviceModel = "逊镭W8（WIFI）";
        entity.Remark = "";
        entity.Insert();

        if (XTrace.Debug) XTrace.WriteLine("完成初始化HardwareDevices[硬件设备]数据！");
    }

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    /// <summary>绑定用户</summary>
    [XmlIgnore, IgnoreDataMember]
    [ScriptIgnore]
    public User? User => Extends.Get(nameof(User), k => User.FindByID(BindUserID));

    /// <summary>绑定用户详情</summary>
    [XmlIgnore, IgnoreDataMember]
    [ScriptIgnore]
    public UserDetail? UserDetail => Extends.Get(nameof(UserDetail), k => UserDetail.FindById(BindUserID));
    #endregion

    #region 扩展查询
    /// <summary>根据设备所在工序查找</summary>
    /// <param name="status">设备所在工序</param>
    /// <returns>实体列表</returns>
    public static IList<HardwareDevices> FindAllByStatus(Int32 status)
    {
        if (status <= 0) return new List<HardwareDevices>();

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Status == (HardwareStatus)status);

        return FindAll(_.Status == status);
    }

    /// <summary>
    /// 返回所有数据
    /// </summary>
    /// <returns>实体集合</returns>
    public static IList<HardwareDevices> GetAll(String? selects = null)
    {
        if (Meta.Session.Count < 1000) return Meta.Cache.Entities;

        return FindAll(null, null, selects);
    }
    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="mac">使用设备Mac地址</param>
    /// <param name="hType">设备类型。0为扫码枪</param>
    /// <param name="start">更新时间开始</param>
    /// <param name="end">更新时间结束</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<HardwareDevices> Search(String mac, Int16 hType, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (!mac.IsNullOrEmpty()) exp &= _.Mac == mac;
        if (hType >= 0) exp &= _.HType == hType;
        exp &= _.UpdateTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= _.Mac.Contains(key) | _.Code.Contains(key) | _.Remark.Contains(key) | _.CreateUser.Contains(key) | _.CreateIP.Contains(key) | _.UpdateUser.Contains(key) | _.UpdateIP.Contains(key);

        return FindAll(exp, page);
    }

    // Select Count(Id) as Id,Category From DH_HardwareDevices Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<HardwareDevices> _CategoryCache = new FieldCache<HardwareDevices>(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public IHardwareDevices ToModel()
    {
        var model = new HardwareDevices();
        model.Copy(this);

        return model;
    }

    #endregion
}