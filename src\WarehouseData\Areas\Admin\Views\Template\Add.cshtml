﻿@{
    Html.AppendTitleParts(T("新增模板").Text);

    var localizationSettings = LocalizationSettings.Current;
}
<style asp-location="true">
    .container .table > tbody > tr > td {
        padding: 5px 5px;
    }

    html {
        background-color: transparent !important;
    }

    body {
        height: 100%;
    }

    .containers {
        width: 100%;
        /* padding-top: 20px;*/
        height: 100%;
        position: relative
    }

    .bala {
        /* float: right; */
        /* padding-right: 66px; */
        padding-bottom: 20px;
        padding-top: 10px;
        text-align: center;
    }

    .container.form-horizontal {
        width: 100%;
    }

    body {
        background-color: #FFFFFF
    }

    input[type=checkbox], input[type=radio] {
        vertical-align: sub !important;
    }

    .containers .form-horizontal .form-group {
        margin-right: 15px;
        margin-left: 15px;
    }

    input[type=checkbox], input[type=radio] {
        margin-left: 10px !important;
        height: 17px;
    }

    .form-group .tables {
        width: 80%
    }

    .input-group.col-sm-9.tables {
        padding-right: 20px;
    }

    .layui-tab.layui-tab-card {
        width: 85%;
        margin: 0 auto;
        margin-bottom: 10px
    }

    .layui-tab.layui-tab-brief {
        width: 85%;
        margin: 0 auto;
        margin-bottom: 10px
    }

    .layui-form-item {
        margin-top: 20px;
    }

    .layui-input-block {
        margin-left: 80px;
    }

    table#tables {
        margin: 0 auto;
        width: 653px;
        border-width: 1px;
        border-style: solid;
        border-radius: 2px;
        box-shadow: 0 2px 5px 0 rgba(0,0,0,.1);
        border-color: #e6e6e6;
    }

        table#tables td {
            border-width: 1px;
            border-style: solid;
            border-radius: 2px;
            border-color: #e6e6e6;
            line-height: 35px;
            padding-left: 10px
        }

        table#tables th {
            border-width: 1px;
            border-style: solid;
            border-radius: 2px;
            border-color: #e6e6e6;
            line-height: 35px;
            text-align: center;
            min-width: 55px;
        }

        table#tables td.childpf {
            padding-left: 0px !important;
        }

    .layui-textarea {
        resize: none;
        /*width: 90%*/
    }

    .layui-tab.layui-tab-brief {
        margin: 0 auto;
        width: 653px;
    }

    .layui-form-label {
        text-align: left;
        padding: 0;
        padding-right: 20px;
        font-size: 13px;
        height: 36px;
        line-height: 36px;
    }

    .layui-input-block {
        margin-left: 0;
    }

    .layui-textarea {
        min-height: 80px;
    }

    .layui-tab-content {
        padding: 0;
    }

    .layui-input, .layui-textarea {
        border-radius: 5px;
        white-space: nowrap;
    }

    .layui-tab-brief > .layui-tab-title .layui-this {
        color: black;
    }

        .layui-tab-brief > .layui-tab-title .layui-this:after {
            border-bottom: 2px solid #1E88E5;
        }

    .editor—wrapper {
        border: 1px solid #ccc;
        z-index: 100; /* 按需定义 */
        margin-top: 20px;
    }

    .toolbar-container {
        border-bottom: 1px solid #ccc;
    }

    .editor-container {
        height: 300px;
    }

    .btn {
        text-align: center;
    }
</style>
<div class="containers">
    <form class="layui-form">
        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            @if (localizationSettings.IsEnable)
            {
                <ul class="layui-tab-title">
                    <li class="layui-this">@T("标准")</li>

                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <li>@item.DisplayName</li>
                    }
                </ul>
            }
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="layui-form-item">
                        <label class="layui-form-label"><span style="color: red;">*</span>@T("模板名称"):</label>
                        <div class="layui-input-block">
                            <input type="text" name="Name" lay-verify="name" value="" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><span style="color: red;">*</span>@T("标题"):</label>
                        <div class="layui-input-block">
                            <input type="text" name="MTitle" lay-verify="title" value="" autocomplete="off" placeholder="@T("请输入标题")" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label"><span style="color: red;">*</span>@T("正文"):</label><br />
                        <div class="layui-input-block">
                            <textarea placeholder="@T("请输入正文")" name="MContent" class="layui-textarea"></textarea>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">@T("短信模板Id"):</label><br />
                        <div class="layui-input-block">
                            <input type="text" name="SmsTplId" lay-verify="smstplid" value="" autocomplete="off" placeholder="@T("请输入短信模板Id")" class="layui-input">
                        </div>
                    </div>
                </div>
                @if (localizationSettings.IsEnable)
                {
                    @foreach (var item in (IEnumerable<DH.Entity.Language>)ViewBag.LanguageList)
                    {
                        <div class="layui-tab-item">
                            <div class="layui-form-item">
                                <label class="layui-form-label"><span style="color: red;">*</span>@T("模板名称"):</label>
                                <div class="layui-input-block">
                                    <input type="text" name="[@item.Id].Name" lay-verify="name" value="" autocomplete="off" class="layui-input">
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label"><span style="color: red;">*</span>@T("标题"):</label>
                                <div class="layui-input-block">
                                    <input type="text" name="[@item.Id].MTitle" lay-verify="title" value="" autocomplete="off" placeholder="@T("请输入标题")" class="layui-input">
                                </div>
                            </div>

                            <div class="layui-form-item layui-form-text">
                                <label class="layui-form-label"><span style="color: red;">*</span>@T("正文"):</label><br />
                                <div class="layui-input-block">
                                    <textarea placeholder="@T("请输入正文")" name="[@item.Id].MContent" class="layui-textarea"></textarea>
                                </div>
                            </div>

                            <div class="layui-form-item layui-form-text">
                                <label class="layui-form-label">@T("短信模板Id"):</label><br />
                                <div class="layui-input-block">
                                    <input type="text" name="[@item.Id].SmsTplId" lay-verify="smstplid" value="" autocomplete="off" placeholder="@T("请输入短信模板Id")" class="layui-input">
                                </div>
                            </div>

                        </div>
                    }
                }
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><span style="color: red;">*</span>@T("模板标识"):</label>
            <div class="layui-input-block">
                <input type="text" name="Code" lay-verify="code" value="" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item btn">
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>
<script asp-location="Footer">
    function reload() {
        var topLayui = parent === self ? layui : top.layui;
        var tabsBody = topLayui.admin.tabsBody(topLayui.admin.tabsPage.index).find('.layadmin-iframe');
        var iframe = tabsBody[0].contentWindow;

        iframe.actives.success('@T("保存成功")');

        iframe.actives.reload();
    }

    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var upload = layui.upload;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        form.on('submit(Submit)', function (data) {

            var name = $("[name='Name']").val();
            if (!name) {
                os.warning('@("标准下的模板名称不能为空")');
                return;
            }

            var mtitle = $("[name='MTitle']").val();
            if (!mtitle) {
                os.warning('@("标准下的标题不能为空")');
                return;
            }

            var MContent = $('#MContent').val();
            if (MContent == "") {
                layui.common.warning("@T("标准下的正文不为能空")");
                return;
            }

            var code = $('#Code').val();
            if (code == "") {
                layui.common.warning("@T("模板标识不为能空")");
                return;
            }


            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("Add")",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });
    });
</script>