﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>硬件设备</summary>
public partial class HardwareDevicesModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>设备Mac地址</summary>
    public String Mac { get; set; } = null!;

    /// <summary>设备号</summary>
    public String? Code { get; set; }

    /// <summary>设备类型。0为扫码枪</summary>
    public Dto.HardwareType HType { get; set; }

    /// <summary>设备型号</summary>
    public String DeviceModel { get; set; } = null!;

    /// <summary>备注</summary>
    public String? Remark { get; set; }

    /// <summary>绑定用户ID</summary>
    public Int32 BindUserID { get; set; }

    /// <summary>绑定用户姓名。冗余字段</summary>
    public String? BindUserRealName { get; set; }

    /// <summary>绑定用户名</summary>
    public String? BindUser { get; set; }

    /// <summary>设备所在工序。1为打单，2为领料，3为生产，4为生产审核，5为出货/入库，6为人为结束，7为取消订单，8为设置，9为打包</summary>
    public Dto.HardwareStatus Status { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IHardwareDevices model)
    {
        Id = model.Id;
        Mac = model.Mac;
        Code = model.Code;
        HType = model.HType;
        DeviceModel = model.DeviceModel;
        Remark = model.Remark;
        BindUserID = model.BindUserID;
        BindUserRealName = model.BindUserRealName;
        BindUser = model.BindUser;
        Status = model.Status;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
