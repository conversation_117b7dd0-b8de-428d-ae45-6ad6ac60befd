﻿using DG.Web.Framework;
using DH;
using DH.Entity;
using HlktechIoT.Entity;
using MessagePack.Formatters;
using Microsoft.AspNetCore.Mvc;
using NewLife;
using NewLife.Agent.CommandHandler;
using NewLife.Data;
using NewLife.Log;
using NPOI.SS.Formula.Functions;
using Pek;
using Pek.Helpers;
using Pek.Models;
using Pek.Webs;
using SharpCompress.Common;
using System.ComponentModel;
using XCode.Membership;
using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers
{
    /// <summary>
    ///工具版本
    /// </summary>
    [DisplayName("工具版本")]
    [Description("用于各种工具的版本管理")]
    [AdminArea]
    [DHMenu(73, ParentMenuName = "System", CurrentMenuUrl = "~/{area}/ToolVersion", CurrentMenuName = "ToolVersionList", LastUpdate = "20250801")]
    public class ToolVersionController : BaseAdminControllerX
    {
        /// <summary>菜单顺序。扫描是会反射读取</summary>
        protected static Int32 MenuOrder { get; set; } = 73;

        /// <summary>
        /// 列表
        /// </summary>
        /// <returns></returns>
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 列表
        /// </summary>
        /// <returns></returns>
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult GetList(Int32 page, Int32 limit,string key)
        {
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = ToolVersion._.Id,
                Desc = true,
            };
            var data = ToolVersion.Search("", null, DateTime.MinValue, DateTime.MinValue, key, pages);
            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("上传文件")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult UploadFiles(IFormFile file)
        {
            var res = new DResult();

            try
            {
                if (file == null)
                {
                    XTrace.WriteLine("获取到的文件为空");

                    res.msg = GetResource("上传有误");
                    return Json(res);
                }

                var OrignfileName = file.FileName;
                var Size = file.Length;

                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(OrignfileName)}";
                var filepath = DHSetting.Current.UploadPath.CombinePath($"ToolVersion/{filename}");
                var saveFileName = filepath.GetFullPath();
                var f = saveFileName.AsFile();
                if (f.Exists)
                {
                    f.Delete();
                }
                saveFileName.EnsureDirectory();
                file.SaveAs(saveFileName);
                res.msg = GetResource("上传成功");
                res.success = true;
                res.data = new { OriginFileName = OrignfileName, FilePath = filepath.Replace("\\", "/"), Size };
                return Json(res);
            }
            catch (Exception ex)
            {
                res.msg = GetResource("上传异常");
                XTrace.WriteLine($"上传异常：{ex.ToString()}");
                return Json(res);
            }
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <returns></returns>
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add() 
        {
            return View();   
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add(String code,String version,String fileName,Int32 fileSize,String content,Boolean isQiangZhi,String filePath)
        {
            DResult res = new();
            if (code.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("工具标识不能为空");
                return Json(res);
            }
            if (version.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("版本号不能为空");
                return Json(res);
            }
            if (fileName.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("文件名不能为空");
                return Json(res);
            }
            if (content.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("升级内容不能为空");
                return Json(res);
            }
            if (filePath.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("文件地址不能为空");
                return Json(res);
            }
            var model = ToolVersion.FindByCodeAndVersion(code,version);
            if (model != null)
            {
                res.msg = GetResource("工具版本已存在");
                return Json(res);
            }
            model = new ToolVersion
            {
                Code = code,
                Version = version,
                FileName = fileName,
                FileSize = fileSize,
                Content = content,
                FilePath = filePath,
                IsQiangZhi = isQiangZhi
            };
            model.Insert();
            res.success = true;
            res.msg = GetResource("添加成功");
            return Json(res);
        }

        /// <summary>
        /// 编辑
        /// </summary>
        /// <returns></returns>
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Edit(Int32 Id)
        {
            var model = ToolVersion.FindById(Id);
            if(model == null)
            {
                return Content(GetResource("工具版本不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 编辑
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Edit(String code, String version, String fileName, Int32 fileSize, String content, Boolean isQiangZhi, String filePath,Int32 Id)
        {
            DResult res = new();
            if (code.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("工具标识不能为空");
                return Json(res);
            }
            if (version.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("版本号不能为空");
                return Json(res);
            }
            if (fileName.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("文件名不能为空");
                return Json(res);
            }
            if (content.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("升级内容不能为空");
                return Json(res);
            }
            if (filePath.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("文件地址不能为空");
                return Json(res);
            }
            var model = ToolVersion.FindByCodeAndVersion(code, version);
            if (model != null && model.Id != Id)
            {
                res.msg = GetResource("工具版本已存在");
                return Json(res);
            }
            else
            {
                model = ToolVersion.FindById(Id);
                if (model == null)
                {
                    res.msg = GetResource("工具版本不存在");
                    return Json(res);
                }
            }
            if (filePath != model.FilePath)
            {
                var file = model.FilePath!.GetFullPath().AsFile();
                if (file.Exists)
                {
                    file.Delete();
                }
            }
            model.Code = code;
            model.Version = version;
            model.FileName = fileName;
            model.FileSize = fileSize;
            model.Content = content;
            model.FilePath = filePath;
            model.IsQiangZhi = isQiangZhi;
            model.Update();
            res.success = true;
            res.msg = GetResource("编辑成功");
            return Json(res);
        }

        /// <summary>
        /// 修改是否强制升级状态
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="Status"></param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("修改状态")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult ModifyState(Int32 Id, Boolean Status)
        {
            var result = new DResult();

            var model = ToolVersion.FindById(Id);
            if (model == null)
            {
                result.msg = GetResource("状态调整出错");
                return Json(result);
            }

            model.IsQiangZhi = Status;
            model.Update();

            result.success = true;
            result.msg = GetResource("状态调整成功");

            return Json(result);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("删除")]
        [EntityAuthorize(PermissionFlags.Delete)]
        public IActionResult Delete(Int32 Id)
        {
            var res = new DResult();
            var model = ToolVersion.FindById(Id);
            if (model == null)
            {
                res.msg = GetResource("工具版本不存在");
                return Json(res);
            }
            var file = model.FilePath!.GetFullPath().AsFile();
            if (file.Exists)
            {
                file.Delete();
            }
            model.Delete();
            ToolVersion.Meta.Cache.Clear("");
            res.success = true;
            res.msg = GetResource("删除成功");
            return Json(res);
        }
    }
}
