﻿using DH.Entity;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Membership;

namespace HlktechIoT.Entity
{
    public class UserEx : UserE
    {
        public static IList<User> Search(Int32 CompanyId, PageParameter Page)
        {
            var exp = new WhereExpression();
            if(CompanyId > 0)
            {
                var UIds = UserDetailEx.FindAllByCompanyId(CompanyId).Select(e => e.Id);
                if (UIds.Count() == 0) return [];
                exp &= _.ID.In(UIds);
            }
            return FindAll(exp, Page);
        }

    }
}
