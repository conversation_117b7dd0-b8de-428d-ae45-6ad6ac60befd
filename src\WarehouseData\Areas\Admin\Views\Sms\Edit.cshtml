﻿@model SmsSettings
@{
    Html.AppendTitleParts(T("编辑短信配置").Text);

    var smsType = String.Empty;
    switch (Model.SmsType)
    {
        case 0:
            smsType = T("国内通知").Value;
            break;

        case 1:
            smsType = T("国际通知").Value;
            break;

        case 2:
            smsType = T("国内营销").Value;
            break;

        case 3:
            smsType = T("国际营销").Value;
            break;
    }
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
</style>
<script asp-location="Head">
    var closeThis = '@T("关 闭 当 前")';
    var closeOther = '@T("关 闭 其 他")';
    var closeAll = '@T("关 闭 全 部")';

    var jsMenuStyle = "@T("菜单风格")";
    var jsTopStyle = "@T("顶部风格")";
    var jsMenu = "@T("菜单")";
    var jsView = "@T("视图")";
    var jsBanner = "@T("通栏")";
    var jsThroughColor = "@T("通色")";
    var jsFooter = "@T("页脚")";
    var jsMoreSettings = "@T("更多设置")";
    var jsOpen = "@T("开")";
    var jsClose = "@T("关")";
    var jsThemeColor = "@T("主题配色")";
    var layuiNoData = '@T("无数据")';
    var layuiAsc = "@T("升序")";
    var layuiDesc = "@T("降序")";

    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
</script>
<div class="containers">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("短信服务商")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" class="layui-input" disabled value="@Model.DisplayName">
                <input type="hidden" id="smscf_type" name="smscf_type" value="@Model.Name" />
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("短信类型")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" class="layui-input" disabled value="@smsType">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("短信签名")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="smscf_sign" id="smscf_sign" placeholder="@T("短信签名")" autocomplete="off" class="layui-input" lay-filter="smscf_sign" value="@Model.PassKey">
            </div>
            <div class="layui-form-mid layui-word-aux">@T("请将短信签名同步设置到短信服务商后台")</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("AccessKey")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="smscf_wj_username" id="smscf_wj_username" placeholder="@T("主账号AccessKey")" autocomplete="off" class="layui-input" lay-filter="smscf_wj_username" value="@Model.AccessKey">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("AccessSecret")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="smscf_wj_key" id="smscf_wj_key" placeholder="@T("主账号AccessSecret")" autocomplete="off" class="layui-input" lay-filter="smscf_wj_key" value="@Model.AccessSecret">
            </div>
        </div>
        @if (Model.Name == "tencent")
        {
            <div class="layui-form-item">
                <label class="layui-form-label label-width"><span>*</span>@T("AppId")</label>
                <div class="layui-input-inline" style="min-width:300px">
                    <input type="text" name="AppId" id="AppId" placeholder="@T("主账号AppId")" autocomplete="off" class="layui-input" lay-filter="AppId" value="@Model.AppId">
                </div>
            </div>
        }
        <div class="layui-form-item">
            <label class="layui-form-label">@T("是否启用")</label>
                <div class="layui-input-inline">
                    <input type="checkbox" lay-filter="switch" name="sms_isEnabled" lay-skin="switch" lay-text="@T("是")|@T("否")" @(Model.IsEnabled ? Html.Raw("checked") : Html.Raw(""))>
                </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">@T("手机登录")</label>
                <div class="layui-input-inline">
                    <input type="checkbox" lay-filter="switch" name="sms_login" lay-skin="switch" lay-text="@T("是")|@T("否")" @(Model.SmsLogin ? Html.Raw("checked") : Html.Raw(""))>
                </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">@T("手机注册")</label>
                <div class="layui-input-inline">
                    <input type="checkbox" lay-filter="switch" name="sms_register" lay-skin="switch" lay-text="@T("是")|@T("否")" @(Model.SmsRegister ? Html.Raw("checked") : Html.Raw(""))>
                </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">@T("找回密码")</label>
                <div class="layui-input-inline">
                    <input type="checkbox" lay-filter="switch" name="sms_password" lay-skin="switch" lay-text="@T("是")|@T("否")" @(Model.SmsPassword ? Html.Raw("checked") : Html.Raw(""))>
                </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("测试手机短信")</label>
            <div class="layui-input-inline" style="min-width:300px">
                <input type="text" name="mobile_test" id="mobile_test" placeholder="@T("手机号码")" autocomplete="off" class="layui-input" lay-filter="mobile_test">
            </div>
            <div class="layui-form-mid layui-word-aux" style="padding-top: 4px !important;">
                <a style="float:left" class="layui-btn layui-btn-sm" name="send_test_mobile" id="send_test_mobile">@T("测试")</a>
            </div>
        </div>

        @if (Model.Name == "fenghuo" || Model.Name == "lianlu")
        {
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">@T("测试短信内容")</label>
                <div class="layui-input-inline" style="min-width:300px">
                    <textarea name="mobile_test_content" id="mobile_test_content" class="layui-textarea"></textarea>
                </div>
                <div class="layui-form-mid layui-word-aux">
                    【@(Model.PassKey)】@T("要换成实际短信签名")
                </div>
            </div>
        }
        else if (Model.Name == "aliyun" || Model.Name == "tencent")
        {
            <div class="layui-form-item">
                <label class="layui-form-label label-width"><span>*</span>@T("模板code")</label>
                <div class="layui-input-inline" style="min-width:300px">
                    <input type="text" name="template_code" id="template_code" placeholder="@T("模板code")" autocomplete="off" class="layui-input" lay-filter="template_code" value="">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label label-width"><span>*</span>@T("模板内容")</label>
                <div class="layui-input-inline" style="min-width:300px">
                    <input type="text" name="template_content" id="template_content" placeholder="@T("模板内容")" autocomplete="off" class="layui-input" lay-filter="template_content" value="">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label label-width"><span>*</span>@T("短信模板变量")</label>
                <div class="layui-input-inline" style="min-width:300px">
                    <input type="text" name="template_param" id="template_param" placeholder="@T("短信模板变量")" autocomplete="off" class="layui-input" lay-filter="template_param" value="">
                </div>
            </div>
        }

        <div class="layui-form-item btn">
            <input hidden name="Id" id="Id" value="@Model.Id" />
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="保存" />
        </div>

    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var upload = layui.upload;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        form.on('submit(Submit)', function (data) {

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("Edit")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

        $('#send_test_mobile').click(function(){
            $.ajax({
                type:'POST',
                url:"@Url.Action("SendSmsTest", "Send", new { area = "" })",
                data:{
                    'sign':$('#smscf_sign').val(),
                    'type': '@Model.Name',
                    'templatecontent':$('#template_content').val(),
                    'templateparam':$('#template_param').val(),
                    'templatecode':$('#template_code').val(),
                    'content':$('#mobile_test_content').val(),
                    'username':$('#smscf_wj_username').val(),
                    'password':$('#smscf_wj_key').val(),
                    'mobiles':$('#mobile_test').val(),
                    'AppId':$('#AppId').val()
                },
                error:function(html){
                    layer.alert('@T("操作失败")');
                },
                success:function(html){
                    if(html.msg){
                        layer.alert(html.msg);
                    }
                },
                dataType:'json'
            });
        });

    });
</Script>