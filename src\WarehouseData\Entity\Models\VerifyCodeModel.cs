﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>验证码</summary>
public partial class VerifyCodeModel
{
    #region 属性
    /// <summary>验证码唯一键</summary>
    public String? Key { get; set; }

    /// <summary>验证码</summary>
    public String? Code { get; set; }

    /// <summary>过期时间</summary>
    public DateTime EndTime { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IVerifyCode model)
    {
        Key = model.Key;
        Code = model.Code;
        EndTime = model.EndTime;
        CreateTime = model.CreateTime;
    }
    #endregion
}
