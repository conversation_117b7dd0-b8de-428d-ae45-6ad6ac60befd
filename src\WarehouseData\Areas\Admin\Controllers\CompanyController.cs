﻿using DG.Web.Framework;
using DH.Entity;
using HlktechIoT.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife;
using NewLife.Data;
using Pek.Exceptions;
using Pek.Models;
using System.ComponentModel;
using System.ComponentModel.Design;
using XCode.Membership;
using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers
{
    /// <summary>
    /// 合作公司
    /// </summary>
    [DisplayName("合作公司")]
    [Description("合作公司管理")]
    [AdminArea]
    [DHMenu(105, ParentMenuName = "DHUser", CurrentMenuUrl = "~/{area}/Company", CurrentMenuName = "CompanyList", LastUpdate = "20250606")]
    public class CompanyController : BaseAdminControllerX
    {
        /// <summary>菜单顺序。扫描是会反射读取</summary>
        protected static Int32 MenuOrder { get; set; } = 105;

        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]   
        public IActionResult Index()
        {
            return View();
        }

        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult GetList(String name,Int32 page = 1, Int32 limit = 10)
        {
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = Company._.Id,
                Desc = true,
            };

            var data = Company.Search(name, null, pages);

            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data = data });
        }

        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)] 
        public IActionResult Add()
        {
            return View();
        }

        [HttpPost]
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult CreateCompany(String Name,String Remark,Boolean Enabled)
        {
            if (Name.IsNullOrWhiteSpace())
            {
                throw new DHException(GetResource("公司名称不可为空"));
            }
            var model = new Company()
            {
                Name = Name,
                Remark = Remark,
                Enabled = Enabled,
            };
            model.Insert();
            return Json(new DResult { success = true, msg = GetResource("创建成功") });
        }

        [DisplayName("修改")]
        [EntityAuthorize(PermissionFlags.Update)]   
        public IActionResult Update(Int32 Id)
        {
            var model = Company.FindById(Id);
            if(model == null)
            {
                return Content(GetResource("合作公司不存在"));
            }
            return View(model);
        }

        [HttpPost]
        [DisplayName("修改")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult UpdateCompany(Int32 Id, String Name, String Remark, Boolean Enabled)
        {
            if (Name.IsNullOrWhiteSpace())
            {
                throw new DHException(GetResource("公司名称不可为空"));
            }
            var model = Company.FindById(Id);
            if(model == null)
            {
                throw new DHException(GetResource("合作公司不存在"));
            }
            model.Name = Name;
            model.Remark = Remark;
            model.Enabled = Enabled;
            model.Update();
            return Json(new DResult { success = true, msg = GetResource("编辑成功") });
        }

        /// <summary>修改状态</summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("修改状态")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult ModifyState(Int32 Id, Boolean Status)
        {
            var result = new DResult();

            var model = Company.FindById(Id);
            if (model == null)
            {
                result.msg = GetResource("状态调整出错");
                return Json(result);
            }

            model.Enabled = Status;
            model.Update();

            result.success = true;
            result.msg = GetResource("状态调整成功");

            return Json(result);
        }

        [HttpPost]
        [DisplayName("删除")]
        [EntityAuthorize(PermissionFlags.Delete)]
        public IActionResult Delete(Int32 Id)
        {
            Company.Delete(Company._.Id == Id);
            Company.Meta.Cache.Clear("", true);
            return Json(new DResult() { success = true, msg = GetResource("删除成功") });
        }

        /// <summary>
        /// 用户列表
        /// </summary>
        /// <returns></returns>
        [DisplayName("用户")]
        [EntityAuthorize((PermissionFlags)16)]
        public IActionResult Userlist(Int32 CompanyId)
        {
            var company = Company.FindById(CompanyId);
            if(company == null)
            {
                return Content(GetResource("公司不存在"));
            }
            return View(company);
        }

        /// <summary>
        /// 用户列表
        /// </summary>
        /// <returns></returns>
        [DisplayName("用户")]
        [EntityAuthorize((PermissionFlags)16)]
        public IActionResult GetUserlist(Int32 CompanyId, Int32 page = 1, Int32 limit = 10)
        {
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = Company._.Id,
                Desc = true,
            };

            var data = UserEx.Search(CompanyId,pages);

            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data = data });
        }

        /// <summary>
        /// 新增用户
        /// </summary>
        /// <returns></returns>
        [DisplayName("新增用户")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult AddUser(Int32 CompanyId)
        {
            var company = Company.FindById(CompanyId);
            if (company == null)
            {
                return Content(GetResource("公司不存在"));
            }
            return View(company);
        }

        /// <summary>搜索用户</summary>
        /// <returns></returns>
        [DisplayName("搜索用户")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult SearchUser(String keyword, Int32 page)
        {
            var res = new DResult();

            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = 10,
                RetrieveTotalCount = true,
                Sort = UserE._.ID,
                Desc = true,
            };

            res.data = UserE.Searchs(false, pages, 0, keyword).Select(e =>
            {
                return new Xmselect<Int32>
                {
                    name = e.DisplayName ?? "",
                    value = e.ID
                };
            });

            res.success = true;

            res.extdata = new { pages.PageCount };

            return Json(res);
        }

        /// <summary>
        /// 新增用户
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("新增用户")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult AddUser(Int32 CompanyId,Int32 UserId)
        {
            DResult res = new();
            var company = Company.FindById(CompanyId);
            if (company == null)
            {
                res.msg = GetResource("公司不存在");
                return Json(res);
            }
            var user = UserDetail.FindById(UserId);
            if(user == null)
            {
                res.msg = GetResource("用户不存在");
                return Json(res);
            }
            user.CompanyId = company.Id;
            user.CompanyName = company.Name;
            user.Update();
            res.success = true;
            res.msg = GetResource("添加成功");
            return Json(res);
        }
        
        /// <summary>
        /// 删除用户
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("删除用户")]
        [EntityAuthorize(PermissionFlags.Delete)]
        public IActionResult DeleteUser(Int32 CompanyId, Int32 UserId)
        {
            DResult res = new();
            var company = Company.FindById(CompanyId);
            if (company == null)
            {
                res.msg = GetResource("公司不存在");
                return Json(res);
            }
            var user = UserDetail.FindById(UserId);
            if (user == null)
            {
                res.msg = GetResource("用户不存在");
                return Json(res);
            }
            user.CompanyId = 0;
            user.CompanyName = "";
            user.Update();
            res.success = true;
            res.msg = GetResource("删除成功");
            return Json(res);
        }
    }
}
