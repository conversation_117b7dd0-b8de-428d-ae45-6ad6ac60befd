﻿@using HlktechIoT.Entity
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@model ProductSnMac
@{
    Layout = null;

    var dgPage = (Pek.Webs.HttpContext.Current.Request.RouteValues["controller"] + "_" + Pek.Webs.HttpContext.Current.Request.RouteValues["action"]).ToLower();
}
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>质检报告</title>
  <script src="https://cdn.jsdelivr.net/npm/pdfjs-dist@2.10.377/build/pdf.min.js"></script>
  <link href="~/css/onlinereport.css" rel="stylesheet" />
  <meta name="referrer" content="no-referrer">
</head>

<body>
  <!-- 添加loading动画 -->
  <div class="loading-container" id="loadingContainer">
    <div class="loading-spinner"></div>
    <div class="loading-text">质检报告加载中...</div>
  </div>

  <div class="pdf">
    <canvas id="pdfCanvas"></canvas>
    <div class="pdf-links">
      <a id="appDownload" href="#" class="pdf-link" target="_blank">App下载</a>
      <a id="manualDownload" href="#" class="pdf-link" target="_blank">产品手册</a>
      <a id="pdfDownload" href="#" class="pdf-link" target="_blank">下载PDF</a>
    </div>
  </div>

  <script>
    // 在DOM加载完成时就显示loading
    document.addEventListener('DOMContentLoaded', function() {
      document.getElementById('loadingContainer').style.display = 'flex';
    });

    // 设置PDF.js worker的路径
    pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.10.377/build/pdf.worker.min.js';

    // 隐藏loading动画的函数
    function hideLoading() {
      const loadingContainer = document.getElementById('loadingContainer');
      loadingContainer.style.display = 'none';
    }

    // PDF.js 初始化代码将在这里编写
    const url = '@Model.FilePath'; // 本地PDF文件的URL
    const loadingTask = pdfjsLib.getDocument(url);
    loadingTask.promise.then(function (pdf) {
      // 设置PDF下载链接
      document.getElementById('pdfDownload').href = url;
      @* console.log(11,'PDF loaded'); *@

      // 获取第一页
      pdf.getPage(1).then(function (page) {
        @* console.log(22,'Page loaded'); *@

        var scale = 1.5; // 设置缩放比例
        var viewport = page.getViewport({ scale: scale });

        // 准备canvas用于渲染PDF页面
        var canvas = document.getElementById('pdfCanvas');
        var context = canvas.getContext('2d');
        canvas.height = viewport.height;
        canvas.width = viewport.width;

        // 渲染PDF页面到canvas上
        var renderContext = {
          canvasContext: context,
          viewport: viewport
        };
        // 获取文本内容 从pdf 中获取链接 换到href中
        page.getTextContent().then(function(textContent) {
          // 遍历文本块，查找"App下载："和"产品手册："后面的内容
          textContent.items.forEach(item => {
            const str = item.str;
            if (str.includes('App下载：')) {
              const match = str.match(/App下载：\s*(https?:\/\/[^\s]+)/);
              if (match && match[1]) {
                document.getElementById('appDownload').href = match[1];
                @* console.log('App下载链接:', match[1]); *@
              }
            }
            if (str.includes('产品手册：')) {
              const match = str.match(/产品手册：\s*(https?:\/\/[^\s]+)/);
              if (match && match[1]) {
                document.getElementById('manualDownload').href = match[1];
                @* console.log('产品手册链接:', match[1]); *@
              }
            }
          });
        });
        // 渲染PDF页面
        page.render(renderContext).promise.then(function() {
          // PDF渲染完成后隐藏loading
          hideLoading();
        });
      });
    }, function (reason) {
      // PDF加载失败的处理函数
      console.error('err',reason);
      // 加载失败也隐藏loading
      hideLoading();
    });

    // 修改a标签的点击事件，使用window.open()打开PDF文件
    document.getElementById('manualDownload').addEventListener('click', function(e) {
      e.preventDefault();
      window.open(this.href, '_blank');
    });

    document.getElementById('appDownload').addEventListener('click', function(e) {
      e.preventDefault();
      window.open(this.href, '_blank');
    });
  </script>

</body>

</html>