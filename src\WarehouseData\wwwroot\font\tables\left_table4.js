// const layui = require('./node_modules/layui/dist/layui.js')

    let left_table4_options = {
      elem: '#left_table4',
      id:'left_table4',
      cols: [[ //标题栏
        {field: 'OrderId', title: '订单号',width:"60%",templet:(d)=>{
          return '<div data-table="left_table4">'+d.OrderId+'</div>'
        }},
        {field: 'ManHour', title: '工时',width:"40%",align:'center' },
      ]],
      data: [{OrderId: '无数据',}]
      ,cellMinWidth: 80
      ,
      height: (()=>{

        let tableHeight = layui.$('div.item_left_box[data-index="4"]').height() 
        return ((tableHeight- (tableHeight * 0.3))  + 'px')
      })(),
        page: false, // 是否显示分页
        limit:3,
        parseData: function(res){ // res 即为原始返回的数据
         console.log('表格初始化了',res);
        },
        error: function(e, msg) {
          console.log('调用错误：',e, msg)
        }
      }
// 导出js
export default left_table4_options
