﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>产品项目资料表</summary>
public partial class ProductProjectDataModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>产品项目Id</summary>
    public Int32 ProductProjectId { get; set; }

    /// <summary>产品资料Id</summary>
    public Int32 ProductDataId { get; set; }

    /// <summary>状态 0待审核 1审核中 2审核通过 3审核失败</summary>
    public Int32 Status { get; set; }

    /// <summary>备注</summary>
    public String? Remark { get; set; }

    /// <summary>反审状态 0默认 1申请反审 2确认反审</summary>
    public Int32 ReAudit { get; set; }

    /// <summary>反审原因</summary>
    public String? ReAuditRemark { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductProjectData model)
    {
        Id = model.Id;
        ProductProjectId = model.ProductProjectId;
        ProductDataId = model.ProductDataId;
        Status = model.Status;
        Remark = model.Remark;
        ReAudit = model.ReAudit;
        ReAuditRemark = model.ReAuditRemark;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
