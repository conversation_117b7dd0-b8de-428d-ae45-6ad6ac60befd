﻿@{
	var dgPage = (Pek.Webs.HttpContext.Current.Request.RouteValues["controller"] + "_" + Pek.Webs.HttpContext.Current.Request.RouteValues["action"]).ToLower();

	// Css
	Html.AppendCssFileParts(ResourceLocation.Head, "/css/other/console1.css");

	// Script
	Html.AppendScriptParts(ResourceLocation.Footer, "~/js/Storage.js");
	Html.AppendScriptParts(ResourceLocation.Footer, "~/js/initSignalr.js");
}

<style>

</style>

<div class="layui-bg-gray" style="padding: 16px;">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md6">
            <div class="layui-card" style="width:100%">
                <div class="layui-card-header">@T("消息通知")</div>
                <div class="layui-card-body">
                    <table class="layui-hide" id="tablist1" lay-filter="tool1"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<script asp-location="Footer">
    const storage = new Storage();  // new Storage(3)

    var currentDomain = window.location.protocol + "//" + window.location.hostname;

    if (window.location.port !== "") {
        currentDomain = currentDomain + ":" + window.location.port;
    }

    var notifyUrl = currentDomain;

    var token = storage.get("AccessToken");
    var dgpage = '@dgPage';

    layui.use(function(){
        var table = layui.table;
        var abp = layui.abp;

        table.render({
            elem:'#tablist1',
            url:'@Url.Action("GetMessageList","Main")',
            page:true,
            cols:[[
                { field: 'Title', title: '@T("消息标题")', minWidth: 60 },
                { field: 'CreateUser', title: '@T("发送者")', minWidth: 80 },
                { field: 'CreateTime', title: '@T("发送时间")', minWidth: 160 },
                { title: '@T("状态")', minWidth: 60, templet:(d)=>{
                    if(d.Status){
                        return '@T("已读")'
                    }else{
                        return '<span style="color:red">@T("未读")</span>'
                    }
                }},
                { fixed: 'right',title: '@T("操作")', toolbar: '#tool1', width: 75 },
            ]],
            limit:5,
            limits:[5, 10, 20, 50],
            id:'tables1',
        });

        window.active = {
            reload: function(){
                table.reload('tables1', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                });
            }
        };

         table.on('tool(tool1)',function(obj)
         {
             var data = obj.data;
             if (obj.event === 'view')
             {
                 window.view(data);
             }
         });

        window.view = function (data) {
            layer.open({
                type: 2,
                title: "@T("查看")",
                content: "@Url.Action("ViewMessage")?Id=" +data.Id,
                area: ["455px", "536px"],
                shade: 0.1,
                btn: ['@T("取消")'],
                yes: function (index, layero) {
                    layer.close(index);
                },
                end:function(){
                    active.reload('tables1');
                }
            });
        }
    });
</script>
<script type="text/html" id="tool1">
    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="view"> @T("查看")</a>
</script>