﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>生产反馈问题</summary>
public partial class ProductionFeedbackModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>问题标题</summary>
    public String Name { get; set; } = null!;

    /// <summary>问题内容</summary>
    public String? Content { get; set; }

    /// <summary>问题类型 0待分配 1软件 2硬件</summary>
    public Int32 DType { get; set; }

    /// <summary>图片。多个图片用,分隔</summary>
    public String? PicPath { get; set; }

    /// <summary>视频。多个视频用,分隔</summary>
    public String? VideoPath { get; set; }

    /// <summary>文件。多个文件用,分隔</summary>
    public String? FilePath { get; set; }

    /// <summary>状态。0为待处理，1为处理中，2为已处理，3为不予处理 4已完结</summary>
    public Int16 Status { get; set; }

    /// <summary>产品型号编号</summary>
    public Int32 ProductTypeId { get; set; }

    /// <summary>生产订单编号</summary>
    public Int64 ProductOrderId { get; set; }

    /// <summary>处理原因</summary>
    public String? Cause { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductionFeedback model)
    {
        Id = model.Id;
        Name = model.Name;
        Content = model.Content;
        DType = model.DType;
        PicPath = model.PicPath;
        VideoPath = model.VideoPath;
        FilePath = model.FilePath;
        Status = model.Status;
        ProductTypeId = model.ProductTypeId;
        ProductOrderId = model.ProductOrderId;
        Cause = model.Cause;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
    }
    #endregion
}
