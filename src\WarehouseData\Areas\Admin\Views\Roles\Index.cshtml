﻿@{
    Html.AppendTitleParts(T("角色管理").Text);
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }
</style>

<script asp-location="Head">
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<div class="layui-card">
    <div class="layui-card-header">@T("角色管理")</div>
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['table', 'abp', 'dg', 'form'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetRole")'
            //, page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 80
            , cols: [[
                { field: "Id", title: 'Id', width: 100 },
                { field: "Name", title: '@T("权限组名")', width: 200 },
                { field: "Remark", title: '@T("描述")', align: "left" },
                { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: 180 }
            ]]
            , id: 'tables'
        });

        window.active = {
            reload: function () {
                table.reload('tables');
            }
        }

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === "look") {
                top.layui.dg.popupRight({
                    id: 'RoleDetail'
                    , title: ' @T("修改角色")'
                    , closeBtn: 1
                    , area: ['780px']
                    , success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("EditDetail")/' + data.Id + '" frameborder="0" class="layadmin-iframe"></iframe>');

                    }
                });
            } else if (obj.event === "extend") {
                top.layui.dg.popupRight({
                    id: 'RoleExIndex'
                    , title: ' @T("权限扩展")'
                    , closeBtn: 1
                    , area: ['780px']
                    , success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Index", "RoleEx")/' + data.Id + '" frameborder="0" class="layadmin-iframe"></iframe>');
                    }
                });
            } else if (obj.event === "del") {
                if (data.Id == 1) {
                    layer.msg('@T("超级管理员不可被删除")', {
                        offset: 't',
                        anim: 6
                    });
                    return;
                }
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success('@T("删除成功")');
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            }
        });

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'add') {
                top.layui.dg.popupRight({
                    id: 'RoleDetail'
                    , title: ' @T("创建角色")'
                    , closeBtn: 1
                    , area: ['800px']
                    , success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("CreateDetail")" frameborder="0" class="layadmin-iframe"></iframe>');
                    }
                });
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });
    });
</script>

<script type="text/html" id="tool">
    {{# if(d.IsSystem == 1) { }}
    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="look"> @T("编辑")</a>
    {{# } else { }}
    @if (this.Has((PermissionFlags)4)){
    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="look"> @T("编辑")</a>
    }
    @if (this.Has((PermissionFlags)8)){
    <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="del"> @T("删除")</a>
    }
    {{# } }}
    @if (ViewBag.IsControllerExists == true)
    {
    <text>
        {{# if(d.Id != 1) { }}
        <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="extend"> @T("扩展")</a>
        {{# } }}
    </text>
    }
</script>

<script type="text/html" id="user-toolbar">
    @if (this.Has((PermissionFlags)2)){
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        @T("新增")
    </button>
    }
</script>