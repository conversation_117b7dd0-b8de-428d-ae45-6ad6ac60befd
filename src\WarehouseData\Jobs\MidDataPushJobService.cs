﻿using System.ComponentModel;

using DH.Core.Infrastructure;
using DH.Entity;
using DH.Services.Jobs;
using DH.SignalR;
using DH.SignalR.Dtos;

using HlktechIoT.SignalR;

using Microsoft.AspNetCore.SignalR;

using NewLife.Log;

namespace HlktechIoT.Jobs;

/// <summary>
/// 刷新大屏页面作业参数
/// </summary>
public class MidDataPushJobArgument {

}

/// <summary>刷新大屏页面服务</summary>
[DisplayName("刷新大屏页面")]
[Description("刷新大屏页面")]
[CronJob("MidDataPush", "0 */10 * * * ? *", Enable = true)]
public class MidDataPushJobService : CubeJobBase<MidDataPushJobArgument> {
    private readonly ITracer _tracer;

    /// <summary>实例化检查固件升级服务</summary>
    /// <param name="tracer"></param>
    public MidDataPushJobService(ITracer tracer)
    {
        _tracer = tracer;
    }

    /// <summary>执行作业</summary>
    /// <param name="argument"></param>
    /// <returns></returns>
    protected override async Task<String> OnExecute(MidDataPushJobArgument argument)
    {
        using var span = _tracer?.NewSpan("MidDataPush", argument);

        //var list = WmsOrder.FindAllByNoFinish();  // 获取所有没有完成的订单数据

        //// 已打单
        //var OrderingList = list.Where(e => e.PickingTime <= DateTime.MinValue).Select(e => new { e.OrderId, User = e.OrderingUser?.DisplayName, ProcessTime = e.OrderingTime, ManHour = DateTimeUtil.BusinessDateFormat(e.OrderingTime) });

        //// 已领料
        //var PickingList = list.Where(e => e.PickingTime > DateTime.MinValue && e.ProductionTime <= DateTime.MinValue && e.AuditingTime <= DateTime.Now).Select(e => new { e.OrderId, User = e.PickingUser?.DisplayName, ProcessTime = e.PickingTime, ManHour = DateTimeUtil.BusinessDateFormat(e.PickingTime) });

        //// 生产中
        //var ProductionList = list.Where(e => e.ProductionTime > DateTime.MinValue && e.AuditingTime <= DateTime.MinValue).Select(e => new { e.OrderId, User = e.ProductionUser?.DisplayName, ProcessTime = e.ProductionTime, ManHour = DateTimeUtil.BusinessDateFormat(e.ProductionTime) });

        //// 待打包
        //var PackList = list.Where(e => e.PackTime <= DateTime.MinValue && e.AuditingTime > DateTime.MinValue).Select(e => new { e.OrderId, ManHour = DateTimeUtil.BusinessDateFormat(e.OrderingTime) });

        //var waitPicking = 0;  // 待领料数量
        //var waitProduction = 0;  // 待生产数量
        //var inProduction = 0;    // 生产中数量
        //var waitPack = 0;  // 待打包数量

        //// 所有未完成订单
        //var NoList = list.OrderBy(e => e.OrderingTime).Select(e =>
        //{
        //    var status = String.Empty;
        //    var processUser = String.Empty;
        //    var processTime = DateTime.Now;

        //    if (e.PickingTime <= DateTime.MinValue)
        //    {
        //        status = LocaleStringResource.GetResource("已打单");
        //        processUser = e.OrderingUser?.DisplayName;
        //        processTime = e.OrderingTime;

        //        waitPicking++;
        //    }
        //    else if (e.PickingTime > DateTime.MinValue && e.ProductionTime <= DateTime.MinValue && e.AuditingTime <= DateTime.Now)
        //    {
        //        status = LocaleStringResource.GetResource("已领料");
        //        processUser = e.PickingUser?.DisplayName;
        //        processTime = e.PickingTime;

        //        waitProduction++;
        //    }
        //    else if (e.ProductionTime > DateTime.MinValue && e.AuditingTime <= DateTime.MinValue)
        //    {
        //        status = LocaleStringResource.GetResource("生产中");
        //        processUser = e.ProductionUser?.DisplayName;
        //        processTime = e.ProductionTime;

        //        inProduction++;
        //    }
        //    else if (e.PackTime <= DateTime.MinValue && e.AuditingTime > DateTime.MinValue)
        //    {
        //        status = LocaleStringResource.GetResource("生产审核");
        //        processUser = e.AuditingUser?.DisplayName;
        //        processTime = e.AuditingTime;

        //        waitPack++;
        //    }

        //    return new
        //    {
        //        e.OrderId,
        //        ManHour = DateTimeUtil.BusinessDateFormat(e.OrderingTime),
        //        Status = status,
        //        ProcessUser = processUser,
        //        ProcessTime = processTime,
        //    };
        //});

        //var _notifyHub = EngineContext.Current.Resolve<IHubContext<BigDataHub, IClientNotifyHub>>();
        //var model = new NotifyConnectsData()
        //{
        //    UserId = -999,
        //    TenantType = "PushList",
        //    NotifyObj = new { OrderingList, PickingList, ProductionList, PackList, NoList, WaitPicking = waitPicking, WaitProduction = waitProduction, InProduction = inProduction, WaitPack = waitPack },
        //};
        //await _notifyHub.Clients.Group("bigdata_index").OnNotify(model);

        var _notifyHub = EngineContext.Current.Resolve<IHubContext<BigDataHub, IClientNotifyHub>>();
        var model = new NotifyConnectsData()
        {
            UserId = -999,
            TenantType = "Refresh",
        };
        await _notifyHub.Clients.All.OnNotify(model);

        return "OK";
    }
}
