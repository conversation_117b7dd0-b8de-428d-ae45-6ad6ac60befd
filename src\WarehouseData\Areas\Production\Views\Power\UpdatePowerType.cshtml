﻿@model ProductType
@{
    Html.AppendTitleParts(T("编辑产品型号").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
        white-space:nowrap;
        min-width:110px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
    .select{
        max-width: 420px !important;
        border: 1px solid #e6e6e6;
    }
</style>
<script asp-location="Head">
    var closeThis = '@T("关 闭 当 前")';
    var closeOther = '@T("关 闭 其 他")';
    var closeAll = '@T("关 闭 全 部")';

    var jsMenuStyle = "@T("菜单风格")";
    var jsTopStyle = "@T("顶部风格")";
    var jsMenu = "@T("菜单")";
    var jsView = "@T("视图")";
    var jsBanner = "@T("通栏")";
    var jsThroughColor = "@T("通色")";
    var jsFooter = "@T("页脚")";
    var jsMoreSettings = "@T("更多设置")";
    var jsOpen = "@T("开")";
    var jsClose = "@T("关")";
    var jsThemeColor = "@T("主题配色")";
    var layuiNoData = '@T("无数据")';
    var layuiAsc = "@T("升序")";
    var layuiDesc = "@T("降序")";

    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
</script>
<div class="containers">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("名称")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Name" placeholder="@T("请输入产品型号名称")" autocomplete="off" class="layui-input" lay-filter="Versions" value="@Model.Name">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("模组类型")</label>
            <div class=" layui-input-inline"  style="min-width:320px">
                <select id="ModuleType" name="ModuleType" lay-filter="ModuleType" lay-search class="layui-input select">
                    <!option value="">@T("请选择模组类型")</!option>
                    <!option value="1" @(Model.ModuleType == 1 ? "selected" : "")>@T("ACDC")</!option>
                    <!option value="2" @(Model.ModuleType == 2 ? "selected" : "")>@T("DCDC")</!option>
                </select>
            </div>
            
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">@T("启用")</label>
                <div class="layui-input-inline">
                    <input type="checkbox" name="Status" lay-filter="Status" lay-skin="switch" @(Model.Status == true ? "checked" : "")>
                </div>
             </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("备注")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Remark" placeholder="@T("请输入备注")" autocomplete="off" class="layui-input" lay-filter="Versions" value="@Model.Remark">
            </div>
        </div>

        <div class="layui-form-item btn">
            <input hidden name="Id" value="@Model.Id" /> 
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" style="margin-left:20px" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var laydate = layui.laydate;

        var parentList = window.parent
        var parentPage = null;

        // 第二步：拿到对应层
        for (let i = 0; i < parentList.length; i++) {
            if (parentList[i].name === 'PowerType') { //这里的name自己去对应层DIY
                parentPage = parentList[i]
                break;
            }
        }
        var parent_window = parentPage.window  //获取父层的window层
        var index = parent.layer.getFrameIndex(window.name);
        var currentPageCloseIndex = parent_window.addPageIndex //当前层的关闭index下标
        var parent_notify = parentPage.layui.abp.notify //获取父层的layui.notify --消息通知

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        form.on('submit(Submit)', function (data) {
            // console.log(data);

            data.field.Status = data.field.Status == 'on';

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("UpdatePowerType")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.close(currentPageCloseIndex);
                parent_notify.success(data.msg);
                parentPage.active.reload();
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>