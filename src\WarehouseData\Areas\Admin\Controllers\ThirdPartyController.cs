﻿using DG.Web.Framework;

using DH.Entity;

using HlktechIoT.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek.Models;

using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>
/// 开放平台
/// </summary>
[DisplayName("开放平台")]
[Description("开放平台项目对接管理")]
[AdminArea]
[DHMenu(90, ParentMenuName = "DHUser", CurrentMenuUrl = "~/{area}/ThirdParty", CurrentMenuName = "ThirdPartyList", LastUpdate = "20250605")]
public class ThirdPartyController : BaseAdminControllerX {

    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 110;

    /// <summary>
    /// 第三方对接列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("第三方对接管理首页")]
    public IActionResult Index()
    {
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.Role.IsSystem || !modelRole.IsAdmin)
        {
            var UId = ManageProvider.User?.ID;

            var model = OpenPlatform.FindByUId(UId ?? -1);
            if (model == null)
            {
                model = new OpenPlatform();
                model.AccessId = "0";
                model.AccessKey = Guid.NewGuid().ToString().Replace("-", "");
                model.Insert();

                model.AccessId = $"Hlktch{model.Id}";
                model.Update();
            }
        }

        return View(modelRole);
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <param name="key">搜索内容</param>
    /// <param name="page">页数</param>
    /// <param name="limit">最大显示数</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("根据条件搜索")]
    public IActionResult GetPageOpenPlatformr(String key, Int32 page = 1, Int32 limit = 10)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = OpenPlatform._.Id,
            Desc = true,
        };

        Int32 UId = 0;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.Role.IsSystem || !modelRole.IsAdmin)
        {
            UId = ManageProvider.User?.ID ?? -1;
        }

        var result = OpenPlatform.Search(key, pages, UId).Select(item =>
        {
            return new { item.Id, item.AccessId, item.AccessKey, item.Enabled, item.CreateTime, DisplayName = item.User?.DisplayName,item.UpdateUser,item.ProjectName,item.Remark };
        });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data = result.ToList() });
    }

    /// <summary>
    /// 添加开放平台信息
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("添加第三方对接")]
    public IActionResult AddThirdParty()
    {
        //ViewBag.List = Project.FindByPType().OrderByDescending(x => x.CreateTime).Select(x => new { name = x.Name, value = x.Id }).ToDynamicList().ToJson();

        ViewBag.UId = ManageProvider.User?.ID;

        return View();
    }

    /// <summary>
    /// 添加开放平台信息
    /// </summary>
    /// <param name="AccessId">商品号</param>
    /// <param name="AccessKey">商户密钥</param>
    /// <param name="OfficialUrl">第三方服务器地址</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("添加第三方对接")]
    public IActionResult AddThirdParty(string AccessId, string AccessKey, string OfficialUrl,string ProjectName,string Remark)
    {
        var result = new DResult();
        if (AccessId.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("商户号不能为空");
            return Json(result);
        }
        if (AccessKey.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("商户密钥不能为空");
            return Json(result);
        }
        if (AccessKey.Length < 20)
        {
            result.msg = GetResource("商户密钥太短");
            return Json(result);
        }
        if (ProjectName.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("项目名称不能为空");
            return Json(result);
        }
        if (!OfficialUrl.IsNullOrWhiteSpace())
        {
            if (!OfficialUrl.StartsWith("http://") && !OfficialUrl.StartsWith("https://"))
            {
                result.msg = "服务器地址不正确";
                return Json(result);
            }
        }

        OpenPlatform model = new OpenPlatform();
        model.AccessKey = AccessKey;
        model.AccessId = AccessId;
        model.ProjectName = ProjectName;
        model.Remark = Remark;
        //model.OfficialUrl = OfficialUrl;
        model.Enabled = true;
        model.Insert();

        //var keyOpenPlatform = $"{UtilSetting.Current.CacheKeyPrefix}:openplatform:{model.CreateUserID}";

        //var rds = EngineContext.Current.Resolve<FullRedis>();
        //rds.Remove(keyOpenPlatform);

        result.msg = GetResource("添加成功");
        result.success = true;
        return Json(result);
    }

    /// <summary>
    /// 编辑开放平台信息
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("编辑第三方对接")]
    public IActionResult EditThirdParty(int Id)
    {
        var model = OpenPlatform.FindById(Id);
        if (model == null)
        {
            return Content("数据有误");
        }

        return View(model);
    }

    /// <summary>
    /// 修改启用状态
    /// </summary>
    /// <param name="id"></param>
    /// <param name="status"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("修改启用状态")]
    public IActionResult modifystate(int id, bool status)
    {
        var res = new DResult();
        var model = OpenPlatform.FindById(id);

        if (model == null)
        {
            res.msg = "对接数据不存在";
            return Json(res);
        }

        bool UId = false;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.Role.IsSystem || !modelRole.IsAdmin)
        {
            UId = true;
        }
        if (UId)
        {
            if (model.UpdateUser == "管理员" && model.Enabled == false)
            {
                res.msg = "管理员已禁用";
                return Json(res);
            }
        }

        model.Enabled = status;
        model.Update();

        //var keyOpenPlatform = $"{UtilSetting.Current.CacheKeyPrefix}:openplatform:{model.CreateUserID}";

        //var rds = EngineContext.Current.Resolve<FullRedis>();
        //rds.Remove(keyOpenPlatform);

        UserE.WriteLog(LocaleStringResource.GetResource("修改启用状态"), true, String.Format(LocaleStringResource.GetResource("用户[{0}]修改启用[{1}]的状态为[{2}]"), ManageProvider.User?.Name, model.AccessId, status));
        res.msg = GetResource("状态调整成功");
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 修改开放平台信息
    /// </summary>
    /// <param name="id">id</param>
    /// <param name="AccessId">商品号</param>
    /// <param name="AccessKey">商户密钥</param>
    /// <param name="Enabled">是否启用</param>
    /// <param name="ProjectName"></param>
    /// <param name="Remark"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("编辑开放平台数据")]
    public IActionResult EditThirdParty(int id, string AccessId, string AccessKey, String Enabled, string ProjectName, string Remark)
    {
        var result = new DResult();
        
        var model = OpenPlatform.FindById(id);
        if (model == null)
        {
            result.msg = GetResource("参数出错");
            return Json(result);
        }
        if (AccessId.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("商户号不能为空");
            return Json(result);
        }
        if (AccessKey.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("商户密钥不能为空");
            return Json(result);
        }
        if (AccessKey.Length < 20)
        {
            result.msg = GetResource("商户密钥太短");
            return Json(result);
        }
        if (ProjectName.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("项目名称不能为空");
            return Json(result);
        }
        bool UId = false;
        var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        if (!modelRole.Role.IsSystem || !modelRole.IsAdmin)
        {
            UId = true;
        }
        if (UId)
        {
            if (model.UpdateUser == "管理员" && model.Enabled == false)
            {
                result.msg = GetResource("管理员已禁用");
                return Json(result);
            }
        }

        model.AccessKey = AccessKey;
        model.AccessId = AccessId;
        model.ProjectName = ProjectName;
        model.Remark = Remark;
        model.Enabled = Enabled == "on";

        model.Update();

        //var keyOpenPlatform = $"{UtilSetting.Current.CacheKeyPrefix}:openplatform:{model.CreateUserID}";

        //var rds = EngineContext.Current.Resolve<FullRedis>();
        //rds.Remove(keyOpenPlatform);

        result.msg = GetResource("修改成功");
        result.success = true;
        return Json(result);
    }

    /// <summary>
    /// 删除开发平台信息
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [HttpPost]
    [DisplayName("删除开发平台信息")]
    public IActionResult Delete(int Id)
    {
        var res = new DResult();

        var model = OpenPlatform.FindById(Id);
        if (model != null)
        {
            //var keyOpenPlatform = $"{UtilSetting.Current.CacheKeyPrefix}:openplatform:{model.CreateUserID}";

            //var rds = EngineContext.Current.Resolve<FullRedis>();
            //rds.Remove(keyOpenPlatform);

            model.Delete();
        }

        res.success = true;

        return Json(res);
    }

}
