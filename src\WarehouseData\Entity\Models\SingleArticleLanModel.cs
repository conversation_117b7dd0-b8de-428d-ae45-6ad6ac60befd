﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>单页文章翻译</summary>
public partial class SingleArticleLanModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>单页文章Id</summary>
    public Int32 SId { get; set; }

    /// <summary>所属语言Id</summary>
    public Int32 LId { get; set; }

    /// <summary>文章标题</summary>
    public String? Name { get; set; }

    /// <summary>内容</summary>
    public String? Content { get; set; }

    /// <summary>上传的文件</summary>
    public String? FileUrl { get; set; }

    /// <summary>Md5</summary>
    public String? Md5 { get; set; }

    /// <summary>扩展字段1</summary>
    public String? Ex1 { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ISingleArticleLan model)
    {
        Id = model.Id;
        SId = model.SId;
        LId = model.LId;
        Name = model.Name;
        Content = model.Content;
        FileUrl = model.FileUrl;
        Md5 = model.Md5;
        Ex1 = model.Ex1;
    }
    #endregion
}
