﻿using DG.Web.Framework;

using DH.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.Models;

using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>
/// 部门管理
/// </summary>
[DisplayName("部门管理")]
[Description("部门管理")]
[AdminArea]
[DHMenu(80,ParentMenuName = "DHUser", CurrentMenuUrl = "~/{area}/Department", CurrentMenuName = "DepartmentManager", LastUpdate = "20240305")]
public class DepartmentController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 80;

    /// <summary>
    /// 部门管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("部门管理")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 部门树型
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("部门树型")]
    public IActionResult GetOrganizationUnitList()
    {
        var result = new DResult();

        var list = Department.FindAllWithCache().OrderBy(e => e.Sort).ThenBy(e => e.ID).Select(e =>
        {
            int? parentId = e.ParentID == 0 ? null : e.ParentID;

            return new { displayName = e.Name, id = e.ID, code = e.Code, parentId = parentId };
        });
        result.data = list;
        result.code = 200;
        result.msg = "";

        return Json(result);
    }

    /// <summary>
    /// 机构列表
    /// </summary>
    /// <param name="Id">上级机构编号</param>
    /// <param name="Name">关键词</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("机构列表")]
    public IActionResult GetPagedOrganizationUnit(String Name, Int32 Id = -1, Int32 page = 1, Int32 limit = 100)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            OrderBy = "Sort Asc, ID Asc"
        };

        if (Id > 0 || !Name.IsNullOrWhiteSpace())
        {
            var list = DepartmentE.Searchs(Id, null, null, Name.SafeString().Trim(), pages, true).Select(e => new
            {
                e.ID,
                e.Name,
                e.ParentID,
                MemberCount = UserE.GetCountByDId(e.ID),
                e.ParentName
            });

            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data = list, statusCode = 200 });
        }
        else
        {
            var list = DepartmentE.GetList().Select(e => new
            {
                e.ID,
                e.Name,
                e.ParentID,
                MemberCount = UserE.GetCountByDId(e.ID),
                e.ParentName
            });
            return Json(new { code = 0, msg = "success", count = 0, data = list, statusCode = 200 });
        }
    }

    /// <summary>
    /// 增加部门机构
    /// </summary>
    /// <param name="ParentId">父部门机构Id</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("增加部门机构")]
    public IActionResult Add(Int32 ParentId)
    {
        ViewBag.ParentId = ParentId;

        return View();
    }

    /// <summary>
    /// 增加部门机构
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("增加部门机构")]
    [HttpPost]
    public IActionResult Add(Int32 ParentId, String displayName)
    {
        var result = new DResult();

        if (displayName.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("机构名称不能为空");
            return Json(result);
        }

        var pList = DepartmentE.FindAllByParentId(ParentId);
        if (pList.Any(e => e.Name == displayName))
        {
            result.msg = GetResource("同层级下不能出现相同机构名称");
            return Json(result);
        }

        var model = new Department();
        model.Name = displayName;
        model.ParentID = ParentId;
        model.Enable = true;
        model.Visible = true;

        var modelParent = model.Parent;

        if (ParentId == 0)
        {
            model.Level = 0;
        }
        else
        {
            if (modelParent != null)
            {
                model.Level = modelParent.Level + 1;
            }
        }
        model.Insert();

        model.Ex4 = ParentId == 0 ? $",{model.ID}," : $"{modelParent?.Ex4}{model.ID},";
        model.Update();

        result.success = true;
        result.msg = GetResource("成功新增机构");

        return Json(result);
    }

    /// <summary>
    /// 编辑部门机构
    /// </summary>
    /// <param name="Id">部门机构Id</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("编辑部门机构")]
    public IActionResult Edit(Int32 Id)
    {
        var model = DepartmentE.FindByID(Id);

        return View(model);
    }

    /// <summary>
    /// 编辑部门机构
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("编辑部门机构")]
    [HttpPost]
    public IActionResult Edit(Int32 Id, String displayName)
    {
        var result = new DResult();

        if (displayName.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("机构名称不能为空");
            return Json(result);
        }

        var model = DepartmentE.FindByID(Id);
        if (model != null)
        {
            var m = DepartmentE.FindByNameAndParentID(displayName, model.ParentID);
            if (m != null && m.ID != model.ID)
            {
                result.msg = GetResource("机构名称不能重复");
                return Json(result);
            }
        }
        else
        {
            result.msg = GetResource("机构不存在");
            return Json(result);
        }

        model.Name = displayName;
        model.Update();

        result.success = true;
        result.msg = GetResource("成功编辑机构");

        return Json(result);
    }

    /// <summary>删除部门</summary>
    /// <returns></returns>
    [DisplayName("删除部门")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult Delete(Int32 Id)
    {
        if (UserE.GetCountByDId(Id) > 0)
        {
            return Json(new DResult() { success = false, msg = GetResource("机构下有用户，请调整后再删除") });
        }

        if (DepartmentE.FindAllByParentId(Id).Any())
        {
            return Json(new DResult() { success = false, msg = GetResource("机构下有子分类，请调整后再删除") });
        }

        var model = Department.FindByID(Id);
        if (model != null)
        {
            model.Delete();
        }

        Department.Meta.Cache.Clear("", true);

        return Json(new DResult() { success = true, msg = GetResource("删除成功") });
    }

    /// <summary>删除部门</summary>
    /// <returns></returns>
    [DisplayName("删除部门")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult DeleteMore([FromBody] List<Department> Id)
    {
        var result = new DResult();

        foreach (var item in Id)
        {
            if (UserE.GetCountByDId(item.ID) > 0)
            {
                result.msg = GetResource("机构下有用户，请调整后再删除");
                return Json(result);
            }
            else
            {
                var list = DepartmentE.FindAllByParentId(item.ID);
                foreach (var item1 in list)
                {
                    if (Id.Count(e => e.ID == item1.ID) == 0)
                    {
                        result.msg = GetResource("机构下有子分类，请调整后再删除");
                        return Json(result);
                    }
                }

            }
        }

        foreach (var item in Id)
        {
            item.Delete();
        }

        result.success = true;
        result.msg = GetResource("删除成功");

        return Json(result);
    }

}