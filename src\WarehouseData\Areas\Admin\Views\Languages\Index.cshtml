﻿@{
    Html.AppendTitleParts(T("语言包").Text);

    var LanguageList = Model!.LanguageList as IEnumerable<SelectListItem>;
}
<style asp-location="true">
    .totalcolor {
        font-size: 18px;
        color: #2db7f5;
    }

    .onlinecolor {
        font-size: 18px;
        color: #19be6b;
    }

    .noactivecolor {
        font-size: 18px;
        color: #f90;
    }

    .layui-card-header {
        height: auto;
    }

    .layui-form-label {
        width: auto;
    }

    .dg-form .layui-form-label {
        width: auto;
    }

    .layui-form-select dl {
        top: 32px;
        padding: 0px
    }

    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }
</style>

<script asp-location="Head">
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-form-item" id="search" style="margin-bottom: 2px;">
            <div class="layui-inline">
                <label class="layui-form-label">@T("翻译键")：</label>
                <div class="layui-input-inline seller-inline-3">
                    <input type="text" name="lanKey" id="lanKey" autocomplete="off" class="layui-input" lay-filter="name">
                </div>
            </div>
            <div class="layui-inline select">
                <label class="layui-form-label">@T("语言")：</label>
                <div class="layui-input-inline ">
                    <select name="cultureId" id="cultureId" lay-filter="culture">
                        @foreach (var item in LanguageList!)
                        {
                            <option value="@item.Value">@item.Text</option>
                        }
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">@T("翻译值")：</label>
                <div class="layui-input-inline seller-inline-4">
                    <input type="text" name="lanValue" id="lanValue" autocomplete="off" class="layui-input" />
                </div>
            </div>
            <div class="layui-inline">
                <button class="pear-btn pear-btn-danger pear-btn-md" id="btnAdd">
                    <i class="layui-icon">&#xe61f;</i>
                    @T("新增")
                </button>
                <button class="pear-btn pear-btn-danger pear-btn-md" id="btnExport">
                    <i class="layui-icon">&#xe601;</i>
                    @T("导出")
                </button>
                <button class="pear-btn pear-btn-danger pear-btn-md" id="btnImport">
                    <i class="layui-icon">&#xe62f;</i>
                    @T("导入")
                </button>
                <input type="file" id="fileInput" style="display: none;" />
            </div>
        </div>

    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['table', 'abp', 'dg', 'form'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("List")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , loading: true
            , cols: [[
                { field: 'LanKey', title: ' @T("翻译键")' }
                , { field: 'LanValue', title: ' @T("翻译值")' }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: 160 }
            ]]
            , height: 'full-120'
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]

            , id: 'tables'
        });

        window.active = {
            reload: function () {
                table.reload('tables', {
                    where: {
                        lanKey: $("#lanKey").val(),
                        cultureId: $("#cultureId").val(),
                        lanValue: $("#lanValue").val()

                    }
                })
            }
        }

        form.on("select(culture)", function () {
            active.reload();
        });
        $("#lanKey").on("input", function () {
            active.reload();
        });
        $("#lanValue").on("input", function () {
            active.reload();
        });

        // 新增逻辑
        $("#btnAdd").click(function () {
            top.layui.dg.popupRight({
                id: 'RoleDetail',
                title: ' @T("新增语言包")',
                closeBtn: 1,
                area: ['750px'],
                success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Add")" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        });

        // 导出逻辑
        $("#btnExport").click(function () {
            // 获取筛选结果
            var cultureId = $("#cultureId").val();
            
            // 创建表单
            var form = $('<form></form>').attr('action', '@Url.Action("Export")').attr('method', 'GET');
            form.append($('<input>').attr('type', 'hidden').attr('name', 'cultureId').attr('value', cultureId));

            // 将表单添加到body并提交
            form.appendTo('body').submit().remove();
            return false
        });
        
        // 导入逻辑
        $("#btnImport").click(function () {
            $("#fileInput").click();
            return false
        });
        $("#fileInput").change(function () {
            var file = this.files[0];
            if (!file) {
                return;
            }
            var formData = new FormData();
            formData.append("file", file);
            $.ajax({
                url: "@Url.Action("Import")",
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.success) {
                        abp.notify.success('@T("导入成功")');
                        active.reload();
                    } else {
                        abp.notify.warn(response.msg);
                    }
                }
            });
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === "edit") {
                top.layui.dg.popupRight({
                    id: 'ThingsDetail'
                    , title: ' @T("编辑语言包")'
                    , closeBtn: 1
                    , area: ['750px']
                    , success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Edit")?id=' + data.LanKey + '" frameborder="0" class="layadmin-iframe"></iframe>');
                    }
                });
            } else if (obj.event === "del") {
                parent.layer.confirm('@T("确认删除吗")?', { icon: 3, btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { Key: data.LanKey }, function (data) {
                        if (data.success) {
                            abp.notify.success('@T("删除成功")');
                            active.reload();
                        } else {
                            //alert(data.msg);
                            //os.success(data.msg);
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            }
        });

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'add') {
                top.layui.dg.popupRight({
                    id: 'RoleDetail'
                    , title: ' @T("新增语言包")'
                    , closeBtn: 1
                    , area: ['750px']
                    , success: function () {
                        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Add")" frameborder="0" class="layadmin-iframe"></iframe>');
                    }
                });
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

    });
</script>

<script type="text/html" id="tool">
    @if (this.Has((PermissionFlags)4)){
    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="edit"> @T("编辑")</a>
    }
    @if (this.Has((PermissionFlags)8)){
    <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="del"> @T("删除")</a>
    }
</script>

<script type="text/html" id="user-toolbar">
    @if (this.Has((PermissionFlags)2)){
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        @T("新增")
    </button>
    }
</script>