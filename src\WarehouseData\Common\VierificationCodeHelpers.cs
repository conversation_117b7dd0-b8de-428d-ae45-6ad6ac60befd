﻿using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp;

using SkiaSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Drawing.Processing;

namespace HlktechIoT.Common;

public static class VierificationCodeHelpers {
    //验证码字体集合
    private static readonly String[] fonts = { "Verdana", "Microsoft Sans Serif", "Comic Sans MS", "Arial", "宋体" };
    private static readonly SKColor[] colors = { SKColors.Black, SKColors.Green, SKColors.Brown };

    public static String CreateBase64Image(String Code)
    {
        var random = new Random();
        var info = new SKImageInfo((int)Code.Length * 18, 32);
        using var bitmap = new SKBitmap(info);
        using var canvas = new SKCanvas(bitmap);

        canvas.Clear(SKColors.White);

        using var pen = new SKPaint();
        pen.FakeBoldText = true;
        pen.Style = SKPaintStyle.Fill;
        pen.TextSize = 20;// 0.6f * info.Width * pen.TextSize / pen.MeasureText(code);

        //绘制随机字符
        for (int i = 0; i < Code.Length; i++)
        {
            pen.Color = random.GetRandom(colors);//随机颜色索引值
            pen.Typeface = SKTypeface.FromFamilyName(random.GetRandom(fonts), 700, 20, SKFontStyleSlant.Italic);//配置字体
            var point = new SKPoint()
            {
                X = i * 16,
                Y = 22// info.Height - ((i + 1) % 2 == 0 ? 2 : 4),

            };
            canvas.DrawText(Code.Substring(i, 1), point, pen);//绘制一个验证字符
        }

        //绘制噪点
        var points = Enumerable.Range(0, 100).Select(
            _ => new SKPoint(random.Next(bitmap.Width), random.Next(bitmap.Height))
        ).ToArray();
        canvas.DrawPoints(
            SKPointMode.Points,
            points,
            pen);

        //绘制贝塞尔线条
        for (int i = 0; i < 2; i++)
        {
            var p1 = new SKPoint(0, 0);
            var p2 = new SKPoint(0, 0);
            var p3 = new SKPoint(0, 0);
            var p4 = new SKPoint(0, 0);

            var touchPoints = new SKPoint[] { p1, p2, p3, p4 };

            using var bPen = new SKPaint();
            bPen.Color = random.GetRandom(colors);
            bPen.Style = SKPaintStyle.Stroke;

            using var path = new SKPath();
            path.MoveTo(touchPoints[0]);
            path.CubicTo(touchPoints[1], touchPoints[2], touchPoints[3]);
            canvas.DrawPath(path, bPen);
        }
        return bitmap.ToBase64String(SKEncodedImageFormat.Png);
    }

    public static T GetRandom<T>(this Random random, T[] tArray)
    {
        if (random == null) random = new Random();
        return tArray[random.Next(tArray.Length)];
    }

    /// <summary>
    /// SKBitmap转Base64String
    /// </summary>
    /// <param name="bitmap"></param>
    /// <param name="format"></param>
    /// <returns></returns>
    public static string ToBase64String(this SKBitmap bitmap, SKEncodedImageFormat format)
    {
        using var memStream = new MemoryStream();
        using var wstream = new SKManagedWStream(memStream);
        bitmap.Encode(wstream, format, 32);
        memStream.TryGetBuffer(out ArraySegment<byte> buffer);
        return $"{Convert.ToBase64String(buffer.Array!, 0, (int)memStream.Length)}";
    }

}

/// <summary>
/// 生成验证码
/// </summary>
public class ValidateCode {
    private static readonly SixLabors.ImageSharp.Color[] Colors = {
            SixLabors.ImageSharp.Color.Black,
            SixLabors.ImageSharp.Color.Red,
            SixLabors.ImageSharp.Color.Blue,
            SixLabors.ImageSharp.Color.Green,
            SixLabors.ImageSharp.Color.Orange,
            SixLabors.ImageSharp.Color.Brown,
            SixLabors.ImageSharp.Color.Brown,
            SixLabors.ImageSharp.Color.DarkBlue
        };
    private static readonly char[] Chars = { '2', '3', '4', '5', '6', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'R', 'S', 'T', 'W', 'X', 'Y' };
    //private static readonly int Width = 90;
    //private static readonly int Height = 35;

    /// <summary>
    /// 获取转码
    /// </summary>
    /// <param name="num"></param>
    /// <returns></returns>
    private static string GenCode(int num)
    {
        var code = string.Empty;
        var r = new Random();

        for (int i = 0; i < num; i++)
        {
            code += Chars[r.Next(Chars.Length)].ToString();
        }

        return code;
    }

    /// <summary>
    /// 生成验证码
    /// </summary>
    /// <param name="CodeLength"></param>
    /// <param name="Width"></param>
    /// <param name="Height"></param>
    /// <param name="FontSize"></param>
    /// <returns></returns>
    public static (string code, string base64) CreateValidateGraphic(int CodeLength, int Width, int Height, int FontSize)
    {
        var code = GenCode(CodeLength);
        var r = new Random();
        using var image = new Image<Rgba32>(Width, Height);
        // 字体
        var font = SixLabors.Fonts.SystemFonts.CreateFont(SixLabors.Fonts.SystemFonts.Families.First().Name, FontSize, SixLabors.Fonts.FontStyle.Bold);
        image.Mutate(ctx =>
        {
            // 白底背景
            ctx.BackgroundColor(SixLabors.ImageSharp.Color.White);

            // 画验证码
            for (int i = 0; i < code.Length; i++)
            {
                ctx.DrawText(code[i].ToString()
                    , font
                    , Colors[r.Next(Colors.Length)]
                    , new PointF(20 * i + 10, r.Next(2, 12)));
            }

            // 画干扰线
            for (int i = 0; i < 6; i++)
            {
                var pen = new SolidPen(Colors[r.Next(Colors.Length)], 1);
                var p1 = new PointF(r.Next(Width), r.Next(Height));
                var p2 = new PointF(r.Next(Width), r.Next(Height));

                ctx.DrawLine(pen, p1, p2);
            }

            // 画噪点
            for (int i = 0; i < 60; i++)
            {
                var pen = new SolidPen(Colors[r.Next(Colors.Length)], 1);
                var p1 = new PointF(r.Next(Width), r.Next(Height));
                var p2 = new PointF(p1.X + 1f, p1.Y + 1f);

                ctx.DrawLine(pen, p1, p2);
            }
        });
        using var ms = new System.IO.MemoryStream();
        //  格式 自定义
        image.SaveAsPng(ms);
        //image.Save(ms, new PngEncoder());
        byte[] imageBytes = ms.ToArray();
        var base64String = Convert.ToBase64String(imageBytes);
        return (code, "data:image/png;base64," + base64String);
    }
}