﻿@model WmsPickingOrder
@{

}
<style asp-location="true">
    html {
        background-color: #f2f2f2;
        background-color: transparent !important;
        color: #666;
    }

    body {
        height: 100%;
    }

    .pear-container {
        background-color: white;
    }

    .containers {
        width: 100%;
        padding-top: 20px;
        height: 100%
    }

    .container.form-horizontal {
        width: 100%;
        padding-left: -50px
    }

    .text-width {
        width: 154px
    }

    .text-width2 {
        width: 240px
    }

    .text-width3 {
        width: 327px
    }

    .label-width span {
        color: red
    }

    .layui-textarea {
        width: 90%;
    }

    .layui-form-item {
        padding: 0px 10px;
    }

        .layui-form-item.btn {
            text-align: center
        }
</style>

<script asp-location="Head">
    var pleaseSelect = '@T("请选择")';
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<div class="containers">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("订单备注")</label>
            <div class="layui-input-inline" style="min-width:400px">
                <textarea id="Remark" name="Remark" placeholder="@T("请输入内容")" class="layui-textarea">@Model.Remark</textarea>
            </div>
        </div>
        <div class="layui-form-item btn">
            <input hidden name="orderId" value="@Model.OrderId" />
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
        <input type="hidden" name="DTime" id="DTime" value="@Model.OrderingTime.ToFullString()" />
    </form>
</div>

<script asp-location="Footer">
    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
            var $ = layui.jquery;
            var abp = layui.abp;
            var form = layui.form;
            var table = layui.table;
            var dg = layui.dg;
            var os = layui.dgcommon;
            var dtree = layui.dtree;
            var laydate = layui.laydate;
            // 日期范围 - 左右面板独立选择模式

        form.on('submit(Submit)', function (data) {
            console.log(data.field)

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("Settings")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });
    });
</script>