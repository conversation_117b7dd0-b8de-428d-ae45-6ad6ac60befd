﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>生产订单</summary>
public partial class ProductOrdersModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int64 Id { get; set; }

    /// <summary>订单号</summary>
    public String OrderId { get; set; } = null!;

    /// <summary>产品型号编号</summary>
    public Int32 ProductTypeId { get; set; }

    /// <summary>产品项目编号</summary>
    public Int32 ProductProjectId { get; set; }

    /// <summary>订单开始时间</summary>
    public DateTime StartTime { get; set; }

    /// <summary>订单开始时间</summary>
    public DateTime EndTime { get; set; }

    /// <summary>合作公司Id</summary>
    public Int32 CompanyId { get; set; }

    /// <summary>数量</summary>
    public Int32 Quantity { get; set; }

    /// <summary>补单数量</summary>
    public Int32 ProductSupplementalQuantity { get; set; }

    /// <summary>审核状态 0待审核 1审核中 2已审核 3审核失败</summary>
    public Int32 Status { get; set; }

    /// <summary>审核备注</summary>
    public String? Remark { get; set; }

    /// <summary>Mac分配范围。使用Json存储多组start、end数据</summary>
    public String? MacRange { get; set; }

    /// <summary>Mac分配范围中总的数量</summary>
    public Int64 MacRangeCount { get; set; }

    /// <summary>审核时间</summary>
    public DateTime AuditTime { get; set; }

    /// <summary>产品固件编号。必填</summary>
    public Int64 FirmwaresId { get; set; }

    /// <summary>固件名称</summary>
    public String? FirmwareName { get; set; }

    /// <summary>固件路径</summary>
    public String? FirmwareFilePath { get; set; }

    /// <summary>订单状态数据 使用Json存储</summary>
    public String? DataInfo { get; set; }

    /// <summary>产测固件</summary>
    public String? ProductionTFirmware { get; set; }

    /// <summary>出货固件</summary>
    public String? ShippedFirmware { get; set; }

    /// <summary>标签模板</summary>
    public String? TagTemplate { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductOrders model)
    {
        Id = model.Id;
        OrderId = model.OrderId;
        ProductTypeId = model.ProductTypeId;
        ProductProjectId = model.ProductProjectId;
        StartTime = model.StartTime;
        EndTime = model.EndTime;
        CompanyId = model.CompanyId;
        Quantity = model.Quantity;
        ProductSupplementalQuantity = model.ProductSupplementalQuantity;
        Status = model.Status;
        Remark = model.Remark;
        MacRange = model.MacRange;
        MacRangeCount = model.MacRangeCount;
        AuditTime = model.AuditTime;
        FirmwaresId = model.FirmwaresId;
        FirmwareName = model.FirmwareName;
        FirmwareFilePath = model.FirmwareFilePath;
        DataInfo = model.DataInfo;
        ProductionTFirmware = model.ProductionTFirmware;
        ShippedFirmware = model.ShippedFirmware;
        TagTemplate = model.TagTemplate;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
