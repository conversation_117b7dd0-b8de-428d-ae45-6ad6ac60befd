﻿@{


    Html.AppendTitleParts(T("审核生产补单").Text);

    Html.AppendCssFileParts(ResourceLocation.Head, "~/libs/pear/css/module/dtree/dtree.css");
    Html.AppendCssFileParts(ResourceLocation.Head, "~/libs/pear/css/module/dtree/font/dtreefont.css");
}

<style asp-location="true">
    .sex {
        min-width: 30px;
        /* margin-top:10px; */
        margin: 10px 0px 0px -20px;
    }
</style>

<div class="layui-tab layui-tab-brief" lay-filter="user-tab">
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show">
            <form class="layui-form" lay-filter="user-form" style="padding: 10px 0 0 0;" autocomplete="off">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">@T("审核状态")</label>
                        <div class="layui-input-inline">
                            <select style="width:150%" name="Status">
                                <option value="2">@T("审核成功")</option>
                                <option value="3">@T("审核失败")</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">@T("备注")</label>
                        <div class="layui-input-inline">
                            <textarea type="text" name="remark" lay-verType="tips" lay-verify="myemail" autocomplete="off" class="layui-input" style="width:150%;height:120px">@Model.Remark</textarea>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <input type="hidden" name="Id" value="@Model.Id" />
                        <button class="layui-btn layui-hide" lay-submit lay-filter="user-submit" id="user-submit">@T("提交")</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'dg', 'form', 'element', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var dg = layui.dg;
        var dtree = layui.dtree;
        var element = layui.element;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        // form.verify({
        //     required: function (value, item) {
        //         if (!value) {
        //             return '@T("请输入公司名称")';
        //         }
        //     }
        // })

        form.on('submit(user-submit)', function (data) {

            var waitIndex = parent.layer.load(2);

            var url = "@Url.Action("AuditSupplementaryOrder")";

            abp.ajax({
                url: url,
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                parent.active.reload();
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;

        });

        element.on('tab(user-tab)', function (data) {
            parent.layer.iframeAuto(index);
        });

        window.submitForm = function () {
            $("#user-submit").click();
        }

        function getOrganizationUnitIds() {
            var selectedNode = dtree.getCheckbarNodesParam("organization-tree");
            var ids = selectedNode.map(function (d) { return d.nodeId });
            return ids;
        }

        function getRoleNames() {
            var roleNames = [];
            var _$roleCheckboxes = $("input[name='role']:checked");
            if (_$roleCheckboxes) {
                for (var roleIndex = 0; roleIndex < _$roleCheckboxes.length; roleIndex++) {
                    var _$roleCheckbox = $(_$roleCheckboxes[roleIndex]);
                    roleNames.push(_$roleCheckbox.val());
                }
            }
            return roleNames;
        }

    });
</script>