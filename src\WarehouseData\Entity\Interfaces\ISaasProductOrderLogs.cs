﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>生产厂家生产记录</summary>
public partial interface ISaasProductOrderLogs
{
    #region 属性
    /// <summary>编号</summary>
    Int64 Id { get; set; }

    /// <summary>订单号</summary>
    String OrderId { get; set; }

    /// <summary>第三方对接</summary>
    Int32 OpenPlatformId { get; set; }

    /// <summary>产品名称/型号</summary>
    String Name { get; set; }

    /// <summary>设备Mac地址</summary>
    String Mac { get; set; }

    /// <summary>文件路径。七牛云文件存储链接</summary>
    String? FileUrl { get; set; }

    /// <summary>产线测试点序号，一个功能点测试算一个节点，如果只有一道测试工序，即为 1</summary>
    Int32 Node { get; set; }

    /// <summary>测试结果， 1 测试通过 ，0 测试失败</summary>
    Int32 Status { get; set; }

    /// <summary>测试结果的描述，最长不超过 1024 字节</summary>
    String? Msg { get; set; }

    /// <summary>固件版本号</summary>
    String? Version { get; set; }

    /// <summary>测试工号，如果一个测试工号负责多个同样测试点测试，可以按 xxxx-01 来区分</summary>
    String? WorkId { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
