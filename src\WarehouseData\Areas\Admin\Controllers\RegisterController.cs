﻿using DG.Web.Framework;

using DH.Entity;
using DH.Helpers;
using DH.Services.Common;
using DH.Services.Services;

using Microsoft.AspNetCore.Mvc;

using NewLife;

using Pek;
using Pek.Helpers;
using Pek.Models;
using Pek.Webs;

using System.ComponentModel;
using System.Text;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>
/// 后台注册控制器
/// </summary>
[AdminArea]
public class RegisterController : BaseAdminControllerX {
    /// <summary>
    /// 密码服务
    /// </summary>
    private readonly PasswordService _passwordService;

    public RegisterController()
    {
        _passwordService = new PasswordService();
    }

    public IActionResult Index()
    {
        var returnUrl = GetRequest("r");
        if (returnUrl.IsNullOrEmpty()) returnUrl = GetRequest("ReturnUrl");
        ViewBag.ReturnUrl = returnUrl;

        ViewBag.PageTitle = GetResource("账号注册");

        return View();
    }

    /// <summary>
    /// 注册账号
    /// </summary>
    /// <param name="Name">账户</param>
    /// <param name="ImgCheckCode">图片验证码</param>
    /// <param name="CodeVal">短信验证码</param>
    /// <param name="OldPwd">密码</param>
    /// <param name="NewPwd">确认密码</param>
    /// <returns></returns>
    [HttpPost()]
    [DisplayName("注册账号")]
    public IActionResult Save([FromForm] String Name, [FromForm] String ImgCheckCode, [FromForm] String CodeVal, [FromForm] String OldPwd, [FromForm] String NewPwd)
    {
        var returnUrl = GetRequest("r");
        if (returnUrl.IsNullOrEmpty()) returnUrl = GetRequest("ReturnUrl");

        var set = DG.Setting.Current;

        var res = new DResult();

        if (!set.AllowManageRegister)
        {
            res.msg = GetResource("未开启注册功能，请联系管理员");
            return Json(res);
        }

        Name = Name.SafeString().Trim();
        if (Name.IsNullOrEmpty())
        {
            res.msg = GetResource("账户不能为空");
            return Json(res);
        }

        ImgCheckCode = ImgCheckCode.SafeString().Trim();
        if (ImgCheckCode.IsNullOrEmpty())
        {
            res.msg = GetResource("图片验证码不能为空");
            return Json(res);
        }

        OldPwd = OldPwd.SafeString().Trim();
        if (OldPwd.IsNullOrEmpty())
        {
            res.msg = GetResource("密码不能为空");
            return Json(res);
        }

        if (OldPwd != NewPwd)
        {
            res.msg = GetResource("两次密码不一致");
            return Json(res);
        }

        //if (!_passwordService.Valid(NewPwd))
        //{
        //    res.msg = GetResource("密码太弱，要求8位起且包含数字大小写字母和符号");
        //    return Json(res);
        //}

        if (NewPwd.Length < 8 || NewPwd.Length > 32)
        {
            res.msg = GetResource("请将密码限制在8到32位");
            return Json(res);
        }

        var RTypes = -1;
        if (ValidateHelper.IsMobile(Name))
        {
            RTypes = 1;
        }
        else if (ValidateHelper.IsEmail(Name))
        {
            RTypes = 2;
        }

        if (RTypes == -1)
        {
            res.msg = GetResource("请输入正确的手机号码/邮箱");
            return Json(res);
        }

        if (CodeVal.IsNullOrEmpty())
        {
            if (RTypes == 1) //短信
            {
                res.msg = GetResource("短信验证码不能为空");
            }
            else
            {
                res.msg = GetResource("邮箱验证码不能为空");
            }
            return Json(res);
        }

        #region 验证图片验证码
        var cResult = CommonHelpers.CheckCode(ImgCheckCode);
        if (!cResult.success)
        {
            return Json(cResult);
        }
        #endregion

        var smsname = HttpContext.Session.GetString("smsname");
        var smscode = HttpContext.Session.GetString("smscode");

        if (smsname.IsNullOrEmpty() || smscode.IsNullOrEmpty())
        {
            if (RTypes == 1) //短信
            {
                res.msg = GetResource("短信验证码过期");
            }
            else
            {
                res.msg = GetResource("邮箱验证码过期");
            }
            return Json(res);
        }

        if (smsname != Name || smscode != CodeVal)
        {
            if (RTypes == 1) //短信
            {
                res.msg = GetResource("短信验证码不正确");
            }
            else
            {
                res.msg = GetResource("邮箱验证码不正确");
            }
            return Json(res);
        }

        var model = new UserE();
        var modelDetail = new UserDetail();

        if (RTypes == 1) //短信
        {
            var Model = UserE.FindByMobile(Name);
            if (Model != null)
            {
                res.msg = GetResource("该手机号已经注册过账户");
                return Json(res);
            }

            model.Mobile = Name;
            modelDetail.MobileBind = true;
        }
        else if (RTypes == 2) //邮箱
        {
            var Model = UserE.FindByMail(Name);
            if (Model != null)
            {
                res.msg = GetResource("该邮箱已注册过账户");
                return Json(res);
            }
            model.Mail = Name;
            modelDetail.EmailBind = true;
        }

        model.Password = ManageProvider.Provider?.PasswordProvider.Hash(NewPwd);
        model.RegisterTime = DateTime.Now;
        model.Enable = true;
        model.Logins = 1;
        model.LastLogin = DateTime.Now;
        model.LastLoginIP =Pek.Helpers.DHWeb.IP;
        model.RegisterIP = Pek.Helpers.DHWeb.IP;
        model.RoleID = Role.GetOrAdd("默认用户").ID;
        model.Name = Name;
        model.DisplayName = Name;
        model.Insert();

        modelDetail.Id = model.ID;
        modelDetail.Insert();

        res.msg = GetResource("成功注册，请返回登录!");
        res.locate = Url.Action("Index", "Login")?.AppendReturn(returnUrl);
        res.success = true;
        return Json(res);
    }

}