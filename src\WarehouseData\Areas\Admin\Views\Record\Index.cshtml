﻿@{
    Html.AppendTitleParts(T("消息记录").Text);
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

        xm-select {
            min-height: 30px!important;
            max-height: 30px!important;
            line-height: 30px!important;
        }

            xm-select .xm-label .xm-label-block {
                height: 20px !important;
                line-height: 20px !important;
            }

        .seller-inline-3 {
            width: 130px !important;
        }
        @if (language.UniqueSeoCode == "en")
        { 
        <text>
        .layui-form-item.layui-inline .layui-form-label {
            width: auto
        }
        .dg-form .layui-form-label {
        width:auto
        }
      
        </text>
        }
</style>

<script asp-location="Head">
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage='@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn='@T("确定")';
    var layuiGoPage='@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc='@T("升序")';
    var layuiDesc='@T("降序")';
</script>

<form class="layui-form dg-form">
   <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
            <div class="layui-inline">
                <label class="layui-form-label">@T("会员名")：</label>
                <div class="layui-input-inline seller-inline-3">
                     <input type="text" name="member_name" id="member_name" placeholder="@T("请输入会员名")" autocomplete="off" class="layui-input wod" lay-filter="member_name">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 110px;">@T("接收手机/邮箱")：</label>
                <div class="layui-input-inline seller-inline-3">
                    <input type="text" name="smslog_phone" id="smslog_phone" placeholder="@T("请输入接收手机/邮箱")" autocomplete="off" class="layui-input wod" lay-filter="smslog_phone">
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">@T("操作时间")：</label>
                <div class="layui-input-inline seller-inline-4">
                    <input type="text" name="date" id="date" placeholder="@T("开始时间") @T("到") @T("结束时间")" autocomplete="off" class="layui-input">
                </div>
            </div>
        </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
        layui.use(['abp', 'form', 'laydate', 'table'], function () {
            var $ = layui.jquery;
            var abp = layui.abp;
            var form = layui.form;
            var laydate = layui.laydate;
            var table = layui.table;
            
            laydate.render({
                elem: '#date',
                range: '@T("到")',
                format: 'yyyy-MM-dd',
                lang: '@language.UniqueSeoCode',
                trigger: 'click', //自动弹出控件的事件，采用click弹出
                done: function (value, date, endDate) {
                    //console.log(value);
                    $("#date").val(value)
                    active.reload();
                }
            });

            table.render({
                elem: '#tablist'
                , url: '@Url.Action("GetList")'
                , page: true
                , toolbar: '#user-toolbar'
                , defaultToolbar: [{
                    title: '@T("刷新")',
                    layEvent: 'refresh',
                    icon: 'layui-icon-refresh',
                }, 'filter', 'print']
                , cellMinWidth: 80
                , smartReloadModel: true
                , cols: [[
                    { field: 'Id', title: '@T("ID")', width: 70, align: "center" }
                  , { field: 'CreateUser', title: '@T("会员名")', width: 89 }
                  , { field: 'Account', title: '@T("接收手机")', width: 150  }
                  , { field: 'Msg', title: '@T("消息内容")' }
                  , { field: 'MType', title: '@T("消息类别")' ,toolbar: '#type', width: 100 }
                  , { field: 'CreateTime', title: '@T("发送时间")', width: 160 }
                ]]
                , limit: 13
                , limits: [10, 13, 20, 30, 50, 100]
                , height: 'full-100'
                , id: 'tables'
            });

            window.active = {
                reload: function () {
                    table.reload('tables',
                        {
                            where: {
                                member_name: $("#member_name").val(),
                                date: $("#date").val(),
                                smslog_phone: $("#smslog_phone").val()
                            }
                        });
                }
            }

            $("#member_name").on("input", function (e) {
                active.reload();
            });

            $("#smslog_phone").on("input", function (e) {
                active.reload();
            });

        });
</script>

<script type="text/html" id="success">
        {{# if(d.Success) { }}
        <i class="pear-icon pear-icon-select-bold" style="color: green;"></i>
        {{# } else { }}
        <i class="pear-icon pear-icon-close-bold" style="color: red;"></i>
        {{# } }}
</script>

<script type="text/html" id="user-toolbar">
</script>
    @*1为注册，2为登录，3为找回密码，4绑定手机/邮箱，5安全验证,6账号申诉，0测试*@
    <script type="text/html" id="type">
        {{# if(d.MType == 1) { }}
        @T("注册")
        {{# } else if(d.MType == 2) { }}
        @T("登陆")
        {{# } else if(d.MType == 3) { }}
        @T("找回密码")
        {{# } else if(d.MType == 4) { }}
        @T("绑定手机/邮箱")
        {{# } else if(d.MType == 5) { }}
        @T("安全验证")
        {{# } else if(d.MType == 6) { }}
        @T("账号申诉")
        {{# } else if(d.MType == 0) { }}
        @T("测试")
        {{# } }}
    </script>