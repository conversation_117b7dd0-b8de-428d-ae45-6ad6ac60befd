﻿using DG.Web.Framework;

using DH;
using DH.Helpers;
using DH.RateLimter;

using HlktechIoT.Common;
using HlktechIoT.Dto;
using HlktechIoT.Entity;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Caching;
using NewLife.Caching.Queues;
using NewLife.Log;
using NewLife.Serialization;

using Pek;
using Pek.Helpers;
using Pek.Ids;
using Pek.Iot;
using Pek.Models;
using Pek.MVC;
using Pek.Swagger;
using Pek.Webs;
using Pek.Webs.Clients;

namespace HlktechIoT.Controllers.Api;

[Produces("application/json")]
[CustomRoute(ApiVersions.V1)]
[Authorize("jwt")]
public class SaasController : ApiControllerBaseX
{
    /// <summary>
    /// 分布式缓存提供者
    /// </summary>
    private readonly ICacheProvider _cacheProvider;

    public SaasController(ICacheProvider cacheProvider)
    {
        _cacheProvider = cacheProvider ?? throw new ArgumentNullException(nameof(cacheProvider));
    }

    /// <summary>
    /// AS201生产结果上报
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Data">质检报告参数</param>
    /// <param name="Id">唯一标识</param>
    /// <returns></returns>
    [HttpPost("AS201InspectionReport")]
    [RateValve(Policy = Policy.Ip, Limit = 60, Duration = 30)]
    public async Task<IActionResult> AS201InspectionReport([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromBody] AS201InspectionReport Data)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(AS201InspectionReport));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }

        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }

        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }

        if (TimeStamp <= 0)
        {
            result.ErrCode = 10003;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }

        if (Data == null)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("质检报告参数未上传", Lng);
            return result;
        }

        if (Data.SN.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10005;
            result.Message = GetResource("设备Sn为空", Lng);
            return result;
        }

        //if (Data.Mac.IsNullOrWhiteSpace())
        //{
        //    result.ErrCode = 10017;
        //    result.Message = GetResource("设备Mac为空", Lng);
        //    return result;
        //}

        if (!Data.Mac.IsNullOrWhiteSpace())
            Data.Mac = MacHelper.NormalizeToColonFormat(Data.Mac);

        if (Data.OrderId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10016;
            result.Message = GetResource("订单号为空", Lng);
            return result;
        }

        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10006;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10007;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10008;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var modelProductOrders = ProductOrders.FindByOrderId(Data.OrderId);
        if (modelProductOrders == null)
        {
            result.ErrCode = 10009;
            result.Message = GetResource($"订单不存在", Lng);
            return result;
        }

        if (modelProductOrders.ProductType == null)
        {
            result.ErrCode = 10019;
            result.Message = GetResource($"平台订单数据有误", Lng);
            return result;
        }

        #region 产品型号参数校验
        var proType = modelProductOrders.ProductType;
        if (proType?.PCBCycle == true && Data.TypeData?.PCBCycle.IsNullOrWhiteSpace() == true)
        {
            result.ErrCode = 10013;
            result.Message = GetResource($"指定参数未传", Lng);
            return result;
        }

        if (proType?.ShieldCycle == true && Data.TypeData?.ShieldCycle.IsNullOrWhiteSpace() == true)
        {
            result.ErrCode = 10014;
            result.Message = GetResource($"指定参数未传", Lng);
            return result;
        }

        if (proType?.MainChipCycle == true && Data.TypeData?.MainChipCycle.IsNullOrWhiteSpace() == true)
        {
            result.ErrCode = 10015;
            result.Message = GetResource($"指定参数未传", Lng);
            return result;
        }
        #endregion

        var list = ProductSnMac.FindAllOtherBySn(Data.SN, modelProductOrders.StartTime, modelProductOrders.EndTime);
        if (list == null || !list.Any())
        {
            result.ErrCode = 10010;
            result.Message = GetResource("非法设备Sn", Lng);
            return result;
        }

        var model = list.FirstOrDefault()!;

        if (model.IsValidate && (!model.IsRepetition || model.IsRepetitionDate <= DateTime.Now))
        {
            result.ErrCode = 10011;
            result.Message = GetResource("设备已上传质检报告", Lng);
            return result;
        }

        DeviceMacs? modelMac = null;
        if (!Data.Mac.IsNullOrWhiteSpace())
        {
            modelMac = DeviceMacs.FindByMac(Data.Mac);
            if (modelMac != null)
            {
                var modelMac1 = ProductSnMac.FindById(modelMac.AssociationId);
                if (modelMac1 != null && modelMac1.Id != model.Id)
                {
                    result.ErrCode = 10018;
                    result.Message = GetResource("设备Mac地址已存在", Lng);
                    return result;
                }
            }
        }

        //XTrace.WriteLine($"获取到的质检数据：{Data.ToJson()}");
        string fileName = model.Sn + Randoms.RandomStr(4);

        if (Data.IsCreateFile)
        {
            // 生成PDF文档
            InspectionReport.CreateInspectionReport(Data, fileName, modelProductOrders.ProductTypeName!, out string newData);
            //生成短链接
            var client = new WebClient();
            var res = await client.Post("https://s.0ht.cn/Url/GetUrl")
                .ContentType(HttpContentType.FormUrlEncoded)
                .IgnoreSsl()
                .Retry(3)
                .Data("Url", UrlHelper.Combine(DHSetting.Current.CurDomainUrl, Url.Action("Index", "OnlineReport", new { sn = Data.SN, orderId = Data.OrderId })!))
                .WhenCatch<HttpRequestException>(ex =>
                {
                    return $"请求失败：{ex.StackTrace}";
                })
                .ResultStringAsync();

            if (res.Contains("请求失败") || res.Contains("false", StringComparison.OrdinalIgnoreCase))
            {
                XTrace.WriteLine($"[ProductOrders.CreateSnMac]{res}");

                result.ErrCode = 10012;
                result.Message = GetResource("短链接请求失败", Lng);
                return result;
            }

            var info = res.ToJsonEntity<DResult>();
            model.DataInfo = newData;
            model.FilePath = UrlHelper.Combine(DHSetting.Current.CurDomainUrl, $"/Uploads/Products/{modelProductOrders?.ProductTypeName}/{fileName}_signed.pdf");
            model.ShorUrl = info?.data?.ToString();
        }

        model.IsValidate = true;
        if (!Data.Mac.IsNullOrWhiteSpace())
            model.Mac = Data.Mac;

        model.Content = Data.Content;
        model.Update();

        if (!Data.Mac.IsNullOrWhiteSpace())
        {
            if (modelMac == null)
            {
                modelMac = new DeviceMacs
                {
                    Mac = Data.Mac,
                    AssociationId = model.Id,
                    OrderId = model.OrderId,
                };
                modelMac.Insert();
            }
        }

        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 生产结果上报
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="File">文件</param>
    /// <param name="Sn">设备Sn/Mac/Mac1</param>
    /// <param name="OrderId">订单号</param>
    /// <param name="CheckType">数据查询类型。1为Sn，2为主Mac，3为副Mac</param>
    /// <param name="Id">唯一标识</param>
    /// <param name="Content">测试结果。以Json的方式存储，比如某一项为key,成功与否为value，方便后续解析json根据key和value来渲染到页面上</param>
    /// <returns></returns>
    [HttpPost("UploadReport")]
    [RateValve(Policy = Policy.Ip, Limit = 60, Duration = 30)]
    public IActionResult UploadReport([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, IFormFile? File, [FromForm] String? Sn, [FromForm] String? OrderId, [FromForm] Int32 CheckType, [FromForm] String? Content)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(UploadReport));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }

        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }

        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10003;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }

        if (TimeStamp <= 0)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }

        if (CheckType <= 0 && CheckType > 3)
        {
            result.ErrCode = 10006;
            result.Message = GetResource("数据查询类型不对", Lng);
            return result;
        }

        if (Sn.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10005;
            result.Message = GetResource("Sn不能为空", Lng);
            return result;
        }

        if (OrderId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10010;
            result.Message = GetResource("订单号为空", Lng);
            return result;
        }

        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10011;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10012;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10013;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var modelProductOrders = ProductOrders.FindByOrderId(OrderId);
        if (modelProductOrders == null)
        {
            result.ErrCode = 10014;
            result.Message = GetResource($"订单不存在", Lng);
            return result;
        }

        if (modelProductOrders.ProductType == null)
        {
            result.ErrCode = 10019;
            result.Message = GetResource($"平台订单数据有误", Lng);
            return result;
        }

        IList<ProductSnMac>? list = null;

        if (CheckType == 1)
        {
            Sn = Sn.Trim();

            list = ProductSnMac.FindAllOtherBySn(Sn, modelProductOrders.StartTime, modelProductOrders.EndTime);
            if (list == null || !list.Any())
            {
                result.ErrCode = 10015;
                result.Message = GetResource("非法设备Sn", Lng);
                return result;
            }
        }
        else if (CheckType == 2)
        {
            Sn = MacHelper.NormalizeToColonFormat(Sn);

            list = ProductSnMac.FindAllOtherByMac(Sn, modelProductOrders.StartTime, modelProductOrders.EndTime);
            if (list == null || !list.Any())
            {
                result.ErrCode = 10016;
                result.Message = GetResource("非法设备Mac", Lng);
                return result;
            }
        }
        else if (CheckType == 3)
        {
            Sn = MacHelper.NormalizeToColonFormat(Sn);

            list = ProductSnMac.FindAllOtherByMac1(Sn, modelProductOrders.StartTime, modelProductOrders.EndTime);
            if (list == null || !list.Any())
            {
                result.ErrCode = 10017;
                result.Message = GetResource("非法设备Mac1", Lng);
                return result;
            }
        }

        var model = list?.FirstOrDefault()!;

        if (model.IsValidate && (!model.IsRepetition || model.IsRepetitionDate <= DateTime.Now))
        {
            result.ErrCode = 10018;
            result.Message = GetResource("已上传过", Lng);
            return result;
        }

        if (File != null)
        {
            var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(File.FileName)}";
            var filepath = DHSetting.Current.UploadPath.CombinePath($"Products/{modelProductOrders.ProductType.Name}/{filename}");

            var saveFileName = filepath.GetFullPath();
            var f = saveFileName.AsFile();
            if (f.Exists)
            {
                f.Delete();
            }
            saveFileName.EnsureDirectory();
            File.SaveAs(saveFileName);

            model.FilePath = filepath;
        }

        model.IsValidate = true;
        model.Content = Content;
        model.Update();

        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 获取短链接
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Data">参数</param>
    /// <param name="Id">唯一标识</param>
    /// <returns></returns>
    [HttpPost("GetShorUrl")]
    [RateValve(Policy = Policy.Ip, Limit = 60, Duration = 30)]
    public async Task<IActionResult> GetShorUrl([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromBody] ShortUrlDto Data)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(GetShorUrl));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }

        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }

        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }

        if (TimeStamp <= 0)
        {
            result.ErrCode = 10003;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }

        if (Data == null)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("质检报告参数未上传", Lng);
            return result;
        }

        if (Data.SN.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10005;
            result.Message = GetResource("设备Sn为空", Lng);
            return result;
        }

        if (Data.OrderId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10013;
            result.Message = GetResource("订单号为空", Lng);
            return result;
        }

        if (Data.CheckType < 1 || Data.CheckType > 3)
        {
            result.ErrCode = 10014;
            result.Message = GetResource("查询类型有误", Lng);
            return result;
        }

        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10006;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10007;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10008;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var modelProductOrders = ProductOrders.FindByOrderId(Data.OrderId);
        if (modelProductOrders == null)
        {
            result.ErrCode = 10009;
            result.Message = GetResource($"订单不存在", Lng);
            return result;
        }

        IList<ProductSnMac>? list;
        if (Data.CheckType == 1)  // Sn
        {
            list = ProductSnMac.FindAllOtherBySn(Data.SN, modelProductOrders.StartTime, modelProductOrders.EndTime);
            if (list == null || !list.Any())
            {
                result.ErrCode = 10010;
                result.Message = GetResource("非法设备Sn", Lng);
                return result;
            }
        }
        else if (Data.CheckType == 2)  // 主Mac
        {
            list = ProductSnMac.FindAllOtherByMac(Data.SN, modelProductOrders.StartTime, modelProductOrders.EndTime);
            if (list == null || !list.Any())
            {
                result.ErrCode = 10011;
                result.Message = GetResource("非法设备Mac", Lng);
                return result;
            }
        }
        else
        {
            list = ProductSnMac.FindAllOtherByMac1(Data.SN, modelProductOrders.StartTime, modelProductOrders.EndTime);
            if (list == null || !list.Any())
            {
                result.ErrCode = 10012;
                result.Message = GetResource("非法设备Mac1", Lng);
                return result;
            }
        }

        var model = list.FirstOrDefault()!;

        if (model.ShorUrl.IsNullOrWhiteSpace())
        {
            if (!model.FilePath.IsNullOrWhiteSpace() && model.FilePath.StartsWith("http"))
            {
                var client = new WebClient();
                var res = await client.Post("https://s.0ht.cn/Url/GetUrl")
                    .ContentType(HttpContentType.FormUrlEncoded)
                    .IgnoreSsl()
                    .Retry(3)
                    .Data("Url", UrlHelper.Combine(DHSetting.Current.CurDomainUrl, Url.Action("Index", "OnlineReport", new { sn = Data.SN, orderId = Data.OrderId })!))
                    .WhenCatch<HttpRequestException>(ex =>
                    {
                        return $"请求失败：{ex.StackTrace}";
                    })
                    .ResultStringAsync();

                XTrace.WriteLine($"判断链接2：{res}");

                if (res.Contains("请求失败") || res.Contains("false", StringComparison.OrdinalIgnoreCase))
                {
                    XTrace.WriteLine($"[ProductOrders.CreateSnMac]{res}");

                    result.ErrCode = 10012;
                    result.Message = GetResource("短链接请求失败", Lng);
                    return result;
                }

                var info = res.ToJsonEntity<DResult>();
                model.ShorUrl = info?.data?.ToString();
                model.Update();
            }
            else
            {
                result.ErrCode = 10015;
                result.Message = GetResource("短网址为空，请确认是否有上传数据或者联系管理员是否短网址生成失败", Lng);
                return result;
            }
        }

        result.Data = model.ShorUrl;
        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 获取未使用的Sn/Mac/Mac1
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Data">参数</param>
    /// <param name="Id">唯一标识</param>
    /// <returns></returns>
    [HttpPost("GetAvailableSN")]
    [RateValve(Policy = Policy.Ip, Limit = 60, Duration = 30)]
    public async Task<IActionResult> GetAvailableSN([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromBody] DeviceSnDto Data)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(GetAvailableSN));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        if (!PekSettings.Current.AllowGetSNMac)
        {
            result.ErrCode = 10015;
            result.Message = GetResource("目前不允许获取SN/Mac", Lng);
            return result;
        }

        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }

        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }

        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }

        if (TimeStamp <= 0)
        {
            result.ErrCode = 10003;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }

        if (Data == null)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("参数未上传", Lng);
            return result;
        }

        if (Data.OrderId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10005;
            result.Message = GetResource("订单号为空", Lng);
            return result;
        }

        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10006;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10007;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10008;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var modelProductOrders = ProductOrders.FindByOrderId(Data.OrderId);

        if (modelProductOrders == null)
        {
            result.ErrCode = 10009;
            result.Message = GetResource($"订单不存在", Lng);
            return result;
        }

        if (modelProductOrders.Status != 2)
        {
            result.ErrCode = 10019;
            result.Message = GetResource($"只能请求审核通过的订单", Lng);
            return result;
        }

        var queueKey = $"WarehouseData:sn_queue:{Data.OrderId}";  // 用于存储Sn/Mac/Mac1的队列缓存键
        var lockKey = $"WarehouseData:sn_lock:{Data.OrderId}";  // 用于分布式锁的缓存键
        var processingKey = $"WarehouseData:sn_processing:{Data.OrderId}:"; // 正在处理中的缓存键（双缓存第一层）
        var completedKey = $"WarehouseData:sn_completed:{Data.OrderId}:"; // 已完成处理的缓存键（双缓存第二层）
        var orderKey = $"WarehouseData:sn_order_status:{Data.OrderId}"; // 订单状态。设备返回没有可用设备时临时缓存1分钟，避免重复请求

        if (_cacheProvider.Cache.ContainsKey(orderKey))
        {
            result.ErrCode = 10018;
            result.Message = GetResource("暂无可用设备", Lng);
            return result;
        }

        var queue = _cacheProvider.GetQueue<String>(queueKey, Data.OrderId) as RedisStream<String>;

        DeviceSnReponseDto? mqMsg = null;
        try
        {
            //var mqMsg1 = await queue!.TakeOneAsync(1).ConfigureAwait(false);
            //var mqMsg1 = await queue!.TakeOneAckAsync(1).ConfigureAwait(false);
            var mqMsg1 = await queue!.TakeMessageAsync(1).ConfigureAwait(false);
            XTrace.WriteLine($"获取到的数据：{mqMsg1?.Id}");

            mqMsg = mqMsg1?.GetBody<String>()?.ToJsonEntity<DeviceSnReponseDto>();
            if (mqMsg != null)
            {
                // 使用共用方法处理设备消息
                if (!ProcessDeviceMessage(mqMsg, processingKey, completedKey, result, Lng))
                {
                    return result;
                }
            }
            else // 未获取到Sn/Mac/Mac1
            {
                // 队列为空，用分布式锁保护初始化
                using var distributedLock = _cacheProvider.AcquireLock(lockKey, 3_000); // 3秒超时

                if (distributedLock == null)
                {
                    result.ErrCode = 10011;
                    result.Message = GetResource("系统繁忙，请稍后重试", Lng);
                    return result;
                }

                // 再次检查队列是否已被其他线程初始化
                var retryQueue = _cacheProvider.GetQueue<String>(queueKey, Data.OrderId) as RedisStream<String>;

                try
                {
                    //mqMsg1 = await retryQueue.TakeOneAsync(1).ConfigureAwait(false);
                    //mqMsg1 = await retryQueue.TakeOneAckAsync(1).ConfigureAwait(false);
                    mqMsg1 = await retryQueue!.TakeMessageAsync(1).ConfigureAwait(false);
                    XTrace.WriteLine($"获取到的数据1：{mqMsg1?.Id}");

                    mqMsg = mqMsg1?.GetBody<String>()?.ToJsonEntity<DeviceSnReponseDto>();
                    if (mqMsg != null)
                    {
                        // 队列已被初始化，使用共用方法处理这个消息
                        if (!ProcessDeviceMessage(mqMsg, processingKey, completedKey, result, Lng))
                        {
                            return result;
                        }
                    }
                    else
                    {
                        // 队列确实为空，进行初始化
                        var list = ProductSnMac.FindAllByOtherProductOrdersId(modelProductOrders.Id, modelProductOrders.StartTime, modelProductOrders.EndTime, 1000, "Id,Sn,Mac,Mac1");

                        if (list == null || !list.Any())
                        {
                            list = [];

                            // 只有当没有可用SN时才执行回收逻辑
                            var timeoutThreshold = DateTime.Now.AddMinutes(-PekSettings.Current.SNRecycleTimeoutMinutes);
                            var timedOutSns = ProductSnMac.FindTimedOutSnMacs(modelProductOrders.Id, modelProductOrders.StartTime, modelProductOrders.EndTime, timeoutThreshold);
                            foreach (var timedOut in timedOutSns)
                            {
                                timedOut.IsConsumed = false;
                                timedOut.ConsumedTime = DateTime.MinValue;
                                timedOut.IsConfirmConsumed = false;
                                timedOut.ConfirmConsumedTime = DateTime.MinValue;
                                timedOut.Update();

                                list.Add(timedOut);

                                // 清理缓存中的要回收的的已消费的Sn/Mac/Mac1
                                _cacheProvider.Cache.Remove($"{completedKey}{timedOut.Id}");

                                XTrace.WriteLine($"Recycled SN: {timedOut.Sn}：{timedOut.ToJson()}");
                            }
                        }

                        if (list != null && list.Any())
                        {
                            var deviceList = list.Select(e => new DeviceSnReponseDto
                            {
                                Id = e.Id,
                                Sn = e.Sn,
                                Mac = e.Mac,
                                Mac1 = e.Mac1
                            }).ToList();

                            mqMsg = deviceList.First();

                            if (deviceList.Count > 1)
                            {
                                var jsonStrings = deviceList.Skip(1).Select(x => x.ToJson()).ToArray();
                                foreach (var item in jsonStrings)
                                {
                                    retryQueue.Add(item);
                                }
                            }

                            // 使用共用方法处理当前请求的消息
                            if (!ProcessDeviceMessage(mqMsg, processingKey, completedKey, result, Lng))
                            {
                                return result;
                            }
                        }
                        else
                        {
                            _cacheProvider.Cache.Set(orderKey, 1, TimeSpan.FromMinutes(1)); // 设置订单状态缓存1分钟，避免重复请求

                            result.ErrCode = 10015;
                            result.Message = GetResource("暂无可用设备", Lng);
                            return result;
                        }
                    }

                    if (mqMsg1 != null)
                        retryQueue.Acknowledge(mqMsg1.Id!); // 确认消息已处理成功，避免重复处理
                }
                catch (TimeoutException ex)
                {
                    XTrace.WriteException(ex);

                    result.ErrCode = 10016;
                    result.Message = GetResource("获取超时，请稍后重试", Lng);
                    return result;
                }
                catch (Exception ex)
                {
                    XTrace.WriteException(ex);
                    result.ErrCode = 10017;
                    result.Message = GetResource("系统异常，请稍后重试", Lng);
                    return result;
                }
            }

            if (mqMsg1 != null)
                queue.Acknowledge(mqMsg1.Id!); // 确认消息已处理成功，避免重复处理
        }
        catch (TimeoutException ex)
        {
            XTrace.WriteException(ex);

            result.ErrCode = 10016;
            result.Message = GetResource("获取超时，请稍后重试", Lng);
            return result;
        }
        catch (Exception ex)
        {
            XTrace.WriteException(ex);
            result.ErrCode = 10017;
            result.Message = GetResource("系统异常，请稍后重试", Lng);
            return result;
        }

        result.Data = mqMsg;
        result.ExtData = new { modelProductOrders.ProductType?.PCBCycle, modelProductOrders.ProductType?.ShieldCycle, modelProductOrders.ProductType?.MainChipCycle };
        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 上位机确认获取Sn/Mac/Mac1成功
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Data">参数</param>
    /// <param name="Id">唯一标识</param>
    /// <returns></returns>
    [HttpPost("ConfirmSN")]
    [RateValve(Policy = Policy.Ip, Limit = 60, Duration = 30)]
    public IActionResult ConfirmSN([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromBody] DeviceSnDto Data)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(ConfirmSN));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }

        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }

        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }

        if (TimeStamp <= 0)
        {
            result.ErrCode = 10003;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }

        if (Data == null)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("参数未上传", Lng);
            return result;
        }

        if (Data.SN.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10005;
            result.Message = GetResource("设备Sn为空", Lng);
            return result;
        }

        if (Data.OrderId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10010;
            result.Message = GetResource("订单号为空", Lng);
            return result;
        }

        if (Data.CheckType < 1 || Data.CheckType > 3)
        {
            result.ErrCode = 10011;
            result.Message = GetResource("查询类型有误", Lng);
            return result;
        }

        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10006;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10007;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10008;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var modelProductOrders = ProductOrders.FindByOrderId(Data.OrderId);

        if (modelProductOrders == null)
        {
            result.ErrCode = 10009;
            result.Message = GetResource($"订单不存在", Lng);
            return result;
        }

        IList<ProductSnMac>? list;
        if (Data.CheckType == 1)  // Sn
        {
            list = ProductSnMac.FindAllOtherBySn(Data.SN, modelProductOrders.StartTime, modelProductOrders.EndTime);
            if (list == null || !list.Any())
            {
                result.ErrCode = 10012;
                result.Message = GetResource("非法设备Sn", Lng);
                return result;
            }
        }
        else if (Data.CheckType == 2)  // 主Mac
        {
            list = ProductSnMac.FindAllOtherByMac(Data.SN, modelProductOrders.StartTime, modelProductOrders.EndTime);
            if (list == null || !list.Any())
            {
                result.ErrCode = 10013;
                result.Message = GetResource("非法设备Sn", Lng);
                return result;
            }
        }
        else
        {
            list = ProductSnMac.FindAllOtherByMac1(Data.SN, modelProductOrders.StartTime, modelProductOrders.EndTime);
            if (list == null || !list.Any())
            {
                result.ErrCode = 10014;
                result.Message = GetResource("非法设备Sn", Lng);
                return result;
            }
        }

        var model = list.FirstOrDefault()!;

        if (model.ConsumedTime <= DateTime.MinValue)
        {
            model.IsConsumed = true;
            model.ConsumedTime = DateTime.Now;
        }

        model.ConfirmConsumedTime = DateTime.Now;
        model.IsConfirmConsumed = true;
        model.Update();

        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 获取Sn/Mac/Mac1剩余数量
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Data">参数</param>
    /// <param name="Id">唯一标识</param>
    /// <returns></returns>
    [HttpPost("GetSnRemaining")]
    [RateValve(Policy = Policy.Ip, Limit = 60, Duration = 30)]
    public IActionResult GetSnRemaining([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromBody] DeviceSnDto Data)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(ConfirmSN));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }

        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }

        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }

        if (TimeStamp <= 0)
        {
            result.ErrCode = 10003;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }

        if (Data == null)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("参数未上传", Lng);
            return result;
        }

        if (Data.OrderId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10010;
            result.Message = GetResource("订单号为空", Lng);
            return result;
        }

        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10006;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10007;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10008;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var modelProductOrders = ProductOrders.FindByOrderId(Data.OrderId);

        if (modelProductOrders == null)
        {
            result.ErrCode = 10009;
            result.Message = GetResource($"订单不存在", Lng);
            return result;
        }

        if (modelProductOrders.Status != 2)
        {
            result.ErrCode = 10010;
            result.Message = GetResource($"只能请求审核通过的订单", Lng);
            return result;
        }

        result.Data = new { RemainingQuantity = ProductSnMac.FindCountByProductOrdersId(modelProductOrders.Id, modelProductOrders.StartTime, modelProductOrders.EndTime), Total = modelProductOrders.Quantity, TotalBCount = modelProductOrders.ProductSupplementalQuantity };
        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 获取产品Sn/Mac/Mac1
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Id">唯一标识</param>
    /// <param name="Data">参数</param>
    /// <returns></returns>
    [HttpPost("GetSnMac")]
    [RateValve(Policy = Policy.Ip, Limit = 60, Duration = 30)]
    public IActionResult GetSnMac([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromBody] DeviceSnDto Data)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(GetSnMac));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }

        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }

        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10003;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }

        if (TimeStamp <= 0)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }

        if (Data == null)
        {
            result.ErrCode = 10005;
            result.Message = GetResource("参数未上传", Lng);
            return result;
        }

        if (Data.OrderId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10006;
            result.Message = GetResource("订单号为空", Lng);
            return result;
        }

        if (Data.CheckType <= 0 && Data.CheckType > 3)
        {
            result.ErrCode = 10007;
            result.Message = GetResource("数据查询类型不对", Lng);
            return result;
        }

        if (Data.CheckType == 1 && Data.SN.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10008;
            result.Message = GetResource("设备Sn不能为空", Lng);
            return result;
        }
        else if (Data.CheckType == 2 && Data.SN.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10009;
            result.Message = GetResource("设备Mac不能为空", Lng);
            return result;
        }
        else if (Data.CheckType == 3 && Data.SN.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10010;
            result.Message = GetResource("设备Mac1不能为空", Lng);
            return result;
        }

        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10011;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10012;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10013;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var modelProductOrders = ProductOrders.FindByOrderId(Data.OrderId);
        if (modelProductOrders == null)
        {
            result.ErrCode = 10014;
            result.Message = GetResource($"订单不存在", Lng);
            return result;
        }

        IList<ProductSnMac>? list = null;

        if (Data.CheckType == 1 && !Data.SN.IsNullOrWhiteSpace())
        {
            Data.SN = Data.SN.Trim();

            list = ProductSnMac.FindAllOtherBySn(Data.SN, modelProductOrders.StartTime, modelProductOrders.EndTime);
            if (list == null || !list.Any())
            {
                result.ErrCode = 10015;
                result.Message = GetResource("非法设备Sn", Lng);
                return result;
            }
        }
        else if (Data.CheckType == 2 && !Data.SN.IsNullOrWhiteSpace())
        {
            Data.SN = MacHelper.NormalizeToColonFormat(Data.SN);

            list = ProductSnMac.FindAllOtherByMac(Data.SN, modelProductOrders.StartTime, modelProductOrders.EndTime);
            if (list == null || !list.Any())
            {
                result.ErrCode = 10016;
                result.Message = GetResource("非法设备Mac", Lng);
                return result;
            }
        }
        else if (Data.CheckType == 3 && !Data.SN.IsNullOrWhiteSpace())
        {
            Data.SN = MacHelper.NormalizeToColonFormat(Data.SN);

            list = ProductSnMac.FindAllOtherByMac1(Data.SN, modelProductOrders.StartTime, modelProductOrders.EndTime);
            if (list == null || !list.Any())
            {
                result.ErrCode = 10017;
                result.Message = GetResource("非法设备Mac1", Lng);
                return result;
            }
        }

        var model = list?.FirstOrDefault()!;

        result.Data = new { model.Sn, model.Mac, model.Mac1 };
        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 获取设备五元组数据。
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Id">唯一标识</param>
    /// <param name="Sn">SN</param>
    /// <returns></returns>
    [HttpPost("GetFiveDeviceInfo")]
    [RateValve(Policy = Policy.Ip, Limit = 60, Duration = 30)]
    public async Task<IActionResult> GetFiveDeviceInfo([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromForm] String Sn)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(GetFiveDeviceInfo));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }

        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }

        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10003;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }

        if (TimeStamp <= 0)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }

        if (Sn.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10005;
            result.Message = GetResource("SN不能为空", Lng);
            return result;
        }

        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10006;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10007;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10008;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var client = new WebClient();
        var res = string.Empty;
#if DEBUG
        res = await client.Post("https://proxy.0ht.cn/api/v1/http2/getfivedeviceinfo")
            .IgnoreSsl()
            .ContentType(HttpContentType.FormUrlEncoded)
            .Header("Id", IdHelper.GetNextId())
            .Header("X-Target-Url", "https://cloud.hlktech.com/")
            .Data("DeviceName", Sn)
            .Retry(3)
            .WhenCatch<HttpRequestException>(ex =>
            {
                return $"请求失败：{ex.StackTrace}";
            })
            .ResultStringAsync();
#else
        res = await client.Post("https://cloud.hlktech.com/api/v1/http2/getfivedeviceinfo")
            .IgnoreSsl()
            .ContentType(HttpContentType.FormUrlEncoded)
            .Header("Id", IdHelper.GetNextId())
            .Header("Lng", Lng)
            .Data("DeviceName", Sn)
            .Retry(3)
            .WhenCatch<HttpRequestException>(ex =>
            {
                return $"请求失败：{ex.StackTrace}";
            })
            .ResultStringAsync();
#endif

        XTrace.WriteLine($"结果：{res}");

        if (res.Contains("请求失败") || res.Contains("false", StringComparison.OrdinalIgnoreCase))
        {
            result.ErrCode = 10009;
            result.Message = GetResource("请求失败", Lng);
            return result;
        }

        var dGResult = res.ToJsonEntity<DGResult>();
        if (dGResult?.Code != StateCode.Ok)
        {
            result.ErrCode = 10010;
            result.Message = GetResource("请求失败", Lng);
            return result;
        }

        result.Data = dGResult.Data;

        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 检查Sn/Mac/Mac1是否已生产
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Id">唯一标识</param>
    /// <param name="Sn">SN</param>
    /// <param name="OrderId">订单号</param>
    /// <param name="CheckType">数据查询类型。1为Sn，2为主Mac，3为副Mac</param>
    /// <returns></returns>
    [HttpPost("CheckHasExists")]
    [RateValve(Policy = Policy.Ip, Limit = 60, Duration = 30)]
    public IActionResult CheckHasExists([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromForm] Int32 CheckType, [FromForm] String Sn, [FromForm] String OrderId)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(CheckHasExists));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }

        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }

        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10003;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }

        if (TimeStamp <= 0)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }

        if (CheckType <= 0 && CheckType > 3)
        {
            result.ErrCode = 10006;
            result.Message = GetResource("数据查询类型不对", Lng);
            return result;
        }

        if (Sn.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10005;
            result.Message = GetResource("SN不能为空", Lng);
            return result;
        }

        if (OrderId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10006;
            result.Message = GetResource("订单号为空", Lng);
            return result;
        }

        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10007;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10008;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10009;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var modelProductOrders = ProductOrders.FindByOrderId(OrderId);
        if (modelProductOrders == null)
        {
            result.ErrCode = 10014;
            result.Message = GetResource($"订单不存在", Lng);
            return result;
        }

        IList<ProductSnMac>? list = null;

        if (CheckType == 1)
        {
            Sn = Sn.Trim();

            list = ProductSnMac.FindAllOtherBySn(Sn, modelProductOrders.StartTime, modelProductOrders.EndTime);
            if (list == null || !list.Any())
            {
                result.ErrCode = 10015;
                result.Message = GetResource("非法设备Sn", Lng);
                return result;
            }
        }
        else if (CheckType == 2)
        {
            Sn = MacHelper.NormalizeToColonFormat(Sn);

            list = ProductSnMac.FindAllOtherByMac(Sn, modelProductOrders.StartTime, modelProductOrders.EndTime);
            if (list == null || !list.Any())
            {
                result.ErrCode = 10016;
                result.Message = GetResource("非法设备Mac", Lng);
                return result;
            }
        }
        else if (CheckType == 3)
        {
            Sn = MacHelper.NormalizeToColonFormat(Sn);

            list = ProductSnMac.FindAllOtherByMac1(Sn, modelProductOrders.StartTime, modelProductOrders.EndTime);
            if (list == null || !list.Any())
            {
                result.ErrCode = 10017;
                result.Message = GetResource("非法设备Mac1", Lng);
                return result;
            }
        }

        var model = list?.FirstOrDefault()!;

        if (model.IsValidate && (!model.IsRepetition || model.IsRepetitionDate <= DateTime.Now))
        {
            result.ErrCode = 10018;
            result.Message = GetResource("已生产", Lng);
            return result;
        }

        if (model.IsValidate && (!model.IsRepetition || model.IsRepetitionDate <= DateTime.Now))
        {
            result.Data = new { Exist = true };
        }
        else
        {
            result.Data = new { Exist = false };
        }

        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 根据Sn获取订单信息
    /// </summary>
    /// <returns></returns>
    [HttpPost("GetOrderBySn")]
    [RateValve(Policy = Policy.Ip, Limit = 60, Duration = 30)]
    public IActionResult GetOrderBySn([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id,  [FromForm] String Sn)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(GetOrderBySn));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }

        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }

        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10003;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }

        if (TimeStamp <= 0)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }

        if (Sn.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10005;
            result.Message = GetResource("SN不能为空", Lng);
            return result;
        }

        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10006;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10007;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10008;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var modelDeviceSns = DeviceSns.FindBySn(Sn);
        if (modelDeviceSns == null)
        {
            result.ErrCode = 10009;
            result.Message = GetResource($"SN不存在", Lng);
            return result;
        }

        var modelProductOrders = ProductOrders.FindByOrderId(modelDeviceSns.OrderId);
        if (modelProductOrders == null)
        {
            result.ErrCode = 10010;
            result.Message = GetResource($"订单不存在", Lng);
            return result;
        }

        var modelProductProject = ProductProject.FindById(modelProductOrders.ProductProjectId);
        if (modelProductProject == null)
        {
            result.ErrCode = 10011;
            result.Message = GetResource($"项目不存在", Lng);
            return result;
        }
        var listProductProjectData = ProductProjectData.FindAllByProductProjectId(modelProductProject.Id);
        result.Data = new
        {
            OrderId = modelDeviceSns.OrderId,
            Firmware = listProductProjectData.FirstOrDefault(e=> e.ProductData !=  null && e.ProductData.Ptype == 2)?.ProductData?.FilePath,
            FirmwareVersion = listProductProjectData.FirstOrDefault(e=> e.ProductData !=  null && e.ProductData.Ptype == 2)?.ProductData?.Version,
            TagTemplate = listProductProjectData.FirstOrDefault(e=> e.ProductData !=  null && e.ProductData.Ptype == 3)?.ProductData?.FilePath,
        };
        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 处理设备消息的共用方法，包含双缓存检查和数据库更新
    /// </summary>
    /// <param name="mqMsg">设备消息</param>
    /// <param name="processingKey">正在处理的缓存键前缀</param>
    /// <param name="completedKey">已完成处理的缓存键前缀</param>
    /// <param name="result">结果对象</param>
    /// <param name="Lng">语言项</param>
    /// <returns>处理成功返回true，失败返回false</returns>
    private Boolean ProcessDeviceMessage(DeviceSnReponseDto mqMsg, String processingKey, String completedKey, DGResult result, String? Lng)
    {
        var processingCacheKey = processingKey + mqMsg.Id;
        var completedCacheKey = completedKey + mqMsg.Id;

        // 使用Redis的原子操作来设置正在处理状态
        var setResult = _cacheProvider.Cache.Add(processingCacheKey, "1", 300); // 5分钟过期
        if (!setResult)
        {
            // 如果设置失败，说明其他线程正在处理这个ID
            result.ErrCode = 10013;
            result.Message = $"{mqMsg.Sn}/{mqMsg.Mac}/{mqMsg.Mac1}" + GetResource("设备Sn正在处理中", Lng);
            return false;
        }

        try
        {
            // 检查是否已经完成处理
            if (_cacheProvider.Cache.ContainsKey(completedCacheKey))
            {
                result.ErrCode = 10014;
                result.Message = $"{mqMsg.Sn}/{mqMsg.Mac}/{mqMsg.Mac1}" + GetResource("设备Sn已被使用", Lng);
                return false;
            }

            var model = ProductSnMac.FindById(mqMsg.Id);
            if (model == null)
            {
                result.ErrCode = 10010;
                result.Message = $"{mqMsg.Sn}/{mqMsg.Mac}/{mqMsg.Mac1}" + GetResource("设备Sn不存在", Lng);
                return false;
            }

            if (model.IsConsumed)
            {
                XTrace.WriteLine($"设备Sn已被消费：{mqMsg.Sn}/{mqMsg.Mac}/{mqMsg.Mac1}");

                result.ErrCode = 10012;
                result.Message = $"{mqMsg.Sn}/{mqMsg.Mac}/{mqMsg.Mac1}" + GetResource("设备Sn已被消费", Lng);
                return false;
            }

            // 更新数据库
            model.IsConsumed = true;
            model.ConsumedTime = DateTime.Now;
            model.Update();

            // 成功更新数据库后，设置已完成缓存（双缓存第二层）
            _cacheProvider.Cache.Set(completedCacheKey, "1", 86400); // 24小时过期，防止重复使用
            return true;
        }
        finally
        {
            // 无论成功失败，都要清除正在处理状态
            _cacheProvider.Cache.Remove(processingCacheKey);
        }
    }
}
