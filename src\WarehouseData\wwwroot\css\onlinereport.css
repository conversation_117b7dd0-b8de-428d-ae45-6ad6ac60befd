﻿body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    background-color: #f5f5f5;
}

.pdf {
    width: 100%;
    max-width: 1200px;
    margin: 1.2vw auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    overflow: auto;
}

#pdfCanvas {
    max-width: 100%;
    height: auto;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    display: block;
    position: relative;
    margin-top: 5vh;
}

.pdf-links {
    margin-top: 34px;
    display: flex;
    flex-direction: row;
    gap: .75vw;
    justify-content: center;
    width: auto;
    position: absolute;
    top: 20px;
    right: 164px;
    transform: none;
}

.pdf-link {
    padding: 8px 15px;
    background-color: #548ce6;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    transition: background-color 0.3s;
    border: none;
    white-space: nowrap;
}

.pdf-link:hover {
    background-color: #3a6fc7;
}

/* Loading动画样式 */
.loading-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #548ce6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading-text {
    position: absolute;
    margin-top: 4.8vh;
    color: #548ce6;
    font-size: 16px;
}

a {
    border: 1px solid #df4747;
    background-color: #548ce6;
}

/* 大屏适配 */
@media screen and (max-width: 1200px) {
    .pdf-links {
        right: 13vw;

    }
}
/* 中屏适配 */
@media screen and (max-width: 992px) {
    .pdf-links {
        right: 5vw;

    }
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
    .pdf {
        margin: .5vw auto;
        width: 100%;
    }

    #pdfCanvas {
        width: 100%;
        margin-top: 6vh;
    }

    .pdf-links {
        margin-top: -12px;
        gap: 10px;
        right: 4vw;
    }

    .pdf-link {
        padding: 6px 12px;
        font-size: 12px;
    }
}