﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>库存告警</summary>
public partial class InventoryAlarmModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>物料编号</summary>
    public String MaterialNumber { get; set; } = null!;

    /// <summary>物料名称</summary>
    public String? MaterialName { get; set; }

    /// <summary>种类</summary>
    public String? Category { get; set; }

    /// <summary>规格型号</summary>
    public String SpecificationModel { get; set; } = null!;

    /// <summary>单位</summary>
    public String? Unit { get; set; }

    /// <summary>仓库编号</summary>
    public String WarehouseNumber { get; set; } = null!;

    /// <summary>仓库名称</summary>
    public String? WarehouseName { get; set; }

    /// <summary>告警数量</summary>
    public Int32 AlarmQuantity { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IInventoryAlarm model)
    {
        Id = model.Id;
        MaterialNumber = model.MaterialNumber;
        MaterialName = model.MaterialName;
        Category = model.Category;
        SpecificationModel = model.SpecificationModel;
        Unit = model.Unit;
        WarehouseNumber = model.WarehouseNumber;
        WarehouseName = model.WarehouseName;
        AlarmQuantity = model.AlarmQuantity;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
