﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>产品项目</summary>
public partial interface IProductProject
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>项目名称</summary>
    String Name { get; set; }

    /// <summary>产品型号Id</summary>
    Int32 ProductTypeId { get; set; }

    /// <summary>状态</summary>
    Boolean Status { get; set; }

    /// <summary>资料类别编号集合</summary>
    String? DataCategoryIds { get; set; }

    /// <summary>项目编号</summary>
    String? ProjectNo { get; set; }

    /// <summary>备注</summary>
    String? Remark { get; set; }

    /// <summary>审核状态 0待审核 1审核中 2审核成功 3审核失败</summary>
    Int32 AuditStatus { get; set; }

    /// <summary>是否反审 0默认 1申请反审 2确认反审</summary>
    Int32 ReAudit { get; set; }

    /// <summary>反审原因</summary>
    String? ReAuditRemark { get; set; }

    /// <summary>提交状态 0未提交 1已提交审核</summary>
    Int32 SubmitStatus { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
