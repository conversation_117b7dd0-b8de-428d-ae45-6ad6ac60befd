﻿@{
    Html.AppendTitleParts(T("添加电源型号配置项目录").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
    }

</style>
<div class="containers">
    <form class="layui-form">

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("名称")</label>
            <div class="layui-input-inline" style="width: 320px;">
                 <input type="text" name="Name" placeholder="@T("请输入名称")" autocomplete="off" class="layui-input" value="@Model.Name">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("备注")</label>
            <div class="layui-input-inline" style="width: 320px;">
                 <textarea class="layui-textarea" placeholder="@T("请输入备注")" name="Remark">@Model.Remark</textarea>
            </div>
        </div>

        <div class="layui-form-item btn">
            <input hidden name="Id" value="@Model.Id"/>
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var upload = layui.upload;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        var parentList = window.parent
        var parentPage = null;

        // 第二步：拿到对应层
        for (let i = 0; i < parentList.length; i++) {
            if (parentList[i].name === 'ItemsDirectory') { //这里的name自己去对应层DIY
                parentPage = parentList[i]
                break;
            }
        }
        // 第三步：操作对应层
        var parent_window = parentPage.window  //获取父层的window层
        var parent_layer = parentPage.layer //获取父层的layer
        var parent_notify = parentPage.layui.abp.notify //获取父层的layui.notify --消息通知
        var parent_layui  = parentPage.layui  //获取父层的layui
        var currentPageCloseIndex = parent_window.editPageIndex //当前层的关闭index下标

        form.on('submit(Submit)', function (data) {

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("EditItemsDirectory")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                // 关闭当前编辑页面
                parent.layer.close(currentPageCloseIndex);
                parent_notify.success(data.msg);
                parentPage.active.reload();
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>