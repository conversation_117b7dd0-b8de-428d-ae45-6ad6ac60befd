﻿using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

namespace HlktechIoT.Dto.Export;

[ExcelExporter(Name = "Sheet1", AutoFitAllColumn = true, TableStyle = OfficeOpenXml.Table.TableStyles.None, AutoCenter = true)]
public class OrderExport {
    /// <summary> 
    /// ERP订单号
    /// </summary> 
    [ExporterHeader(DisplayName = "ERP订单号", IsBold = false)]
    public string? OrderId { get; set; }

    /// <summary> 
    /// 打单人
    /// </summary> 
    [ExporterHeader(DisplayName = "打单人", IsBold = false)]
    public string? OrderingUser { get; set; }

    /// <summary> 
    /// 打单时间
    /// </summary> 
    [ExporterHeader(DisplayName = "打单时间", IsBold = false, Format = "yyyy-MM-dd HH:mm:ss", Width = 20)]
    public DateTime? OrderingTime { get; set; }

    /// <summary> 
    /// 领料人
    /// </summary> 
    [ExporterHeader(DisplayName = "领料人", IsBold = false)]
    public string? PickingUser { get; set; }

    /// <summary> 
    /// 领料时间
    /// </summary> 
    [ExporterHeader(DisplayName = "领料时间", IsBold = false, Format = "yyyy-MM-dd HH:mm:ss", Width = 20)]
    public DateTime? PickingTime { get; set; }

    /// <summary> 
    /// 生产人
    /// </summary> 
    [ExporterHeader(DisplayName = "生产人", IsBold = false)]
    public string? ProductionUser { get; set; }

    /// <summary> 
    /// 生产时间
    /// </summary> 
    [ExporterHeader(DisplayName = "生产时间", IsBold = false, Format = "yyyy-MM-dd HH:mm:ss", Width = 20)]
    public DateTime? ProductionTime { get; set; }

    /// <summary> 
    /// 审核人
    /// </summary> 
    [ExporterHeader(DisplayName = "审核人", IsBold = false)]
    public string? AuditingUser { get; set; }

    /// <summary> 
    /// 审核时间
    /// </summary> 
    [ExporterHeader(DisplayName = "审核时间", IsBold = false, Format = "yyyy-MM-dd HH:mm:ss", Width = 20)]
    public DateTime? AuditingTime { get; set; }

    /// <summary> 
    /// 打包人
    /// </summary> 
    [ExporterHeader(DisplayName = "打包人", IsBold = false)]
    public string? PackUser { get; set; }

    /// <summary> 
    /// 打包时间
    /// </summary> 
    [ExporterHeader(DisplayName = "打包时间", IsBold = false, Format = "yyyy-MM-dd HH:mm:ss", Width = 20)]
    public DateTime? PackTime { get; set; }

    /// <summary> 
    /// 订单状态
    /// </summary> 
    [ExporterHeader(DisplayName = "订单状态", IsBold = false)]
    public string? SpecificState { get; set; }

    /// <summary> 
    /// 备注
    /// </summary> 
    [ExporterHeader(DisplayName = "备注", IsBold = false)]
    public string? Remark { get; set; }
}
