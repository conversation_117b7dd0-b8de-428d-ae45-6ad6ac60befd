"use strict";var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};layui.define(["jquery","layer","laypage"],function(t){var e=layui.jquery,i=layui.layer,a=layui.laypage,n={config:{test:"test",thumb:{nopic:"",width:100,height:100},icon_url:"ico/",btn_upload:!0,btn_create:!0},cache:{},index:layui.fm?layui.fm.index+1e4:0,set:function(t){var i=this;return i.config=e.extend({},i.config,t),i},on:function(t,e){return layui.onevent.call(this,"fileManager",t,e)},dirRoot:[{path:"",name:"根目录"}],v:"1.0.1.2019.12.26"},o=function t(){var e=this,i=e.config,a=i.id||i.index;return a&&(t.that[a]=e,t.config[a]=i),{config:i,reload:function(t){e.reload.call(e,t)}}},l=function(t){var e=o.config[t];return e||hint.error("The ID option was not found in the fm instance"),e||null},r=function(t){var i=this;i.config=e.extend({},i.config,n.config,t),o.that={},o.config={},i.render()};r.prototype.render=function(){var t=this,i=t.config;if(i.elem=e(i.elem),i.where=i.where||{},i.id=i.id||i.elem.attr("id")||t.index,i.request=e.extend({pageName:"page",limitName:"limit"},i.request),i.response=e.extend({statusName:"code",statusCode:0,msgName:"msg",dataName:"data",countName:"count"},i.response),"object"===_typeof(i.page)&&(i.limit=i.page.limit||i.limit,i.limits=i.page.limits||i.limits,t.page=i.page.curr=i.page.curr||1,delete i.page.elem,delete i.page.jump),!i.elem[0])return t;var a="";i.btn_create&&(a+='<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" id="new_dir">建文件夹</button>'),i.btn_upload&&(a+='<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" id="uploadfile">上传文件</button>');var n='<div class="layui-card" ><div class="layui-card-body"><div class="layui-btn-group tool_bar">'+a+'<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" id="back"><i class="layui-icon layui-icon-left line"></i></button></div><div class="layui-inline path_bar" id=""><a ><i class="layui-icon layui-icon-more-vertical line" ></i>根目录</a></div></div><hr><div class="layui-card-body"><div class="file-body layui-form" style=""><ul class="fileManager layui-row fm_body layui-col-space10" ></ul></div><hr><div ><div class="layui_page_'+i.id+'" id="layui_page_'+i.id+'"></div></div></div>';i.elem.html(n),i.index=t.index,t.key=i.id||i.index,t.layPage=i.elem.find(".layui_page_"+i.id),t.layBody=i.elem.find(".fm_body"),t.layPathBar=i.elem.find(".path_bar"),t.layToolBar=i.elem.find(".tool_bar"),t.pullData(t.page),t.events()},r.prototype.page=1,r.prototype.pullData=function(t){var i=this,a=i.config,n=a.request,o=a.response,l=!1;if(i.startTime=(new Date).getTime(),a.url){var r={};r[n.pageName]=t,r[n.limitName]=a.limit;var c=e.extend(r,a.where);a.contentType&&0==a.contentType.indexOf("application/json")&&(c=JSON.stringify(c)),i.loading(),e.ajax({type:a.method||"get",url:a.url,contentType:a.contentType,data:c,async:!1,dataType:"json",headers:a.headers||{},success:function(e){"function"==typeof a.parseData&&(e=a.parseData(e)||e),e[o.statusName]!=o.statusCode?i.errorView(e[o.msgName]||'返回的数据不符合规范，正确的成功状态码应为："'+o.statusName+'": '+o.statusCode):(i.renderData(e,t,e[o.countName]),a.time=(new Date).getTime()-i.startTime+" ms"),"function"==typeof a.done&&a.done(e,t,e[o.countName]),l=!0},error:function(t,e){i.errorView("数据接口请求异常："+e)}})}return l},r.prototype.renderData=function(t,i,o){var l=this,r=l.config,c=t[r.response.dataName]||[],u="";if(layui.each(c,function(t,e){var i=void 0,a=void 0;switch(a=e.type,e.type){case"directory":i='<div  style="width:'+r.thumb.width+"px;height:"+r.thumb.height+"px;line-height:"+r.thumb.height+'px"><img src="ico/dir.png" style="vertical-align:middle;"></div>',a="DIR";break;default:i="png"==e.type||"gif"==e.type||"jpg"==e.type||"image"==e.type?'<img src="'+e.thumb+'" width="'+r.thumb.width+'" height="'+r.thumb.height+'" onerror=\'this.src="'+r.thumb.nopic+"\"'  />":'<div  style="width:'+r.thumb.width+"px;height:"+r.thumb.height+"px;line-height:"+r.thumb.height+'px"><img src="'+r.icon_url+e.type+'.png"  onerror=\'this.src="'+r.thumb.nopic+"\"' /></div>"}u+='<li style="display:inline-block" data-type="'+a+'" data-index="'+t+'"><div class="content" align="center">'+i+'<p class="layui-elip" title="'+e.name+'">'+e.name+" </p></div></li>"}),r.elem.find(".fileManager").html(u),n.cache[r.id]=c,l.layPage[0==o||0===c.length&&1==i?"addClass":"removeClass"]("layui-hide"),0===c.length)return l.errorView("空目录");r.page&&(r.page=e.extend({elem:"layui_page_"+r.id,count:o,limit:r.limit,limits:r.limits||[10,20,30,40,50,60,70,80,90],groups:3,layout:["prev","page","next","skip","count","limit"],prev:'<i class="layui-icon">&#xe603;</i>',next:'<i class="layui-icon">&#xe602;</i>',jump:function(t,e){e||(l.page=t.curr,r.limit=t.limit,l.pullData(t.curr))}},r.page),r.page.count=o,a.render(r.page))},r.prototype.updatePathBar=function(){var t=this,e=t.config,i=n.dirRoot[n.dirRoot.length-1];e.where={path:i.path},0!=t.pullData(1)&&(t.layPathBar.html(""),n.dirRoot.map(function(e,i,a){var n=0==i?"layui-icon-more-vertical":"layui-icon-right",o='<i class="layui-icon '+n+'"></i><a  data-path="'+e.path+'" data-name="'+e.name+'" >'+e.name+"</a>";t.layPathBar.append(o)}))},r.prototype.events=function(){var t=this,a=t.config,o=(e("body"),a.elem.attr("lay-filter"));t.layBody.on("click","li",function(){l.call(this,"pic")}),t.layBody.on("click","li[data-type=DIR]",function(){var i=e(this),o=n.cache[a.id];o=o[i.data("index")]||{},n.dirRoot.push({path:o.path,name:o.name}),t.updatePathBar()}),t.layToolBar.on("click","#back",function(){e(this);if(1==n.dirRoot.length)return i.msg("已经是根目录");n.dirRoot.length>1&&n.dirRoot.pop(),t.updatePathBar()}),t.layToolBar.on("click","#uploadfile",function(){var t=e(this);layui.event.call(this,"fileManager","uploadfile("+o+")",{obj:t,path:n.dirRoot[n.dirRoot.length-1].path})}),t.layToolBar.on("click","#new_dir",function(){var t=e(this);i.prompt({title:"请输入新文件夹名字",formType:0},function(e,a){i.close(a),layui.event.call(this,"fileManager","new_dir("+o+")",{obj:t,folder:e,path:n.dirRoot[n.dirRoot.length-1].path})})});var l=function(t){var i=e(this),l=n.cache[a.id],r=i.data("index");"DIR"!=i.data("type")&&(l=l[r]||{},layui.event.call(this,"fileManager",t+"("+o+")",{obj:i,data:l}))}},r.prototype.loading=function(t){var i=this;i.config.loading&&(t?(i.layInit&&i.layInit.remove(),delete i.layInit,i.layBox.find(ELEM_INIT).remove()):(i.layInit=e(['<div class="layui-table-init">','<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>',"</div>"].join("")),i.layBox.append(i.layInit)))},r.prototype.errorView=function(t){i.msg(t)},r.prototype.reload=function(t){var i=this;t=t||{},delete i.haveInit,t.data&&t.data.constructor===Array&&delete i.config.data,i.config=e.extend(!0,{},i.config,t),i.render()},n.reload=function(t,e){if(l(t)){var i=o.that[t];return i.reload(e),o.call(i)}},n.render=function(t){var e=new r(t);return o.call(e)},t("fileManager",n)});