﻿using DH.Npoi.Attributes;

namespace HlktechIoT.Dto.Export
{
    [Sheet(SheetName = "库存告警导入", AutoColumnWidthEnabled = true)]
    public class InventoryAlarmImport
    {
        /// <summary>
        /// 物料编号
        /// </summary>
        [Column(Title = "物料编号")]
        public string? MaterialNumber { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        [Column(Title = "物料名称")]
        public string? MaterialName { get; set; }

        /// <summary>
        /// 种类
        /// </summary>
        [Column(Title = "种类")]
        public string? Category { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        [Column(Title = "规格型号")]
        public string? SpecificationModel { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Column(Title = "单位")]
        public string? Unit { get; set; }

        /// <summary>
        /// 仓库编号
        /// </summary>
        [Column(Title = "仓库编号")]
        public string? WarehouseNumber { get; set; }

        /// <summary>
        /// 仓库名称
        /// </summary>
        [Column(Title = "仓库名称")]
        public string? WarehouseName { get; set; }

        /// <summary>
        /// 库存数量
        /// </summary>
        [Column(Title = "库存数量")]
        public int InventoryQuantity { get; set; }

        /// <summary>
        /// 占用数(含出货通知)
        /// </summary>
        [Column(Title = "占用数(含出货通知)")]
        public int OccupiedQuantityWithNotification { get; set; }

        /// <summary>
        /// 占用数(不含通知单)
        /// </summary>
        [Column(Title = "占用数(不含通知单)")]
        public int OccupiedQuantityWithoutNotification { get; set; }

        /// <summary>
        /// 可用数
        /// </summary>
        [Column(Title = "可用数")]
        public int AvailableQuantity { get; set; }

        /// <summary>
        /// 仓位
        /// </summary>
        [Column(Title = "仓位")]
        public string? Location { get; set; }

        /// <summary>
        /// 含税平均成本单价
        /// </summary>
        [Column(Title = "含税平均成本单价")]
        public decimal TaxIncludedAverageCost { get; set; }

        /// <summary>
        /// 库存批次首次入库日期
        /// </summary>
        [Column(Title = "库存批次首次入库日期")]
        public DateTime? FirstStockInDate { get; set; }

        /// <summary>
        /// 最新入库日期
        /// </summary>
        [Column(Title = "最新入库日期")]
        public DateTime LatestStockInDate { get; set; }

        /// <summary>
        /// 最近异动日期
        /// </summary>
        [Column(Title = "最近异动日期")]
        public DateTime LatestChangeDate { get; set; }

        /// <summary>
        /// 采购员
        /// </summary>
        [Column(Title = "采购员")]
        public string? Purchaser { get; set; }
    }
}
