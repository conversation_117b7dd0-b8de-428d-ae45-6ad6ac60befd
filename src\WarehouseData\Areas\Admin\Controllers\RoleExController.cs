﻿using DG.Web.Framework;

using DH.Entity;

using HlktechIoT.Data;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek.Models;

using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>
/// 权限扩展列表
/// </summary>
[DisplayName("权限扩展管理")]
[Description("权限扩展管理列表")]
[AdminArea]
[DHMenu(70,ParentMenuName = "System", CurrentMenuUrl = "~/{area}/RoleEx", CurrentMenuName = "RoleEx", CurrentVisible = false, LastUpdate = "20240124")]
public class RoleExController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 70;

    /// <summary>
    /// 权限扩展列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("权限扩展列表")]
    public IActionResult Index(Int32 Id)
    {
        ViewBag.Id = Id;
        return View();
    }

    /// <summary>
    /// 获取扩展权限列表
    /// </summary>
    /// <param name="name"></param>
    /// <param name="roleId"></param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("获取扩展权限列表")]
    public IActionResult GetList(String name, Int32 roleId, Int32 page = 1, Int32 limit = 100)
    {
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true
        };
        RoleEx roleEx = RoleEx.FindById(roleId);

        if (roleEx == null || roleEx?.Roles.IsNullOrWhiteSpace() == true)
        {
            return Json(new { code = 0, msg = "success", count = 0, data = new List<RoleExItem>().Select(item => new { item.Id, item.Name, item.Code,item.Remark }) });
        }

        var list = RoleExItem.Search(pages, name, roleEx?.Roles).Select(item => new { item.Id, item.Name, item.Code,item.Remark });
        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data = list });
    }

    /// <summary>
    /// 选择权限
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("选择权限")]
    public IActionResult SelectRole(Int32 Id)
    {
        ViewBag.Id = Id;
        return View();
    }

    /// <summary>
    /// 搜索权限扩展项
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("搜索权限扩展项")]
    [HttpPost]
    public IActionResult SearchRoleItem(String keyword, Int32 page, Int32 Id)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
        };

        res.data = RoleExItem.Search(keyword, pages).Select(e =>
        {
            var selected = false;

            return new Xmselect<String>
            {
                name = e.Name+$"({e.Code})",
                value = e.Code!,
                selected = selected
            };
        });

        res.extdata = pages.PageCount;
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 选择权限
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("选择权限")]
    [HttpPost]
    public IActionResult SelectRole(String Select, Int32 Id)
    {
        var res = new DResult();

        if (Select.IsNullOrWhiteSpace())
        {
            return Json(new { success = false, msg = GetResource("权限编号不能为空") });
        }

        // 将传入字符串用逗号分割，并移除空白、重复
        var selects = Select
            .Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(s => s.Trim())
            .Where(s => !string.IsNullOrWhiteSpace(s))
            .Distinct()
            .ToList();

        var modelRoleEx = RoleEx.FindById(Id);
        if (modelRoleEx == null)
        {
            modelRoleEx = new RoleEx
            {
                Id = Id,
                Roles = $",{string.Join(",", selects)},"
            };
            modelRoleEx.Insert();
        }
        else
        {
            // 取出现有角色扩展权限
            var list = modelRoleEx.Roles?
                .Trim(',')
                .Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(s => s.Trim())
                .ToList() ?? new List<string>();

            // 如果任意一个要添加的角色扩展权限在现有列表中，则提示已存在
            if (list.Intersect(selects).Any())
            {
                return Json(new { success = false, msg = GetResource("权限已存在") });
            }

            // 将新的角色扩展权限全部添加并去重
            list.AddRange(selects);
            list = list.Distinct().ToList();
            modelRoleEx.Roles = $",{string.Join(",", list)},";

            modelRoleEx.Update();
        }

        res.success = true;
        res.msg = GetResource("选择成功");
        return Json(res);
    }

    /// <summary>
    /// 新增
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("新增")]
    public IActionResult Add(Int32 Id)
    {
        ViewBag.Id = Id;
        return View();
    }

    /// <summary>
    /// 新增
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("新增")]
    [HttpPost]
    public IActionResult Add(String Name, String Code, Int32 Id,String Remark)
    {
        var res = new DResult();

        if (Name.IsNullOrWhiteSpace())
        {
            return Json(new { success = false, msg = GetResource("权限名称不能为空") });
        }

        if (Code.IsNullOrWhiteSpace())
        {
            return Json(new { success = false, msg = GetResource("权限编号不能为空") });
        }

        if (RoleExItem.FindByName(Name) != null)
        {
            return Json(new { success = false, msg = GetResource("权限名称已存在") });
        }

        if (RoleExItem.FindByCode(Code) != null)
        {
            return Json(new { success = false, msg = GetResource("权限编号已存在") });
        }

        var model = new RoleExItem();
        model.Name = Name;
        model.Code = Code;
        model.Remark = Remark;
        model.Insert();

        var modelRoleEx = RoleEx.FindById(Id);
        if (modelRoleEx == null)
        {
            modelRoleEx = new RoleEx();
            modelRoleEx.Id = Id;
            modelRoleEx.Roles = $",{model.Code},";
            modelRoleEx.Insert();
        }
        else
        {
            var list = modelRoleEx.Roles?.Trim(',').Split(',').ToList();
            list ??= [];
            list.Add(model.Code);

            modelRoleEx.Roles = $",{list.Join()},";
            modelRoleEx.Update();
        }

        res.success = true;
        res.msg = GetResource("新增成功");
        return Json(res);
    }

    /// <summary>
    /// 删除
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除")]
    [HttpPost]
    public IActionResult Delete(String Code, Int32 Id)
    {
        var res = new DResult();

        var modelRoleEx = RoleEx.FindById(Id);
        if (modelRoleEx == null)
        {
            modelRoleEx = new RoleEx();
            modelRoleEx.Id = Id;
            modelRoleEx.Insert();
        }
        else
        {
            var list = modelRoleEx.Roles?
                .Trim(',')
                .Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(s => s.Trim())
                .ToList() ?? new List<string>();

            // 移除所有重复出现的 Code
            list.RemoveAll(x => x.Equals(Code, StringComparison.OrdinalIgnoreCase));

            modelRoleEx.Roles = list.Count > 0
                ? $",{string.Join(",", list)},"
                : string.Empty;

            modelRoleEx.Update();
        }

        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }
}
