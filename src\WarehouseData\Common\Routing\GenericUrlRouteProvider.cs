﻿using DG.Web.Framework.Routing;

using DH;
using DH.Core.Domain.Localization;

using DH.Core.Infrastructure;
using DH.Entity;

namespace HlktechIoT.Common.Routing;

/// <summary>
/// 代表提供一般路由的供应商
/// </summary>
public partial class GenericUrlRouteProvider : IRouteProvider {
    #region 方法

    /// <summary>
    /// 注册路由
    /// </summary>
    /// <param name="endpoints">路由构造器</param>
    public void RegisterRoutes(IEndpointRouteBuilder endpoints)
    {
        var pattern = "{SeName}";
        endpoints.MapDynamicControllerRoute<SlugRouteTransformer>("{SeName}");

        var localizationSettings = LocalizationSettings.Current;

        var pattern1 = String.Empty;
        if (DHSetting.Current.IsInstalled)
        {
            if (localizationSettings.SeoFriendlyUrlsForLanguagesEnabled && localizationSettings.IsEnable)
            {
                var languages = Language.FindByDefault();
                pattern = "{language:lang=" + languages.UniqueSeoCode + "}/{SeName}";
                pattern1 = "{language:lang=" + languages.UniqueSeoCode + "}/";

                endpoints.MapDynamicControllerRoute<SlugRouteTransformer>(pattern);
            }
        }

        // 默认路由
        endpoints.MapControllerRoute(
                "Default",
                "{controller=CubeHome}/{action=Index}/{id?}"
                );
        if (localizationSettings.IsEnable) // 是否启用多语言
        {
            endpoints.MapControllerRoute(
                "MuliDefault",
                pattern1 + "{controller=CubeHome}/{action=Index}/{id?}"
                );
        }

        endpoints.MapControllerRoute(
                name: "api",
                pattern: "{controller}/{id?}");

        if (!DHSetting.Current.IsSpa)
        {
            // 通用网址
            endpoints.MapControllerRoute(
                name: "GenericUrl",
                pattern: "{GenericSeName}",
                new { controller = "Common", action = "GenericUrl" });
            if (localizationSettings.IsEnable) // 是否启用多语言
            {
                endpoints.MapControllerRoute(
                name: "GenericUrl",
                pattern: pattern1 + "{GenericSeName}",
                new { controller = "Common", action = "GenericUrl" });
            }
        }

        endpoints.MapControllerRoute("Product", pattern, new { controller = "UserAgreement", action = "Index" });
    }

    #endregion

    #region 属性

    /// <summary>
    /// 获取路由提供者的优先级
    /// </summary>
    public int Priority
    {
        // 它应该是最后一条路由。 我们没有将其设置为-int.MaxValue，因此可以将其覆盖（如果需要）
        get { return -999999; }
    }

    #endregion
}