﻿@using Pek.Configs
@{
    Html.AppendTitleParts(T("编辑产品型号问题反馈").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }

    .layui-upload-list {
        width: 226px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 10px;

    }

    .layui-elem-quote {
        border-left: 5px solid #fff !important;
        border-left: 1px solid #eee !important;
    }

    /* 图片预览容器样式 */
    .image-preview-item {
        position: relative;
        display: inline-block;
        margin: 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
        width: 100px;
        height: 100px;
    }

    .image-preview-item img {
        width: 100%;
        height: 100px;
        object-fit: contain;
        cursor: pointer;
        transition: transform 0.2s;
    }

    .image-preview-item img:hover {
        transform: scale(1.05);
    }

    /* 图片操作按钮容器 */
    .image-actions {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.2s;
        /* 确保操作按钮层在最上方 */
        z-index: 10;
        @* opacity: 1; *@
    }

    .image-preview-item:hover .image-actions {
        opacity: 1;
    }

    /* 操作按钮样式 */
    .image-actions i {
        font-size: 30px !important;
        color: #fff !important;
        font-weight: 300;
    }


    /* 全屏预览样式 */
    .fullscreen-preview {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 99999;
        display: none;
        align-items: center;
        justify-content: space-evenly;
    }

    .fullscreen-preview img {
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
    }

    .fullscreen-close {
        position: absolute;
        top: 20px;
        right: 30px;
        color: white;
        font-size: 30px;
        cursor: pointer;
        z-index: 100000;
    }

    .fullscreen-close:hover {
        color: #ccc;
    }

    /* 视频预览容器样式 */
    .video-preview-item {
        position: relative;
        display: inline-block;
        margin: 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
        width: 100px;
        height: 100px;
    }

    .video-preview-item video {
        width: 100%;
        height: 100%;
        object-fit: contain;
        cursor: pointer;
        transition: transform 0.2s;
    }

    .video-preview-item video:hover {
        transform: scale(1.05);
    }

    /* 视频操作按钮容器 */
    .video-actions {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.2s;
        z-index: 10;
    }

    .video-preview-item:hover .video-actions {
        opacity: 1;
    }

    /* 视频操作按钮样式 */
    .video-actions i {
        font-size: 30px !important;
        color: #fff !important;
        font-weight: 300;
    }

    /* 全屏视频预览样式 */
    .fullscreen-preview video {
        width: 100%;
        height: 50%;
        object-fit: contain;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
<div class="containers">
    <form class="layui-form">

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("问题标题")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Name" autocomplete="off" class="layui-input" lay-filter="Versions" value="@Model.Name">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("问题内容")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Content" autocomplete="off" class="layui-input" lay-filter="Versions" value="@Model.Content">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("问题类型")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <select name="DType">
                    <!option value="0" @(Model.DType == 0 ? "selected":"")>@T("待分配")</!option>
                    <!option value="1" @(Model.DType == 1 ? "selected":"")>@T("软件")</!option>
                    <!option value="2" @(Model.DType == 2 ? "selected":"")>@T("硬件")</!option>
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">
                @T("图片")
            </label>
            <div class="layui-input-inline">
                <div class="layui-upload-drag" id="imgUpload">
                    <button type="button" class="layui-btn" id="ID-upload-demo-btn-2">
                        @T("上传图片")
                    </button>
                    <blockquote class="layui-elem-quote layui-quote-nm" style="margin-top: 11px;">
                        @T("预览")：
                        <div class="layui-upload-list" id="upload-demo-preview1"></div>
                    </blockquote>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">
                @T("视频")
            </label>
            <div class="layui-input-inline">
                <div class="layui-upload-drag" id="videoUpload">
                    <button type="button" class="layui-btn" id="ID-upload-demo-btn-2">
                        @T("上传视频")
                    </button>
                    <blockquote class="layui-elem-quote layui-quote-nm" style="margin-top: 11px;">
                        @T("预览")：
                        <div class="layui-upload-list" id="upload-demo-preview2"></div>
                    </blockquote>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("状态")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <select name="Status">
                    <!option value="0" @(Model.Status == 0 ? "selected":"")>@T("待处理")</!option>
                    <!option value="1" @(Model.Status == 1 ? "selected":"")>@T("处理中")</!option>
                    <!option value="2" @(Model.Status == 2 ? "selected":"")>@T("已处理")</!option>
                    <!option value="3" @(Model.Status == 3 ? "selected":"")>@T("不予处理")</!option>
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("处理原因")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Cause" autocomplete="off" class="layui-input" lay-filter="Versions" value="@Model.Cause">
            </div>
        </div>

        <div class="layui-form-item btn">
            <input hidden name="Id" value="@Model.Id" />
            <input type="button" lay-submit lay-filter="Submit" id="Submit"
                class="pear-btn pear-btn-primary pear-btn-normal" value="@T("保存")" />
        </div>
    </form>
</div>

<!-- 全屏媒体预览 -->
<div class="fullscreen-preview" id="fullscreenPreview">
    <span class="fullscreen-close" id="fullscreenClose">&times;</span>
    <img id="fullscreenImage" src="" alt="全屏预览" style="display: none;">
    <video id="fullscreenVideo" controls style="display: none;"></video>
</div>
<script>

</script>
<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var upload = layui.upload;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        var Images = [];
        var Videos = [];

        var domain = '@OssSetting.Current.QiNiu.Domain';
        if(domain && !domain.endsWith("/"))
        {
            domain = domain + '/';
        }
        if('@Model.PicPath' != null && '@Model.PicPath' != '')
        {
            var data = '@Html.Raw(Model.PicPath)';
            var PicPath = data.split(',');
            for(var i = 0;i < PicPath.length;i++)
            {
                var pic = domain+PicPath[i];
                var $previewItem = createImagePreviewItem(pic, i);
                $('#upload-demo-preview1').append($previewItem);
                Images.push(PicPath[i]);
            }
        }

        if('@Model.VideoPath' != null && '@Model.VideoPath' != '')
        {
            var data = '@Html.Raw(Model.VideoPath)';
            var VideoPath = data.split(',');
            for(var i = 0;i < VideoPath.length;i++)
            {
                var video = domain+VideoPath[i];
                var $newItem = createVideoPreviewItem(video, i);
                $('#upload-demo-preview2').append($newItem);
                Videos.push(VideoPath[i])
            }
        }

        @* 全屏媒体预览 *@
            // 全屏图片预览功能
            function showFullscreenPreview(mediaSrc, event, isVideo = false) {
                // 阻止事件冒泡，禁用替换图片功能
                if (event) {
                    event.preventDefault();
                    event.stopPropagation();
                    event.stopImmediatePropagation();
                }

                // 禁止页面滚动
                $('body').css('overflow', 'hidden');

                if (isVideo) {
                    $('#fullscreenImage').hide();
                    $('#fullscreenVideo').attr('src', mediaSrc).show();
                } else {
                    $('#fullscreenVideo').hide();
                    $('#fullscreenImage').attr('src', mediaSrc).show();
                }

                $('#fullscreenPreview').css('display', 'flex');
            }

        // 关闭全屏预览
        function closeFullscreenPreview() {
            // 恢复页面滚动
            $('body').css('overflow', 'auto');

            // 隐藏所有媒体元素
            $('#fullscreenImage').hide();
            $('#fullscreenVideo').hide().attr('src', ''); // 清空视频源以停止播放
            $('#fullscreenPreview').hide();
        }

        // 绑定关闭事件
        $('#fullscreenClose').click(closeFullscreenPreview);
        $('#fullscreenPreview').click(function (e) {
            if (e.target === this) {
                closeFullscreenPreview();
            }
        });

        // ESC键关闭预览
        $(document).keyup(function (e) {
            if (e.keyCode === 27) { // ESC键
                closeFullscreenPreview();
            }
        });

        @* 图片删除 *@
            // 删除图片功能
            function deleteImage(index, element, event) {
                // 阻止事件冒泡，禁用替换图片功能
                if (event) {
                    event.preventDefault();
                    event.stopPropagation();
                    event.stopImmediatePropagation();
                }

                // 从数组中移除
                Images.splice(index, 1);
                // 从DOM中移除
                $(element).closest('.image-preview-item').remove();
                // 重新索引剩余图片
                updateImageIndexes();
            }

        // 更新图片索引
        function updateImageIndexes() {
            $('#upload-demo-preview1 .image-preview-item').each(function (index) {
                var $item = $(this);
                var imageSrc = $item.find('img').attr('src');

                // 重新绑定事件处理器，而不是使用onclick属性
                $item.find('.delete-btn').off('click').on('click', function (event) {
                    deleteImage(index, this, event);
                });

                $item.find('.preview-btn').off('click').on('click', function (event) {
                    showFullscreenPreview(imageSrc, event);
                });

                // 为整个图片容器添加点击保护
                $item.find('.image-actions').off('click').on('click', function (event) {
                    // 阻止事件冒泡到父级元素
                    event.preventDefault();
                    event.stopPropagation();
                    event.stopImmediatePropagation();
                });
            });
        }

        // 创建图片预览项
        function createImagePreviewItem(imageSrc, index) {
            var $item = $(`
                <div class="image-preview-item">
                    <img src="${imageSrc}" alt="预览图片">
                    <div class="image-actions">
                            <i  title="全屏预览" class="layui-icon layui-icon-eye preview-btn"></i>
                            <i  title="删除图片" class="layui-icon layui-icon-delete  delete-btn"></i>
                    </div>
                </div>
            `);

            // 绑定事件处理器
            $item.find('.preview-btn').on('click', function (event) {
                showFullscreenPreview(imageSrc, event);
            });

            $item.find('.delete-btn').on('click', function (event) {
                deleteImage(index, this, event);
            });

            // 为整个图片容器添加点击保护
            $item.find('.image-actions').on('click', function (event) {
                // 阻止事件冒泡到父级元素
                event.preventDefault();
                event.stopPropagation();
                event.stopImmediatePropagation();
            });

            return $item;
        }

        @* 视频删除和预览 *@
            // 删除视频功能
            function deleteVideo(index, element, event) {
                // 阻止事件冒泡，禁用替换视频功能
                if (event) {
                    event.preventDefault();
                    event.stopPropagation();
                    event.stopImmediatePropagation();
                }

                // 从数组中移除
                Videos.splice(index, 1);
                // 从DOM中移除
                $(element).closest('.video-preview-item').remove();
                // 重新索引剩余视频
                updateVideoIndexes();
            }

        // 更新视频索引
        function updateVideoIndexes() {
            $('#upload-demo-preview2 .video-preview-item').each(function (index) {
                var $item = $(this);
                var videoSrc = $item.find('video').attr('src');

                // 重新绑定事件处理器
                $item.find('.delete-btn').off('click').on('click', function (event) {
                    deleteVideo(index, this, event);
                });

                $item.find('.preview-btn').off('click').on('click', function (event) {
                    showFullscreenPreview(videoSrc, event, true);
                });

                // 为整个视频容器添加点击保护
                $item.find('.video-actions').off('click').on('click', function (event) {
                    // 阻止事件冒泡到父级元素
                    event.preventDefault();
                    event.stopPropagation();
                    event.stopImmediatePropagation();
                });
            });
        }

        // 创建视频预览项
        function createVideoPreviewItem(videoSrc, index) {
            var $item = $(`
                <div class="video-preview-item">
                    <video src="${videoSrc}" alt="预览视频"></video>
                    <div class="video-actions">
                            <i  title="全屏预览" class="layui-icon layui-icon-eye preview-btn"></i>
                            <i  title="删除视频" class="layui-icon layui-icon-delete  delete-btn"></i>
                    </div>
                </div>
            `);

            // 绑定事件处理器
            $item.find('.preview-btn').on('click', function (event) {
                showFullscreenPreview(videoSrc, event, true);
            });

            $item.find('.delete-btn').on('click', function (event) {
                deleteVideo(index, this, event);
            });

            // 为整个视频容器添加点击保护
            $item.find('.video-actions').on('click', function (event) {
                // 阻止事件冒泡到父级元素
                event.preventDefault();
                event.stopPropagation();
                event.stopImmediatePropagation();
            });

            return $item;
        }

        //拖拽阈值表上传
        upload.render({
            elem: '#imgUpload'
            , url: '@Url.Action("UploadFiles")' //改成您自己的上传接口
            , done: function (res) {
                if (!res.success) { //失败打印
                    os.warning(res.msg);
                    return;
                }
                os.success('@T("上传成功")');
                Images.push(res.data.FilePath);

                // 更新预览区域，使用服务器返回的完整图片URL
                var currentIndex = Images.length - 1;
                var $lastPreviewItem = $('#upload-demo-preview1 .image-preview-item').last();
                var $newItem = createImagePreviewItem(res.data.FileUrl, currentIndex);
                $lastPreviewItem.replaceWith($newItem);
            }
            , before: function (obj) {
                // 验证文件类型
                obj.preview(function (index, file, result) {
                    // 先显示本地预览，上传成功后会被替换
                    var tempIndex = Images.length;
                    var $previewItem = createImagePreviewItem(result, tempIndex);
                    $('#upload-demo-preview1').append($previewItem);
                });
            }
            , accept: 'images' //只允许上传图片类型

        });

        upload.render({
            elem: '#videoUpload'
            , url: '@Url.Action("UploadFiles")' //改成您自己的上传接口
            , done: function (res) {
                if (!res.success) { //失败打印
                    os.warning(res.msg);
                    return;
                }
                os.success('@T("上传成功")');
                Videos.push(res.data.FilePath);

                // 更新预览区域，使用服务器返回的视频路径
                var currentIndex = Videos.length - 1;
                var $lastPreviewItem = $('#upload-demo-preview2 .video-preview-item').last();
                var $newItem = createVideoPreviewItem(res.data.FileUrl, currentIndex);
                $lastPreviewItem.replaceWith($newItem);
            }
            , before: function (obj) {
                // 预读本地文件示例，不支持ie8
                obj.preview(function (index, file, result) {
                    // 先显示本地预览，上传成功后会被替换
                    var tempIndex = Videos.length;
                    var $previewItem = createVideoPreviewItem(result, tempIndex);
                    $('#upload-demo-preview2').append($previewItem);
                });
            }
            , accept: 'video' //允许上传的文件类型

        });

        var parentList = window.parent
        var parentPage = null;

        // 第二步：拿到对应层
        for (let i = 0; i < parentList.length; i++) {
            if (parentList[i].name === 'Feedback') { //这里的name自己去对应层DIY
                parentPage = parentList[i]
                break;
            }
        }
        // 第三步：操作对应层
        var parent_window = parentPage.window  //获取父层的window层
        var parent_layer = parentPage.layer //获取父层的layer
        var parent_notify = parentPage.layui.abp.notify //获取父层的layui.notify --消息通知
        var parent_layui = parentPage.layui  //获取父层的layui
        var currentPageCloseIndex = parent_window.editPageIndex //当前层的关闭index下标

        form.on('submit(Submit)', function (data) {

            var waitIndex = parent.layer.load(2);

            data.field.PicPath = Images.join(',');
            data.field.VideoPath = Videos.join(',');

            abp.ajax({
                url: "@Url.Action("FeedbackUpdate")",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.close(currentPageCloseIndex);
                parent_notify.success(data.msg);
                parentPage.active.reload();
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>