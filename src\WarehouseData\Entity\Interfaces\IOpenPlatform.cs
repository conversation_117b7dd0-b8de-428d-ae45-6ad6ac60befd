﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>第三方对接</summary>
public partial interface IOpenPlatform
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>商户号</summary>
    String AccessId { get; set; }

    /// <summary>商户密钥</summary>
    String? AccessKey { get; set; }

    /// <summary>是否启用</summary>
    Boolean Enabled { get; set; }

    /// <summary>IP白名单</summary>
    String? IPWhite { get; set; }

    /// <summary>项目名称</summary>
    String ProjectName { get; set; }

    /// <summary>备注</summary>
    String? Remark { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
