﻿using DG.Web.Framework;
using DH.Entity;
using DH.Helpers;
using HlktechIoT.Data;
using HlktechIoT.Dto;
using HlktechIoT.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Serialization;
using Newtonsoft.Json;
using Pek;
using Pek.Configs;
using Pek.Models;
using Pek.QiNiu.Extensions;
using Pek.Timing;
using SixLabors.ImageSharp.Metadata.Profiles.Iptc;
using StackExchange.Redis;
using System.ComponentModel;
using XCode;
using XCode.Membership;
using YRY.Web.Controllers.Common;
using static Microsoft.Extensions.Logging.EventSource.LoggingEventSource;

namespace HlktechIoT.Areas.Production.Controllers;

/// <summary>电源生产数据</summary>
[DisplayName("电源生产数据")]
[Description("电源生产数据")]
[ProductionArea]
[DHMenu(50, ParentMenuName = "ProductionsManager", CurrentMenuUrl = "~/{area}/Power", CurrentMenuName = "PowerList", LastUpdate = "20250708")]
public class PowerController : BaseAdminControllerX
{

    /// <summary>
    /// 电源生产数据列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("电源生产数据列表")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 获取电源数据
    /// </summary>
    /// <param name="ParentId">父级id</param>
    /// <param name="Name">分类名称</param>
    /// <param name="page">页码</param>
    /// <param name="limit">条数</param>
    /// <returns></returns>
    [DisplayName("列表")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult GetList(String orderId, int productTypeId, DateTime start, DateTime end, Int32 page, Int32 limit)
    {
        var order = ProductOrders.FindByOrderId(orderId);
        if (start.IsNull()||start<=DateTime.MinValue)
        {
            start = new DateTime(DateTime.Now.Year, 1, 1);
        }
        if (end.IsNull() || end <= DateTime.MinValue)
        {
            end = DateTime.Now;
        }
        XTrace.WriteLine("开始时间：" + start.ToString());
        XTrace.WriteLine("结束时间：" + start.ToString());
        if (order != null)
        {
            start = order.StartTime;
            end = order.EndTime;
        }
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true,
        };
        var data = PowerProduction.Search(orderId, productTypeId, start, end, "", pages).Select(e => new
        {
            Id = e.Id.SafeString(),
            e.OrderId,
            e.ProductTypeName,
            e.CreateUser,
            e.CreateTime
        });
        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    [DisplayName("搜索电源产品型号")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult SearchProductType(String keyword, Int32 page, Int32 Id)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = ProductType._.Id,
            Desc = true,
        };

        res.data = ProductType.SearchPower(0, keyword, true, "", pages).Select(e =>
        {
            return new
            {
                name = e.Name,
                value = e.Id,
                e.NeedSn,
                e.NeedMac,
                e.NeedMac1,
                e.PCBCycle,
                e.ShieldCycle,
                e.MainChipCycle,
            };
        });

        res.success = true;

        var model = ProductType.FindById(Id);

        if (model == null)
        {
            res.extdata = new { pages.PageCount };
        }
        else
        {
            res.extdata = new { pages.PageCount, data = new List<object>() { new { name = model.Name, value = model.Id, NeedSn = model.NeedSn, NeedMac = model.NeedMac, model.NeedMac1, model.PCBCycle, model.ShieldCycle, model.MainChipCycle } } };
        }

        return Json(res);
    }


    [DisplayName("新增电源数据")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult Add()
    {
        return View();
    }

    [DisplayName("新增电源数据")]
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    public IActionResult Add(string OrderId,int ProductType)
    {
        if (OrderId.IsNullOrEmpty())
        {
            return Json(new DResult() { success = false,msg = GetResource("请输入订单号") });
        }
        var order = ProductOrders.FindByOrderId(OrderId);
        if (order==null)
        {
            return Json(new DResult() { success = false, msg = GetResource("该订单不存在") });
        }
        if (ProductType==0||ProductType.IsNull())
        {
            return Json(new DResult() { success = false, msg = GetResource("请选择产品型号") });
        }
        var model = new PowerProduction();
        model.OrderId = OrderId;
        model.ProductTypeId = ProductType;
        model.Insert();
        return Json(new DResult() { success = true, msg = GetResource("添加成功") });
    }

    [DisplayName("编辑电源数据")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Edit(long Id)
    {
        var model = PowerProduction.FindById(Id);
        if (model == null) return Json(new DResult() { success = false, msg = GetResource("电源数据不存在") });
        return View(model);
    }

    [DisplayName("编辑电源数据")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult Edit(long Id,string OrderId,int ProductType)
    {
        var model = PowerProduction.FindById(Id);
        if (model==null) return Json(new DResult() { success = false, msg = GetResource("电源数据不存在") });
        if (OrderId.IsNullOrEmpty())
        {
            return Json(new DResult() { success = false,msg = GetResource("请输入订单号") });
        }
        var order = ProductOrders.FindByOrderId(OrderId);
        if (order==null)
        {
            return Json(new DResult() { success = false, msg = GetResource("该订单不存在") });
        }
        if (ProductType.IsNull()||ProductType==0)
        {
            return Json(new DResult() { success = false, msg = GetResource("请选择产品型号") });
        }
        model.OrderId = OrderId;
        model.ProductTypeId = ProductType;
        model.Update();
        return Json(new DResult() { success = true, msg = GetResource("修改成功") });
    }

    [DisplayName("删除电源数据")]
    [EntityAuthorize(PermissionFlags.Delete)]
    [HttpPost]
    public IActionResult Delete(long Id)
    {
        PowerProduction.Delete(PowerProduction._.Id == Id);
        PowerProduction.Meta.Cache.Clear("", true);
        return Json(new DResult() { success = true, msg = GetResource("删除成功") });
    }

    /// <summary>
    /// 电源型号列表
    /// </summary>
    /// <param name="FirmwaresId">生产固件id</param>
    /// <param name="Name">名称</param>
    /// <param name="page">页码</param>
    /// <param name="limit">条数</param>
    /// <returns></returns>
    [DisplayName("电源型号列表")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult PowerType(Int64 FirmwaresId, String Name, Int32 page, Int32 limit)
    {
        return View();
    }

    /// <summary>
    /// 电源型号列表
    /// </summary>
    /// <param name="FirmwaresId">生产固件id</param>
    /// <param name="Name">名称</param>
    /// <param name="page">页码</param>
    /// <param name="limit">条数</param>
    /// <returns></returns>
    [DisplayName("电源型号列表")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult GetPowerTypeList(Int64 FirmwaresId, String Name, Int32 page, Int32 limit)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true,
        };
        var data = ProductType.SearchPower(FirmwaresId, Name, null, "", pages).Select(e => new
        {
            e.Id,
            e.Name,
            ModuleType = e.ModuleType switch
            {
                1 => "ACDC",
                2 => "DCDC",
                _ => "无"
            },
            e.Status,
            e.CreateTime
        });
        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }
    /// <summary>
    /// 添加产品类型
    /// </summary>
    /// <returns></returns>
    [DisplayName("添加产品类型")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddPowerType()
    {
        return View();
    }

    /// <summary>
    /// 添加电源型号
    /// </summary>
    /// <param name="Name">名称</param>
    /// <param name="Status">启用状态</param>
    /// <param name="Remark">备注</param>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("添加")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddPowerType(String Name, Boolean Status, String Remark, Int32 ModuleType)
    {
        DResult res = new();
        if (Name.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("产品型号名称不能为空");
            return Json(res);
        }
        if (ModuleType.IsNull()|| ModuleType==0)
        {
            res.msg = GetResource("请选择模组类型");
            return Json(res);
        }
        var model = ProductType.FindByName(Name);
        if (model != null)
        {
            res.msg = GetResource("产品型号名称已存在");
            return Json(res);
        }

        model = new ProductType
        {
            Name = Name,
            ModuleType = ModuleType,
            Status = Status,
            Remark = Remark,
            PType = 1
        };
        model.Insert();
        res.success = true;
        res.msg = GetResource("添加成功");
        return Json(res);
    }
    /// <summary>
    /// 编辑产品型号
    /// </summary>
    /// <param name="Id">型号id</param>
    /// <returns></returns>
    [DisplayName("编辑")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult UpdatePowerType(Int32 Id)
    {
        var model = ProductType.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("产品型号不存在"));
        }
        return View(model);
    }

    /// <summary>
    /// 编辑产品型号
    /// </summary>
    /// <param name="Id">型号id</param>
    /// <param name="Status">启用状态</param>
    /// <param name="Name">名称</param>
    /// <param name="Remark">备注</param>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("编辑")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult UpdatePowerType(Int32 Id, Boolean Status, String Name, String Remark,Int32 ModuleType)
    {
        DResult res = new();
        if (Name.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("产品型号名称不能为空");
            return Json(res);
        }

        if (ModuleType.IsNull() || ModuleType == 0)
        {
            res.msg = GetResource("请选择模组类型");
            return Json(res);
        }

        var model = ProductType.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("产品型号不存在");
            return Json(res);
        }
        var modelProductType = ProductType.FindByName(Name);
        if (modelProductType != null && modelProductType.Id != model.Id)
        {
            res.msg = GetResource("产品型号名称已存在");
            return Json(res);
        }
        model.Name = Name;
        model.ModuleType = ModuleType;
        model.Remark = Remark;
        model.Status = Status;
        model.Update();
        res.success = true;
        res.msg = GetResource("编辑成功");
        return Json(res);
    }

    /// <summary>
    /// 配置项
    /// </summary>
    /// <returns></returns>
    [DisplayName("配置")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Expansion(Int32 Id)
    {
        ViewBag.Id = Id;
        return View();
    }

    [DisplayName("获取配置列表")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult GetExpansionList(String key, Int32 Id, Int32 page = 1, Int32 limit = 100)
    {
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true
        };
        var modelProductTypeItemEx = ProductTypeItemEx.FindById(Id);

        if (modelProductTypeItemEx == null && modelProductTypeItemEx?.Items.IsNullOrEmpty() == true)
        {
            return Json(new { code = 0, msg = "success", count = 0, data = new List<PowerTypeItem>().Select(item => new { item.Id, item.Name, item.Code, item.Unit, item.Remark }) });
        }

        List<PowerConfigDto> listconfig = new List<PowerConfigDto>();
        try
        {
            listconfig = JsonConvert.DeserializeObject<List<PowerConfigDto>>(modelProductTypeItemEx.Items ?? "") ?? new List<PowerConfigDto>();//原有的配置
        }
        catch (Exception)
        {
            listconfig = new List<PowerConfigDto>();//如果解析失败，初始化为空列表
        }

        listconfig = listconfig.OrderBy(e => e.Sort).ToList();

        var codeStr = listconfig.Select(e => e.Code).Join(",");
        var sortDict = listconfig.ToDictionary(e => e.Code, e => e.Sort);
        var valueDict = listconfig.ToDictionary(e => e.Code, e => e.Value.SafeString());

        var list = PowerTypeItem.Search(key, codeStr, pages).Select(item => new
        {
            item.Id,
            item.Name,
            item.Code,
            item.Unit,
            item.Remark,
            Sort = sortDict.ContainsKey(item.Code) ? sortDict[item.Code] : 0,
            Value = valueDict.ContainsKey(item.Code) ? valueDict[item.Code] ?? "" : "",
        });
        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data = list });

    }

    [DisplayName("选择配置项")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult SelectItems(int id)
    {
        ViewBag.Id = id;
        return View();
    }

    [DisplayName("搜索配置项")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult SearchItem(String keyword, Int32 page, Int32 Id)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = PowerTypeItem._.Id,
            Desc = true
        };

        res.data = PowerTypeItem.Search(keyword, pages).Select(e =>
        {
            var selected = false;

            return new Xmselect<String>
            {
                name = e.Name + $"({e.Code})",
                value = e.Code!,
                selected = selected
            };
        });

        res.extdata = pages.PageCount;
        res.success = true;
        return Json(res);
    }

    [DisplayName("添加配置项")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult AddExItem(Int32 Id)
    {
        ViewBag.Id = Id;
        return View();
    }

    [DisplayName("添加配置项")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult AddExItem(Int32 Id, String Name, String Code, String Unit, String Remark)
    {
        var res = new DResult();

        if (Name.IsNullOrWhiteSpace())
        {
            return Json(new { success = false, msg = GetResource("名称不能为空") });
        }

        if (Code.IsNullOrWhiteSpace())
        {
            return Json(new { success = false, msg = GetResource("编号不能为空") });
        }

        if (PowerTypeItem.FindByName(Name) != null)
        {
            return Json(new { success = false, msg = GetResource("名称已存在") });
        }

        if (PowerTypeItem.FindByCode(Code) != null)
        {
            return Json(new { success = false, msg = GetResource("编号已存在") });
        }

        var model = new PowerTypeItem();
        model.Name = Name;
        model.Code = Code;
        model.Unit = Unit;
        model.Remark = Remark;
        model.Insert();

        //添加后加入到产品型号的配置中
        var modelProductTypeItemEx = ProductTypeItemEx.FindById(Id);
        if (!modelProductTypeItemEx.IsNull())
        {
            var listconfig = JsonConvert.DeserializeObject<List<PowerConfigDto>>(modelProductTypeItemEx.Items ?? "") ?? new List<PowerConfigDto>();//原有的配置
            var list = listconfig.Select(e => e.Code).ToList();

            // 将新的配置权限全部添加并去重
            listconfig.AddRange( new PowerConfigDto()
            {
                Code = model.Code,
                Sort = 0 // 默认排序为0
            });
            modelProductTypeItemEx.Items = listconfig.Count > 0 ? listconfig.ToJson() : "";

            modelProductTypeItemEx.Update();
        }

        res.success = true;
        res.msg = GetResource("新增成功");
        return Json(res);
    }

    /// <summary>
    /// 选择配置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("选择配置")]
    [HttpPost]
    public IActionResult SelectItems(String Select, Int32 Id)
    {
        var res = new DResult();

        if (Select.IsNullOrWhiteSpace())
        {
            return Json(new { success = false, msg = GetResource("编号不能为空") });
        }

        // 将传入字符串用逗号分割，并移除空白、重复
        var selects = Select
            .Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(s => s.Trim())
            .Where(s => !string.IsNullOrWhiteSpace(s))
            .Distinct()
            .ToList();

        var model = ProductTypeItemEx.FindById(Id);
        if (model==null)
        {
            return Json(new { success = false, msg = GetResource("目录不存在") });
        }

        // 取出现有角色配置权限
        List<PowerConfigDto> listconfig = new List<PowerConfigDto>();
        try
        {
            listconfig = JsonConvert.DeserializeObject<List<PowerConfigDto>>(model.Items ?? "") ?? new List<PowerConfigDto>();//原有的配置
        }
        catch (Exception)
        {
            listconfig = new List<PowerConfigDto>();//如果解析失败，初始化为空列表
        }

        var list = listconfig.Select(e => e.Code).ToList();
       

        // 如果任意一个要添加的配置在现有列表中，则提示已存在
        if (list.Intersect(selects).Any())
        {
            return Json(new { success = false, msg = GetResource("配置已存在") });
        }

        // 将新的配置权限全部添加并去重
        listconfig.AddRange(selects.Select(e => new PowerConfigDto()
        {
            Code = e,
            Sort = 0 // 默认排序为0
        }));
      

        //list.AddRange(selects);
        //list = list.Distinct().ToList();
        model.Items = listconfig.Count>0? listconfig.ToJson():"";

        model.Update();

        res.success = true;
        res.msg = GetResource("选择成功");
        return Json(res);
    }

    /// <summary>
    /// 删除
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除")]
    [HttpPost]
    public IActionResult DeleteExItem(String Code, Int32 Id)
    {
        var res = new DResult();

        var model = ProductTypeItemEx.FindById(Id);
        if (model == null)
        {
            res.success = false;
            res.msg = GetResource("型号不存在");
            return Json(res);
        }

        List<PowerConfigDto> listconfig = new List<PowerConfigDto>();
        try
        {
            listconfig = JsonConvert.DeserializeObject<List<PowerConfigDto>>(model.Items ?? "") ?? new List<PowerConfigDto>();//原有的配置
        }
        catch (Exception)
        {
            listconfig = new List<PowerConfigDto>();//如果解析失败，初始化为空列表
        }
        listconfig.RemoveWhere(e => e.Code == Code);
        //var list = model.ExpansionItems?
        //    .Trim(',')
        //    .Split(',', StringSplitOptions.RemoveEmptyEntries)
        //    .Select(s => s.Trim())
        //    .ToList() ?? new List<string>();

        // 移除所有重复出现的 Code
        //list.RemoveAll(x => x.Equals(Code, StringComparison.OrdinalIgnoreCase));

        model.Items = listconfig.Count > 0
            ? listconfig.ToJson()
            : string.Empty;

        model.Update();

        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改配置排序")]
    public IActionResult UpdateConfigSort(int Id, string Code, int Sort)
    {
        if (Id.IsNull())
        {
            return Json(new DResult() { success = false, msg = "型号ID为空" });
        }
        var itemex = ProductTypeItemEx.FindById(Id);
        if (itemex.IsNull())
        {
            return Json(new DResult() { success = false, msg = "型号为空" });
        }
        var listConfig = JsonConvert.DeserializeObject<List<PowerConfigDto>>(itemex.Items ?? "") ?? new List<PowerConfigDto>();

        var item = listConfig.FirstOrDefault(x => x.Code == Code);
        if (item != null)
        {
            item.Sort = Sort;
            // 保存到数据库
            itemex.Items = listConfig.ToJson();
            itemex.Update();
            return Json(new DResult { success = true, msg = "排序已更新" });
        }
        else
        {
            return Json(new DResult { success = false, msg = "未找到对应配置项" });
        }
    }

    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改配置值")]
    public IActionResult UpdateConfigValue(int Id, string Code, decimal Value)
    {
        if (Id.IsNull())
        {
            return Json(new DResult() { success = false, msg = "型号ID为空" });
        }
        var itemex = ProductTypeItemEx.FindById(Id);
        if (itemex.IsNull())
        {
            return Json(new DResult() { success = false, msg = "型号为空" });
        }
        var listConfig = JsonConvert.DeserializeObject<List<PowerConfigDto>>(itemex.Items ?? "") ?? new List<PowerConfigDto>();

        var item = listConfig.FirstOrDefault(x => x.Code == Code);
        if (item != null)
        {
            item.Value = Value;
            // 保存到数据库
            itemex.Items = listConfig.ToJson();
            itemex.Update();
            return Json(new DResult { success = true, msg = "设置成功" });
        }
        else
        {
            return Json(new DResult { success = false, msg = "未找到对应配置项" });
        }
    }

    /// <summary>
    /// 测试项页面
    /// </summary>
    /// <returns></returns>
    [DisplayName("测试项")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult TestItems(Int32 Id)
    {
        var model = ProductType.FindById(Id);
        if(model == null)
        {
            return Content(GetResource("产品型号不存在"));
        }
        return View(model);
    }

    /// <summary>
    /// 测试项列表
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [DisplayName("测试项")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult TestItemsList(Int32 Id, Int32 page = 1, Int32 limit = 10)
    {
        List<PowerTestItem> data = new();

        var model = ProductType.FindById(Id);

        if(model != null && !model.TestItems.IsNullOrWhiteSpace())
        {
            foreach (var item in model.TestItems.Split(","))
            {
                var entity = PowerTestItem.FindByCode(item);
                if(entity != null)
                {
                    data.Add(entity);
                }
            }
        }
        return Json(new { code = 0, msg = "success", count = data.Count, data = data.Skip((page - 1) * limit).Take(limit) });
    }


    /// <summary>
    /// 选择测试项页面
    /// </summary>
    /// <returns></returns>
    [DisplayName("选择测试项")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult SelectTestItems(Int32 Id)
    {
        var model = ProductType.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("产品型号不存在"));
        }
        return View(model);
    }

    /// <summary>
    /// 搜索测试项
    /// </summary>
    /// <returns></returns>
    [DisplayName("搜索测试项")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult SearchTestItems(String keyword, Int32 page)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = PowerTestItem._.Id,
            Desc = true
        };

        res.data = PowerTestItem.Search(keyword,DateTime.MinValue,DateTime.MinValue,"", pages).Select(e =>
        {
            var selected = false;

            return new Xmselect<String>
            {
                name = e.Name + $"({e.Code})",
                value = e.Code!,
                selected = selected
            };
        });

        res.extdata = pages.PageCount;
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 添加测试项
    /// </summary>
    /// <returns></returns>
    [DisplayName("添加测试项")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddTestItems()
    {
        return View();
    }

    /// <summary>
    /// 添加测试项
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("添加测试项")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddTestItems(String Name,String Code,String Remark)
    {
        DResult res = new();
        if (Name.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("名称不能为空");
            return Json(res);
        }
        if (Code.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("Code不能为空");
            return Json(res);
        }
        if (PowerTestItem.FindByName(Name) != null)
        {
            res.msg = GetResource("名称已存在");
            return Json(res);
        }
        if (PowerTestItem.FindByCode(Code) != null)
        {
            res.msg = GetResource("Code已存在");
            return Json(res);
        }
        var model = new PowerTestItem()
        {
            Name = Name,
            Code = Code,
            Remark = Remark,
        };
        model.Insert();
        res.success = true;
        res.msg = GetResource("添加成功");
        return Json(res);
    }

    /// <summary>
    /// 选择测试项
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("选择测试项")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult SelectTestItems(Int32 Id,String Select)
    {
        DResult res = new();

        if (Select.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("测试项不能为空");
            return Json(res);
        }

        var model = ProductType.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("型号不存在");
            return Json(res);
        }

        if (model.TestItems.IsNullOrWhiteSpace()) 
        {
            model.TestItems = $",{Select},";
        }
        else
        {
            model.TestItems = $"{model.TestItems}{Select},";
        }

        model.Update();

        res.success = true;
        res.msg = GetResource("选择成功");
        return Json(res);
    }

    /// <summary>
    /// 删除测试项
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="Code"></param>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("删除测试项")]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult DeleteTestItems(Int32 Id, String Code)
    {
        DResult res = new();

        if (Code.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("测试项不能为空");
            return Json(res);
        }

        var model = ProductType.FindById(Id);
        if(model == null)
        {
            res.msg = GetResource("型号不存在");
            return Json(res);
        }
        if (model.TestItems.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("型号测试项为空");
            return Json(res);
        }

        if (model.TestItems == $",{Code},")
        {
            model.TestItems = "";
        }
        else
        {
            model.TestItems = model?.TestItems?.Replace($",{Code},", ",");
        }

        model.Update();
        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 配置项目录
    /// </summary>
    /// <returns></returns>
    [DisplayName("配置项目录")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult ItemsDirectory(Int32 ProductTypeId)
    {
        var model = ProductType.FindById(ProductTypeId);
        if(model == null)
        {
            return Content(GetResource("型号不存在"));
        }
        return View(model);
    }

    /// <summary>
    /// 配置项目录
    /// </summary>
    /// <returns></returns>
    [DisplayName("配置项目录")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult ItemsDirectoryList(Int32 productTypeId, Int32 page, Int32 limit, string key)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = ProductTypeItemEx._.Id,
            Desc = true,
        };
        var data = ProductTypeItemEx.Search(productTypeId, -1, DateTime.MinValue, DateTime.MinValue, key, pages);
        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 添加配置项目录
    /// </summary>
    /// <param name="ProductTypeId"></param>
    /// <returns></returns>
    [DisplayName("添加配置项目录")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddItemsDirectory(Int32 ProductTypeId)
    {
        var model = ProductType.FindById(ProductTypeId);
        if (model == null)
        {
            return Content(GetResource("型号不存在"));
        }
        return View(model);
    }

    /// <summary>
    /// 添加配置项目录
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("添加配置项目录")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddItemsDirectory(Int32 ProductTypeId,String Name,String Remark)
    {
        DResult res = new();
        if (Name.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("名称不能为空");
            return Json(res);
        }
        var model = ProductTypeItemEx.FindByProductTypeIdAndName(ProductTypeId, Name);
        if(model != null)
        {
            res.msg = GetResource("名称已存在");
            return Json(res);
        }
        model = new ProductTypeItemEx();
        model.ProductTypeId = ProductTypeId;
        model.Name = Name;
        model.Status = 0;
        model.Remark = Remark;
        model.Insert();
        res.success = true;
        res.msg = GetResource("添加成功");
        return Json(res);
    }

    /// <summary>
    /// 编辑配置项目录
    /// </summary>
    /// <returns></returns>
    [DisplayName("编辑配置项目录")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult EditItemsDirectory(Int32 Id)
    {
        var model = ProductTypeItemEx.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("目录不存在"));
        }
        return View(model);
    }

    /// <summary>
    /// 编辑配置项目录
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("编辑配置项目录")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult EditItemsDirectory(Int32 Id, String Name, String Remark)
    {
        DResult res = new();
        if (Name.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("名称不能为空");
            return Json(res);
        }
        var model = ProductTypeItemEx.FindById(Id);
        if(model == null)
        {
            res.msg = GetResource("目录不存在");
            return Json(res);
        }
        var modelEx = ProductTypeItemEx.FindByProductTypeIdAndName(model.ProductTypeId, Name);
        if (modelEx != null && modelEx.Id != model.Id)
        {
            res.msg = GetResource("名称已存在");
            return Json(res);
        }
        model.Name = Name;
        model.Remark = Remark;
        model.Update();
        res.success = true;
        res.msg = GetResource("编辑成功");
        return Json(res);
    }

    /// <summary>
    /// 删除配置项目录
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("删除配置项目录")]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult DeleteItemsDirectory(Int32 Id)
    {
        DResult res = new();
        var model = ProductTypeItemEx.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("目录不存在");
            return Json(res);
        }
        if(model.Status == 1)
        {
            res.msg = GetResource("已发布不能删除");
            return Json(res);
        }
        model.Delete();
        ProductTypeItemEx.Meta.Cache.Clear("");
        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 发布配置项
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("发布配置项")]
    [EntityAuthorize((PermissionFlags)16)]
    public IActionResult PublishItemsDirectory(Int32 Id)
    {
        DResult res = new();
        var model = ProductTypeItemEx.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("目录不存在");
            return Json(res);
        }
        if (model.Status == 1)
        {
            res.msg = GetResource("不可重复发布");
            return Json(res);
        }
        model.Status = 1;
        model.Update();
        res.success = true;
        res.msg = GetResource("发布成功");
        return Json(res);
    }

    /// <summary>
    /// 电源型号批次配置
    /// </summary>
    /// <param name="ProductTypeId">电源型号Id</param>
    /// <returns></returns>
    [DisplayName("电源型号批次配置")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult PowerTypeBatch(Int32 ProductTypeId)
    {
        var model = ProductType.FindById(ProductTypeId);
        if (model == null)
        {
            return Content(GetResource("型号不存在"));
        }
        return View(model);
    }

    [DisplayName("电源型号批次配置")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult PowerTypeBatchList(Int32 ProductTypeId)
    {
        var data = PowerTypeBatchItem.FindAllByProductTypeId(ProductTypeId);
        return Json(new { code = 0, msg = "success",  data });
    }

    /// <summary>
    /// 添加电源型号批次配置
    /// </summary>
    /// <param name="ProductTypeId">电源型号Id</param>
    /// <returns></returns>
    [DisplayName("添加电源型号批次配置")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddPowerTypeBatch(Int32 ProductTypeId)
    {
        var model = ProductType.FindById(ProductTypeId);
        if (model == null)
        {
            return Content(GetResource("型号不存在"));
        }

        var data = PowerTypeBatchItem.FindAllByProductTypeId(ProductTypeId).Select(e=>e.BatchField).ToList();

        List<String> list = new();
        for (int i = 1; i <= 10; i++)
        {
            var item = $"BatchField{i}";
            if (data.Contains(item)) continue;
            list.Add(item);
        }
        ViewBag.DataList = list;
        return View(model);
    }

    /// <summary>
    /// 添加电源型号批次配置
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("添加电源型号批次配置")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddPowerTypeBatch(Int32 ProductTypeId,String Name,String BatchField)
    {
        DResult res = new();
        if (Name.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("批次名称不能为空");
            return Json(res);
        }
        if (BatchField.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("对应批次不能为空");
            return Json(res);
        }
        var model = new PowerTypeBatchItem();
        model.ProductTypeId = ProductTypeId;
        model.Name = Name;
        model.BatchField = BatchField;
        model.Insert();
        res.success = true;
        res.msg = GetResource("添加成功");
        return Json(res);
    }

    /// <summary>
    /// 编辑电源型号批次配置
    /// </summary>
    /// <returns></returns>
    [DisplayName("编辑电源型号批次配置")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult EditPowerTypeBatch(Int32 Id)
    {
        var model = PowerTypeBatchItem.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("批次不存在"));
        }
        List<String> list = new();
        for (int i = 1; i <= 10; i++)
        {
            var item = $"BatchField{i}";
            list.Add(item);
        }
        ViewBag.DataList = list;
        return View(model);
    }

    /// <summary>
    /// 编辑电源型号批次配置
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("编辑电源型号批次配置")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult EditPowerTypeBatch(Int32 Id, String Name)
    {
        DResult res = new();
        if (Name.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("批次名称不能为空");
            return Json(res);
        }
        var model = PowerTypeBatchItem.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("批次不存在");
            return Json(res);
        }
        model.Name = Name;
        model.Update();
        res.success = true;
        res.msg = GetResource("编辑成功");
        return Json(res);
    }

    /// <summary>
    /// 删除电源型号批次配置
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("删除电源型号批次配置")]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult DeletePowerTypeBatch(Int32 Id)
    {
        DResult res = new();
        var model = PowerTypeBatchItem.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("批次不存在");
            return Json(res);
        }
        model.Delete();
        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

}