﻿namespace HlktechIoT.Dto;

public class DeviceSnDto {
    /// <summary>
    /// 数据查询类型。1为Sn，2为主Mac，3为副Mac
    /// </summary>
    public Int32 CheckType { get; set; }

    /// <summary>
    /// 产品ID(SN/Mac/Mac1)
    /// </summary>
    public String? SN { get; set; }

    /// <summary>
    /// 订单号
    /// </summary>
    public String? OrderId { get; set; }
}

public class  DeviceSnReponseDto {
    /// <summary>
    /// 编号
    /// </summary>
    public Int64 Id { get; set; }

    /// <summary>
    /// 设备Sn
    /// </summary>
    public String? Sn { get; set; }

    /// <summary>
    /// 设备主Mac地址
    /// </summary>
    public String? Mac { get; set; }

    /// <summary>
    /// 设备副Mac地址
    /// </summary>
    public String? Mac1 { get; set; }
}