﻿using DG.Web.Framework.Routing;

using DH;
using DH.Core.Domain.Localization;
using DH.Core.Infrastructure;
using DH.Entity;

namespace HlktechIoT.Common.Routing;

public partial class AreaRouteProvider : IRouteProvider {
    #region 方法

    /// <summary>
    /// 注册路由
    /// </summary>
    /// <param name="endpoints">路由构造器</param>
    public void RegisterRoutes(IEndpointRouteBuilder endpoints)
    {
        // 对路由进行重新排序，以便最常用的路由排在最前面。 它可以提高性能
        var UrlSuffix = DG.Setting.Current.IsAllowUrlSuffix ? DG.Setting.Current.UrlSuffix : "";
        var pattern = string.Empty;

        var localizationSettings = LocalizationSettings.Current;

        if (DHSetting.Current.IsInstalled)
        {
            if (localizationSettings.SeoFriendlyUrlsForLanguagesEnabled && localizationSettings.IsEnable)
            {
                var languages = Language.FindByDefault();
                pattern = "{language:lang=" + languages.UniqueSeoCode + "}/";
            }
        }

        // 默认区域路由
        endpoints.MapControllerRoute(name: "areas", pattern: "{area:exists}/{controller=Home}/{action=Index}/{id?}");

        endpoints.MapControllerRoute(name: "areaRouteRoleExIndex",
            pattern: "{area:exists}/RoleEx/{Id:int}" + UrlSuffix, new { controller = "RoleEx", action = "Index" });

        //endpoints.MapControllerRoute(name: "areaRouteFaBu",
        //    pattern: "{area:exists}/FaBu" + UrlSuffix, new { controller = "FaBu", action = "Index" });
        //endpoints.MapControllerRoute(name: "areaRoute",
        //    pattern: "{area:exists}/FaBuXinXi/{Id}" + UrlSuffix, new { controller = "FaBu", action = "FaBuXinxi" });
    }

    #endregion

    #region 属性

    /// <summary>
    /// 获取路由提供者的优先级
    /// </summary>
    public int Priority
    {
        get { return 1; }
    }

    #endregion
}