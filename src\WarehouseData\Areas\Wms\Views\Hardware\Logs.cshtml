﻿@model HardwareDevices
@{
    Html.AppendTitleParts(T("设备日志").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }

    xm-select {
        margin-top: 10px;
        line-height: 30px;
        min-height: 30px !important;
    }

    .box > div {
        width: 100%;
        display: flex;
        justify-content: center:
    }

    .layui-form-item {
        display: flex;
        justify-content: center:
    }

    label {
        white-space: nowrap;
    }

</style>
<script asp-location="Head">
    var pleaseSelect = '@T("请选择")';
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>


<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="box flex" style="padding-top: 10px;width:100%;">


            <div>
                <label class="layui-form-label" style="width: auto;margin-left: 20px;">@T("筛选时间")：</label>
                <div class="layui-input-inline">
                    <input type="text" name="start" id="start" readonly placeholder="@T("开始时间")" autocomplete="off" class="layui-input">
                </div>

                <div class="layui-form-mid">-</div>
                <div class="layui-input-inline">
                    <input type="text" name="end" id="end" readonly placeholder="@T("结束时间")" autocomplete="off" class="layui-input">
                </div>

            </div>



            <div>

                <label class="layui-form-label" style="margin:10px 5px 0 20px;">@T("用户")</label>
                <div class="layui-input-inline" style=" max-width:190px;">
                    <div id="demo1" style=" width: 100%;" style="max-height:30px;"></div>
                </div>

                <label class="layui-form-label" style="margin:10px 5px 0 -30px;"> @T("工序")</label>
                <div class="layui-input-inline" style="margin-top:10px; max-width:190px;">

                    <select name="Proccess" id="Proccess" lay-filter="onchange">
                        <option value="">@T("请选择")</option>
                        <option value="1">@T("打单")</option>
                        <option value="2">@T("领料")</option>
                        <option value="3">@T("生产")</option>
                        <option value="4">@T("生产审核")</option>
                        <option value="5">@T("打包")</option>
                    </select>

                </div>
            </div>





        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;
        var laydate = layui.laydate;

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetLogList")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 80
            , cols: [[
                { field: 'Id', title: '@T("编号")', width: 80 }
                , { field: 'DId', title: '@T("设备Id")', width: 80 }
                , { field: 'Process', title: '@T("工序")', width: 85 }
                , { field: 'CreateUser', title: '@T("操作者")', width: 100 }
                , { field: 'CreateTime', title: '@T("操作时间")', width: 160 }
    @* , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', minWidth: 200 } *@
                    ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-180'
            , id: 'tables'
            , where: {
                Id: "@Model.Id",
                process: "",
                createUserId: "",
                start: "",
                end: ""
            }
        });

        let createUserId = undefined;
        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        where: {
                            Id: "@Model.Id",
                            process: $("#Proccess").val(),
                            createUserId: createUserId === undefined ? '' : createUserId,
                            start: $("#start").val(),
                            end: $("#end").val(),
                        }
                    });
            }
        }

        document.getElementById("Proccess").addEventListener("change", (e) => {
            console.log('选择', e)
        })

    @* 选择事件 *@
            form.on('select(onchange)', function (data) {
                active.reload();
            });

    @* $("#Proccess").on("change", function() {
            console.log('选择')
            }); *@
                const change = (e) => {
            console.log('选择', e)
        }

        $("#key").on("input", function (e) {
            active.reload();
        });

        var demo1 = xmSelect.render({
            el: '#demo1',
            radio: true, //单选
            name: 'createUserId',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            disabled: false, // 设置禁用
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];

                // 接口数据
                $.post('@Url.Action("SearchUser")', { keyword: val, page: pageIndex, }, function (res) {
                    // console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                            if (res.extdata.data != null) {
                                demo1.setValue(res.extdata.data)// 传入一个-默认值-数组
                            }
                            cb(res.data, res.extdata.PageCount);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择
                if (data.arr.length == 0) {
                    createUserId = undefined
                    setTimeout(() => {
                        active.reload('tables')
                    }, 200)
                    return;
                }
                if (data.arr.length > 0) {
                    createUserId = data.arr[0].value
                    setTimeout(() => {
                        active.reload('tables')
                    }, 200);
                }


            },
        });

        table.on('toolbar(tool)', function (obj) {
            let data = obj.config
            if (obj.event === 'add') {
                window.add(data);
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success(data.msg);
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'edit') {
                window.edit(data);
            }
        });

        window.saveCallback = function (data) {
            parent.layer.close(data.index);
            abp.notify.success(data.msg);
            table.reload("tables");
        }

        window.add = function (data) {
            top.layui.dg.popupRight({
                id: 'Add'
                , title: ' @T("新增设备")'
                , closeBtn: 1
                , area: ['580px']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Add")" frameborder="0" class="layadmin-iframe" name="iframe' + data.index + '"></iframe>');
                }
            });
        }
        window.edit = function (data) {
            top.layui.dg.popupRight({
                id: 'Edit'
                , title: ' @T("编辑设备")'
                , closeBtn: 1
                , area: ['580px']
                , success: function (obj, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Edit")' + abp.utils.formatString("?id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        //时间插件
        var startDate = laydate.render({
            elem: '#start',
            btns: ['clear', "confirm"],//只显示清空和确定按钮
            type: 'datetime',       // 设置日期选择类型为年月
            format: 'yyyy-MM-dd HH:MM:ss',   // 设置日期的格式，这里是年-月
            done: function (value, date) {
                $("#start").val(value);
                checkDateValidity();
            },
            choose: function (date) {
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });

        var endDate = laydate.render({
            elem: '#end',
            btns: ["clear", "confirm"],
            type: 'datetime',       // 设置日期选择类型为年月
            format: 'yyyy-MM-dd HH:MM:ss',   // 设置日期的格式，这里是年-月
            done: function (value, date) {
                $("#end").val(value);
                checkDateValidity();
            },
            choose: function (date) {
                console.log(date);
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });

        function checkDateValidity() {
            var startValue = $("#start").val();
            var endValue = $("#end").val();

            if (startValue && endValue) {
                var startDate = new Date(startValue + "-01");
                var endDate = new Date(endValue + "-01");

                // if (startDate > endDate) {
                //     os.warning('开始时间不能晚于结束时间，请重新选择。');
                //     $("#start").val(""); // 清空开始时间输入框
                //     $("#end").val("");   // 清空结束时间输入框
                //     return;
                // }

                //     console.log(   $("#start").val(),
                //         $("#end").val());
                // }
                active.reload("tables")

            }
        }



        window.warning = function (msg) {
            os.warning(msg);
        }
    });
</script>

<script type="text/html" id="tool">
    @if (this.Has((PermissionFlags)4))
    {
             <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="edit"> @T("编辑")</a>
    }
    @if (this.Has((PermissionFlags)8))
    {
             <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="del"> @T("删除")</a>
    }
</script>

<script type="text/html" id="online">
    {{# if(d.Online) { }}
      <i class="layui-icon layui-icon-ok" style="color: #16b777"></i>
    {{# } else { }}
      <i class="layui-icon layui-icon-close" style="color: #ff5722"></i>
    {{# } }}
</script>

<script type="text/html" id="switchTpl">
    @if (this.Has((PermissionFlags)4))
    {
            <input type="checkbox" name="status" data-id="{{d.Id}}" lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" {{ d.Enable == "1" ? 'checked' : ''}}>
    }
    else
    {
            <input type="checkbox" name="status" data-id="{{d.Id}}" lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" {{ d.Enable == "1" ? 'checked' : ''}} disabled>
    }
</script>

<script type="text/html" id="user-toolbar">
    @* <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        @T("新增")
    </button>  *@
</script>