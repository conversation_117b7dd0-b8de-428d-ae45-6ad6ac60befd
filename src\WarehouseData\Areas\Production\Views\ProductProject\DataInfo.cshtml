@{
    Html.AppendTitleParts(T("产品项目资料").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/components/dynamic-operation-column.js");

    // Css
    Html.AppendCssFileParts(ResourceLocation.Head, "~/css/components/dynamic-operation-column.css");
}

<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }

    xm-select {
        margin-top: 10px;
        line-height: 30px;
        min-height: 30px !important;
    }

    .layui-form-item {
        display: flex;
        flex-wrap: wrap;
        justify-content: center:
    }

    label {
        white-space: nowrap;
    }
</style>
<script asp-location="Head">
    var pleaseSelect = '@T("请选择")';
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<form class="layui-form dg-form">
    <div class="layui-form-item" style="margin-bottom: 15px; padding: 15px 10px; display: flex; flex-wrap: wrap; align-items: center; gap: 20px;">

@*         <div style="display: flex; align-items: center; white-space: nowrap;">
            <label class="layui-form-label" style="width: auto; margin: 0 8px 0 0; padding: 0;">@T("关键字")：</label>
            <div class="layui-input-inline" style="width: 180px; margin: 0;">
                <input type="text" name="Name" id="key" placeholder="@T("请输入名称或物料")" autocomplete="off" class="layui-input">
            </div>
        </div> *@
@* 
        <div class="layui-inline" style="padding-top: 28px;">
            <label class="layui-form-label" style="width: auto;margin:0px 0px 0 10px;">@T("产品资料类别")：</label>
            <div class="layui-inline" id="">
                <div class="layui-input-inline">
                    
                </div>
            </div>
        </div> *@

        <div style="display: flex; align-items: center; white-space: nowrap;">
            <label class="layui-form-label" style="width: auto; margin: 0 8px 0 0; padding: 0;">@T("资料")：</label>
            <div class="layui-input-inline" style="width: 180px; margin: 0;">
                <div id="demo3" style=" width: 100%;position:relative;bottom:5px"></div>
            </div>
        </div>

        <div style="display: flex; align-items: center; white-space: nowrap;">
            <label class="layui-form-label" style="width: auto; margin: 0 8px 0 0; padding: 0;">@T("状态")：</label>
            <div class="layui-input-inline" style="width: 180px; margin: 0;">
                <select id="Status" lay-filter="status">
                    <option value="-1">@T("请选择")</option>
                    <option value="0">@T("待审核")</option>
                    <option value="1">@T("审核中")</option>
                    <option value="2">@T("审核成功")</option>
                    <option value="3">@T("审核失败")</option>
                </select>
            </div>
        </div>

        

    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    // 设置变量保存选中行信息
    let ids = new Array();
    // 保存当前页全部数据id，点击全选时使用
    let tableIds = new Array();

    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;
        var laydate = layui.laydate;

        // 使用通用动态操作列组件
        var operationButtons = [
            @if (this.Has((PermissionFlags)512))
            {
                    @:{
                    @:    text: '@T("确认反审")',
                    @:    event: 'confirmReAudit',
                    @:    class: 'pear-btn pear-btn-primary',
                    @:    condition: function(d) { return d.ReAudit == 1 },
                    @:    alwaysShow: true
                    @:},
            }
            @if (this.Has((PermissionFlags)256))
            {
                    @:{
                    @:    text: '@T("申请反审")',
                    @:    event: 'reaudit',
                    @:    class: 'pear-btn pear-btn-warming',
                    @:    condition: function(d) { return d.Status == 2 && d.ReAudit != 1 },
                    @:    alwaysShow: true
                    @:},
            }
            @if (this.Has((PermissionFlags)64))
            {
                    @:{
                    @:    text: '@T("审核")',
                    @:    event: 'audit',
                    @:    class: 'pear-btn pear-btn-primary',
                    @:    condition: function(d) { return d.Status < 2 && @Model.SubmitStatus == 1 ; },
                    @:    alwaysShow: true
                    @:},
            }
           @if (this.Has((PermissionFlags)8))
            {
                    @:{
                    @:    text: '@T("删除")',
                    @:    event: 'del',
                    @:    class: 'pear-btn pear-btn-danger',
                    @:    condition: function(d) { return (d.Status < 2  && @Model.SubmitStatus == 0) || @Model.ReAudit == 2},
                    @:    alwaysShow: true
                    @:}
            }
        ];

        // 初始化动态操作列组件
        var operationColumnWidth = window.dynamicOperationColumn.init({
            buttons: operationButtons,
            tableId: 'tablist',
            debug: true  // 开启调试模式
        });

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("DataInfoList")?ProjectId='+'@Model.Id'
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 60
            , cols: [[
                  { type: 'checkbox', minWidth: 60 }
                , { field: 'Id', title: '@T("编号")', minWidth: 100 }
                , { field: 'ProductDataCategoryName1', title: '@T("资料类别")', minWidth: 120 }
                , { field: 'ProductDataCategoryName2', title: '@T("资料")', minWidth: 120 }
                , { title: '@T("类型")', minWidth: 80,templet:(d)=>{
                    if(d.Ptype == 0){
                        return  '@T("普通")'
                    }else if(d.Ptype == 1){
                        return  '@T("产测固件")'
                    }else if(d.Ptype == 2){
                        return  '@T("出货固件")'
                    }else if(d.Ptype == 3){
                        return  '@T("标签模板")'
                    }else{
                        return ''
                    }
                } }
                , { title: '@T("状态")', minWidth: 80,templet:(d)=>{
                    if(d.Status == 0){
                        return  '@T("待审核")'
                    }else if(d.Status == 1){
                        return  '@T("审核中")'
                    }else if(d.Status == 2){
                        return  '<span style="color:green">@T("审核成功")</span>'
                    }else if(d.Status == 3){
                        return  '<span style="color:red">@T("审核失败")</span>'
                    }
                } }
                , { field: 'FileName', title: '@T("文件名")', minWidth: 200 }
                , { title: '@T("文件路径")', minWidth: 280, templet:(d)=>{
                    if(!d.FilePath){
                        return ''
                    }
                    return `<a href="/${d.FilePath}" download="${d.FileName}" target="_blank">${d.FilePath}</a>`;
                }}
                , { field: 'CreateUser', title: '@T("创建者")', minWidth: 120 }
                , {
                    title: '@T("创建时间")', minWidth: 160, templet: (d) => {
                        if (d.CreateTime != undefined && d.CreateTime[0] != 0) {
                            return `<div>${d.CreateTime}</div>`
                        }
                        return `<div></div>`
                    }
                }
                , { field: 'Remark', title: '@T("备注")', minWidth: 140 }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: operationColumnWidth }
            ]]
            // , limit: 13
            // , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-170'
            , id: 'tables'
            , done: function (res) {
                // 设置当前页全部数据Id到全局变量
                // tableIds = res.data.map(function (value) {
                //     return value.Id;
                // });

                // 设置当前页选中项
                $.each(res.data, function (idx, val) {
                    if (ids.indexOf(val.Id) > -1) {
                        val["LAY_CHECKED"] = 'true';
                        //找到对应数据改变勾选样式，呈现出选中效果
                        let index = val['LAY_INDEX'];
                        $('tr[data-index=' + index + '] input[type="checkbox"]').click();
                        form.render('checkbox'); //刷新checkbox选择框渲染
                    }
                });
                // 获取表格勾选状态，全选中时设置全选框选中
                let checkStatus = table.checkStatus('tables');
                if (checkStatus.isAll) {
                    $('.layui-table-header th[data-field="0"] input[type="checkbox"]').prop('checked', true);
                    form.render('checkbox'); //刷新checkbox选择框渲染
                }

                // 使用通用组件应用操作列宽度
                window.dynamicOperationColumn.delayApplyWidth('tablist',300, true);
            },
        });

        // 监听勾选事件
        table.on('checkbox(tool)', function (obj) {
            if (obj.checked == true) {
                if (obj.type == 'one') {
                    ids.push(obj.data.Id);
                } else {
                    for (let i = 0; i < tableIds.length; i++) {
                        //当全选之前选中了部分行进行判断，避免重复
                        if (ids.indexOf(tableIds[i]) == -1) {
                            ids.push(tableIds[i]);
                        }
                    }
                }
            } else {
                if (obj.type == 'one') {
                    let i = ids.length;
                    while (i--) {
                        if (ids[i] == obj.data.Id) {
                            ids.splice(i, 1);
                        }
                    }
                } else {
                    let i = ids.length;
                    while (i--) {
                        if (tableIds.indexOf(ids[i]) != -1) {
                            ids.splice(i, 1);
                        }
                    }
                }
            }
        });

        let DataCategoryId = undefined;
        window.active = {
            reload: function () {
                table.reload('tables', {
                    where: {
                        Key: $("#key").val(),
                        Status: $("#Status").val(),
                        DataCategoryId: DataCategoryId === undefined ? '' : DataCategoryId
                    },
                    done: function(res, curr, count) {
                        try {
                            setTimeout(function() {
                            window.dynamicOperationColumn.delayApplyWidth('tablist', 300, true);
                                console.log('产品型号表格重载完成，已重新应用基于DOM测量的操作列宽度');
                            }, 300);
                        } catch (error) {
                            console.error('表格重载done回调中出错:', error);
                        }
                    }
                })
            }
        }

      var demo3 = xmSelect.render({
            el: '#demo3',
            radio: true, //设置单选
            name: 'DataCategoryId',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            disabled: false, // 设置禁用
            remoteMethod: function (val, cb, show, pageIndex)
            {  // 远程方法
                var obj = [];
                // 接口数据
                $.post('@Url.Action("SearchDataCategory2")', { keyword: val, page: pageIndex,ProjectId:'@Model.Id' }, function (res)
                {
                    // console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                            cb(res.data, res.extdata.PageCount);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });
            },
            on: function (data)
            {
                // 监听选择
                   if (data.arr.length == 0) {
                    DataCategoryId = undefined
                    ids = [];
                    if(DataCategoryId >= 0){
                        setTimeout(() => {
                            active.reload('tables')
                        }, 200);
                    }
                    return;
                }
                if (data.arr.length > 0) {
                    DataCategoryId = data.arr[0].value
                    ids = [];
                    setTimeout(() => {
                        active.reload('tables')
                    }, 200);
                }
            }
        });

        form.on('select(progress)', function (data) {
            ids = [];
            active.reload('tables')
        });

        form.on('select(status)', function (data) {
            ids = [];
            active.reload('tables')
        });

        form.on('select(hasRemark)', function (data) {
            ids = [];
            active.reload('tables')
        });

                        //监听开关事件
        form.on('switch(statusedit)', function (data) {
            //开关是否开启，true或者false
            var checked = data.elem.checked;
            //获取所属属性值
            var id = data.elem.attributes['data-id'].nodeValue;
            $.post('@Url.Action("ModifyState")', { id: id, status: checked }, function (res) {
                if (res.success) {
                    abp.notify.success(res.msg);
                }
                else {
                    abp.notify.error(res.msg);
                }
            });
        });

        // 监听输入订单
        $("#key").on("input", function (e) {
            ids = [];
            active.reload('tables', {
                where: { "orderId": $("#key").val() },
            })
        });

        table.on('toolbar(tool)', function (obj) {
            let data = obj.config
            var that = this
            if (obj.event === 'add') {
                window.add(data);
            } else if (obj.event === 'refresh') {
                active.reload();
            } else if (obj.event === 'sub1') {
                parent.layer.confirm('@T("确认提交吗")?',{btn:['@T("确认")','@T("取消")'],title:'@T("提示")'},function(index){
                    $.post('@Url.Action("SubmitPush")', { ProjectId: '@Model.Id' }, function (data) {
                        if (data.success) {
                            abp.notify.success(data.msg);
                            window.location.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                })
            } else if (obj.event === 'projectaudit') {
                window.projectaudit(data);
            } else if (obj.event === 'reapply') {
                 window.reapply(data);
            } else if (obj.event === 'comaudit') {
                 window.comaudit(data);
            }else if (obj.event === 'import') {
                 window.import(data);
            }else if (obj.event === 'sub2') {
                parent.layer.confirm('@T("确认反提交吗")?',{btn:['@T("确认")','@T("取消")'],title:'@T("提示")'},function(index){
                    $.post('@Url.Action("SubmitPush")', { ProjectId: '@Model.Id' }, function (data) {
                        if (data.success) {
                            abp.notify.success(data.msg);
                            window.location.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                })
            }
        });

        window.reaudit = function (data) {
            layer.open({
                type: 2,
                title: "@T("申请")",
                content: "@Url.Action("DataApplyReAudit")?ProjectDataId="+data.Id,
                area: ["460px", "336px"],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    window['layui-layer-iframe' + index].submitForm();
                },
                end:function(){
                    window.location.reload();
                }
            });
        }

        window.import = function (data) {
            window.importPageIndex = top.layui.dg.popupRight({
                id: 'Import'
                , title: ' @T("导入")'
                , closeBtn: 1
                , area: ['580px']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("DataInfoImport")' + abp.utils.formatString("?ProjectId={0}", '@Model.Id') + '" frameborder="0" class="layadmin-iframe" name="iframe' + data.index + '"></iframe>');
                }
            });
            window.name = 'ProductProjectData';
        }

        window.comaudit = function () {
            layer.open({
                type: 2,
                title: "@T("确认")",
                content: "@Url.Action("ConfirmReAudit")?ProjectId="+"@Model.Id",
                area: ["460px", "336px"],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    window['layui-layer-iframe' + index].submitForm();
                },
                end:function(){
                    window.location.reload();
                }
            });
        }

        window.reapply = function () {
            layer.open({
                type: 2,
                title: "@T("申请")",
                content: "@Url.Action("ApplyReAudit")?ProjectId="+"@Model.Id",
                area: ["460px", "336px"],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    window['layui-layer-iframe' + index].submitForm();
                },
                end:function(){
                    window.location.reload();
                }
            });
        }

        window.projectaudit = function () {
            layer.open({
                type: 2,
                title: "@T("审核")",
                content: "@Url.Action("Audit")?ProjectId="+"@Model.Id",
                area: ["460px", "336px"],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    window['layui-layer-iframe' + index].submitForm();
                },
                end:function(){
                    window.location.reload();
                }
            });
        }

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("DeleteDataInfo")', { Id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success(data.msg);
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'edit') {
                window.edit(data);
            } else if (obj.event === 'config') {
                window.config(data);
            }else if (obj.event === 'audit') {
                window.audit(data);
            }else if (obj.event === 'reaudit') {
                 window.reaudit(data);
            }else if (obj.event === 'confirmReAudit') {
                 window.confirmReAudit(data);
            }
        });

        window.confirmReAudit = function (data) {
            layer.open({
                type: 2,
                title: "@T("确认")",
                content: "@Url.Action("DataConfirmReAudit")?ProjectDataId="+data.Id,
                area: ["460px", "336px"],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    window['layui-layer-iframe' + index].submitForm();
                },
                end:function(){
                    window.location.reload();
                }
            });
        }

        window.audit = function (data) {
            layer.open({
                type: 2,
                title: "@T("审核")",
                content: "@Url.Action("AuditDataInfo")?Id="+data.Id,
                area: ["460px", "336px"],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    window['layui-layer-iframe' + index].submitForm();
                },
                end:function(){
                    window.location.reload();
                }
            });
        }

        window.config = function (data) {
            top.layui.dg.popupRight({
                id: 'config'
                , title: ' @T("配置")'
                , closeBtn: 1
                , area: ['1280px']
                , success: function (obj, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Config")' + abp.utils.formatString("?ProjectId={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        window.saveCallback = function (data) {
            console.log(data);
            layer.close(data.index);
            abp.notify.success(data.msg);
            table.reload("tables");
        }

        window.add = function () {
            layer.open({
                type: 2,
                title: "@T("添加产品项目资料")",
                content: "@Url.Action("AddDataInfo")?ProjectId="+"@Model.Id",
                area: ["460px", "336px"],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    window['layui-layer-iframe' + index].submitForm();
                }
            });
        }

        window.edit = function (data) {
            top.layui.dg.popupRight({
                id: 'Edit'
                , title: ' @T("编辑")'
                , closeBtn: 1
                , area: ['580px']
                , success: function (obj, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Update")' + abp.utils.formatString("?Id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        function checkDateValidity() {
            var startValue = $("#start").val();
            var endValue = $("#end").val();

            if (startValue && endValue) {
                ids = [];
                active.reload("tables")

            }
        }

        window.warning = function (msg) {
            os.warning(msg);
        }
    });
</script>
<script type="text/html" id="tool">
    <div class="operation-column">
        {{#  layui.each(window.operationButtons, function(index, button){ }}
            {{#  var isEnabled = button.condition(d); }}
            {{#  var buttonClass = button.class + ' pear-btn-xs'; }}
            {{#  if(!isEnabled){ }}
                {{#  buttonClass += ' disabled-button'; }}
            {{#  } }}
            <a class="{{buttonClass}}" lay-event="{{isEnabled ? button.event : 'disabled'}}"
               title="{{!isEnabled ? '当前状态下不可操作' : ''}}"
               data-enabled="{{isEnabled}}">{{button.text}}</a>
        {{#  }); }}
    </div>
</script>
<script type="text/html" id="user-toolbar">
    @if (this.Has((PermissionFlags)2))
    {
        @if(Model.SubmitStatus == 0)
        {
                <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
                    <i class="layui-icon layui-icon-add-1"></i>
                    @T("新增")
                </button>
        }
        @if(Model.AuditStatus < 2)
        {
            @if(Model.SubmitStatus == 0)
            {
                <button class="pear-btn pear-btn-warming pear-btn-md" lay-event="sub1">@T("提交审核")</button>
            }
            @if(Model.SubmitStatus == 1 && Model.AuditStatus == 0)
            {
                <button class="pear-btn pear-btn-warming pear-btn-md" lay-event="sub2">@T("反提交")</button>
            }
        }
    }
    @if (this.Has((PermissionFlags)128))
    {
        @if (Model.SubmitStatus == 1 && Model.AuditStatus <= 1) 
        { 
                <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="projectaudit">@T("项目审核")</button>
        }
    }
    @if (this.Has((PermissionFlags)256))
    {
        @if(Model.AuditStatus != 0 && Model.ReAudit == 0)
        {
            <button class="pear-btn pear-btn-warming pear-btn-md" lay-event="reapply">
                @T("申请反审")
             </button>
        }

    }
    @if (this.Has((PermissionFlags)512))
    {
        @if (Model.ReAudit == 1)
        {
                <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="comaudit">
                    @T("确认反审")
                </button>
        }
    }
    @if (this.Has((PermissionFlags)1024))
    {
        @if(Model.SubmitStatus == 0)
        {
            <button class="pear-btn pear-btn-warming pear-btn-md" lay-event="import">
                @T("导入")
            </button>
        }
    }
    
</script>
<script type="text/html" id="switchTpl">
    <input type="checkbox" name="status" data-id="{{d.Id}}" lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" {{ d.Status ? 'checked' : ''}}>
</script>