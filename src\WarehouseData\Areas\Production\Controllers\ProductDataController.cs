﻿using DG.Web.Framework;
using DH;
using DH.Entity;
using HlktechIoT.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using Pek;
using Pek.Helpers;
using Pek.Models;
using Pek.Webs;
using System.ComponentModel;
using XCode;
using XCode.Membership;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Production.Controllers
{
    /// <summary>产品资料</summary>
    [DisplayName("产品资料")]
    [Description("产品资料")]
    [ProductionArea]
    [DHMenu(82, ParentMenuName = "ProductionsManager", ParentMenuDisplayName = "生产管理", ParentMenuUrl = "", ParentMenuOrder = 30, ParentIcon = "layui-icon-component", CurrentMenuUrl = "~/{area}/ProductData", CurrentMenuName = "ProductDataList", LastUpdate = "20250616")]
    public class ProductDataController : BaseAdminControllerX
    {
        protected static Int32 MenuOrder { get; set; } = 82;

        /// <summary>
        /// 产品资料列表
        /// </summary>
        /// <returns></returns>
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 产品资料列表
        /// </summary>
        /// <param name="page">页码</param>
        /// <param name="limit">条数</param>
        /// <param name="start">开始时间</param>
        /// <param name="end">结束时间</param>
        /// <param name="ProductDataCategoryId2">资料子分类</param>
        /// <param name="Ptype">类型</param>
        /// <param name="Name">关键字</param>
        /// <returns></returns>
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult GetList(Int32 page, Int32 limit, DateTime start, DateTime end, Int32 ProductDataCategoryId2,String Name, Int32 Ptype = -1)
        {
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "ProductDataCategoryId2",
                Desc = true,
            };

            Int32 UId = -1;

            var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
            if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",0003,")))
            {
                UId = ManageProvider.User?.ID ?? 0;
            }

            var data = ProductData.Search(Ptype,UId, 0, ProductDataCategoryId2, start, end, Name, pages).Select(e => new
            {
                Id = e.Id.SafeString(),
                e.FilePath,
                e.Remark,
                ProductDataCategoryName1 = ProductDataCategory.FindById(e.ProductDataCategoryId1)?.Name,
                ProductDataCategoryName2 = ProductDataCategory.FindById(e.ProductDataCategoryId2)?.Name,
                e.CreateTime,
                e.CreateUser,
                e.Ptype,
                e.Version,
                e.FileName,
            });

            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
        }

        /// <summary>
        /// 搜索产品资料分类
        /// </summary>
        /// <param name="keyword">关键字</param>
        /// <param name="page">页码</param>
        /// <param name="Id">分类id</param>
        /// <returns></returns>
        [DisplayName("搜索资料类别")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult SearchDataCategory(String keyword, Int32 page, Int32 Id)
        {
            var res = new DResult();
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = 10,
                RetrieveTotalCount = true,
                Sort = ProductDataCategory._.Id,
            };

            var exp = new WhereExpression();
            exp &= ProductDataCategory._.ParentId > 0 & ProductDataCategory._.Status == true;

            res.data = ProductDataCategory.FindAll(exp, pages).Select(e =>
            {
                var p = ProductDataCategory.FindById(e.ParentId);
                return new
                {
                    name = e.Name + $"({p?.Name})",
                    value = e.Id,
                };
            });

            var model = ProductDataCategory.FindById(Id);
            if (model != null)
            {
                var p = ProductDataCategory.FindById(model.ParentId);
                res.extdata = new { pages.PageCount, data = new List<object>() { new { name = model.Name + $"({p?.Name})", value = model.Id } } };
            }
            else
            {
                res.extdata = new { pages.PageCount };
            }

            res.success = true;

            return Json(res);
        }

        /// <summary>
        /// 添加产品资料
        /// </summary>
        /// <returns></returns>
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add()
        {
            return View();
        }

        /// <summary>
        /// 上传资料文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("上传资料")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult UploadFiles(IFormFile file)
        {
            var res = new DResult();

            try
            {
                if (file == null)
                {
                    XTrace.WriteLine("获取到的文件为空");

                    res.msg = GetResource("资料上传有误");
                    return Json(res);
                }

                var OrignfileName = file.FileName;

                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(OrignfileName)}";
                //var filename = $"{OrignfileName}";
                var filepath = DHSetting.Current.UploadPath.CombinePath($"ProductData/{filename}");
                var saveFileName = filepath.GetFullPath();
                var f = saveFileName.AsFile();
                if (f.Exists)
                {
                    f.Delete();
                }
                saveFileName.EnsureDirectory();
                file.SaveAs(saveFileName);
                res.msg = GetResource("资料上传成功");
                res.success = true;
                res.data = new { OriginFileName = OrignfileName, FilePath = filepath.Replace("\\", "/") };
                return Json(res);
            }
            catch (Exception ex)
            {
                res.msg = GetResource("资料上传异常");
                XTrace.WriteLine($"资料上传异常：{ex.ToString()}");
                return Json(res);
            }
        }

        /// <summary>
        /// 添加产品资料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add(String FilePath,Int32 ProductDataCategoryId,String Remark,Int32 Ptype,String Version,String FileName)
        {
            DResult res = new();
            if (ProductDataCategoryId <= 0)
            {
                res.msg = GetResource("产品资料类别不能为空");
                return Json(res);
            }
            if (FilePath.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("产品资料文件不能为空");
                return Json(res);
            }
            var modelProductDataCategory = ProductDataCategory.FindById(ProductDataCategoryId);
            if (modelProductDataCategory == null)
            {
                res.msg = GetResource("产品资料类别信息不存在");
                return Json(res);
            }     

            var model = new ProductData()
            {
                FilePath = FilePath,
                ProductDataCategoryId1 = modelProductDataCategory.ParentId,
                ProductDataCategoryId2 = modelProductDataCategory.Id,
                Ptype = Ptype,
                Remark = Remark,
                Version = Version,
                FileName = FileName,
            };
            model.Insert();
            res.success = true;
            res.msg = GetResource("添加成功");
            return Json(res);
        }

        /// <summary>
        /// 编辑产品资料
        /// </summary>
        /// <param name="Id">资料id</param>
        /// <returns></returns>
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Update(Int32 Id)
        {
            var model = ProductData.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("产品资料不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 编辑产品资料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Update(Int32 Id, String FilePath, Int32 ProductDataCategoryId, String Remark,Int32 Ptype,String Version,String FileName)
        {
            DResult res = new DResult();
            if (ProductDataCategoryId <= 0)
            {
                res.msg = GetResource("产品资料类别不能为空");
                return Json(res);
            }
            if (FilePath.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("产品资料文件不能为空");
                return Json(res);
            }
            var modelProductDataCategory = ProductDataCategory.FindById(ProductDataCategoryId);
            if (modelProductDataCategory == null)
            {
                res.msg = GetResource("产品资料类别信息不存在");
                return Json(res);
            }
            var model = ProductData.FindById(Id);
            if(model == null)
            {
                res.msg = GetResource("产品资料信息不存在");
                return Json(res);
            }
            if (ProductProjectData.FindCount(ProductProjectData._.ProductDataId == model.Id & ProductProjectData._.Status == 2) > 0)
            {
                res.msg = GetResource("产品资料已被项目使用 无法操作");
                return Json(res);
            }

            if(FilePath != model.FilePath)
            {
                var file = model.FilePath!.GetFullPath().AsFile();
                if (file.Exists)
                {
                    file.Delete();
                }
            }

            model.FilePath = FilePath;
            model.ProductDataCategoryId1 = modelProductDataCategory.ParentId;
            model.ProductDataCategoryId2 = modelProductDataCategory.Id;
            model.Remark = Remark;
            model.Ptype = Ptype;
            model.Version = Version;
            model.FileName = FileName;
            model.Update();
            res.success = true;
            res.msg = GetResource("编辑成功");
            return Json(res);
        }

        /// <summary>
        /// 删除产品资料
        /// </summary>
        /// <param name="Id">资料id</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("删除")]
        [EntityAuthorize(PermissionFlags.Delete)]
        public IActionResult Delete(Int32 Id)
        {
            var res = new DResult();

            var model = ProductData.FindById(Id);
            if (model != null)
            {
                var list = ProductProjectData.FindAllByProductDataId(Id);
                if(list.Count() > 0)
                {
                    res.msg = GetResource("资料关联了项目 无法删除");
                    return Json(res);
                }

                var file = model.FilePath!.GetFullPath().AsFile();
                if (file.Exists)
                {
                    file.Delete();
                }
                model.Delete();
            }
            else
            {
                res.msg = GetResource("产品资料不存在");
                return Json(res);
            }

            res.success = true;
            res.msg = GetResource("删除成功");
            return Json(res);
        }

        /// <summary>
        /// 类别管理
        /// </summary>
        /// <returns></returns>
        [DisplayName("类别管理")]
        [EntityAuthorize((PermissionFlags)16)]
        public IActionResult Category()
        {
            return View();
        }

        /// <summary>
        /// 类别管理
        /// </summary>
        /// <returns></returns>
        [DisplayName("类别管理")]
        [EntityAuthorize((PermissionFlags)16)]
        public IActionResult CategoryList(Int32 ParentId, String Name, Int32 page, Int32 limit)
        {
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "Id",
                Desc = true,
            };
            var data = ProductDataCategory.Search(Name, ParentId, null, DateTime.MinValue, DateTime.MinValue, "", pages);
            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
        }

        /// <summary>
        /// 子类别管理
        /// </summary>
        /// <returns></returns>
        [DisplayName("类别管理")]
        [EntityAuthorize((PermissionFlags)16)]
        public IActionResult CategoryChild(Int32 Id)
        {
            var model = ProductDataCategory.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("数据不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 添加类别
        /// </summary>
        /// <returns></returns>
        [DisplayName("添加类别")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult AddCategory()
        {
            return View();
        }

        /// <summary>
        /// 添加子类别
        /// </summary>
        /// <returns></returns>
        [DisplayName("添加子类别")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult AddCategoryChild(Int32 Id)
        {
            var model = ProductDataCategory.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("数据不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 添加类别
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("添加类别")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult AddCategory(String Name, Boolean Status, String Remark, Int32 ParentId, Boolean Must)
        {
            DResult res = new();
            if (Name.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("名称不能为空");
                return Json(res);
            }
            var model = ProductDataCategory.FindByName(Name);
            if (model != null)
            {
                res.msg = GetResource("名称已存在");
                return Json(res);
            }
            model = new ProductDataCategory
            {
                Name = Name,
                Status = Status,
                Must = Must,
                Remark = Remark,
                ParentId = ParentId,
            };
            model.Insert();
            res.success = true;
            res.msg = GetResource("添加成功");
            return Json(res);
        }

        /// <summary>
        /// 编辑类别
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [DisplayName("编辑类别")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult UpdateCategory(Int32 Id)
        {
            var model = ProductDataCategory.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("数据不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 编辑子类别
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [DisplayName("编辑子类别")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult UpdateCategoryChild(Int32 Id)
        {
            var model = ProductDataCategory.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("数据不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 编辑类别
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("编辑类别")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult UpdateCategory(String Name, Boolean Status, String Remark, Int32 Id, Boolean Must)
        {
            DResult res = new();
            if (Name.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("名称不能为空");
                return Json(res);
            }
            var model = ProductDataCategory.FindByName(Name);
            if (model != null && model.Id != Id)
            {
                res.msg = GetResource("名称已存在");
                return Json(res);
            }

            model = ProductDataCategory.FindById(Id);
            if (model == null)
            {
                res.msg = GetResource("数据不存在");
                return Json(res);
            }
            model.Name = Name;
            model.Status = Status;
            model.Remark = Remark;
            model.Must = Must;
            model.Update();
            res.success = true;
            res.msg = GetResource("编辑成功");
            return Json(res);
        }

        /// <summary>
        /// 修改类别启用状态
        /// </summary>
        /// <param name="Id">分类id</param>
        /// <param name="Status">状态</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("修改状态")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult ModifyState(Int32 Id, Boolean Status)
        {
            var result = new DResult();

            var model = ProductDataCategory.FindById(Id);
            if (model == null)
            {
                result.msg = GetResource("状态调整出错");
                return Json(result);
            }

            var list = ProductDataCategory.FindAllByParentId(Id);
            foreach (var item in list)
            {
                item.Status = Status;
                item.Update();
            }

            model.Status = Status;
            model.Update();

            result.success = true;
            result.msg = GetResource("状态调整成功");

            return Json(result);
        }

        /// <summary>
        /// 修改类别必选状态
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("修改状态")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult ModifyMust(Int32 Id, Boolean Status)
        {
            var result = new DResult();

            var model = ProductDataCategory.FindById(Id);
            if (model == null)
            {
                result.msg = GetResource("状态调整出错");
                return Json(result);
            }

            var list = ProductDataCategory.FindAllByParentId(Id);
            foreach (var item in list)
            {
                item.Status = Status;
                item.Update();
            }

            model.Must = Status;
            model.Update();

            result.success = true;
            result.msg = GetResource("状态调整成功");

            return Json(result);
        }

        /// <summary>
        /// 删除类别
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("删除类别")]
        [EntityAuthorize(PermissionFlags.Delete)]
        public IActionResult DeleteCategory(Int32 Id)
        {
            var res = new DResult();
            var count = ProductData.FindCount(ProductData._.ProductDataCategoryId2 == Id | ProductData._.ProductDataCategoryId1 == Id);
            if (count > 0)
            {
                res.msg = GetResource("该类别下有产品资料，不能删除");
                return Json(res);
            }
            var num = ProductDataCategory.FindCount(ProductDataCategory._.ParentId == Id);
            if (num > 0)
            {
                res.msg = GetResource("该类别下有子类别 请先删除子类别");
                return Json(res);
            }
            ProductDataCategory.Delete(ProductDataCategory._.Id == Id);
            res.success = true;
            res.msg = GetResource("删除成功");
            return Json(res);

        }
    }
}
