﻿@{
    Html.AppendTitleParts(T("数据库管理").Text);
}

<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }
</style>

<script asp-location="Head">
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<div class="layui-card">
    <div class="layui-card-header">@T("数据库管理")</div>
    <div class="layui-card-body">
        <div class="table-body">
            <table class="layui-hide" id="tablist" lay-filter="tool"></table>
        </div>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetPage")'
            //, page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 80
            , cols: [[
                { field: 'Name', title: '@T("名称")' }
                , { field: 'Type', title: '@T("类型")', toolbar: '#Type' }
                , { field: 'ConnStr', title: '@T("链接字符串")', sort: true }
                , { field: 'Version', title: '@T("版本")', sort: true, width: 100 }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', align: "center", width: 262 }
            ]]
            //, limit: 16
            , limits: [10, 16, 20, 30, 50, 100]
            , height: 'full-106'
            , id: 'tables'
        });

        var active = {
            reload: function () {
                table.reload('tables');
            }
        }

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === "Backup") {
                $.post("@Url.Action("Backup")", { dbName: data.Name }, (res) => {
                    if (res.success) {
                        os.success('@T("备份成功")');
                    } else {
                        os.warning(data.msg);
                    }
                })
            } else if (obj.event === "info") {
                $.post("@Url.Action("BackupAndCompress")", { dbName: data.Name }, (res) => {
                    if (res.success) {
                        os.success('@T("备份并压缩成功")');
                    } else {
                        os.warning(data.msg);
                    }
                })
            } else if (obj.event === "Download") {
                var href = '@Url.Action("Download")?dbName=' + data.Name;
                $(this).attr("href", href);
            } else if (obj.event === "Edit") {
                layuiIndex = os.OpenNoTop('@T("编辑数据库链接")', "@Url.Action("EditSetting")?Name=" + data.Name, '420px', '350px', function () {
                    if ($("#state").val() == 1) abp.notify.success("@T("编辑成功")");
                    active.reload();
                });
            } else if (obj.event === "Manage") {
                layuiIndex = os.Open('@T("管理")', "@Url.Action("Manage")?Name=" + data.Name, '100%', '100%');
            }
        })

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'refresh') {
                active.reload();
            }
        });

    });
</script>

<script type="text/html" id="tool">
    @*<a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="Edit"> @T("编辑")</a>*@
    @if (this.Has((PermissionFlags)1)){
    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="Backup"> @T("备份")</a>
    }
    @if (this.Has((PermissionFlags)1)){
    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="info"> @T("备份并压缩")</a>
    }
    @if (this.Has((PermissionFlags)1)){
    <a href="" class="pear-btn pear-btn-primary pear-btn-xs" lay-event="Download"> @T("下载")</a>
    }
    @if (this.Has((PermissionFlags)64))
    {
        <a href="" class="pear-btn pear-btn-primary pear-btn-xs" lay-event="Manage"> @T("管理")</a>
    }
</script>

<script type="text/html" id="Type">
    {{# if(d.Type == "6") { }}
    <span class="layui-badge layui-bg-green">@T("SQLite")</span>
    {{# } else if(d.Type == "4") { }}
    <span class="layui-badge layui-bg-cyan"> @T("MySql")</span>
    {{# } }}
</script>