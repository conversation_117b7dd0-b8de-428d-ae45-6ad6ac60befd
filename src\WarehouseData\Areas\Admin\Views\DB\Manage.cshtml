﻿@using System.Text.Json
@using XCode.DataAccessLayer
@{
    Html.AppendTitleParts(T("管理").Text);
    List<HlktechIoT.Areas.Admin.Controllers.DBController.TableViewModel> tableViewModels = Model.Tables;
    var primaryKeyColumns = new Dictionary<string, string>();
}
<style asp-location="true">
    .width {
        width: 230px
    }

    .layui-form-item {
        margin-bottom: 10px;
    }

    .layui-form-select {
        width: 230px
    }

        .layui-form-select .layui-edge {
            right: 10px;
        }

    .layui-input-inline.inlineSelect {
        width: 230px;
    }

    .container {
        display: flex;
    }

    .tables-list {
        width: 20%;
        border-right: 1px solid #ccc;
        padding: 10px;
        max-height: 90vh; /* 设置最大高度 */
        overflow-y: auto; /* 启用垂直滚动条 */
    }

    .content {
        width: 80%;
        padding: 10px;
    }

    .sql-input {
        margin-bottom: 20px;
    }

    .results {
        border-top: 1px solid #ccc;
        padding-top: 10px;
        max-height: 70vh; /* 设置最大高度 */
        overflow-y: auto; /* 启用垂直滚动条 */
    }

        .results table {
            width: 100%;
            border-collapse: collapse;
        }

        .results th, .results td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: left;
        }

    .toggle-button {
        cursor: pointer;
        padding: 5px;
        background-color: #ccc;
        border: 1px solid #999;
        margin-bottom: 10px;
        text-align: center;
    }

    .tables-list ul {
        display: none; /* 初始状态下隐藏表名列表 */
    }

    .tables-list.expanded ul {
        display: block; /* 展开状态下显示表名列表 */
    }

    .arrow {
        display: inline-block;
        margin-right: 5px;
        transition: transform 0.3s;
    }

        .arrow.right {
            transform: rotate(0deg);
        }

        .arrow.down {
            transform: rotate(90deg);
        }

    .table-details {
        padding-left: 20px;
        background-color: #f9f9f9;
    }

    .columns-list, .indexes-list {
        padding-left: 20px;
        background-color: #f1f1f1;
    }

        .columns-list li, .indexes-list li {
            padding-left: 10px;
        }

    .context-menu {
        display: none;
        position: absolute;
        z-index: 1000;
        background-color: #fff;
        border: 1px solid #ccc;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }

    .context-menu-item {
        padding: 8px 12px;
        cursor: pointer;
    }

        .context-menu-item:hover {
            background-color: #f1f1f1;
        }
</style>

<div class="container">
    <div class="context-menu" id="context-menu">
        <div class="context-menu-item" id="delete-row">删除行</div>
        <div class="context-menu-item" id="copy-mysql">复制为SQLite/MySQL语句</div>
        <div class="context-menu-item" id="copy-sqlserver">复制为SQL Server语句</div>
        <div class="context-menu-item" id="copy-postgresql">复制为PostgreSQL语句</div>
    </div>
    <div class="tables-list" id="tables-list">
        <h3 id="tables-header">
            <span class="arrow right" id="arrow-icon">▶</span>Tables
        </h3>
        <ul>
            @* 这里可以动态生成表列表 *@
            @foreach (var table in tableViewModels)
            {
                <li>
                    @if (table.Columns.Count > 0 || table.Indexes.Count > 0)
                    {
                        <span class="arrow right table-arrow" data-table="@table.TableName">▶</span>
                    }
                    <a href="#" class="table-link" data-table="@table.TableName">@table.TableName</a>
                    @if (table.Columns.Count > 0 || table.Indexes.Count > 0)
                    {
                        <ul class="table-details" data-table="@table.TableName" style="display: none;">
                            @if (table.Columns.Count > 0)
                            {
                                <li>
                                    <span class="arrow right columns-arrow" data-table="@table.TableName">▶</span>Columns
                                    <ul class="columns-list" data-table="@table.TableName" style="display: none;">
                                        @foreach (IDataColumn column in table.Columns)
                                        {
                                            <li>@column.Name</li> @* 假设 column.Name 是字段的名称 *@
                                        }
                                    </ul>
                                </li>
                            }
                            @if (table.Indexes.Count > 0 || table.PRIMARY.Count > 0)
                            {
                                <li>
                                    <span class="arrow right indexes-arrow" data-table="@table.TableName">▶</span>Indexes
                                    <ul class="indexes-list" data-table="@table.TableName" style="display: none;">
                                        @foreach (IDataIndex index in table.Indexes)
                                        {
                                            <li>@index.Name</li> @* 假设 index.Name 是索引的名称 *@
                                        }
                                        @if (table.PRIMARY.Count > 0)
                                        {
                                            @* 在循环中处理 primaryKeyColumns *@
                                            if (table.PRIMARY.Count > 0)
                                            {
                                                primaryKeyColumns[table.TableName] = table.PRIMARY[0].Name;
                                            }
                                            <li>PRIMARY
                                                <ul>
                                                    @foreach (var primary in table.PRIMARY)
                                                    {
                                                        <li>@primary.Name</li> @* 假设 primary.Name 是主键字段的名称 *@
                                                    }
                                                </ul>
                                            </li>
                                        }
                                    </ul>
                                </li>
                            }
                        </ul>
                    }
                </li>
            }
        </ul>
    </div>
    <div class="content">
        <div class="sql-input">
            <h3>SQL 语句</h3>
            <textarea id="sql-textarea" class="layui-textarea" rows="5" readonly></textarea>
            <input type="hidden" id="db-name" name="Name" value="@Model.Name" />
            <button id="execute-btn" class="layui-btn">执行</button>
            <select id="export-format-select" class="layui-select">
                <option value="csv">导出到CSV</option>
                <option value="excel">导出到Excel</option>
            </select>
            <button id="export-btn" class="layui-btn">导出</button>
        </div>
        <div class="results">
            <h3>结果</h3>
            <div id="results-container">
                @* 这里可以动态生成查询结果 *@
            </div>
            <div class="pagination">
                <label for="page-size-select">每页显示:</label>
                <select id="page-size-select" class="layui-select">
                    <option value="10">10</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                    <option value="200">200</option>
                    <option value="300">300</option>
                    <option value="400">400</option>
                    <option value="500">500</option>
                    <option value="1000">1000</option>
                    <option value="2000">2000</option>
                    <option value="5000">5000</option>
                    <option value="10000">10000</option>
                    <option value="50000">50000</option>
                    <option value="0">不限制</option>
                </select>
                <button id="prev-page-btn" class="layui-btn">上一页</button>
                <span id="page-info">第 1 页，共 1 页</span> <!-- 添加显示总页数的元素 -->
                <button id="next-page-btn" class="layui-btn">下一页</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        var tableLinks = document.querySelectorAll(".table-link");
        var currentPage = 1;
        var pageSize = 10; // 默认每页显示10条记录
        var contextMenu = document.getElementById("context-menu");
        var deleteRowItem = document.getElementById("delete-row");
        document.getElementById("copy-mysql").addEventListener("click", function () {
            copyRowAsSql('mysql');
        });

        document.getElementById("copy-sqlserver").addEventListener("click", function () {
            copyRowAsSql('sqlserver');
        });

        document.getElementById("copy-postgresql").addEventListener("click", function () {
            copyRowAsSql('postgresql');
        });
        var targetRow;
        var sqlTextarea = document.getElementById("sql-textarea");
        var executeBtn = document.getElementById("execute-btn");
        var pageSizeSelect = document.getElementById("page-size-select");
        var dbName = document.getElementById("db-name").value;
        var tablesHeader = document.getElementById("tables-header");
        var tablesList = document.getElementById("tables-list");
        var arrowIcon = document.getElementById("arrow-icon");
        var tableArrows = document.querySelectorAll(".table-arrow");
        var columnsArrows = document.querySelectorAll(".columns-arrow");
        var indexesArrows = document.querySelectorAll(".indexes-arrow");
        var currentTableName = "";
        var primaryKeyColumn = "";
        var currentFilterQuery = ""; // 添加全局变量保存当前的筛选条件

        document.addEventListener("contextmenu", function (event) {
            if (event.target.closest("td")) {
                event.preventDefault();
                targetRow = event.target.closest("td");
                contextMenu.style.display = "block";
                contextMenu.style.left = event.pageX + "px";
                contextMenu.style.top = event.pageY + "px";
            } else {
                contextMenu.style.display = "none";
            }
        });

        document.addEventListener("click", function () {
            contextMenu.style.display = "none";
        });

        deleteRowItem.addEventListener("click", function () {
            if (targetRow) {
                var id = targetRow.getAttribute('data-id');
                deleteRow(id);
            }
        });

        function copyRowAsSql(dbType) {
            if (!targetRow) return;

            var row = targetRow.parentElement;
            var cells = row.querySelectorAll("td.editable");
            var columns = [];
            var values = [];

            cells.forEach(function (cell) {
                var column = cell.getAttribute("data-column");
                var value = cell.textContent.replace(/'/g, "''"); // 转义单引号
                columns.push(column);
                values.push(`'${value}'`);
            });

            var sql = "";
            switch (dbType) {
                case 'mysql':
                    sql = `INSERT INTO \`${currentTableName}\` (\`${columns.join("`, `")}\`) VALUES (${values.join(", ")});`;
                    break;
                case 'sqlserver':
                    sql = `INSERT INTO [${currentTableName}] ([${columns.join("], [")}]) VALUES (${values.join(", ")});`;
                    break;
                case 'postgresql':
                    sql = `INSERT INTO "${currentTableName}" ("${columns.join('", "')}") VALUES (${values.join(", ")});`;
                    break;
            }

            navigator.clipboard.writeText(sql).then(function () {
                alert(`SQL语句已复制到剪贴板！`);
            }, function (err) {
                alert("复制失败：" + err);
            });
        }

        pageSizeSelect.addEventListener("change", function () {
            pageSize = parseInt(this.value);
            currentPage = 1; // 重置为第一页
            totalPages = pageSize > 0 ? Math.ceil(totalRecords / pageSize) : 1; // 重新计算总页数
            updatePaginationButtons();
            updateSqlTextarea();
            executeBtn.click(); // 模拟点击执行按钮
        });

        // 保存主键列名的字典
        var primaryKeyColumns = @Html.Raw(JsonSerializer.Serialize(primaryKeyColumns));

        tableLinks.forEach(function (link) {
            link.addEventListener("click", function (event) {
                event.preventDefault();
                currentTableName = this.getAttribute("data-table");
                currentPage = 1;
                updateSqlTextarea();
                currentFilterQuery = ""; // 重置筛选条件"
                executeBtn.click();
            });
        });

        tableArrows.forEach(function (arrow) {
            arrow.addEventListener("click", function () {
                var tableName = this.getAttribute("data-table");
                var tableDetails = document.querySelector('.table-details[data-table="' + tableName + '"]');
                tableDetails.style.display = tableDetails.style.display === "none" ? "block" : "none";
                this.classList.toggle("right");
                this.classList.toggle("down");
            });
        });

        columnsArrows.forEach(function (arrow) {
            arrow.addEventListener("click", function () {
                var tableName = this.getAttribute("data-table");
                var columnsList = document.querySelector('.columns-list[data-table="' + tableName + '"]');
                columnsList.style.display = columnsList.style.display === "none" ? "block" : "none";
                this.classList.toggle("right");
                this.classList.toggle("down");
            });
        });

        indexesArrows.forEach(function (arrow) {
            arrow.addEventListener("click", function () {
                var tableName = this.getAttribute("data-table");
                var indexesList = document.querySelector('.indexes-list[data-table="' + tableName + '"]');
                indexesList.style.display = indexesList.style.display === "none" ? "block" : "none";
                this.classList.toggle("right");
                this.classList.toggle("down");
            });
        });

        executeBtn.addEventListener("click", function () {
            var sqlQuery = sqlTextarea.value;

            // 简单的SQL注入检测
            if (sqlQuery.toLowerCase().includes('drop') || sqlQuery.toLowerCase().includes('delete')) {
                alert('检测到潜在的SQL注入风险，请修改SQL语句。');
                return;
            }

            // 获取当前的过滤查询，currentFilterQuery为sqlQuery中WHERE开始的字符串
            var match = sqlQuery.match(/WHERE\s+(.+?)(?=\s+LIMIT|$)/i);
            if (match) {
                currentFilterQuery = " " + match[0];
            }

            // 执行 fetchRowCount
            fetchRowCount(currentFilterQuery);

            // 发送 AJAX 请求
            fetch('@Url.Action("Manage")', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    dbName: dbName,
                    sqlQuery: sqlQuery
                })
            })
                .then(response => response.json())
                .then(data => {
                    // 处理返回的数据
                    var resultsContainer = document.getElementById("results-container");
                    resultsContainer.innerHTML = generateTable(data);
                })
                .catch(error => console.error('Error:', error));
        });

        function generateTable(data) {
            if (!data || !data.Table || data.Table.length === 0) {
                return data.success === false ? data.msg : data.code === 500 ? data.message : "<p>No data available</p>";
            }
            data = data.Table;

            var table = "<table class='layui-table'>";
            table += "<thead><tr>";

            // 添加表头并添加点击事件
            Object.keys(data[0]).forEach((key, index) => {
                table += `<th onclick="sortTable('${index}')">${key}</th>`;
            });

            table += "</tr><tr>";

            // 添加筛选输入框
            Object.keys(data[0]).forEach((key, index) => {
                table += `<th><input type="text" class="filter-input" data-column="${key}" placeholder="Filter" /></th>`;
            });

            table += "</tr></thead><tbody>";

            // 获取主键列名
            primaryKeyColumn = primaryKeyColumns[currentTableName] || Object.keys(data[0])[0]; // 没有主键才使用第一列

            // 添加表格内容
            data.forEach(row => {
                table += "<tr>";
                Object.entries(row).forEach(([key, value], index) => {
                    table += `<td class="editable" data-column="${key}" data-id="${row[primaryKeyColumn]}" contenteditable="true">${value}</td>`;
                });
                table += "</tr>";
            });

            // 添加空行
            table += "<tr>";
            Object.keys(data[0]).forEach((key, index) => {
                table += `<td><input type="text" class="new-row-input" data-column="${key}" placeholder="新增" oninput="updateNewRowSql()" /></td>`;
            });
            table += "</tr>";

            table += "</tbody></table>";
            return table;
        }

        // 使用事件委托绑定 input 事件监听器
        document.getElementById("results-container").addEventListener('input', function (event) {
            if (event.target.classList.contains('editable')) {
                var cell = event.target;
                var column = cell.getAttribute('data-column');
                var id = cell.getAttribute('data-id');
                var newValue = cell.textContent; // 获取最新的内容

                // 更新 onclick 属性中的 value
                cell.setAttribute('onclick', `addToSqlTextarea('${column}', '${newValue}', '${id}')`);

                // 更新 SQL 语句
                addToSqlTextarea(column, newValue, id);
            }
        });

        window.addToSqlTextarea = function (column, value, primaryKeyValue) {
            sqlTextarea.value = `UPDATE ${currentTableName} SET ${column} = '${value}' WHERE ${primaryKeyColumn} = '${primaryKeyValue}'`;
        };

        window.updateNewRowSql = function () {
            var newRowInputs = document.querySelectorAll(".new-row-input");
            var columns = [];
            var values = [];
            newRowInputs.forEach(function (input) {
                var column = input.getAttribute("data-column");
                var value = input.value;
                if (value) {
                    columns.push(column);
                    values.push(`'${value}'`);
                }
            });
            if (columns.length > 0 && values.length > 0) {
                sqlTextarea.value = `INSERT INTO ${currentTableName} (${columns.join(", ")}) VALUES (${values.join(", ")})`;
            } else {
                sqlTextarea.value = "";
            }
        };

        window.deleteRow = function (primaryKeyValue) {
            sqlTextarea.value = `DELETE FROM ${currentTableName} WHERE ${primaryKeyColumn} = '${primaryKeyValue}'`;
        };

        window.sortTable = function (columnIndex) {
            var table = document.querySelector("#results-container table");
            var tbody = table.querySelector("tbody");
            var rows = Array.from(tbody.querySelectorAll("tr"));

            var sortedRows = rows.sort(function (a, b) {
                var aText = a.children[columnIndex].innerText;
                var bText = b.children[columnIndex].innerText;

                return aText.localeCompare(bText);
            });

            // 清空表格内容
            while (tbody.firstChild) {
                tbody.removeChild(tbody.firstChild);
            }

            // 重新插入排序后的行
            sortedRows.forEach(function (row) {
                tbody.appendChild(row);
            });
        }

        tablesHeader.addEventListener("click", function () {
            tablesList.classList.toggle("expanded");
            arrowIcon.classList.toggle("right");
            arrowIcon.classList.toggle("down");
        });

        document.addEventListener("input", function (event) {
            if (event.target.classList.contains("filter-input")) {
                var filters = [];
                var filterInputs = document.querySelectorAll(".filter-input");
                filterInputs.forEach(function (filterInput) {
                    var column = filterInput.getAttribute("data-column");
                    var value = filterInput.value;
                    if (value) {
                        if ("@Model.DBType" == "@DatabaseType.PostgreSQL"){
                            filters.push(`${column} ::text LIKE '%${value}%'`);
                        } else {
                            // 默认使用 LIKE
                            filters.push(`${column} LIKE '%${value}%'`);
                        }
                    }
                });
                var filterQuery = filters.length > 0 ? " WHERE " + filters.join(" AND ") : "";
                updateSqlTextarea(filterQuery);
            }
        });

        document.getElementById("export-btn").addEventListener("click", function () {
            var resultsContainer = document.getElementById("results-container");
            var table = resultsContainer.querySelector("table");
            if (!table) {
                alert("没有数据可以导出");
                return;
            }

            var format = document.getElementById("export-format-select").value;
            if (format === "csv") {
                exportToCSV(table);
            } else if (format === "excel") {
                exportToExcel(table);
            }
        });

        function exportToCSV(table) {
            var csv = [];
            var rows = table.querySelectorAll("tr");

            rows.forEach(function (row, index) {
                // 跳过第2行筛选输入框
                if (index === 1) return;

                var cols = row.querySelectorAll("td, th");
                var rowData = [];
                cols.forEach(function (col) {
                    rowData.push(col.innerText);
                });
                csv.push(rowData.join(","));
            });

            var csvContent = "data:text/csv;charset=utf-8," + csv.join("\n");
            var encodedUri = encodeURI(csvContent);
            var link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "query_results.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function exportToExcel(table) {
            // 克隆表格
            var clonedTable = table.cloneNode(true);
            var rows = clonedTable.querySelectorAll("tr");

            rows.forEach(function (row, index) {
                // 跳过第2行筛选输入框
                if (index === 1) {
                    row.remove();
                }
            });

            var tableHTML = clonedTable.outerHTML.replace(/ /g, '%20');
            var dataType = 'application/vnd.ms-excel';
            var filename = 'query_results.xls';
            var link = document.createElement("a");

            document.body.appendChild(link);
            link.href = 'data:' + dataType + ', ' + tableHTML;
            link.download = filename;
            link.click();
            document.body.removeChild(link);
        }

        document.getElementById("prev-page-btn").addEventListener("click", function () {
            if (currentPage > 1) {
                currentPage--;
                updatePaginationButtons();
                updateSqlTextarea(currentFilterQuery);
                executeBtn.click(); // 模拟点击执行按钮
            }
        });

        document.getElementById("next-page-btn").addEventListener("click", function () {
            if (currentPage < totalPages) {
                currentPage++;
                updatePaginationButtons();
                updateSqlTextarea(currentFilterQuery);
                executeBtn.click();
            }
        });

        var totalRecords = 0;
        var totalPages = 0;
        var pageInfo = document.getElementById("page-info");

        function updateSqlTextarea(filterQuery = "") {
            var offset = (currentPage - 1) * pageSize;
            var limitClause = pageSize > 0 ? ` LIMIT ${pageSize} OFFSET ${offset}` : "";
            sqlTextarea.value = `SELECT * FROM ${currentTableName}${filterQuery}${limitClause}`;
        }

        function fetchRowCount(filterQuery = "") {
            fetch('@Url.Action("RowCount")', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `dbName=${encodeURIComponent(dbName)}&tbName=${encodeURIComponent(currentTableName)}&filterQuery=${encodeURIComponent(filterQuery)}`
            })
            .then(response => response.json())
            .then(data => {
                totalRecords = data;
                totalPages = pageSize > 0 ? Math.ceil(totalRecords / pageSize) : 1;
                updatePaginationButtons();
            })
            .catch(error => console.error('Error:', error));
        }

        function updatePaginationButtons() {
            document.getElementById("prev-page-btn").disabled = currentPage <= 1;
            document.getElementById("next-page-btn").disabled = currentPage >= totalPages;
            pageInfo.textContent = `第 ${currentPage} 页，共 ${totalPages} 页`; // 更新总页数显示
        }
    });
</script>
