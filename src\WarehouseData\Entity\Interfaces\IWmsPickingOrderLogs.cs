﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>领料订单日志</summary>
public partial interface IWmsPickingOrderLogs
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>ERP订单编号</summary>
    String OrderId { get; set; }

    /// <summary>操作工序。1为打单，2为领料，3为生产，4为生产审核，5为入库，6为人为结束，7为取消订单，8为设置</summary>
    Int32 Process { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>备注</summary>
    String? Remark { get; set; }
    #endregion
}
