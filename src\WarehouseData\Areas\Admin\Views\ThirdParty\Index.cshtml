﻿@{
    Html.AppendTitleParts(T("第三方对接").Text);
    
    // 引入动态操作列组件
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/components/dynamic-operation-column.js");
    
    // Css
    Html.AppendCssFileParts(ResourceLocation.Head, "~/css/components/dynamic-operation-column.css");
}

<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }
    .layui-table .layui-table-cell {
        height: 38px;
        line-height: 38px;
    }
</style>
<script asp-location="Head">
    var pleaseSelect = '@T("请选择")';
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("搜索内容")：</label>
            <div class="layui-input-inline">
                <input type="text" name="key" id="key" placeholder="@T("商户号、秘钥、项目名称")" autocomplete="off" class="layui-input" lay-filter="name">
            </div>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;

        // 按钮配置集中定义
        var operationButtons = [
            { 
                text: '@T("编辑")', 
                event: 'edit', 
                class: 'pear-btn pear-btn-primary', 
                condition: function(d) { return true; }, 
                alwaysShow: true 
            },
            @if (Model.Role.IsSystem && Model.IsAdmin && this.Has((PermissionFlags)8))
            {
                @:{ 
                @:    text: '@T("删除")', 
                @:    event: 'del', 
                @:    class: 'pear-btn pear-btn-danger', 
                @:    condition: function(d) { return true; },
                @:    alwaysShow: true
                @:}
            }
        ];

        // 初始化动态操作列组件
        var operationColumnWidth = window.dynamicOperationColumn.init({
            buttons: operationButtons,
            tableId: 'tablist',
            debug: true  // 开启调试模式
        });

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetPageOpenPlatformr")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 80
            , cols:[[
                    @if (Model.Role.IsSystem && Model.IsAdmin)
                    {
                        @:{ field: 'Id', title: '@T("编号")', width: '5%' },
                        // @:{ field: 'DisplayName', title: '@T("所属用户")', width: 120 },
                    }
                    { field: 'ProjectName', title: '@T("项目名称")', width:  '15%' }
                    ,{ field: 'AccessId', title: '@T("商户号")', width:  '15%' }
                    , { field: 'AccessKey', title: '@T("商户密钥")', width:  '15%' }
                    
                    , { field: 'Remark', title: '@T("备注")', width:  '15%' }
                    // , { field: 'OfficialUrl', title: '@T("第三方服务器地址")', minWidth: 200 }
                    , { field: 'Enabled', title: '@T("是否启用")', templet: '#switchTpl', width:  '12%' }
                    , { field: 'CreateTime', title: '@T("创建时间")', width:  '15%' }
                    , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: operationColumnWidth }
                ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'tables'
            , done: function() {
                try {
                    // 使用通用组件应用操作列宽度
                    window.dynamicOperationColumn.delayApplyWidth('tablist', 300, true);
                    console.log('第三方对接表格渲染完成，已应用操作列样式');
                } catch (error) {
                    console.error('表格done回调中出错:', error);
                }
            }
        });
        
        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        where: {
                            key: $("#key").val(),
                        },
                        page: {
                            curr: 1
                        },
                        done: function(res, curr, count) {
                            try {
                                setTimeout(function() {
                                    // 使用通用组件应用操作列宽度
                                    window.dynamicOperationColumn.delayApplyWidth('tablist', 300, true);
                                    console.log('量产列表表格渲染完成，已应用动态操作列宽度');
                                }, 300);
                            } catch (error) {
                                console.error('表格重载done回调中出错:', error);
                            }
                        }
                    });
            }
        }

        $("#key").on("input", function (e) {
            active.reload();
        });

        var lastSelectValue = null; //上一次选中的值

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            
            // 检查按钮是否被禁用
            if (obj.event === 'disabled') {
                abp.notify.warn('@T("当前状态下不可操作")');
                return false;
            }
            
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success('@T("删除成功")');
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'edit') {
                window.edit(data);
            }
        });

        window.saveCallback = function (data) {
            layer.close(data.index);
            abp.notify.success(data.msg);
            table.reload("tables");
        }

        window.add = function () {
            top.layui.dg.popupRight({
                id: 'OpenPlatformDetail'
                , title: ' @T("新增开放平台")'
                , closeBtn: 1
                , area: ['600px']
                , success: function () {
                   $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("AddThirdParty")" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        window.edit = function (data) {
            top.layui.dg.popupRight({
                id: 'EditOpen'
                , title: ' @T("编辑开放平台")'
                , closeBtn: 1
                , area: ['600px']
                , success: function () {
                   $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("EditThirdParty")?id=' + data.Id + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        //监听开关事件
        form.on('switch(statusedit)', function (data) {
            //开关是否开启，true或者false
            var checked = data.elem.checked;
            //获取所属属性值
            var id = data.elem.attributes['data-id'].nodeValue;
            $.post('@Url.Action("modifystate")', { id: id, status: checked }, function (res) {
                if (res.success) {
                    abp.notify.success(res.msg);
                }
                else {
                    abp.notify.error(res.msg);
                }
            });
        });
    });
</script>

<script type="text/html" id="tool">
    <div class="operation-column">
        {{#  layui.each(window.operationButtons, function(index, button){ }}
            {{#  var isEnabled = button.condition(d); }}
            {{#  var buttonClass = button.class + ' pear-btn-xs'; }}
            {{#  if(!isEnabled){ }}
                {{#  buttonClass += ' disabled-button'; }}
            {{#  } }}
            <a class="{{buttonClass}}" lay-event="{{isEnabled ? button.event : 'disabled'}}" 
               title="{{!isEnabled ? '当前状态下不可操作' : ''}}"
               data-enabled="{{isEnabled}}">{{button.text}}</a>
        {{#  }); }}
    </div>
</script>

<script type="text/html" id="switchTpl">
    @if (Model.Role.IsSystem && Model.IsAdmin && this.Has((PermissionFlags)8))
    {
            <input type="checkbox" name="status" data-id="{{d.Id}}" lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" {{ d.Enabled==true ?'checked':''}} >
    }
    else
    {
            <input type="checkbox" name="status" data-id="{{d.Id}}" lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" {{d.UpdateUser=='管理员'&&d.Enabled==false?'disabled':''}} {{ d.Enabled==true ?'checked':''}} >
    }
</script>

<script type="text/html" id="user-toolbar">
    @if (this.Has((PermissionFlags)2))
    {
        <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
            <i class="layui-icon  layui-icon-add-1"></i>
            @T("新增")
        </button>
    }
</script>