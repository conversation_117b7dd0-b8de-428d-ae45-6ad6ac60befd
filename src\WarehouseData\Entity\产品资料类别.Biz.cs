﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using Pek.Events;
using Pek.Infrastructure;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;
using XCode.Shards;

namespace HlktechIoT.Entity;

public partial class ProductDataCategory : CubeEntityBase<ProductDataCategory>
{
    #region 对象操作
    static ProductDataCategory()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(ParentId));

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add(new UserModule { AllowEmpty = false });
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add(new IPModule { AllowEmpty = false });

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;

        // 单对象缓存
        var sc = Meta.SingleCache;
        // sc.Expire = 60;
        sc.FindSlaveKeyMethod = k => Find(_.Name == k);
        sc.GetSlaveKeyMethod = e => e.Name;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 这里验证参数范围，建议抛出参数异常，指定参数名，前端用户界面可以捕获参数异常并聚焦到对应的参数输入框
        if (Name.IsNullOrEmpty()) throw new ArgumentNullException(nameof(Name), "类别名称不能为空！");

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (method == DataMethod.Insert && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
            if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
        }*/
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
        //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;

        // 检查唯一索引
        // CheckExist(method == DataMethod.Insert, nameof(Name));

        return true;
    }

    /// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    [EditorBrowsable(EditorBrowsableState.Never)]
    protected override void InitData()
    {
        // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
        if (Meta.Session.Count > 0) return;

        if (XTrace.Debug) XTrace.WriteLine("开始初始化ProductDataCategory[产品资料类别]数据……");
        var model1 = new ProductDataCategory() { Name = "硬件", ParentId = 0, Status = true };
        model1.Insert();
        var model2 = new ProductDataCategory() { Name = "Gerber文件", ParentId = model1.Id, Status = true };
        model2.Insert();
        var model3 = new ProductDataCategory() { Name = "BOM", ParentId = model1.Id, Status = true };
        model3.Insert();
        var model4 = new ProductDataCategory() { Name = "位号图", ParentId = model1.Id, Status = true };
        model4.Insert();
        var model5 = new ProductDataCategory() { Name = "坐标文件", ParentId = model1.Id, Status = true };
        model5.Insert();
        var model6 = new ProductDataCategory() { Name = "治具", ParentId = model1.Id, Status = true };
        model6.Insert();
        var model7 = new ProductDataCategory() { Name = "关键器件规格书", ParentId = model1.Id, Status = true };
        model7.Insert();
        var model8 = new ProductDataCategory() { Name = "结构件资料", ParentId = model1.Id, Status = true };
        model8.Insert();

        var model9 = new ProductDataCategory() { Name = "软件", ParentId = 0, Status = true };
        model9.Insert();
        var model10 = new ProductDataCategory() { Name = "固件", ParentId = model9.Id, Status = true };
        model10.Insert();
        var model11 = new ProductDataCategory() { Name = "升级工具及升级流程说明文档", ParentId = model9.Id, Status = true };
        model11.Insert();
        var model12 = new ProductDataCategory() { Name = "检测工具及检测说明文档", ParentId = model9.Id, Status = true };
        model12.Insert();
        var model13 = new ProductDataCategory() { Name = "密钥烧写工具及使用说明文档", ParentId = model9.Id, Status = true };
        model13.Insert();
        var model14 = new ProductDataCategory() { Name = "固件烧录方式", ParentId = model9.Id, Status = true };
        model14.Insert();
        var model15 = new ProductDataCategory() { Name = "检测流程说明书", ParentId = model9.Id, Status = true };
        model15.Insert();

        var model16 = new ProductDataCategory() { Name = "文档资料", ParentId = 0, Status = true };
        model16.Insert();
        var model17 = new ProductDataCategory() { Name = "软件说明书", ParentId = model16.Id, Status = true };
        model17.Insert();
        var model18 = new ProductDataCategory() { Name = "硬件说明书", ParentId = model16.Id, Status = true };
        model18.Insert();

        if (XTrace.Debug) XTrace.WriteLine("完成初始化ProductDataCategory[产品资料类别]数据！");
    }

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    public String? ParentName { get; set; }
    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="name">类别名称</param>
    /// <param name="parentId">父级Id</param>
    /// <param name="status">状态</param>
    /// <param name="start">更新时间开始</param>
    /// <param name="end">更新时间结束</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<ProductDataCategory> Search(String name, Int32 parentId, Boolean? status, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (!name.IsNullOrEmpty()) exp &= _.Name.Contains(name);
        if (parentId >= 0) exp &= _.ParentId == parentId;
        if (status != null) exp &= _.Status == status;
        exp &= _.UpdateTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= SearchWhereByKeys(key);

        return FindAll(exp, page);
    }

    /// <summary>
    /// 根据产品项目id查找
    /// </summary>
    /// <param name="ProductId"></param>
    /// <param name="page"></param>
    /// <returns></returns>
    public static IList<ProductDataCategory> FindAllByProjectId(Int32 ProductId, PageParameter page)
    {
        var project = ProductProject.FindById(ProductId);
        if (project == null || project.DataCategoryIds.IsNullOrWhiteSpace()) return [];
        return FindAll(_.Id.In(project.DataCategoryIds.Trim(',')), page);
    }

    // Select Count(Id) as Id,Category From DH_ProductDataCategory Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<ProductDataCategory> _CategoryCache = new(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public IProductDataCategory ToModel()
    {
        var model = new ProductDataCategory();
        model.Copy(this);

        return model;
    }

    #endregion
}
