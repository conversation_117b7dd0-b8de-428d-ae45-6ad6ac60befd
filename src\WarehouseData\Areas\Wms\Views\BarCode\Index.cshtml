﻿@{
    Html.AppendTitleParts(T("快递单号列表").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }

    xm-select {
        margin-top: 10px;
        line-height: 30px;
        min-height: 30px !important;
    }

    .layui-form-item {
        display: flex;
        flex-wrap: wrap;
        justify-content: center:
    }

    label {
        white-space: nowrap;
    }
</style>
<script asp-location="Head">
    var pleaseSelect = '@T("请选择")';
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<form class="layui-form dg-form">
    <div class="layui-form-item" style="margin-bottom: 3px;">
        <label class="layui-form-label" style="width: auto;margin:10px 5px 0 10px;"> @T("快递单号")：</label>
        <div class="layui-input-inline" style="width: auto;margin-top:10px;">
            <input type="text" name="code" id="code" placeholder="@T("请输入")" autocomplete="off" class="layui-input">
        </div>
        <div class="layui-inline" style="padding-top: 10px;">
            <label class="layui-form-label" style="width: auto;margin:0px 0px 0 10px;">@T("筛选时间")：</label>
            <div class="layui-inline" id="ID-laydate-range">
                <div class="layui-input-inline">
                    <input type="text" name="start" id="start" readonly placeholder="@T("开始时间")" autocomplete="off" class="layui-input">
                </div>

                <div class="layui-form-mid">-</div>
                <div class="layui-input-inline">
                    <input type="text" name="end" id="end" readonly placeholder="@T("结束时间")" autocomplete="off" class="layui-input">
                </div>
            </div>
        </div>
        <label class="layui-form-label" style="width: auto;margin:10px 5px 0 10px;"> @T("快递公司")：</label>
        <div class="layui-input-inline Select" style="width: auto;margin-top:10px;">
            <select name="types" id="types" lay-filter="types">
                <option value="All">@T("全部")</option>
                <option value="DPK">@T("德邦")</option>
                <option value="SF">@T("顺丰")</option>
                <option value="Others">@T("其他")</option>
            </select>
        </div>

    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    // 设置变量保存选中行信息
    let ids = new Array();
    // 保存当前页全部数据id，点击全选时使用
    let tableIds = new Array();

    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;
        var laydate = layui.laydate;
        // 日期范围 - 左右面板独立选择模式
        laydate.render({
            elem: '#ID-laydate-range',
            range: ['#start', '#end'],
            type: 'datetime',       // 设置日期选择类型为年月
            format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
            done: function (value, date) {
                $("#start").val(value.split(" - ")[0]);
                $("#end").val(value.split(" - ")[1]);
                checkDateValidity();
            },
            choose: function (date) {
                console.log(date);
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });
        // 获取当前年份和日期
        var currentYear = new Date().getFullYear();
        var currentDate = new Date().toISOString().split('T')[0];

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetList")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 60
            , cols: [[
                { type: 'checkbox', width: 60 }
                , { field: 'Id', title: '@T("编号")', width: '30%' }
                , { field: 'KDTypes', title: '@T("快递公司")', width: '20%' }
                , { field: 'Code', title: '@T("快递单号")', width: '20%' }
                , { field: 'CreateTime', title: '@T("创建时间")', width: '20%' }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: '5%' }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-150'
            , id: 'tables'
            , done: function (res) {
                // 设置当前页全部数据Id到全局变量
                tableIds = res.data.map(function (value) {
                    return value.Id;
                });

                // 设置当前页选中项
                $.each(res.data, function (idx, val) {
                    if (ids.indexOf(val.Id) > -1) {
                        val["LAY_CHECKED"] = 'true';
                        //找到对应数据改变勾选样式，呈现出选中效果
                        let index = val['LAY_INDEX'];
                        $('tr[data-index=' + index + '] input[type="checkbox"]').click();
                        form.render('checkbox'); //刷新checkbox选择框渲染
                    }
                });
                // 获取表格勾选状态，全选中时设置全选框选中
                let checkStatus = table.checkStatus('tables');
                if (checkStatus.isAll) {
                    $('.layui-table-header th[data-field="0"] input[type="checkbox"]').prop('checked', true);
                    form.render('checkbox'); //刷新checkbox选择框渲染
                }

            },
        });

        // 监听勾选事件
        table.on('checkbox(tool)', function (obj) {
            if (obj.checked == true) {
                if (obj.type == 'one') {
                    ids.push(obj.data.Id);
                } else {
                    for (let i = 0; i < tableIds.length; i++) {
                        //当全选之前选中了部分行进行判断，避免重复
                        if (ids.indexOf(tableIds[i]) == -1) {
                            ids.push(tableIds[i]);
                        }
                    }
                }
            } else {
                if (obj.type == 'one') {
                    let i = ids.length;
                    while (i--) {
                        if (ids[i] == obj.data.Id) {
                            ids.splice(i, 1);
                        }
                    }
                } else {
                    let i = ids.length;
                    while (i--) {
                        if (tableIds.indexOf(ids[i]) != -1) {
                            ids.splice(i, 1);
                        }
                    }
                }
            }
        });


        window.active = {
            reload: function () {
                table.reload('tables', {
                    page: {
                        curr: 1
                    },
                    where: {
                        code: $("#code").val(),
                        types: $("#types").val(),
                        start: $("#start").val(),
                        end: $("#end").val(),
                    }
                })
            }
        }
       
        // 监听输入订单
        $("#code").on("input", function (e) {
            ids = [];
            active.reload('tables', {
                where: { "code": $("#code").val() },
            })
        });
          form.on('select(types)', function (data) {
            ids = [];
            active.reload('tables')
        });

        table.on('toolbar(tool)', function (obj) {
            let data = obj.config
            var that = this
            if (obj.event === 'exportall') {
                // 创建表单
                var form = $('<form></form>').attr('action', '@Url.Action("ExportAll")').attr('method', 'POST');
                form.append($('<input>').attr('type', 'hidden').attr('name', 'ids').attr('value', ids.length <= 0 ? "" : ids.join(',')));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'code').attr('value', $("#code").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'start').attr('value', $("#start").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'end').attr('value', $("#end").val()));
                // 将表单添加到body并提交
                form.appendTo('body').submit().remove();
            }
        });
       
        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success(data.msg);
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            }
        });

        window.saveCallback = function (data) {
            parent.layer.close(data.index);
            abp.notify.success(data.msg);
            table.reload("tables");
        }

        //时间插件
        var startDate = laydate.render({
            elem: '#start',
            btns: ['clear', "confirm"],//只显示清空和确定按钮
            type: 'datetime',       // 设置日期选择类型为年月
            format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
            value: currentYear + '-01-01 00:00:00', // 设置默认值为当年1月1日
            choose: function (date) {
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });

        var endDate = laydate.render({
            elem: '#end',
            btns: ["clear", "confirm"],
            type: 'datetime',       // 设置日期选择类型为年月
            format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
            value: currentDate + ' 23:59:59', // 设置默认值为当年1月1日
            choose: function (date) {
                console.log(date);
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });

        function checkDateValidity() {
            var startValue = $("#start").val();
            var endValue = $("#end").val();

            if (startValue && endValue) {

                // if (startValue.substr(0, 4) != endValue.substr(0, 4)) {
                //     os.warning('开始时间和结束时间必须在同一年，请重新选择。');
                //     $("#start").val(""); // 清空开始时间输入框
                //     $("#end").val("");   // 清空结束时间输入框
                //     return;
                // }

                //     console.log(   $("#start").val(),
                //         $("#end").val());
                // }
                ids = [];
                active.reload("tables")

            }
        }

        window.warning = function (msg) {
            os.warning(msg);
        }
    });
</script>

<script type="text/html" id="tool">
  
    @if (this.Has((PermissionFlags)32))
    {
         <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="del"> @T("删除")</a>
    }
</script>

<script type="text/html" id="online">
    {{# if(d.Online) { }}
      <i class="layui-icon layui-icon-ok" style="color: #16b777"></i>
    {{# } else { }}
      <i class="layui-icon layui-icon-close" style="color: #ff5722"></i>
    {{# } }}
</script>

<script type="text/html" id="user-toolbar">
   
    @if (this.Has((PermissionFlags)32))
    {
                    <a class="pear-btn pear-btn-primary pear-btn-md" lay-event="exportall">
                        <i class="layui-icon layui-icon-export"></i>
            @T("导出")
                    </a>
    }

</script>