﻿using DG.Web.Framework;

using DH.Entity;
using DH.Models;

using HlktechIoT.Dto.Export;

using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;

using NewLife;
using NewLife.Data;
using NewLife.Log;

using Pek.Models;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>
///多语言
/// </summary>
[DisplayName("语言包")]
[Description("用于系统多语言的各种语言包")]
[AdminArea]
[DHMenu(95,ParentMenuName = "Language", CurrentMenuUrl = "~/{area}/Languages", CurrentMenuName = "Languages", LastUpdate = "20240124")]
public class LanguagesController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 95;

    /// <summary>语言包列表</summary>
    /// <returns></returns>
    [DisplayName("语言包列表")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult Index()
    {
        dynamic viewModel = new ExpandoObject();

        var cultureList = Language.FindAllWithCache().Where(e => e.Status).OrderBy(e => e.DisplayOrder);
        var list = cultureList.Select(item => new SelectListItem { Value = item.Id.ToString(), Text = WorkingLanguage.UniqueSeoCode == "en" ? item.EnglishName : item.Name, Selected = item.Id == Language.FindByDefault().Id });

        viewModel.LanguageList = list;

        return View(viewModel);
    }

    /// <summary>
    /// 语言包列表接口
    /// </summary>
    /// <returns></returns>
    [DisplayName("语言包列表接口")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult List(int page, int limit, String lanKey, String lanValue, Int32 cultureId)
    {
        if (lanKey != null) lanKey = lanKey.Trim();
        if (lanValue != null) lanValue = lanValue.Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = Language._.Id,
            Desc = true
        };

        if (cultureId == 0)
        {
            var language = Language.FindByDefault();
            cultureId = language == null ? 0 : language.Id;
        }

        var result = LocaleStringResource.Search(lanKey, cultureId, lanValue, pages).Select(item => new { item.Id, item.LanKey, item.CultureName, item.LanValue });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data = result.ToList() });
    }

    /// <summary>添加语言包</summary>
    /// <returns></returns>
    [DisplayName("添加语言包")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult Add()
    {
        dynamic viewModel = new ExpandoObject();

        viewModel.LanguageList = Language.FindAllWithCache().Where(e => e.Status).OrderBy(e => e.DisplayOrder);

        return View(viewModel);
    }

    /// <summary>添加语言包</summary>
    /// <returns></returns>
    [DisplayName("添加语言包")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddLanguage(String LanKey)
    {
        var list = Language.FindAllWithCache().Where(e => e.Status);

        if (LanKey.IsNullOrWhiteSpace())
        {
            return Json(new { success = false, msg = GetResource("翻译键不能为空") });
        }

        var nlist = new List<LocaleStringResource>();
        foreach (var item in list)
        {
            var model = new LocaleStringResource();
            model.LanKey = LanKey;
            model.CultureId = item.Id;
            model.LanValue = GetRequest($"[{item.Id}].LanValue");
            nlist.Add(model);
        }

        nlist.Insert(true);
        return Json(new DResult { success = true, msg = GetResource("添加成功") });
    }

    /// <summary>编辑语言包</summary>
    /// <returns></returns>
    [DisplayName("编辑语言包")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Edit(String id)
    {
        ViewBag.LanguageList = Language.FindAllWithCache().Where(e => e.Status).OrderBy(e => e.DisplayOrder);
        var model = LocaleStringResource.FindByLanKey(id);
        ViewBag.LanKey = id;
        return View(model);
    }

    /// <summary>编辑语言包</summary>
    /// <returns></returns>
    [DisplayName("编辑语言包")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult EditLanguage(String LanKey)
    {
        var list = Language.FindAllWithCache().Where(e => e.Status);

        using (var tran1 = Language.Meta.CreateTrans())
        {
            foreach (var item in list)
            {
                var cultureId = GetRequest($"[{item.Id}].Id").ToInt();
                var lanKey = GetRequest($"[{item.Id}].LanKey");
                var model = LocaleStringResource.FindByLanKeyAndCultureId(lanKey, cultureId);

                if (model == null)
                {
                    model = new LocaleStringResource();
                    model.LanKey = LanKey;
                    model.CultureId = item.Id;
                    model.LanValue = GetRequest($"[{item.Id}].LanValue");
                    model.Insert();
                }
                else
                {
                    model.LanKey = LanKey;
                    model.LanValue = GetRequest($"[{item.Id}].LanValue");
                    model.Update();
                }
            }

            tran1.Commit();
        }
        return Json(new DResult { success = true, msg = GetResource("保存成功") });
    }

    /// <summary>删除翻译</summary>
    /// <returns></returns>
    [DisplayName("删除翻译")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult Delete(String Key)
    {
        LocaleStringResource.Delete(LocaleStringResource._.LanKey.Equal(Key));

        return Json(new DResult() { success = true, msg = GetResource("删除成功") });
    }

    /// <summary>删除翻译键的所有语言值</summary>
    /// <returns></returns>
    [DisplayName("删除翻译键所有语言值")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult DeleteAll(String Key)
    {
        Key = Key.Trim(',');

        foreach (var item in Key.Split(','))
        {
            LocaleStringResource.Delete(LocaleStringResource._.LanKey.Equal(item));
        }

        return Json(new DResult() { success = true, msg = GetResource("删除成功") });
    }
    /// <summary>
    /// 导出翻译项
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)16)]
    [DisplayName("导出")]
    public async Task<IActionResult> Export(Int32 cultureId)
    {
        var pages = new PageParameter()
        {
            PageIndex = 1,
            PageSize = 0,
            RetrieveTotalCount = true,
            OrderBy = "Id desc"
        };

        IExporter exporter = new ExcelExporter();

        var defaultLanguage = Language.FindByDefault();

        var modelLanguage = Language.FindById(cultureId);

        var list = LocaleStringResource.Search("", defaultLanguage.Id, "", pages).Select(e => new LocalStringExport { LanKey = e.LanKey, LanValue = e.LanValue, Culture = modelLanguage?.Name, TransValue = LocaleStringResource.FindByLanKeyAndCultureId(e.LanKey, cultureId)?.LanValue });

        var result = await exporter.ExportAsByteArray(list.ToList()).ConfigureAwait(false);

        return File(result, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{"Language Packs"} {DateTime.Now:yyyyMMddhhmm}.xlsx");
    }

    /// <summary>
    /// 导入翻译项
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)32)]
    [DisplayName("导入")]
    public async Task<IActionResult> Import(IFormFile file)
    {
        var res = new DResult();

        if (file == null)
        {
            res.msg = GetResource("导入文件有误");
            return Json(res);
        }

        var OrignfileName = file.FileName;

        if (OrignfileName.IndexOf("xlsx") == -1)
        {
            res.msg = GetResource("文件名称格式不对");
            return Json(res);
        }

        IExcelImporter Importer = new ExcelImporter();
        var import = await Importer.Import<LocalStringImport>(file.OpenReadStream()).ConfigureAwait(false);

        foreach (var item in import.Data)
        {
            var modelLanguage = Language.FindByName(item.Culture);
            if (modelLanguage != null)
            {
                var model = LocaleStringResource.FindByLanKeyAndCultureId(item.LanKey, modelLanguage.Id);
                if (model != null)
                {
                    model.LanValue = item.TransValue;
                    model.Update();
                }
                else
                {
                    try
                    {
                        model = new LocaleStringResource();
                        model.LanKey = item.LanKey;
                        model.CultureId = modelLanguage.Id;
                        model.LanValue = item.TransValue;
                        model.Insert();
                    }
                    catch (Exception ex)
                    {
                        XTrace.WriteException(ex);
                    }
                }
            }
        }

        res.msg = GetResource("上传成功");
        res.success = true;
        return Json(res);
    }
}