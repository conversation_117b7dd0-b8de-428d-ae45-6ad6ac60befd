﻿using DH;
using DH.Core.Domain.Localization;
using DH.Core.Infrastructure;
using DH.Entity;
using DH.Extensions;
using DH.MVC.Routing;

using Microsoft.AspNetCore.Mvc.Routing;

using NewLife;
using Pek;

namespace HlktechIoT.Common.Routing;

/// <summary>
/// slug路由转换器
/// </summary>
public class SlugRouteTransformer : DynamicRouteValueTransformer {
    public override ValueTask<RouteValueDictionary> TransformAsync(HttpContext httpContext, RouteValueDictionary values)
    {
        // 这里是自定义路由区域
        if (values == null)
            return new ValueTask<RouteValueDictionary>(values);

        if (!values.TryGetValue("SeName", out var slugValue) || string.IsNullOrEmpty(slugValue as string))
            return new ValueTask<RouteValueDictionary>(values);

        var slug = slugValue as string;

        var UrlSuffix = DG.Setting.Current.IsAllowUrlSuffix ? DG.Setting.Current.UrlSuffix : "";

        if (UrlSuffix.IsNotNullAndWhiteSpace())
            slug = slug.TrimEnd(UrlSuffix);
        var urlRecord = UrlRecord.FindBySlug(slug);

        // 没有发现指定的Url记录
        if (urlRecord == null)
            return new ValueTask<RouteValueDictionary>(values);

        // 虚拟目录路径
        var pathBase = httpContext.Request.PathBase;

        // 如果网址记录无效，让我们找到最新的
        if (!urlRecord.IsActive)
        {
            var activeSlug = UrlRecord.GetActiveSlug(urlRecord.EntityId, urlRecord.EntityName, urlRecord.LanguageId);
            if (activeSlug.IsNullOrWhiteSpace())
                return new ValueTask<RouteValueDictionary>(values);

            // 重定向到活动的Slug（如果找到）
            values[DHRoutingDefaults.RouteValue.Controller] = "Common";
            values[DHRoutingDefaults.RouteValue.Action] = "InternalRedirect";
            values[DHRoutingDefaults.RouteValue.Url] = $"{pathBase}/{activeSlug}{httpContext.Request.QueryString}";
            values[DHRoutingDefaults.RouteValue.PermanentRedirect] = true;
            httpContext.Items["dg.RedirectFromGenericPathRoute"] = true;

            return new ValueTask<RouteValueDictionary>(values);
        }

        var localizationSettings = LocalizationSettings.Current;

        // 确保该Slug与当前语言相同，
        // 否则，当客户选择一种新的语言，但是一团糟时，它可能会引起一些问题
        if (localizationSettings.SeoFriendlyUrlsForLanguagesEnabled && localizationSettings.IsEnable)
        {
            var urllanguage = values["language"];
            if (urllanguage != null && urllanguage.ToString().IsNotNullOrWhiteSpace())
            {
                var language = Language.FindByUniqueSeoCode(urllanguage.ToString()!.ToLowerInvariant());
                if (language == null)
                    language = Language.FindByDefault();

                var slugForCurrentLanguage = UrlRecord.GetActiveSlug(urlRecord.EntityId, urlRecord.EntityName, language.Id);
                if (slugForCurrentLanguage.IsNotNullOrWhiteSpace() && !slugForCurrentLanguage.Equals(slug, StringComparison.InvariantCultureIgnoreCase))
                {
                    // 我们应该在上面进行验证，因为某些实体没有针对标准（Id = 0）语言的SeName（例如新闻，博客文章）

                    // 重定向到当前语言页面
                    values[DHRoutingDefaults.RouteValue.Controller] = "Common";
                    values[DHRoutingDefaults.RouteValue.Action] = "InternalRedirect";
                    values[DHRoutingDefaults.RouteValue.Url] = $"{pathBase}/{language.UniqueSeoCode}/{slugForCurrentLanguage}{httpContext.Request.QueryString}";
                    values[DHRoutingDefaults.RouteValue.PermanentRedirect] = false;
                    httpContext.Items["dg.RedirectFromGenericPathRoute"] = true;

                    return new ValueTask<RouteValueDictionary>(values);
                }
            }
        }

        // 因为我们在这里，所以没问题，所以处理网址
        switch (urlRecord.EntityName.ToLowerInvariant())
        {
            default:

                break;
        }

        return new ValueTask<RouteValueDictionary>(values);
    }
}