﻿using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

namespace HlktechIoT.Dto.Export
{
    [ExcelExporter(Name = "Sheet1", AutoFitAllColumn = true, TableStyle = OfficeOpenXml.Table.TableStyles.None, AutoCenter = true)]
    public class ManufacturerOrderExport
    {
        [ExporterHeader(DisplayName = "SN", IsBold = false)]
        public string? SN { get; set; }

        [ExporterHeader(DisplayName = "主Mac", IsBold = false)]
        public string? Mac { get; set; }

        [ExporterHeader(DisplayName = "副Mac", IsBold = false)]
        public string? Mac1 { get; set; }

        [ExporterHeader(DisplayName = "订单号", IsBold = false)]
        public string? OrderNum { get; set; }

    }
}
