﻿using NewLife;
using NewLife.Caching;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;

using Pek.Helpers;
using Pek.Ids;

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;
using XCode.Shards;

namespace HlktechIoT.Entity;

public partial class ProductSupplementalOrders : CubeEntityBase<ProductSupplementalOrders>
{
    #region 对象操作
    static ProductSupplementalOrders()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(ProductTypeId));

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add(new UserModule { AllowEmpty = false });
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add(new IPModule { AllowEmpty = false });

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;

        // 单对象缓存
        var sc = Meta.SingleCache;
        // sc.Expire = 60;
        sc.FindSlaveKeyMethod = k => Find(_.OrderId == k);
        sc.GetSlaveKeyMethod = e => e.OrderId;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 这里验证参数范围，建议抛出参数异常，指定参数名，前端用户界面可以捕获参数异常并聚焦到对应的参数输入框
        if (OrderId.IsNullOrEmpty()) throw new ArgumentNullException(nameof(OrderId), "订单号不能为空！");

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (method == DataMethod.Insert && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
            if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
        }*/
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
        //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;

        // 检查唯一索引
        // CheckExist(method == DataMethod.Insert, nameof(OrderId));

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化ProductSupplementalOrders[生产订单补单]数据……");

    //    var entity = new ProductSupplementalOrders();
    //    entity.Id = 0;
    //    entity.OrderId = "abc";
    //    entity.ProductTypeId = 0;
    //    entity.Quantity = 0;
    //    entity.Status = 0;
    //    entity.AuditTime = DateTime.Now;
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化ProductSupplementalOrders[生产订单补单]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    /// <summary>产品型号编号</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public ProductType? ProductType => Extends.Get(nameof(ProductType), k => ProductType.FindById(ProductTypeId));

    /// <summary>产品型号编号</summary>
    [Map(nameof(ProductTypeId), typeof(ProductType), "Id")]
    public String? ProductTypeName => ProductType?.Name;
    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="orderId">订单号</param>
    /// <param name="status">审核状态 0待审核 1审核中 2已审核 3审核失败</param>
    /// <param name="productTypeId">产品型号编号</param>
    /// <param name="start">编号开始</param>
    /// <param name="end">编号结束</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<ProductSupplementalOrders> Search(String orderId, Int32 status,Int32 productTypeId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (!orderId.IsNullOrEmpty()) exp &= _.OrderId == orderId;
        if (status >= 0) exp &= _.Status == status;
        if (productTypeId > 0) exp &= _.ProductTypeId == productTypeId;
        exp &= _.Id.Between(start, end, Meta.Factory.Snow);
        if (!key.IsNullOrEmpty()) exp &= SearchWhereByKeys(key);

        return FindAll(exp, page);
    }

    // Select Count(Id) as Id,Category From DH_ProductSupplementalOrders Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<ProductSupplementalOrders> _CategoryCache = new(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public IProductSupplementalOrders ToModel()
    {
        var model = new ProductSupplementalOrders();
        model.Copy(this);

        return model;
    }

    /// <summary>
    /// 生成Sn
    /// </summary>
    /// <param name="cacheProvider"></param>
    /// <param name="macQueue"></param>
    public void CreateSnMac(ICacheProvider cacheProvider, ConcurrentQueue<String> macQueue)
    {
        var model = this;

        for (var n = 0; n < model.Quantity; n++)
        {
            ProductSnMac? productSnMac = new()
            {
                ProductOrdersId = model.Id,
                OrderId = model.OrderId
            };

            if (model.ProductType?.NeedSn ?? false)
            {
                productSnMac.Sn = Randoms.RandomStr(2) + IdHelper.GetIdString() + Randoms.RandomStr(1) + DateTime.Now.ToString("yyyy");

                var ishave = false;
                while (!ishave)
                {
                    if (cacheProvider.Cache.ContainsKey(productSnMac.Sn))
                    {
                        productSnMac.Sn = Randoms.RandomStr(2) + IdHelper.GetIdString() + Randoms.RandomStr(1) + DateTime.Now.ToString("yyyy");
                    }
                    else
                    {
                        cacheProvider.Cache.Set(productSnMac.Sn, productSnMac.Sn, 3600);
                        ishave = true;
                    }
                }
            }
            if (model.ProductType?.NeedMac == true || model.ProductType?.NeedMac1 == true)
            {
                if (model.ProductType?.NeedMac == true)
                {
                    if (macQueue.TryDequeue(out var item))
                    {
                        productSnMac.Mac = item;
                    }
                }
                if (model.ProductType?.NeedMac1 == true)
                {
                    if (macQueue.TryDequeue(out var item))
                    {
                        productSnMac.Mac1 = item;
                    }
                }
            }

            productSnMac.Insert();
            if (!productSnMac.Sn.IsNullOrWhiteSpace())//添加Sn记录
            {
                var modelSns = new DeviceSns
                {
                    Sn = productSnMac.Sn,
                    AssociationId = productSnMac.Id,
                };
                modelSns.Insert();
            }
            if (!productSnMac.Mac.IsNullOrWhiteSpace())
            {
                var modelMacs = new DeviceMacs
                {
                    Mac = productSnMac.Mac,
                    AssociationId = productSnMac.Id,
                };
                modelMacs.Insert();
            }
            if (!productSnMac.Mac1.IsNullOrWhiteSpace())
            {
                var modelMacs = new DeviceMacs
                {
                    Mac = productSnMac.Mac1,
                    AssociationId = productSnMac.Id,
                };
                modelMacs.Insert();
            }
        }

        model.Status = 2;
        model.Update();
    }

    #endregion
}
