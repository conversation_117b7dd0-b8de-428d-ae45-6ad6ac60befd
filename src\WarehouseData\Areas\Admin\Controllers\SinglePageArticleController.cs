﻿using DG.Web.Framework;

using DH;
using DH.Core.Domain.Localization;
using DH.Entity;

using HlktechIoT.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;

using Pek;
using Pek.Models;
using Pek.Webs;

using System.ComponentModel;
using System.Dynamic;
using System.Security.Cryptography;
using System.Text;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>
/// 单页文章管理
/// </summary>
[DisplayName("单页文章管理")]
[Description("对单页文章管理的管理")]
[AdminArea]
[DHMenu(65,ParentMenuName = "System", CurrentMenuUrl = "~/{area}/SinglePageArticle", CurrentMenuName = "SinglePageArticle", LastUpdate = "20240124")]
public class SinglePageArticleController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 65;

    /// <summary>
    /// 单页文章管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("单页文章管理")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 单页文章列表接口
    /// </summary>
    /// <returns></returns>
    [DisplayName("单页文章列表接口")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult GetList(int page, int limit)
    {
        var roles = SingleArticle.GetAll().OrderBy(e => e.Id).Select(item =>
        {
            var (Name, Content, FileUrl, Md5, Ex1) = SingleArticleLan.FindBySIdAndLId(item.Id, WorkingLanguage.Id, true);
            return new { Id = item.Id, Name, item.Code, item.CreateTime, item.CreateUser };
        });

        return Json(new { code = 0, msg = "success", count = Role.Meta.Count, data = roles });
    }

    /// <summary>
    /// 添加单页文章管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("添加单页文章管理")]
    public IActionResult Add()
    {
        dynamic viewModel = new ExpandoObject();

        var Sort = SingleArticle.FindMax("Sort");
        ViewBag.Sort = Sort + 1;

        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

        return View(viewModel);
    }

    /// <summary>
    /// 添加单页文章管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("添加单页文章管理")]
    [HttpPost]
    public IActionResult Add(String Name, String Content, String Code, String FilePath, String Ex1)
    {
        var res = new DResult();

        if (Name.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("文章名称不能为空");
            return Json(res);
        }

        if (Code.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("调用别名不能为空");
            return Json(res);
        }

        if (SingleArticle.FindByCode(Code) != null)
        {
            res.msg = GetResource("调用别名不允许重复");
            return Json(res);
        }

        using (var tran1 = SingleArticle.Meta.CreateTrans())
        {
            var model = new SingleArticle();
            model.Name = Name;
            model.Content = Content;
            if (!model.Content.IsNullOrWhiteSpace())
            {
                model.Md5 = MD5Stream(new MemoryStream(Encoding.UTF8.GetBytes(Content)));
            }
            model.Code = Code;
            model.FileUrl = FilePath;
            model.Ex1 = Ex1;
            model.Insert();

            var localizationSettings = LocalizationSettings.Current;

            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

                var lanlist = SingleArticleLan.FindAllBySId(model.Id);
                foreach (var item in Languagelist)
                {
                    var modelLan = lanlist.Find(x => x.LId == item.Id);
                    if (modelLan == null)
                    {
                        modelLan = new SingleArticleLan();
                    }

                    var NameLan = GetRequest($"[{item.Id}].Name").SafeString().Trim();
                    var ContentLan = GetRequest($"[{item.Id}].Content").SafeString().Trim();
                    var Ex1Lan = GetRequest($"[{item.Id}].Ex1").SafeString().Trim();
                    modelLan.Name = NameLan;
                    modelLan.Content = ContentLan;
                    modelLan.Ex1 = Ex1Lan;

                    if (!modelLan.Content.IsNullOrWhiteSpace())
                    {
                        modelLan.Md5 = MD5Stream(new MemoryStream(Encoding.UTF8.GetBytes(modelLan.Content)));
                    }
                    modelLan.SId = model.Id;
                    modelLan.LId = item.Id;
                    modelLan.FileUrl = GetRequest($"[{item.Id}].FilePath").SafeString().Trim();
                    modelLan.Save();
                }
            }
            tran1.Commit();
        }
        SingleArticle.Meta.Cache.Clear("", true);//清除缓存
        SingleArticleLan.Meta.Cache.Clear("", true);//清除缓存

        res.msg = "添加成功";
        res.success = true;
        return Json(res);
    }

    private static String MD5Stream(Stream stream)
    {
        using var md5 = MD5.Create();
        var result = md5?.ComputeHash(stream);

        var b = md5?.Hash;
        md5?.Clear();

        var sb = new StringBuilder(32);
        for (var i = 0; i < b?.Length; i++)
        {
            sb.Append(b[i].ToString("X2"));
        }
        return sb.ToString();
    }

    /// <summary>
    /// 编辑单页文章
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("编辑单页文章")]
    public IActionResult Edit(Int32 Id)
    {
        var model = SingleArticle.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("数据不存在或已被删除"));
        }

        dynamic viewModel = new ExpandoObject();
        viewModel.Model = model;

        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

        return View(viewModel);
    }

    /// <summary>
    /// 编辑单页文章
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("编辑单页文章")]
    [HttpPost]
    public IActionResult Edit(Int32 Id, String Name, String Content, String Code, String FilePath, String Ex1)
    {
        var res = new DResult();

        if (Name.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("文章名称不能为空");
            return Json(res);
        }

        if (Code.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("调用别名不能为空");
            return Json(res);
        }

        var model = SingleArticle.FindByCode(Code);
        if (model != null && model.Id != Id)
        {
            res.msg = GetResource("调用别名不允许重复");
            return Json(res);
        }
        model = SingleArticle.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("数据不存在或已被删除");
            return Json(res);
        }

        using (var tran1 = SingleArticle.Meta.CreateTrans())
        {
            model.Name = Name;
            model.Content = Content;
            if (!model.Content.IsNullOrWhiteSpace())
            {
                model.Md5 = MD5Stream(new MemoryStream(Encoding.UTF8.GetBytes(Content)));
            }
            model.Code = Code;
            model.FileUrl = FilePath;
            model.Ex1 = Ex1;
            model.Update();

            var localizationSettings = LocalizationSettings.Current;

            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

                var lanlist = SingleArticleLan.FindAllBySId(model.Id);
                foreach (var item in Languagelist)
                {
                    var modelLan = lanlist.Find(x => x.LId == item.Id);
                    if (modelLan == null)
                    {
                        modelLan = new SingleArticleLan();
                    }

                    var NameLan = GetRequest($"[{item.Id}].Name").SafeString().Trim();
                    var ContentLan = GetRequest($"[{item.Id}].Content").SafeString().Trim();
                    var Ex1Lan = GetRequest($"[{item.Id}].Ex1").SafeString().Trim();
                    modelLan.Name = NameLan;
                    modelLan.Content = ContentLan;
                    modelLan.Ex1 = Ex1Lan;

                    if (!modelLan.Content.IsNullOrWhiteSpace())
                    {
                        modelLan.Md5 = MD5Stream(new MemoryStream(Encoding.UTF8.GetBytes(modelLan.Content)));
                    }
                    modelLan.SId = model.Id;
                    modelLan.LId = item.Id;
                    modelLan.FileUrl = GetRequest($"[{item.Id}].FilePath").SafeString().Trim();
                    modelLan.Save();
                }
            }
            tran1.Commit();
        }
        SingleArticle.Meta.Cache.Clear("", true);//清除缓存
        SingleArticleLan.Meta.Cache.Clear("", true);//清除缓存

        res.msg = "添加成功";
        res.success = true;
        return Json(res);
    }

    /// <summary>
    /// 图片上传
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片上传")]
    [HttpPost]
    public IActionResult UploadImg(IFormFile fileupload)
    {
        var bytes = fileupload.OpenReadStream().ReadBytes(fileupload.Length);
        if (!bytes.IsImageFile())
        {
            return Json(new { errno = 1, message = GetResource("非法操作，请上传图片！") });
        }

        var filename = $"{Pek.Helpers.Randoms.RandomStr(13).ToLower()}{Path.GetExtension(fileupload.FileName)}";
        var filepath = DHSetting.Current.UploadPath.CombinePath($"SingleArticle/{filename}");

        var saveFileName = filepath.GetFullPath();
        saveFileName.EnsureDirectory();
        fileupload.SaveAs(saveFileName);

        return Json(new { errno = 0, data = new { url = Pek.Helpers.DHWeb.GetSiteUrl() + "/" + filepath.Replace("\\", "/") } });
    }

    /// <summary>
    /// 上传文件
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("上传文件")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult UploadFiles(IFormFile file)
    {
        var res = new DResult();
        if (file == null)
        {
            res.msg = GetResource("文件上传有误");
            return Json(res);
        }

        var OrignfileName = file.FileName;

        var filename = $"{Pek.Helpers.Randoms.RandomStr(13).ToLower()}{Path.GetExtension(OrignfileName)}";

        var filepath = DHSetting.Current.UploadPath.CombinePath($"SingleArticle/{filename}");

        var saveFileName = filepath.GetFullPath();
        var f = saveFileName.AsFile();
        if (f.Exists)
        {
            f.Delete();
        }
        saveFileName.EnsureDirectory();
        file.SaveAs(saveFileName);

        res.msg = GetResource("上传成功");
        res.success = true;
        res.data = new { OriginFileName = filename, FilePath = filepath.Replace("\\", "/") };
        return Json(res);
    }

    /// <summary>
    /// 单页文章删除
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("单页文章删除")]
    public IActionResult Delete(String Id)
    {
        var res = new DResult();

        SingleArticle.DelByIds(Id.Trim(','));
        SingleArticleLan.DelBySIds(Id.Trim(','));

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

}