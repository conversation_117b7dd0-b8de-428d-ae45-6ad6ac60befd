﻿@{
    var modelUser = Model!.UserModel as User;
    var modelDetail = UserDetail.FindById(modelUser!.ID);

    if (modelUser.ID == 0)
    {
        Html.AppendTitleParts(T("新增管理员").Text);
    }
    else
    {
        Html.AppendTitleParts(T("编辑管理员").Text);
    }
}
<style asp-location="true">
    .width {
        width: 230px
    }

    .layui-form-item {
        margin-bottom: 10px;
    }

    .layui-form-select {
        width: 230px
    }

        .layui-form-select .layui-edge {
            right: 10px;
        }

    .layui-input-inline.inlineSelect {
        width: 230px;
    }
</style>

<form class="layui-form form-cus" lay-filter="" autocomplete="off">
    <div class="layui-form-item" style="margin-top:20px;">
        @if (modelUser.RoleID != 1)
        {
            <div class="layui-inline">
                <label class="layui-form-label">@T("角色")</label>
                <div class="layui-input-inline inlineSelect">
                    <select name="RoleID" class="city" lay-verify="role" lay-verType="tips">
                        <option value="">@T("请选择")</option>
                        @foreach (var item in Model.Roles)
                        {
                            if (modelUser.RoleID == item.ID)
                            {
                                <option value="@item.ID" selected>@item.Name</option>
                            }
                            else
                            {
                                <option value="@item.ID">@item.Name</option>
                            }
                        }
                    </select>
                </div>
            </div>
        }
        <div class="layui-inline">
            <label class="layui-form-label">@T("账号"):</label>
            <div class="layui-input-inline">
                <input type="text" name="name" lay-verify="account" lay-verType="tips" value="@modelUser.Name" @(modelUser.Name == null ? "" : "disabled") autocomplete="username" class="layui-input width">
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">@T("密码"):</label>
            <div class="layui-input-inline">
                <input type="password" name="password" lay-verify="pass" lay-verType="tips" autocomplete="new-password" class="layui-input width">
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">@T("姓名"):</label>
            <div class="layui-input-inline">
                <input type="text" name="realName" lay-verType="tips" autocomplete="off" class="layui-input width" value="@modelDetail?.TrueName">
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">@T("手机号"):</label>
            <div class="layui-input-inline">
                <input type="text" name="mobile" lay-verify="myphone" lay-verType="tips" autocomplete="off" class="layui-input width" value="@modelUser?.Mobile">
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">@T("启用"):</label>
            <div class="layui-input-inline">
                <input type="checkbox" name="Enabled" lay-filter="Enabled" lay-skin="switch" @(@modelUser?.Enable == true ? Html.Raw("checked") : Html.Raw(""))>
            </div>
        </div>
    </div>
    <div class="layui-form-item" style="text-align:center;">
        <div class="layui-inline">
            <input type="hidden" name="Id" value="@modelUser?.ID" />
            <button type="button" lay-submit class="pear-btn pear-btn-primary" lay-filter="Submit"><i class="layui-icon layui-icon-loading layui-icon layui-anim layui-anim-rotate layui-anim-loop layui-hide"></i>@T("确定")</button>
            <button type="reset" class="layui-btn btn-open-close" style="border: 1px solid #C9C9C9;background-color: #fff !important;color: #555; margin-left: 20px;">@T("取消")</button>
        </div>
    </div>
</form>
<script asp-location="Footer">
    layui.use(['abp', 'dg', 'form'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var dg = layui.dg;
        var index = parent.layer.getFrameIndex(window.name); //获取窗口索引

        form.verify({
            account: function (value, item) {
                if (!value) {
                    return '@T("账户名不可为空")'
                }
            },
            role: function (value, item) {
                if (!value) {
                    return '@T("请选择")';
                }
            },
            pass: function (value, item) {
                var exp = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,20}$/;
                if (value && !exp.test(value)) {
                    return '@T("密码必须是8-20位，并且必须包含数字和字母")';
                }
            },
            nickname: function (value, item) { //value：表单的值、item：表单的DOM对象
                if (!new RegExp("^[a-zA-Z0-9_\u4e00-\u9fa5\\s·]+$").test(value)) {
                    //return '';
                    return "@T("用户名不能有特殊字符")";
                }
                if (/(^\_)|(\__)|(\_+$)/.test(value)) {
                    //return '用户名首尾不能出现下划线\'_\'';
                    return "@T("用户名首尾不能出现下划线\'_\'")";
                }
                if (/^\d+\d+\d$/.test(value)) {
                    return "@T("用户名不能全为数字")";

                }
            },
            myemail: function (value, item) { //value：表单的值、item：表单的DOM对象
                if (value != "") {
                    if (!/^[a-z0-9._%-]+@@([a-z0-9-]+\.)+[a-z]{2,4}$|^1[3|4|5|7|8]\d{9}$/.test(value)) {
                        return '@T("邮箱格式错误")';
                    }
                }
            },
            myphone: function (value, item) {
                if (value != "") {
                    if (!/^1[3|4|5|6|7|8|9]\d{9}$/.test(value)) {
                        return '@T("手机号格式错误")';
                    }
                }
            }
        })

        form.on('submit(Submit)', function (data) {
            var url = "@Url.Action("UpdateUserPwd")";
            if (data.field.Id == 0) {
                url = "@Url.Action("CreateUser")";
            }

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: url,
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();

                let parentWindow = parent.selectedWindow().window;
                parentWindow.active.reload();

                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;

        });

        $(".btn-open-close").on('click', function () {
            parent.layer.close(index);
        });

    });

</script>