﻿using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

namespace HlktechIoT.Dto.Export;

[ExcelExporter(Name = "Sheet1", AutoFitAllColumn = true, TableStyle = OfficeOpenXml.Table.TableStyles.None, AutoCenter = true)]
public class BarCodeExport {
    /// <summary> 
    /// 序号Id
    /// </summary> 
    [ExporterHeader(DisplayName = "序号", IsBold = false)]
    public long Id { get; set; }
    /// <summary> 
    /// 快递类型
    /// </summary> 
    [ExporterHeader(DisplayName = "快递类型", IsBold = false)]
    public string? Types { get; set; }
    /// <summary> 
    /// 快递单号
    /// </summary> 
    [ExporterHeader(DisplayName = "快递单号", IsBold = false, Width = 100)]
    public string? Code { get; set; }

    /// <summary> 
    /// 创建时间
    /// </summary> 
    [ExporterHeader(DisplayName = "创建时间", IsBold = false, Width = 100)]
    public DateTime? CreateTime { get; set; }

}
