﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;
using XCode.Shards;

namespace HlktechIoT.Entity;

public partial class ProductProjectData : CubeEntityBase<ProductProjectData>
{
    #region 对象操作
    static ProductProjectData()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(ProductProjectId));

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add(new UserModule { AllowEmpty = false });
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add(new IPModule { AllowEmpty = false });

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (method == DataMethod.Insert && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
            if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
        }*/
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
        //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化ProductProjectData[产品项目资料表]数据……");

    //    var entity = new ProductProjectData();
    //    entity.ProductProjectId = 0;
    //    entity.ProductDataId = 0;
    //    entity.Status = 0;
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化ProductProjectData[产品项目资料表]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    /// <summary>产品项目Id</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public ProductProject? ProductProject => Extends.Get(nameof(ProductProject), k => ProductProject.FindById(ProductProjectId));

    /// <summary>产品项目Id</summary>
    [Map(nameof(ProductProjectId), typeof(ProductProject), "Id")]
    public String? ProductProjectName => ProductProject?.Name;
    /// <summary>产品资料Id</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public ProductData? ProductData => Extends.Get(nameof(ProductData), k => ProductData.FindById(ProductDataId));
    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="dataCategoryId">产品资料类别id</param>
    /// <param name="productProjectId">产品项目Id</param>
    /// <param name="productDataId">产品资料Id</param>
    /// <param name="status">状态 0待审核 1审核中 2审核通过 3审核失败</param>
    /// <param name="start">更新时间开始</param>
    /// <param name="end">更新时间结束</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<ProductProjectData> Search(Int32 dataCategoryId, Int32 productProjectId, Int32 productDataId, Int32 status, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if(dataCategoryId > 0)
        {
            var ids = ProductData.FindAllByProductDataCategoryId2(dataCategoryId).Select(e => e.Id);
            if (ids.Count() == 0) return [];
            exp &= _.ProductDataId.In(ids);
        }

        if (productProjectId > 0) exp &= _.ProductProjectId == productProjectId;
        if (productDataId > 0) exp &= _.ProductDataId == productDataId;
        if (status >= 0) exp &= _.Status == status;
        exp &= _.UpdateTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= SearchWhereByKeys(key);

        return FindAll(exp, page);
    }

    /// <summary>
    /// 根据产品项目Id和产品资料Id查找
    /// </summary>
    /// <param name="ProductProjectId"></param>
    /// <param name="ProductDataId"></param>
    /// <returns></returns>
    public static ProductProjectData? FindByProductProjectIdAndProductDataId(Int32 ProductProjectId,Int32 ProductDataId)
    {
        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.ProductProjectId == ProductProjectId && e.ProductDataId == ProductDataId);

        return Find(_.ProductProjectId == ProductProjectId & _.ProductDataId == ProductDataId);
    }

    /// <summary>
    /// 根据产品项目ID和产品资料类别Id2查找数量
    /// </summary>
    /// <returns></returns>
    public static Int32 FindCount(Int32 ProductProjectId,Int32 ProductDataCategoryId2,Int32 PType)
    {

        if (Meta.Session.Count < 1000) 
            return Meta.Cache.FindAll(e => e.ProductData?.ProductDataCategoryId2 == ProductDataCategoryId2 && e.ProductData?.Ptype == PType && e.ProductProjectId == ProductProjectId).Count;

        var list = FindAllByProductProjectId(ProductProjectId);

        var count = list.Count(e => e.ProductData?.ProductDataCategoryId2 == ProductDataCategoryId2 && e.ProductData?.Ptype == PType);

        return count;
    }

    /// <summary>
    /// 根据产品项目ID和产品资料类别Id2查找
    /// </summary>
    /// <param name="ProductProjectId"></param>
    /// <param name="DataCategoryId"></param>
    /// <returns></returns>
    public static ProductProjectData? FindByProductProjectIdAndDataCategoryId(Int32 ProductProjectId, Int32 DataCategoryId)
    {
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.ProductData?.ProductDataCategoryId2 == DataCategoryId && e.ProductProjectId == ProductProjectId);
        var list = FindAllByProductProjectId(ProductProjectId);
        return list.FirstOrDefault(e=>e.ProductData?.ProductDataCategoryId2 == DataCategoryId);
    }

    // Select Count(Id) as Id,Category From DH_ProductProjectData Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<ProductProjectData> _CategoryCache = new(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public IProductProjectData ToModel()
    {
        var model = new ProductProjectData();
        model.Copy(this);

        return model;
    }

    #endregion
}
