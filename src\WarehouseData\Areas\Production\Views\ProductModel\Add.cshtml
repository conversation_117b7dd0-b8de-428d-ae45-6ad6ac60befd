﻿@{
    Html.AppendTitleParts(T("添加产品型号").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
        white-space:nowrap;
        min-width:110px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
    .select{
        max-width: 420px !important;
        border: 1px solid #e6e6e6;
    }
</style>
<script asp-location="Head">
    var closeThis = '@T("关 闭 当 前")';
    var closeOther = '@T("关 闭 其 他")';
    var closeAll = '@T("关 闭 全 部")';

    var jsMenuStyle = "@T("菜单风格")";
    var jsTopStyle = "@T("顶部风格")";
    var jsMenu = "@T("菜单")";
    var jsView = "@T("视图")";
    var jsBanner = "@T("通栏")";
    var jsThroughColor = "@T("通色")";
    var jsFooter = "@T("页脚")";
    var jsMoreSettings = "@T("更多设置")";
    var jsOpen = "@T("开")";
    var jsClose = "@T("关")";
    var jsThemeColor = "@T("主题配色")";
    var layuiNoData = '@T("无数据")';
    var layuiAsc = "@T("升序")";
    var layuiDesc = "@T("降序")";

    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
</script>
<div class="containers">
    <form class="layui-form">

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("型号名称")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Name" placeholder="@T("请输入产品型号名称")" autocomplete="off" class="layui-input" lay-filter="" value="">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span>*</span>@T("物料编号")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Material" placeholder="@T("请输入物料编号")" autocomplete="off" class="layui-input" lay-filter="" value="">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("软件工程师")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <div id="demo1" style=" width: 100%;"></div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("硬件工程师")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <div id="demo2" style=" width: 100%;"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">@T("启用")</label>
                <div class="layui-input-inline">
                    <input type="checkbox" name="Status" lay-filter="Status" lay-skin="switch" checked>
                </div>
             </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("备注")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Remark" placeholder="@T("请输入备注")" autocomplete="off" class="layui-input" lay-filter="Versions" value="">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("生成SN/五元组")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <input type="checkbox" name="NeedSn" lay-filter="NeedSn" lay-skin="switch">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("生成主Mac")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <input type="checkbox" name="NeedMac" lay-filter="NeedMac" lay-skin="switch">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("生成副Mac")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <input type="checkbox" name="NeedMac1" lay-filter="NeedMac1" lay-skin="switch">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("PCB周期")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <input type="checkbox" name="PCBCycle" lay-filter="PCBCycle" lay-skin="switch">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("屏蔽罩周期")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <input type="checkbox" name="ShieldCycle" lay-filter="ShieldCycle" lay-skin="switch">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("主芯片周期")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <input type="checkbox" name="MainChipCycle" lay-filter="MainChipCycle" lay-skin="switch">
            </div>
        </div>

        <div class="layui-form-item btn">
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" style="margin-left:20px" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var laydate = layui.laydate;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        var demo1 = xmSelect.render({
            el: '#demo1',
            radio: true, //设置单选
            name: 'SoftwareId',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            disabled: false, // 设置禁用
            remoteMethod: function (val, cb, show, pageIndex) 
            {  // 远程方法
                var obj = [];
                // 接口数据
                $.post('@Url.Action("SearchUser")', { keyword: val, page: pageIndex }, function (res) 
                {
                    // console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                            cb(res.data, res.extdata.PageCount);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });
            },
            on: function (data) 
            {  
                // 监听选择
            }
        });

        var demo2 = xmSelect.render({
            el: '#demo2',
            radio: true, //设置单选
            name: 'HardwareId',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            disabled: false, // 设置禁用
            remoteMethod: function (val, cb, show, pageIndex) 
            {  // 远程方法
                var obj = [];
                // 接口数据
                $.post('@Url.Action("SearchUser")', { keyword: val, page: pageIndex }, function (res) 
                {
                    // console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                            cb(res.data, res.extdata.PageCount);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });
            },
            on: function (data) 
            {  
                // 监听选择
            }
        });

        // 监听主Mac开关
        form.on('switch(NeedMac)', function (data) {
            if (!data.elem.checked) {
                // 主Mac未启用，自动关闭并禁用副Mac
                $("input[name='NeedMac1']").prop("checked", false);
                $("input[name='NeedMac1']").prop("disabled", true);
                form.render('checkbox');
            } else {
                // 主Mac启用，允许副Mac可选
                $("input[name='NeedMac1']").prop("disabled", false);
                form.render('checkbox');
            }
        });

        // 页面初始化时判断一次
        $(function () {
            if (!$("input[name='NeedMac']").prop("checked")) {
                $("input[name='NeedMac1']").prop("checked", false);
                $("input[name='NeedMac1']").prop("disabled", true);
                layui.form.render('checkbox');
            }
        });

        form.on('submit(Submit)', function (data) {
            // console.log(data);

            data.field.Status = data.field.Status == 'on';
            data.field.NeedSn = data.field.NeedSn == 'on';
            data.field.NeedMac = data.field.NeedMac == 'on';
            data.field.NeedMac1 = data.field.NeedMac1 == 'on';
            data.field.PCBCycle = data.field.PCBCycle == 'on';
            data.field.ShieldCycle = data.field.ShieldCycle == 'on';
            data.field.MainChipCycle = data.field.MainChipCycle == 'on';

            if(data.field.NeedSn == false && data.field.NeedMac == false && data.field.NeedMac1 == false && data.field.PType != 1){
                abp.notify.error('@T("请至少启用一个SN或MAC或MAC1")');
                return;
            }

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("Add")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>