﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>产品型号</summary>
public partial class ProductTypeModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>型号名称</summary>
    public String Name { get; set; } = null!;

    /// <summary>软件工程师</summary>
    public Int32 SoftwareId { get; set; }

    /// <summary>硬件工程师</summary>
    public Int32 HardwareId { get; set; }

    /// <summary>状态</summary>
    public Boolean Status { get; set; }

    /// <summary>物料编号</summary>
    public String? Material { get; set; }

    /// <summary>备注</summary>
    public String? Remark { get; set; }

    /// <summary>产品类型。0为无，1为电源</summary>
    public Int16 PType { get; set; }

    /// <summary>生成Sn</summary>
    public Boolean NeedSn { get; set; }

    /// <summary>生成主Mac</summary>
    public Boolean NeedMac { get; set; }

    /// <summary>生成副Mac</summary>
    public Boolean NeedMac1 { get; set; }

    /// <summary>PCB周期</summary>
    public Boolean PCBCycle { get; set; }

    /// <summary>屏蔽罩周期</summary>
    public Boolean ShieldCycle { get; set; }

    /// <summary>主芯片周期</summary>
    public Boolean MainChipCycle { get; set; }

    /// <summary>测试项</summary>
    public String? TestItems { get; set; }

    /// <summary>配置项</summary>
    public String? ExpansionItems { get; set; }

    /// <summary>模组类型(0:无,1:ACDC,2:DCDC)</summary>
    public Int32 ModuleType { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductType model)
    {
        Id = model.Id;
        Name = model.Name;
        SoftwareId = model.SoftwareId;
        HardwareId = model.HardwareId;
        Status = model.Status;
        Material = model.Material;
        Remark = model.Remark;
        PType = model.PType;
        NeedSn = model.NeedSn;
        NeedMac = model.NeedMac;
        NeedMac1 = model.NeedMac1;
        PCBCycle = model.PCBCycle;
        ShieldCycle = model.ShieldCycle;
        MainChipCycle = model.MainChipCycle;
        TestItems = model.TestItems;
        ExpansionItems = model.ExpansionItems;
        ModuleType = model.ModuleType;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
