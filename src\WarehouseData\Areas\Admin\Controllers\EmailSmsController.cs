﻿using System.ComponentModel;
using System.Dynamic;

using DG.Web.Framework;

using DH;
using DH.Core.Domain.Localization;
using DH.Entity;
using DH.Models;
using DH.Services;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.Configs;
using Pek.Models;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>
/// 邮箱短信管理
/// </summary>
[DisplayName("邮箱短信管理")]
[Description("邮箱短信设置，管理系统中的邮箱短信配置及消息模板管理")]
[AdminArea]
[DHMenu(88,ParentMenuName = "System", CurrentMenuUrl = "", CurrentMenuName = "EmailSms", LastUpdate = "20240124")]
public class EmailSmsController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 88;
}

/// <summary>
/// 邮箱设置
/// </summary>
[DisplayName("邮箱设置")]
[Description("邮箱设置")]
[AdminArea]
[DHMenu(100,ParentMenuName = "EmailSms", CurrentMenuUrl = "~/{area}/Email", CurrentMenuName = "Email", LastUpdate = "20240124")]
public class EmailController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 100;

    /// <summary>
    /// 邮箱设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("邮箱设置")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 邮件设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("邮件设置")]
    public IActionResult UpdateEmailSetting(String email_host, Boolean email_secure, Int32 email_port, String email_addr, String email_id, String email_pass, String fromname)
    {
        var result = new DResult();

        var path = @"Settings/Mail.json";
        ConfigFileHelper.AddOrUpdateAppSetting("Email:Host", email_host, path);
        ConfigFileHelper.AddOrUpdateAppSetting("Email:Port", email_port, path);
        ConfigFileHelper.AddOrUpdateAppSetting("Email:UserName", email_id, path);
        ConfigFileHelper.AddOrUpdateAppSetting("Email:Password", email_pass, path);
        ConfigFileHelper.AddOrUpdateAppSetting("Email:From", email_addr, path);
        ConfigFileHelper.AddOrUpdateAppSetting("Email:FromName", fromname, path);
        ConfigFileHelper.AddOrUpdateAppSetting("Email:IsSSL", email_secure, path);

        result.success = true;
        result.msg = GetResource("保存成功");

        return Json(result);
    }
}

/// <summary>
/// 短信设置
/// </summary>
[DisplayName("短信设置")]
[Description("短信设置")]
[AdminArea]
[DHMenu(90,ParentMenuName = "EmailSms", CurrentMenuUrl = "~/{area}/Sms", CurrentMenuName = "Sms", LastUpdate = "20240124")]
public class SmsController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 90;

    /// <summary>
    /// 短信平台设置首页
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("短信平台设置")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 根据供应商名查询数据
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("管理员管理")]
    public IActionResult GetList(String name)
    {
        var list = SmsSettings.GetAll();
        if (!name.IsNullOrWhiteSpace())
        {
            list = list.FindAll(e => e.DisplayName.Contains(name, StringComparison.OrdinalIgnoreCase));
        }

        return Json(new { code = 0, msg = "success", count = list.Count, data = list });
    }

    /// <summary>修改状态</summary>
    /// <returns></returns>
    [DisplayName("修改状态")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult ModifyState(Int32 Id, Boolean Status)
    {
        var result = new DResult();

        var model = cdb.FindById<SmsSettings>(Id);
        if (model == null)
        {
            result.msg = GetResource("状态调整出错");
            return Json(result);
        }

        var list = SmsSettings.GetAll().FindAll(e => e.SmsType == model.SmsType);
        foreach (var item in list)
        {
            item.IsEnabled = false;
            item.Update();
        }

        model.IsEnabled = Status;
        model.Update();

        result.success = true;
        result.msg = GetResource("状态调整成功");

        return Json(result);
    }

    /// <summary>
    /// 编辑短信配置
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [DisplayName("编辑短信配置")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Edit(Int32 Id)
    {
        var model = cdb.FindById<SmsSettings>(Id);
        if (model == null)
        {
            return Content(GetResource("指定短信平台不存在！"));
        }

        return View(model);
    }

    /// <summary>
    /// 提交短信平台设置
    /// </summary>
    /// <param name="smscf_sign">短信签名</param>
    /// <param name="Id">短信服务Id</param>
    /// <param name="smscf_wj_username">短信平台账号</param>
    /// <param name="smscf_wj_key">短信平台密码</param>
    /// <param name="sms_register">手机注册</param>
    /// <param name="sms_isEnabled">是否启用</param>
    /// <param name="sms_login">手机登录</param>
    /// <param name="sms_password">找回密码</param>
    /// <param name="AppId">腾讯AppId</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("提交短信平台设置")]
    [HttpPost]
    public IActionResult Edit(String smscf_sign, Int32 Id, String smscf_wj_username, String smscf_wj_key, String sms_register, String sms_isEnabled, String sms_login, String sms_password, String AppId)
    {
        var result = new DResult();

        var model = cdb.FindById<SmsSettings>(Id);
        if (model == null)
        {
            result.msg = GetResource("指定短信平台不存在！");
            return Json(result);
        }

        model.AccessKey = smscf_wj_username;
        model.AccessSecret = smscf_wj_key;
        model.PassKey = smscf_sign;

        var list = SmsSettings.GetAll().FindAll(e => e.SmsType == model.SmsType);
        foreach (var item in list)
        {
            item.IsEnabled = false;
            item.Update();
        }

        model.IsEnabled = sms_isEnabled == "on";
        model.SmsLogin = sms_login == "on";
        model.SmsRegister = sms_register == "on";
        model.SmsPassword = sms_password == "on";
        model.AppId = AppId;
        model.Update();

        result.success = true;
        result.msg = GetResource("保存成功");

        return Json(result);
    }
}

/// <summary>
/// 模板管理
/// </summary>
[DisplayName("模板管理")]
[Description("模板管理")]
[AdminArea]
[DHMenu(80,ParentMenuName = "EmailSms", CurrentMenuUrl = "~/{area}/Template", CurrentMenuName = "Template", LastUpdate = "20240124")]
public class TemplateController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 80;

    /// <summary>
    /// 模板管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("模板管理")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 短信模板
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("短信模板")]
    public IActionResult GetList(String name, Int32 page = 1, Int32 limit = 10)
    {
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Desc = true,
            Sort = OtherMsgTpl._.Id,
        };

        var List = OtherMsgTpl.Search(pages, name).Select(e =>
        {
            var model = OtherMsgTplLan.FindByOIdAndLId(e.Id, WorkingLanguage.Id, true);
            return new { e.Id, model.MName, model.MTitle, model.MContent, model.SmsTplId, e.CreateTime, e.UpdateTime, e.MCode };
        });
        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data = List });
    }

    /// <summary>
    /// 新增模板信息
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("新增模板信息")]
    public IActionResult Add()
    {
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

        return View();
    }

    /// <summary>
    /// 新增模板信息
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("新增模板信息")]
    [HttpPost]
    public IActionResult Add(String Name, String MTitle, String MContent, String SmsTplId, String Code)
    {
        var result = new DResult();

        MTitle = MTitle.SafeString().Trim();
        if (MTitle.IsNullOrEmpty())
        {
            result.msg = GetResource("标题不能为空");
            return Json(result);
        }

        Name = Name.SafeString().Trim();
        if (Name.IsNullOrEmpty())
        {
            result.msg = GetResource("模板名称不能为空");
            return Json(result);
        }

        //SmsTplId = SmsTplId.SafeString().Trim();
        //if (SmsTplId.IsNullOrEmpty())
        //{
        //    result.msg = GetResource("短信模板Id不能为空");
        //    return Json(result);

        //}

        MContent = MContent.SafeString().Trim();
        if (MContent.IsNullOrEmpty())
        {
            result.msg = GetResource("正文不能为空");
            return Json(result);
        }

        Code = Code.SafeString().Trim();
        if (Code.IsNullOrEmpty())
        {
            result.msg = GetResource("模板标识不能为空");
            return Json(result);
        }

        using (var tran1 = OtherMsgTpl.Meta.CreateTrans())
        {
            var model = new OtherMsgTpl();

            model.MName = Name;
            model.MTitle = MTitle;
            model.MContent = MContent;
            model.SmsTplId = SmsTplId;
            model.MCode = Code;
            model.Insert();

            var localizationSettings = LocalizationSettings.Current;

            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

                var lanlist = OtherMsgTplLan.FindAllByOId(model.Id);
                foreach (var item in Languagelist)
                {
                    var modelLan = lanlist.Find(x => x.LId == item.Id);
                    if (modelLan == null)
                    {
                        modelLan = new OtherMsgTplLan();
                    }

                    var NameLan = GetRequest($"[{item.Id}].MTitle").SafeString().Trim();
                    var ContentLan = GetRequest($"[{item.Id}].MContent").SafeString().Trim();
                    var MNameLan = GetRequest($"[{item.Id}].Name").SafeString().Trim();
                    var SmsTplIdLan = GetRequest($"[{item.Id}].SmsTplId").SafeString().Trim();
                    modelLan.MTitle = NameLan;
                    modelLan.MContent = ContentLan;
                    modelLan.MName = MNameLan;
                    modelLan.SmsTplId = SmsTplIdLan;
                    modelLan.OId = model.Id;
                    modelLan.LId = item.Id;
                    modelLan.Save();
                }
            }

            tran1.Commit();
        }

        result.success = true;
        result.msg = GetResource($"编辑成功");
        return Json(result);
    }

    /// <summary>
    /// 修改模板信息
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改模板信息")]
    public IActionResult Edit(int Id)
    {
        var model = OtherMsgTpl.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("数据不存在或已被删除"));
        }

        dynamic viewModel = new ExpandoObject();
        viewModel.Model = model;

        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

        return View(viewModel);
    }

    /// <summary>
    /// 修改模板信息
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="MTitle"></param>
    /// <param name="MContent"></param>
    /// <param name="SmsTplId"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("修改模板信息")]
    public IActionResult Edit(int Id, string MTitle, string MContent, string SmsTplId)
    {
        var result = new DResult();

        MTitle = MTitle.SafeString().Trim();
        if (MTitle.IsNullOrEmpty())
        {
            result.msg = GetResource("标题不能为空");
            return Json(result);

        }

        //SmsTplId = SmsTplId.SafeString().Trim();
        //if (SmsTplId.IsNullOrEmpty())
        //{
        //    result.msg = GetResource("短信模板Id不能为空");
        //    return Json(result);

        //}

        MContent = MContent.SafeString().Trim();
        if (MContent.IsNullOrEmpty())
        {
            result.msg = GetResource("正文不能为空");
            return Json(result);

        }
        var model = OtherMsgTpl.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("数据不存在或已被删除！");
            return Json(result);

        }

        using (var tran1 = OtherMsgTpl.Meta.CreateTrans())
        {
            model.MTitle = MTitle;
            model.MContent = MContent;
            model.SmsTplId = SmsTplId;
            model.Update();

            var localizationSettings = LocalizationSettings.Current;

            if (localizationSettings.IsEnable)
            {
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言

                var lanlist = OtherMsgTplLan.FindAllByOId(model.Id);
                foreach (var item in Languagelist)
                {
                    var modelLan = lanlist.Find(x => x.LId == item.Id);
                    if (modelLan == null)
                    {
                        modelLan = new OtherMsgTplLan();
                    }

                    var NameLan = GetRequest($"[{item.Id}].MTitle").SafeString().Trim();
                    var ContentLan = GetRequest($"[{item.Id}].MContent").SafeString().Trim();
                    var MNameLan = GetRequest($"[{item.Id}].Name").SafeString().Trim();
                    var SmsTplIdLan = GetRequest($"[{item.Id}].SmsTplId").SafeString().Trim();
                    modelLan.MTitle = NameLan;
                    modelLan.MContent = ContentLan;
                    modelLan.MName = MNameLan;
                    modelLan.SmsTplId = SmsTplIdLan;
                    modelLan.OId = model.Id;
                    modelLan.LId = item.Id;
                    modelLan.Save();
                }
            }

            tran1.Commit();
        }

        result.success = true;
        result.msg = GetResource($"编辑成功");
        return Json(result);
    }

    /// <summary>
    /// 删除模板信息
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [HttpPost]
    [DisplayName("删除模板信息")]
    public IActionResult Delete(int Id)
    {
        var result = new DResult();

        var model = OtherMsgTpl.FindById(Id);
        if(model != null)
        {
            var list = OtherMsgTplLan.FindAllByOId(model.Id);
            list.Delete();

            model.Delete();
        }

        result.success = true;
        result.msg = GetResource($"删除成功");
        return Json(result);
    }
}

/// <summary>
/// 消息记录
/// </summary>
[DisplayName("消息记录")]
[Description("消息记录")]
[AdminArea]
[DHMenu(70,ParentMenuName = "EmailSms", CurrentMenuUrl = "~/{area}/Record", CurrentMenuName = "Record", LastUpdate = "20240223")]
public class RecordController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 70;

    /// <summary>
    /// 消息记录
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("消息记录")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 短信记录接口
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("短信记录")]
    public IActionResult GetList(String date, string member_name, string smslog_phone, Int32 page = 1, Int32 limit = 10)
    {
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Desc = true,
            Sort = SendLog._.Id,
        };
        DateTime startTime = default;
        DateTime endTime = default;

        if (!date.IsNullOrWhiteSpace())
        {
            var searchDateSplite = date.Trim().Split(GetResource("到"));
            if (searchDateSplite.Length == 2)
            {
                startTime = searchDateSplite[0].ToDateTime();
                endTime = searchDateSplite[1].ToDateTime().AddDays(1).Date;
            }
        }
        var List = SendLog.Searchs(smslog_phone, member_name, startTime, endTime, pages);
        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data = List });
    }

}