﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>生产订单补单</summary>
public partial class ProductSupplementalOrdersModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int64 Id { get; set; }

    /// <summary>订单号</summary>
    public String OrderId { get; set; } = null!;

    /// <summary>生产订单编号</summary>
    public Int64 ProductOrderId { get; set; }

    /// <summary>产品型号编号</summary>
    public Int32 ProductTypeId { get; set; }

    /// <summary>数量</summary>
    public Int32 Quantity { get; set; }

    /// <summary>补单原因</summary>
    public String? Reason { get; set; }

    /// <summary>审核状态 0待审核 1审核中 2已审核 3审核失败</summary>
    public Int32 Status { get; set; }

    /// <summary>审核备注</summary>
    public String? Remark { get; set; }

    /// <summary>审核时间</summary>
    public DateTime AuditTime { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductSupplementalOrders model)
    {
        Id = model.Id;
        OrderId = model.OrderId;
        ProductOrderId = model.ProductOrderId;
        ProductTypeId = model.ProductTypeId;
        Quantity = model.Quantity;
        Reason = model.Reason;
        Status = model.Status;
        Remark = model.Remark;
        AuditTime = model.AuditTime;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
