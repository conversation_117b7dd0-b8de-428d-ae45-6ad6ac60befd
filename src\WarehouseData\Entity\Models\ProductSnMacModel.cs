﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>生产SnMac</summary>
public partial class ProductSnMacModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int64 Id { get; set; }

    /// <summary>生产订单编号</summary>
    public Int64 ProductOrdersId { get; set; }

    /// <summary>订单号。冗余字段</summary>
    public String OrderId { get; set; } = null!;

    /// <summary>设备Sn/DeviceName</summary>
    public String? Sn { get; set; }

    /// <summary>设备Mac地址</summary>
    public String? Mac { get; set; }

    /// <summary>设备Mac地址</summary>
    public String? Mac1 { get; set; }

    /// <summary>对应的短网址</summary>
    public String? ShorUrl { get; set; }

    /// <summary>文件路径</summary>
    public String? FilePath { get; set; }

    /// <summary>测试结果。以Json的方式存储，比如某一项为key,成功与否为value，方便后续解析json根据key和value来渲染到页面上</summary>
    public String? Content { get; set; }

    /// <summary>用户上传数据</summary>
    public String? DataInfo { get; set; }

    /// <summary>是否允许重复</summary>
    public Boolean IsRepetition { get; set; }

    /// <summary>是否允许重复时间段</summary>
    public DateTime IsRepetitionDate { get; set; }

    /// <summary>是否生产校验</summary>
    public Boolean IsValidate { get; set; }

    /// <summary>是否已被消费</summary>
    public Boolean IsConsumed { get; set; }

    /// <summary>消费时间</summary>
    public DateTime ConsumedTime { get; set; }

    /// <summary>是否确认消费</summary>
    public Boolean IsConfirmConsumed { get; set; }

    /// <summary>确认消费时间</summary>
    public DateTime ConfirmConsumedTime { get; set; }

    /// <summary>消费时间</summary>
    public DateTime Double { get; set; }

    /// <summary>PCB周期</summary>
    public String? PCBCycle { get; set; }

    /// <summary>屏蔽罩周期</summary>
    public String? ShieldCycle { get; set; }

    /// <summary>主芯片周期</summary>
    public String? MainChipCycle { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductSnMac model)
    {
        Id = model.Id;
        ProductOrdersId = model.ProductOrdersId;
        OrderId = model.OrderId;
        Sn = model.Sn;
        Mac = model.Mac;
        Mac1 = model.Mac1;
        ShorUrl = model.ShorUrl;
        FilePath = model.FilePath;
        Content = model.Content;
        DataInfo = model.DataInfo;
        IsRepetition = model.IsRepetition;
        IsRepetitionDate = model.IsRepetitionDate;
        IsValidate = model.IsValidate;
        IsConsumed = model.IsConsumed;
        ConsumedTime = model.ConsumedTime;
        IsConfirmConsumed = model.IsConfirmConsumed;
        ConfirmConsumedTime = model.ConfirmConsumedTime;
        Double = model.Double;
        PCBCycle = model.PCBCycle;
        ShieldCycle = model.ShieldCycle;
        MainChipCycle = model.MainChipCycle;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
