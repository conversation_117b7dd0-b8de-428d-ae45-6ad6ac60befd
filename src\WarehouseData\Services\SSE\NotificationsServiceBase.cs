﻿using DH.ServerSentEvents;

namespace HlktechIoT.Services.SSE;

internal abstract class NotificationsServiceBase {
    #region Fields
    private INotificationsServerSentEventsService _notificationsServerSentEventsService;
    #endregion

    #region Constructor
    protected NotificationsServiceBase(INotificationsServerSentEventsService notificationsServerSentEventsService)
    {
        _notificationsServerSentEventsService = notificationsServerSentEventsService;
    }
    #endregion

    #region Methods
    protected Task SendSseEventAsync(string notification, bool alert)
    {
        return _notificationsServerSentEventsService.SendEventAsync(new ServerSentEvent
        {
            Type = alert ? "alert" : null,
            Data = new List<string>(notification.Split(new string[] { "\r\n", "\n" }, StringSplitOptions.None))
        });
    }
    #endregion
}