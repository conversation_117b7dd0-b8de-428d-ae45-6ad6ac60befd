﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>电源生产数据</summary>
[Serializable]
[DataObject]
[Description("电源生产数据")]
[BindIndex("IX_DH_PowerProduction_OrderId", false, "OrderId")]
[BindIndex("IX_DH_PowerProduction_CompanyId", false, "CompanyId")]
[BindIndex("IX_DH_PowerProduction_ProductTypeId", false, "ProductTypeId")]
[BindIndex("IX_DH_PowerProduction_CreateTime", false, "CreateTime")]
[BindIndex("IX_DH_PowerProduction_BatchField1", false, "BatchField1")]
[BindIndex("IX_DH_PowerProduction_BatchField2", false, "BatchField2")]
[BindIndex("IX_DH_PowerProduction_BatchField3", false, "BatchField3")]
[BindIndex("IX_DH_PowerProduction_BatchField4", false, "BatchField4")]
[BindIndex("IX_DH_PowerProduction_BatchField5", false, "BatchField5")]
[BindIndex("IX_DH_PowerProduction_BatchField6", false, "BatchField6")]
[BindIndex("IX_DH_PowerProduction_BatchField7", false, "BatchField7")]
[BindIndex("IX_DH_PowerProduction_BatchField8", false, "BatchField8")]
[BindIndex("IX_DH_PowerProduction_BatchField9", false, "BatchField9")]
[BindIndex("IX_DH_PowerProduction_BatchField10", false, "BatchField10")]
[BindTable("DH_PowerProduction", Description = "电源生产数据", ConnName = "DH", DbType = DatabaseType.None)]
public partial class PowerProduction : IPowerProduction, IEntity<IPowerProduction>
{
    #region 属性
    private Int64 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, false, false, 0)]
    [BindColumn("Id", "编号", "", DataScale = "timeShard:yy")]
    public Int64 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String _OrderId = null!;
    /// <summary>订单号</summary>
    [DisplayName("订单号")]
    [Description("订单号")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("OrderId", "订单号", "", Master = true)]
    public String OrderId { get => _OrderId; set { if (OnPropertyChanging("OrderId", value)) { _OrderId = value; OnPropertyChanged("OrderId"); } } }

    private String? _Material;
    /// <summary>模块物料编号</summary>
    [DisplayName("模块物料编号")]
    [Description("模块物料编号")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("Material", "模块物料编号", "")]
    public String? Material { get => _Material; set { if (OnPropertyChanging("Material", value)) { _Material = value; OnPropertyChanged("Material"); } } }

    private Int32 _ProductTypeId;
    /// <summary>产品型号编号</summary>
    [DisplayName("产品型号编号")]
    [Description("产品型号编号")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ProductTypeId", "产品型号编号", "")]
    public Int32 ProductTypeId { get => _ProductTypeId; set { if (OnPropertyChanging("ProductTypeId", value)) { _ProductTypeId = value; OnPropertyChanged("ProductTypeId"); } } }

    private Int32 _PowerTypeItemId;
    /// <summary>电源型号配置编号</summary>
    [DisplayName("电源型号配置编号")]
    [Description("电源型号配置编号")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("PowerTypeItemId", "电源型号配置编号", "")]
    public Int32 PowerTypeItemId { get => _PowerTypeItemId; set { if (OnPropertyChanging("PowerTypeItemId", value)) { _PowerTypeItemId = value; OnPropertyChanged("PowerTypeItemId"); } } }

    private String? _Sn;
    /// <summary>SN</summary>
    [DisplayName("SN")]
    [Description("SN")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Sn", "SN", "")]
    public String? Sn { get => _Sn; set { if (OnPropertyChanging("Sn", value)) { _Sn = value; OnPropertyChanged("Sn"); } } }

    private DateTime _TestTime;
    /// <summary>测试时间</summary>
    [DisplayName("测试时间")]
    [Description("测试时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("TestTime", "测试时间", "")]
    public DateTime TestTime { get => _TestTime; set { if (OnPropertyChanging("TestTime", value)) { _TestTime = value; OnPropertyChanged("TestTime"); } } }

    private String? _Remak;
    /// <summary>备注</summary>
    [DisplayName("备注")]
    [Description("备注")]
    [DataObjectField(false, false, true, 512)]
    [BindColumn("Remak", "备注", "")]
    public String? Remak { get => _Remak; set { if (OnPropertyChanging("Remak", value)) { _Remak = value; OnPropertyChanged("Remak"); } } }

    private String? _Content;
    /// <summary>测试结果。使用Json存储</summary>
    [DisplayName("测试结果")]
    [Description("测试结果。使用Json存储")]
    [DataObjectField(false, false, true, 1024)]
    [BindColumn("Content", "测试结果。使用Json存储", "")]
    public String? Content { get => _Content; set { if (OnPropertyChanging("Content", value)) { _Content = value; OnPropertyChanged("Content"); } } }

    private Boolean _TestResult;
    /// <summary>测试结果。成功或者失败</summary>
    [DisplayName("测试结果")]
    [Description("测试结果。成功或者失败")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("TestResult", "测试结果。成功或者失败", "")]
    public Boolean TestResult { get => _TestResult; set { if (OnPropertyChanging("TestResult", value)) { _TestResult = value; OnPropertyChanged("TestResult"); } } }

    private String? _CompanyId;
    /// <summary>检测工厂编号</summary>
    [DisplayName("检测工厂编号")]
    [Description("检测工厂编号")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CompanyId", "检测工厂编号", "")]
    public String? CompanyId { get => _CompanyId; set { if (OnPropertyChanging("CompanyId", value)) { _CompanyId = value; OnPropertyChanged("CompanyId"); } } }

    private String? _CompanyName;
    /// <summary>检测工厂名称</summary>
    [DisplayName("检测工厂名称")]
    [Description("检测工厂名称")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CompanyName", "检测工厂名称", "")]
    public String? CompanyName { get => _CompanyName; set { if (OnPropertyChanging("CompanyName", value)) { _CompanyName = value; OnPropertyChanged("CompanyName"); } } }

    private String? _TestStation;
    /// <summary>测试工位</summary>
    [DisplayName("测试工位")]
    [Description("测试工位")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("TestStation", "测试工位", "")]
    public String? TestStation { get => _TestStation; set { if (OnPropertyChanging("TestStation", value)) { _TestStation = value; OnPropertyChanged("TestStation"); } } }

    private String? _BatchField1;
    /// <summary>批次字段1</summary>
    [DisplayName("批次字段1")]
    [Description("批次字段1")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("BatchField1", "批次字段1", "")]
    public String? BatchField1 { get => _BatchField1; set { if (OnPropertyChanging("BatchField1", value)) { _BatchField1 = value; OnPropertyChanged("BatchField1"); } } }

    private String? _BatchField2;
    /// <summary>批次字段2</summary>
    [DisplayName("批次字段2")]
    [Description("批次字段2")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("BatchField2", "批次字段2", "")]
    public String? BatchField2 { get => _BatchField2; set { if (OnPropertyChanging("BatchField2", value)) { _BatchField2 = value; OnPropertyChanged("BatchField2"); } } }

    private String? _BatchField3;
    /// <summary>批次字段3</summary>
    [DisplayName("批次字段3")]
    [Description("批次字段3")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("BatchField3", "批次字段3", "")]
    public String? BatchField3 { get => _BatchField3; set { if (OnPropertyChanging("BatchField3", value)) { _BatchField3 = value; OnPropertyChanged("BatchField3"); } } }

    private String? _BatchField4;
    /// <summary>批次字段4</summary>
    [DisplayName("批次字段4")]
    [Description("批次字段4")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("BatchField4", "批次字段4", "")]
    public String? BatchField4 { get => _BatchField4; set { if (OnPropertyChanging("BatchField4", value)) { _BatchField4 = value; OnPropertyChanged("BatchField4"); } } }

    private String? _BatchField5;
    /// <summary>批次字段5</summary>
    [DisplayName("批次字段5")]
    [Description("批次字段5")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("BatchField5", "批次字段5", "")]
    public String? BatchField5 { get => _BatchField5; set { if (OnPropertyChanging("BatchField5", value)) { _BatchField5 = value; OnPropertyChanged("BatchField5"); } } }

    private String? _BatchField6;
    /// <summary>批次字段6</summary>
    [DisplayName("批次字段6")]
    [Description("批次字段6")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("BatchField6", "批次字段6", "")]
    public String? BatchField6 { get => _BatchField6; set { if (OnPropertyChanging("BatchField6", value)) { _BatchField6 = value; OnPropertyChanged("BatchField6"); } } }

    private String? _BatchField7;
    /// <summary>批次字段7</summary>
    [DisplayName("批次字段7")]
    [Description("批次字段7")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("BatchField7", "批次字段7", "")]
    public String? BatchField7 { get => _BatchField7; set { if (OnPropertyChanging("BatchField7", value)) { _BatchField7 = value; OnPropertyChanged("BatchField7"); } } }

    private String? _BatchField8;
    /// <summary>批次字段8</summary>
    [DisplayName("批次字段8")]
    [Description("批次字段8")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("BatchField8", "批次字段8", "")]
    public String? BatchField8 { get => _BatchField8; set { if (OnPropertyChanging("BatchField8", value)) { _BatchField8 = value; OnPropertyChanged("BatchField8"); } } }

    private String? _BatchField9;
    /// <summary>批次字段9</summary>
    [DisplayName("批次字段9")]
    [Description("批次字段9")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("BatchField9", "批次字段9", "")]
    public String? BatchField9 { get => _BatchField9; set { if (OnPropertyChanging("BatchField9", value)) { _BatchField9 = value; OnPropertyChanged("BatchField9"); } } }

    private String? _BatchField10;
    /// <summary>批次字段10</summary>
    [DisplayName("批次字段10")]
    [Description("批次字段10")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("BatchField10", "批次字段10", "")]
    public String? BatchField10 { get => _BatchField10; set { if (OnPropertyChanging("BatchField10", value)) { _BatchField10 = value; OnPropertyChanged("BatchField10"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IPowerProduction model)
    {
        Id = model.Id;
        OrderId = model.OrderId;
        Material = model.Material;
        ProductTypeId = model.ProductTypeId;
        PowerTypeItemId = model.PowerTypeItemId;
        Sn = model.Sn;
        TestTime = model.TestTime;
        Remak = model.Remak;
        Content = model.Content;
        TestResult = model.TestResult;
        CompanyId = model.CompanyId;
        CompanyName = model.CompanyName;
        TestStation = model.TestStation;
        BatchField1 = model.BatchField1;
        BatchField2 = model.BatchField2;
        BatchField3 = model.BatchField3;
        BatchField4 = model.BatchField4;
        BatchField5 = model.BatchField5;
        BatchField6 = model.BatchField6;
        BatchField7 = model.BatchField7;
        BatchField8 = model.BatchField8;
        BatchField9 = model.BatchField9;
        BatchField10 = model.BatchField10;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "OrderId" => _OrderId,
            "Material" => _Material,
            "ProductTypeId" => _ProductTypeId,
            "PowerTypeItemId" => _PowerTypeItemId,
            "Sn" => _Sn,
            "TestTime" => _TestTime,
            "Remak" => _Remak,
            "Content" => _Content,
            "TestResult" => _TestResult,
            "CompanyId" => _CompanyId,
            "CompanyName" => _CompanyName,
            "TestStation" => _TestStation,
            "BatchField1" => _BatchField1,
            "BatchField2" => _BatchField2,
            "BatchField3" => _BatchField3,
            "BatchField4" => _BatchField4,
            "BatchField5" => _BatchField5,
            "BatchField6" => _BatchField6,
            "BatchField7" => _BatchField7,
            "BatchField8" => _BatchField8,
            "BatchField9" => _BatchField9,
            "BatchField10" => _BatchField10,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToLong(); break;
                case "OrderId": _OrderId = Convert.ToString(value); break;
                case "Material": _Material = Convert.ToString(value); break;
                case "ProductTypeId": _ProductTypeId = value.ToInt(); break;
                case "PowerTypeItemId": _PowerTypeItemId = value.ToInt(); break;
                case "Sn": _Sn = Convert.ToString(value); break;
                case "TestTime": _TestTime = value.ToDateTime(); break;
                case "Remak": _Remak = Convert.ToString(value); break;
                case "Content": _Content = Convert.ToString(value); break;
                case "TestResult": _TestResult = value.ToBoolean(); break;
                case "CompanyId": _CompanyId = Convert.ToString(value); break;
                case "CompanyName": _CompanyName = Convert.ToString(value); break;
                case "TestStation": _TestStation = Convert.ToString(value); break;
                case "BatchField1": _BatchField1 = Convert.ToString(value); break;
                case "BatchField2": _BatchField2 = Convert.ToString(value); break;
                case "BatchField3": _BatchField3 = Convert.ToString(value); break;
                case "BatchField4": _BatchField4 = Convert.ToString(value); break;
                case "BatchField5": _BatchField5 = Convert.ToString(value); break;
                case "BatchField6": _BatchField6 = Convert.ToString(value); break;
                case "BatchField7": _BatchField7 = Convert.ToString(value); break;
                case "BatchField8": _BatchField8 = Convert.ToString(value); break;
                case "BatchField9": _BatchField9 = Convert.ToString(value); break;
                case "BatchField10": _BatchField10 = Convert.ToString(value); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static PowerProduction? FindById(Int64 id)
    {
        if (id < 0) return null;

        return Find(_.Id == id);
    }

    /// <summary>根据订单号查找</summary>
    /// <param name="orderId">订单号</param>
    /// <returns>实体列表</returns>
    public static IList<PowerProduction> FindAllByOrderId(String orderId)
    {
        if (orderId.IsNullOrEmpty()) return [];

        return FindAll(_.OrderId == orderId);
    }

    /// <summary>根据检测工厂编号查找</summary>
    /// <param name="companyId">检测工厂编号</param>
    /// <returns>实体列表</returns>
    public static IList<PowerProduction> FindAllByCompanyId(String? companyId)
    {
        if (companyId == null) return [];

        return FindAll(_.CompanyId == companyId);
    }

    /// <summary>根据产品型号编号查找</summary>
    /// <param name="productTypeId">产品型号编号</param>
    /// <returns>实体列表</returns>
    public static IList<PowerProduction> FindAllByProductTypeId(Int32 productTypeId)
    {
        if (productTypeId < 0) return [];

        return FindAll(_.ProductTypeId == productTypeId);
    }

    /// <summary>根据批次字段1查找</summary>
    /// <param name="batchField1">批次字段1</param>
    /// <returns>实体列表</returns>
    public static IList<PowerProduction> FindAllByBatchField1(String? batchField1)
    {
        if (batchField1 == null) return [];

        return FindAll(_.BatchField1 == batchField1);
    }

    /// <summary>根据批次字段2查找</summary>
    /// <param name="batchField2">批次字段2</param>
    /// <returns>实体列表</returns>
    public static IList<PowerProduction> FindAllByBatchField2(String? batchField2)
    {
        if (batchField2 == null) return [];

        return FindAll(_.BatchField2 == batchField2);
    }

    /// <summary>根据批次字段3查找</summary>
    /// <param name="batchField3">批次字段3</param>
    /// <returns>实体列表</returns>
    public static IList<PowerProduction> FindAllByBatchField3(String? batchField3)
    {
        if (batchField3 == null) return [];

        return FindAll(_.BatchField3 == batchField3);
    }

    /// <summary>根据批次字段4查找</summary>
    /// <param name="batchField4">批次字段4</param>
    /// <returns>实体列表</returns>
    public static IList<PowerProduction> FindAllByBatchField4(String? batchField4)
    {
        if (batchField4 == null) return [];

        return FindAll(_.BatchField4 == batchField4);
    }

    /// <summary>根据批次字段5查找</summary>
    /// <param name="batchField5">批次字段5</param>
    /// <returns>实体列表</returns>
    public static IList<PowerProduction> FindAllByBatchField5(String? batchField5)
    {
        if (batchField5 == null) return [];

        return FindAll(_.BatchField5 == batchField5);
    }

    /// <summary>根据批次字段6查找</summary>
    /// <param name="batchField6">批次字段6</param>
    /// <returns>实体列表</returns>
    public static IList<PowerProduction> FindAllByBatchField6(String? batchField6)
    {
        if (batchField6 == null) return [];

        return FindAll(_.BatchField6 == batchField6);
    }

    /// <summary>根据批次字段7查找</summary>
    /// <param name="batchField7">批次字段7</param>
    /// <returns>实体列表</returns>
    public static IList<PowerProduction> FindAllByBatchField7(String? batchField7)
    {
        if (batchField7 == null) return [];

        return FindAll(_.BatchField7 == batchField7);
    }

    /// <summary>根据批次字段8查找</summary>
    /// <param name="batchField8">批次字段8</param>
    /// <returns>实体列表</returns>
    public static IList<PowerProduction> FindAllByBatchField8(String? batchField8)
    {
        if (batchField8 == null) return [];

        return FindAll(_.BatchField8 == batchField8);
    }

    /// <summary>根据批次字段9查找</summary>
    /// <param name="batchField9">批次字段9</param>
    /// <returns>实体列表</returns>
    public static IList<PowerProduction> FindAllByBatchField9(String? batchField9)
    {
        if (batchField9 == null) return [];

        return FindAll(_.BatchField9 == batchField9);
    }

    /// <summary>根据批次字段10查找</summary>
    /// <param name="batchField10">批次字段10</param>
    /// <returns>实体列表</returns>
    public static IList<PowerProduction> FindAllByBatchField10(String? batchField10)
    {
        if (batchField10 == null) return [];

        return FindAll(_.BatchField10 == batchField10);
    }
    #endregion

    #region 数据清理
    /// <summary>清理指定时间段内的数据</summary>
    /// <param name="start">开始时间。未指定时清理小于指定时间的所有数据</param>
    /// <param name="end">结束时间</param>
    /// <returns>清理行数</returns>
    public static Int32 DeleteWith(DateTime start, DateTime end)
    {
        return Delete(_.Id.Between(start, end, Meta.Factory.Snow));
    }

    /// <summary>删除指定时间段内的数据表</summary>
    /// <param name="start">开始时间</param>
    /// <param name="end">结束时间</param>
    /// <returns>清理行数</returns>
    public static Int32 DropWith(DateTime start, DateTime end)
    {
        return Meta.AutoShard(start, end, session =>
        {
            try
            {
                return session.Execute($"Drop Table {session.FormatedTableName}");
            }
            catch (Exception ex)
            {
                XTrace.WriteException(ex);
                return 0;
            }
        }
        ).Sum();
    }
    #endregion

    #region 字段名
    /// <summary>取得电源生产数据字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>订单号</summary>
        public static readonly Field OrderId = FindByName("OrderId");

        /// <summary>模块物料编号</summary>
        public static readonly Field Material = FindByName("Material");

        /// <summary>产品型号编号</summary>
        public static readonly Field ProductTypeId = FindByName("ProductTypeId");

        /// <summary>电源型号配置编号</summary>
        public static readonly Field PowerTypeItemId = FindByName("PowerTypeItemId");

        /// <summary>SN</summary>
        public static readonly Field Sn = FindByName("Sn");

        /// <summary>测试时间</summary>
        public static readonly Field TestTime = FindByName("TestTime");

        /// <summary>备注</summary>
        public static readonly Field Remak = FindByName("Remak");

        /// <summary>测试结果。使用Json存储</summary>
        public static readonly Field Content = FindByName("Content");

        /// <summary>测试结果。成功或者失败</summary>
        public static readonly Field TestResult = FindByName("TestResult");

        /// <summary>检测工厂编号</summary>
        public static readonly Field CompanyId = FindByName("CompanyId");

        /// <summary>检测工厂名称</summary>
        public static readonly Field CompanyName = FindByName("CompanyName");

        /// <summary>测试工位</summary>
        public static readonly Field TestStation = FindByName("TestStation");

        /// <summary>批次字段1</summary>
        public static readonly Field BatchField1 = FindByName("BatchField1");

        /// <summary>批次字段2</summary>
        public static readonly Field BatchField2 = FindByName("BatchField2");

        /// <summary>批次字段3</summary>
        public static readonly Field BatchField3 = FindByName("BatchField3");

        /// <summary>批次字段4</summary>
        public static readonly Field BatchField4 = FindByName("BatchField4");

        /// <summary>批次字段5</summary>
        public static readonly Field BatchField5 = FindByName("BatchField5");

        /// <summary>批次字段6</summary>
        public static readonly Field BatchField6 = FindByName("BatchField6");

        /// <summary>批次字段7</summary>
        public static readonly Field BatchField7 = FindByName("BatchField7");

        /// <summary>批次字段8</summary>
        public static readonly Field BatchField8 = FindByName("BatchField8");

        /// <summary>批次字段9</summary>
        public static readonly Field BatchField9 = FindByName("BatchField9");

        /// <summary>批次字段10</summary>
        public static readonly Field BatchField10 = FindByName("BatchField10");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得电源生产数据字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>订单号</summary>
        public const String OrderId = "OrderId";

        /// <summary>模块物料编号</summary>
        public const String Material = "Material";

        /// <summary>产品型号编号</summary>
        public const String ProductTypeId = "ProductTypeId";

        /// <summary>电源型号配置编号</summary>
        public const String PowerTypeItemId = "PowerTypeItemId";

        /// <summary>SN</summary>
        public const String Sn = "Sn";

        /// <summary>测试时间</summary>
        public const String TestTime = "TestTime";

        /// <summary>备注</summary>
        public const String Remak = "Remak";

        /// <summary>测试结果。使用Json存储</summary>
        public const String Content = "Content";

        /// <summary>测试结果。成功或者失败</summary>
        public const String TestResult = "TestResult";

        /// <summary>检测工厂编号</summary>
        public const String CompanyId = "CompanyId";

        /// <summary>检测工厂名称</summary>
        public const String CompanyName = "CompanyName";

        /// <summary>测试工位</summary>
        public const String TestStation = "TestStation";

        /// <summary>批次字段1</summary>
        public const String BatchField1 = "BatchField1";

        /// <summary>批次字段2</summary>
        public const String BatchField2 = "BatchField2";

        /// <summary>批次字段3</summary>
        public const String BatchField3 = "BatchField3";

        /// <summary>批次字段4</summary>
        public const String BatchField4 = "BatchField4";

        /// <summary>批次字段5</summary>
        public const String BatchField5 = "BatchField5";

        /// <summary>批次字段6</summary>
        public const String BatchField6 = "BatchField6";

        /// <summary>批次字段7</summary>
        public const String BatchField7 = "BatchField7";

        /// <summary>批次字段8</summary>
        public const String BatchField8 = "BatchField8";

        /// <summary>批次字段9</summary>
        public const String BatchField9 = "BatchField9";

        /// <summary>批次字段10</summary>
        public const String BatchField10 = "BatchField10";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";
    }
    #endregion
}
