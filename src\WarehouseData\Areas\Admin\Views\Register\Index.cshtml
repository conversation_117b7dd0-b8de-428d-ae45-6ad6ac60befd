﻿@inject IWorkContext workContext
@{
    Layout = null;

    var language = workContext.WorkingLanguage;

    var adminarea = YRY.Web.Controllers.Areas.Admin.AdminArea.AreaName.ToLower();
    var bgRnd = NewLife.Security.Rand.Next(1, 9);

    var site = SiteInfo.FindDefault();

    var localizationSettings = LocalizationSettings.Current;
 }
<!DOCTYPE html>
<html lang="@language.LanguageCulture" @(this.ShouldUseRtlTheme() ? Html.Raw(" dir=\"rtl\"") : null) @Html.DGPageCssClasses()>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>@T("用户注册") - @T($"{site?.SiteName}")</title>
    <link rel="shortcut icon" href="~/images/favicon.ico" asp-append-version="true">
    <link rel="stylesheet" href="~/libs/pear/css/pear.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/views/login.css" asp-append-version="true" />
    <style>
        @if (language.UniqueSeoCode == "en")
        {
            <text>
            .title
            {
                font-size: 21px !important;
            }
            </text>
        }

        form.layui-form {
            width: 500px !important;
            margin: 10% auto !important;
        }

        .layadmin-user-login-header {
            text-align: center;
            padding-top: 30px;
        }

        #LAY-user-get-vercode {
            height: 42px;
        }
    </style>
</head>
<body style="background-image: url(/images/background.svg);background-color:#e5e5e5;">
 <div class="layui-container">
     <form method="post" class="layui-form">
         <div class="layadmin-user-login-main" style="background-color: #fff;box-shadow:5px 5px 10px 5px #ccc; padding-bottom: 1px;">
                <div class="layadmin-user-login-box layadmin-user-login-header">
                    <h2>@T("注册")</h2>
                </div>
                <div class="layadmin-user-login-box layadmin-user-login-body layui-form">
                    <div class="layui-form-item">
                        <label class="layadmin-user-login-icon layui-icon layui-icon-username" for="Name"></label>
                        <input type="text" name="Name" id="Name" placeholder="@T("手机号/邮箱")" class="layui-input dhvalidate">
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-row">
                            <div class="layui-col-xs7">
                                <label class="layadmin-user-login-icon layui-icon layui-icon-vercode" for="LAY-user-login-vercode"></label>
                                <input type="text" name="ImgCheckCode" id="LAY-user-login-vercode" placeholder="@T("图形验证码")" class="layui-input dhvalidate">
                            </div>
                            <div class="layui-col-xs5">
                                <div style="margin-left: 10px;">
                                    <img src="@DG.Setting.Current.CaptChaUrl" class="layadmin-user-login-codeimg" id="LAY-user-get-vercode">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-row">
                            <div class="layui-col-xs7">
                                <label class="layadmin-user-login-icon layui-icon layui-icon-vercode" for="CodeVal"></label>
                                <input type="text" name="CodeVal" id="CodeVal" placeholder="@T("验证码")" class="layui-input dhvalidate">
                            </div>
                            <div class="layui-col-xs5">
                                <div style="margin-left: 10px;">
                                    <input type="button" class="btn btn-default" id="GetCode" onclick="send(this)" style="background-color: #66b1ff; width: 100px; color: #FFFFFF;float: right; border: 0; border-radius: 5px; height: 36px; cursor: pointer;" value="@T("获取验证码")">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layadmin-user-login-icon layui-icon layui-icon-password" for="LAY-user-login-password"></label>
                        <input type="password" name="OldPwd" id="LAY-user-login-password" placeholder="@T("密码")" class="layui-input dhvalidate">
                    </div>
                    <div class="layui-form-item">
                        <label class="layadmin-user-login-icon layui-icon layui-icon-password" for="LAY-user-login-NewPwd"></label>
                        <input type="password" name="NewPwd" id="LAY-user-login-NewPwd" placeholder="@T("确认密码")" class="layui-input dhvalidate">
                    </div>
                    <div class="layui-form-item" style="display: flex; align-items: flex-end;">
                        <span style="display: inline-block;"><input type="checkbox" name="" id="selected" lay-skin="primary"></span><span style="font-size: inherit; line-height: inherit;"><a href="https://account.hlktech.com/UserAgreement.html" style="margin-top: 10px;" target="_blank">用户使用协议</a></span>
                    </div>

                    <div class="layui-form-item">
                        <button id="dglogin" type="button" class="layui-btn layui-btn-fluid" lay-submit lay-filter="LAY-user-login-submit">@T("确定")</button>
                    </div>
                </div>
         </div>
    </form>
</div>
    
    <div class="user-login-foot">
        2009-2024 © IOT @DHSetting.Current.CurrentVersion
    </div>

    <script src="~/libs/layui/layui.js" asp-append-version="true"></script>
    <script src="~/libs/pear/pear.js" asp-append-version="true"></script>
    <script src="~/js/jquery3.2.1.min.js" type="text/javascript" charset="utf-8" asp-append-version="true"></script>
    <script src="~/js/Storage.js" asp-append-version="true"></script>
    <script>
        layui.use(['abp', 'form', 'element', 'jquery', 'dgcommon'], function () {
            var $ = layui.jquery;
            var abp = layui.abp;
            var form = layui.form;
            var element = layui.element;
            var os = layui.dgcommon;

            form.render();

            if(window != top){
                top.location.href=location.href;
            }

            $(document).keyup(function (event) {
                if (event.keyCode == 13) {
                    document.getElementById("dglogin").click();
                }
            });

            $("#LAY-user-get-vercode").on('click', function() {
                this.src = '@(DG.Setting.Current.CaptChaUrl)?t=' + new Date().getTime()
            });

            $('.dhvalidate').on('input', function () {
                var passwordValue = $(this).val();
                var trimmedValue = passwordValue.replace(/\s/g, '');
                $(this).val(trimmedValue);
            });

            //提交
            form.on('submit(LAY-user-login-submit)', function (obj) {
                if (!$("#selected").prop('checked')) {
                    os.error("请先选中同意用户协议");
                    return;
                }

                if (obj.field.Name.length == 0) {
                    os.error('@T("手机号/邮箱不能为空")');
                    return;
                }
                if (obj.field.ImgCheckCode.length == 0) {
                    os.error('@T("图形验证码不能为空")');
                    return;
                }
                if (obj.field.CodeVal.length == 0) {
                    os.error('@T("验证码不能为空")');
                    return;
                }
                if (obj.field.OldPwd.length == 0) {
                    os.error('@T("密码不能为空")');
                    return;
                }
                if (obj.field.OldPwd.length == 0) {
                    os.error('@T("确认密码不能为空")');
                    return;
                }

                $.post("@Url.Action("Save", "Register")", obj.field, function (res) {
                    if (res.success) {
                        layer.msg(res.msg, { time: 1500 }, function () {
                            location.href = res.locate;
                        });
                    }
                    else {
                        if (res.code == -1) {
                            os.error(res.msg);
                            //layer.msg(res.msg);
                            $("#change_captcha").attr('src', '/CaptCha/GetCheckCode?' + (new Date().getTime()));
                            return;
                        }
                        //layer.msg(res.msg);
                        os.error(res.msg);
                    }
                });

            });

        });

        //正则验证
        var tel = /^1(3|4|5|6|7|8|9)\d{9}$/;
        var email = /^\w+((-\w+)|(\.\w+))*\@@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
        var registertype = 0;//1为手机号  2是邮箱
        var countdown = 60;  //及时60S

        //获取验证码倒计时缓存
        $(function () {
            var hastime = localStorage.getItem("verification");

            if (hastime != null) {
                if (hastime > Date.parse(new Date())) {
                    countdown = (hastime - Date.parse(new Date())) / 1000;
                    settime(document.getElementsByClassName("yzm")[0]);
                }
                else {
                    localStorage.removeItem("verification");
                }
            }
        })

        //倒计时
        function settime(obj) {
            if (countdown == 0) {
                obj.removeAttribute("disabled");
                obj.value = "获取验证码";
                countdown = 60;
                return;
            } else {
                obj.setAttribute("disabled", true);
                obj.value = "重新发送" + countdown + "s";
                countdown--;
            }
            setTimeout(function () {
                settime(obj)
            }
                , 1000)
        }

        // 获取验证码验证
        function send(obj) {
            var tels = $("#Name").val(); // 邮箱

            if (!tel.test(tels)) {
                if (!email.test(tels)) {
                   layer.msg('请输入正确的手机号或者邮箱');
                   // layer.msg('请输入正确的手机号');
                    $("#Name").focus();
                    return;
                }
                else {
                    registertype = 2;
                }
            }
            else {
                registertype = 1;
            }

            // 图片验证码
            var Code = $("#LAY-user-login-vercode").val();
            if (!Code) {
                layer.msg('请先输入图片验证码');
                return;
            }

            // 发送短信/邮箱验证码
            $.post("/api/V1/Send/SendRegisteredCode", { Name: tels, ImgCheckCode: Code, Type: registertype }, function (res) {
                if (res.success) {
                    localStorage.setItem("verification", Date.parse(new Date()) + 60000);
                    layer.msg(res.msg);
                    settime(obj);//60秒禁止再次点击
                }
                else {
                    if (res.code == -1) {
                        layer.msg(res.msg);
                        $("#LAY-user-login-vercode").attr('src', '/CaptCha/GetCheckCode?' + (new Date().getTime()));
                        return;
                    }
                    else if (res.code == -2) {
                        countdown = res.data;
                        layer.msg(res.msg);
                        return;
                    }
                    layer.msg(res.msg);
                }
            })
        }
    </script>
</body>
</html>