﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>产品项目</summary>
[Serializable]
[DataObject]
[Description("产品项目")]
[BindIndex("IU_DH_ProductProject_Name", true, "Name")]
[BindIndex("IX_DH_ProductProject_ProductTypeId", false, "ProductTypeId")]
[BindIndex("IX_DH_ProductProject_ProjectNo", false, "ProjectNo")]
[BindTable("DH_ProductProject", Description = "产品项目", ConnName = "DH", DbType = DatabaseType.None)]
public partial class ProductProject : IProductProject, IEntity<IProductProject>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String _Name = null!;
    /// <summary>项目名称</summary>
    [DisplayName("项目名称")]
    [Description("项目名称")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("Name", "项目名称", "", Master = true)]
    public String Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private Int32 _ProductTypeId;
    /// <summary>产品型号Id</summary>
    [DisplayName("产品型号Id")]
    [Description("产品型号Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ProductTypeId", "产品型号Id", "")]
    public Int32 ProductTypeId { get => _ProductTypeId; set { if (OnPropertyChanging("ProductTypeId", value)) { _ProductTypeId = value; OnPropertyChanged("ProductTypeId"); } } }

    private Boolean _Status;
    /// <summary>状态</summary>
    [DisplayName("状态")]
    [Description("状态")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Status", "状态", "")]
    public Boolean Status { get => _Status; set { if (OnPropertyChanging("Status", value)) { _Status = value; OnPropertyChanged("Status"); } } }

    private String? _DataCategoryIds;
    /// <summary>资料类别编号集合</summary>
    [DisplayName("资料类别编号集合")]
    [Description("资料类别编号集合")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("DataCategoryIds", "资料类别编号集合", "")]
    public String? DataCategoryIds { get => _DataCategoryIds; set { if (OnPropertyChanging("DataCategoryIds", value)) { _DataCategoryIds = value; OnPropertyChanged("DataCategoryIds"); } } }

    private String? _ProjectNo;
    /// <summary>项目编号</summary>
    [DisplayName("项目编号")]
    [Description("项目编号")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("ProjectNo", "项目编号", "")]
    public String? ProjectNo { get => _ProjectNo; set { if (OnPropertyChanging("ProjectNo", value)) { _ProjectNo = value; OnPropertyChanged("ProjectNo"); } } }

    private String? _Remark;
    /// <summary>备注</summary>
    [DisplayName("备注")]
    [Description("备注")]
    [DataObjectField(false, false, true, 255)]
    [BindColumn("Remark", "备注", "")]
    public String? Remark { get => _Remark; set { if (OnPropertyChanging("Remark", value)) { _Remark = value; OnPropertyChanged("Remark"); } } }

    private Int32 _AuditStatus;
    /// <summary>审核状态 0待审核 1审核中 2审核成功 3审核失败</summary>
    [DisplayName("审核状态0待审核1审核中2审核成功3审核失败")]
    [Description("审核状态 0待审核 1审核中 2审核成功 3审核失败")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("AuditStatus", "审核状态 0待审核 1审核中 2审核成功 3审核失败", "")]
    public Int32 AuditStatus { get => _AuditStatus; set { if (OnPropertyChanging("AuditStatus", value)) { _AuditStatus = value; OnPropertyChanged("AuditStatus"); } } }

    private Int32 _ReAudit;
    /// <summary>是否反审 0默认 1申请反审 2确认反审</summary>
    [DisplayName("是否反审0默认1申请反审2确认反审")]
    [Description("是否反审 0默认 1申请反审 2确认反审")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ReAudit", "是否反审 0默认 1申请反审 2确认反审", "")]
    public Int32 ReAudit { get => _ReAudit; set { if (OnPropertyChanging("ReAudit", value)) { _ReAudit = value; OnPropertyChanged("ReAudit"); } } }

    private String? _ReAuditRemark;
    /// <summary>反审原因</summary>
    [DisplayName("反审原因")]
    [Description("反审原因")]
    [DataObjectField(false, false, true, 255)]
    [BindColumn("ReAuditRemark", "反审原因", "")]
    public String? ReAuditRemark { get => _ReAuditRemark; set { if (OnPropertyChanging("ReAuditRemark", value)) { _ReAuditRemark = value; OnPropertyChanged("ReAuditRemark"); } } }

    private Int32 _SubmitStatus;
    /// <summary>提交状态 0未提交 1已提交审核</summary>
    [DisplayName("提交状态0未提交1已提交审核")]
    [Description("提交状态 0未提交 1已提交审核")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("SubmitStatus", "提交状态 0未提交 1已提交审核", "")]
    public Int32 SubmitStatus { get => _SubmitStatus; set { if (OnPropertyChanging("SubmitStatus", value)) { _SubmitStatus = value; OnPropertyChanged("SubmitStatus"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductProject model)
    {
        Id = model.Id;
        Name = model.Name;
        ProductTypeId = model.ProductTypeId;
        Status = model.Status;
        DataCategoryIds = model.DataCategoryIds;
        ProjectNo = model.ProjectNo;
        Remark = model.Remark;
        AuditStatus = model.AuditStatus;
        ReAudit = model.ReAudit;
        ReAuditRemark = model.ReAuditRemark;
        SubmitStatus = model.SubmitStatus;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "Name" => _Name,
            "ProductTypeId" => _ProductTypeId,
            "Status" => _Status,
            "DataCategoryIds" => _DataCategoryIds,
            "ProjectNo" => _ProjectNo,
            "Remark" => _Remark,
            "AuditStatus" => _AuditStatus,
            "ReAudit" => _ReAudit,
            "ReAuditRemark" => _ReAuditRemark,
            "SubmitStatus" => _SubmitStatus,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "ProductTypeId": _ProductTypeId = value.ToInt(); break;
                case "Status": _Status = value.ToBoolean(); break;
                case "DataCategoryIds": _DataCategoryIds = Convert.ToString(value); break;
                case "ProjectNo": _ProjectNo = Convert.ToString(value); break;
                case "Remark": _Remark = Convert.ToString(value); break;
                case "AuditStatus": _AuditStatus = value.ToInt(); break;
                case "ReAudit": _ReAudit = value.ToInt(); break;
                case "ReAuditRemark": _ReAuditRemark = Convert.ToString(value); break;
                case "SubmitStatus": _SubmitStatus = value.ToInt(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static ProductProject? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据项目名称查找</summary>
    /// <param name="name">项目名称</param>
    /// <returns>实体对象</returns>
    public static ProductProject? FindByName(String name)
    {
        if (name.IsNullOrEmpty()) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Name.EqualIgnoreCase(name));

        // 单对象缓存
        return Meta.SingleCache.GetItemWithSlaveKey(name) as ProductProject;

        //return Find(_.Name == name);
    }

    /// <summary>根据产品型号Id查找</summary>
    /// <param name="productTypeId">产品型号Id</param>
    /// <returns>实体列表</returns>
    public static IList<ProductProject> FindAllByProductTypeId(Int32 productTypeId)
    {
        if (productTypeId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.ProductTypeId == productTypeId);

        return FindAll(_.ProductTypeId == productTypeId);
    }

    /// <summary>根据项目编号查找</summary>
    /// <param name="projectNo">项目编号</param>
    /// <returns>实体列表</returns>
    public static IList<ProductProject> FindAllByProjectNo(String? projectNo)
    {
        if (projectNo == null) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.ProjectNo.EqualIgnoreCase(projectNo));

        return FindAll(_.ProjectNo == projectNo);
    }
    #endregion

    #region 字段名
    /// <summary>取得产品项目字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>项目名称</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>产品型号Id</summary>
        public static readonly Field ProductTypeId = FindByName("ProductTypeId");

        /// <summary>状态</summary>
        public static readonly Field Status = FindByName("Status");

        /// <summary>资料类别编号集合</summary>
        public static readonly Field DataCategoryIds = FindByName("DataCategoryIds");

        /// <summary>项目编号</summary>
        public static readonly Field ProjectNo = FindByName("ProjectNo");

        /// <summary>备注</summary>
        public static readonly Field Remark = FindByName("Remark");

        /// <summary>审核状态 0待审核 1审核中 2审核成功 3审核失败</summary>
        public static readonly Field AuditStatus = FindByName("AuditStatus");

        /// <summary>是否反审 0默认 1申请反审 2确认反审</summary>
        public static readonly Field ReAudit = FindByName("ReAudit");

        /// <summary>反审原因</summary>
        public static readonly Field ReAuditRemark = FindByName("ReAuditRemark");

        /// <summary>提交状态 0未提交 1已提交审核</summary>
        public static readonly Field SubmitStatus = FindByName("SubmitStatus");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得产品项目字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>项目名称</summary>
        public const String Name = "Name";

        /// <summary>产品型号Id</summary>
        public const String ProductTypeId = "ProductTypeId";

        /// <summary>状态</summary>
        public const String Status = "Status";

        /// <summary>资料类别编号集合</summary>
        public const String DataCategoryIds = "DataCategoryIds";

        /// <summary>项目编号</summary>
        public const String ProjectNo = "ProjectNo";

        /// <summary>备注</summary>
        public const String Remark = "Remark";

        /// <summary>审核状态 0待审核 1审核中 2审核成功 3审核失败</summary>
        public const String AuditStatus = "AuditStatus";

        /// <summary>是否反审 0默认 1申请反审 2确认反审</summary>
        public const String ReAudit = "ReAudit";

        /// <summary>反审原因</summary>
        public const String ReAuditRemark = "ReAuditRemark";

        /// <summary>提交状态 0未提交 1已提交审核</summary>
        public const String SubmitStatus = "SubmitStatus";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
