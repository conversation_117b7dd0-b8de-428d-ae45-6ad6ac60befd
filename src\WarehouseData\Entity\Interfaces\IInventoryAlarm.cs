﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>库存告警</summary>
public partial interface IInventoryAlarm
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>物料编号</summary>
    String MaterialNumber { get; set; }

    /// <summary>物料名称</summary>
    String? MaterialName { get; set; }

    /// <summary>种类</summary>
    String? Category { get; set; }

    /// <summary>规格型号</summary>
    String SpecificationModel { get; set; }

    /// <summary>单位</summary>
    String? Unit { get; set; }

    /// <summary>仓库编号</summary>
    String WarehouseNumber { get; set; }

    /// <summary>仓库名称</summary>
    String? WarehouseName { get; set; }

    /// <summary>告警数量</summary>
    Int32 AlarmQuantity { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
