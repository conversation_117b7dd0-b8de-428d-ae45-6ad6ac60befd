﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>生产品质反馈扩展表</summary>
public partial interface IProductionQualityFeedbackEx
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>品质反馈编号</summary>
    Int32 FeedbackId { get; set; }

    /// <summary>类型 0补充 1处理</summary>
    Int32 DType { get; set; }

    /// <summary>内容</summary>
    String? Content { get; set; }

    /// <summary>图片。多个图片用,分隔</summary>
    String? PicPath { get; set; }

    /// <summary>视频。多个视频用,分隔</summary>
    String? VideoPath { get; set; }

    /// <summary>文件。多个文件用,分隔</summary>
    String? FilePath { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }
    #endregion
}
