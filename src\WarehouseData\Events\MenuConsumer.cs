﻿using DH.Models.EventModel;

using Pek.Events;

namespace HlktechIoT.Events;

/// <summary>
/// 菜单消费
/// </summary>
public class MenuConsumer
    : IConsumer<MenuEvent> {
    public int Sort { get; set; } = 0;

    public async Task HandleEventAsync(MenuEvent eventMessage)
    {
        //XTrace.WriteLine($"进来菜单消费了吗？");

        //var model = eventMessage.Menu;

        //model.Ex1 = 1;
        //model.Update();

        await Task.CompletedTask;
    }

}
