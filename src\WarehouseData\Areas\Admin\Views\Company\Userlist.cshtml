﻿@{
    Html.AppendTitleParts(T("合作公司").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/components/dynamic-operation-column.js");

    // Css
    Html.AppendCssFileParts(ResourceLocation.Head, "~/css/components/dynamic-operation-column.css");

}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }
</style>
<script asp-location="Head">
    var pleaseSelect = '@T("请选择")';
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
@*             <label class="layui-form-label" style="width: auto">@T("公司名称")：</label>
            <div class="layui-input-inline">
                <input type="text" name="name" id="name" placeholder="@T("请输入公司名称")" autocomplete="off" class="layui-input" lay-filter="name">
            </div> *@
            @*   <div class="layui-inline select">
                <label class="layui-form-label">@T("部门")：</label>
                <div class="layui-input-inline ">
                    <ul id="demoTree2" class="dtree" data-id="0" style="z-index: 99999"></ul>
                </div>
            </div> *@
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;

    // 使用通用动态操作列组件
         var operationButtons = [
            @if (this.Has((PermissionFlags)8))
            {
                    @:{
                    @:    text: '@T("删除")',
                    @:    event: 'del',
                    @:    class: 'pear-btn pear-btn-danger',
                    @:    condition: function(d) { return true; },
                    @:    alwaysShow: true
                    @:},
            }
         ];

         // 初始化动态操作列组件
         var operationColumnWidth = window.dynamicOperationColumn.init({
             buttons: operationButtons,
             tableId: 'tablist',
             debug: true  // 开启调试模式
         });


        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetUserlist")?CompanyId='+'@Model.Id'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 80
            , cols: [[
                { field: 'ID', title: '@T("ID")',width:80 }
                , { field: 'DisplayName', title: '@T("昵称")', minWidth:120 }
                //, { field: 'Enabled', title: '@T("是否启用")', templet: '#switchTpl', minWidth: 130 }
                , { field: 'Mobile', title: '@T("手机号")', minWidth: 160 }
                , { field: 'Mail', title: '@T("邮箱")', minWidth: 160 }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: operationColumnWidth }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-120'
            , id: 'tables'
            , done: function (res) {
                         // 设置当前页全部数据Id到全局变量
                         // tableIds = res.data.map(function (value) {
                         //     return value.Id;
                         // });

                         // // 设置当前页选中项
                         // $.each(res.data, function (idx, val) {
                         //     if (ids.indexOf(val.Id) > -1) {
                         //         val["LAY_CHECKED"] = 'true';
                         //         //找到对应数据改变勾选样式，呈现出选中效果
                         //         let index = val['LAY_INDEX'];
                         //         $('tr[data-index=' + index + '] input[type="checkbox"]').click();
                         //         form.render('checkbox'); //刷新checkbox选择框渲染
                         //     }
                         // });
                         // // 获取表格勾选状态，全选中时设置全选框选中
                         // let checkStatus = table.checkStatus('tables');
                         // if (checkStatus.isAll) {
                         //     $('.layui-table-header th[data-field="0"] input[type="checkbox"]').prop('checked', true);
                         //     form.render('checkbox'); //刷新checkbox选择框渲染
                         // }
                         // 使用通用组件应用操作列宽度
                         window.dynamicOperationColumn.delayApplyWidth(300, true);
                     },
        });

        // dtree.render({
        //     elem: "#demoTree2",
        //     initLevel: "1",
        //     width: "100%",
        //     method: 'get',
        //     url: "@Url.Action("GetDepartmentList")",
        //     //url: "/test.json",
        //     select: true
        // });

        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        page: {
                            curr: 1
                        },
                        where: {
                            name: $("#name").val(),
                        },
                        done: function(res, curr, count) {
                            try {
                                setTimeout(function() {
                                    // 使用DOM测量获取精确宽度
                                    var measuredWidth = window.dynamicOperationColumn.calculateOperationColumnWidthFromDOM();

                                    if (measuredWidth && measuredWidth !== operationColumnWidth) {
                                        console.log('表格重载：基于DOM测量调整宽度从', operationColumnWidth, '到', measuredWidth);
                                        operationColumnWidth = measuredWidth;
                                    }

                                    window.dynamicOperationColumn.applyOperationColumnWidth(false); // 使用精确测量的宽度
                                    console.log('产品型号表格重载完成，已重新应用基于DOM测量的操作列宽度');
                                }, 300);
                            } catch (error) {
                                console.error('表格重载done回调中出错:', error);
                            }
                        }
                    });
            }
        }

        $("#name").on("input", function (e) {
            active.reload();
        });

        var lastSelectValue = null; //上一次选中的值

        dtree.on('node("demoTree2")', function (obj) {
    @* console.log('选中',obj.param,lastSelectValue) *@
            if(lastSelectValue != null && lastSelectValue === obj.param.context ){
                lastSelectValue = null
                //清除选中值
    @* console.log('触发',dtree) *@
                dtree.reload("demoTree2", {
                    initLevel: "1",
                    width: "100%",
                    method: 'get',
                    url: "@Url.Action("GetDepartmentList")",
                    select: true,
                    where: { "dId": '' },
                    checkData: [] // 清空选中的值
                });
    @* 重新拉数据 *@
                table.reload('tables', {
                    initLevel: "1",
                    where: { "dId": '' },
                    select: true,
                    page: {
                        curr: 1
                    }
                    , checkData: [''] // 清空选中的值
                })
                return;
            }
            if(lastSelectValue == null || lastSelectValue != obj.param.context ){
                lastSelectValue = obj.param.context
            }
    @* console.log(obj) *@
            table.reload('tables', {
                where: { "dId": obj.param.nodeId },
                page: {
                    curr: 1
                }
            })
        });

        table.on('toolbar(tool)', function (obj) {
            var data = obj.config;
            if (obj.event === 'add') {
                window.add(data);
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("DeleteUser")', { UserId: data.ID,CompanyId:'@Model.Id' }, function (data) {
                        if (data.success) {
                            abp.notify.success('@T("删除成功")');
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'edit') {
                window.edit(data);
            }else if (obj.event === 'user') {
                window.user(data);
            }

        });

        window.saveCallback = function (data) {
            layer.close(data.index);
            abp.notify.success(data.msg);
            table.reload("tables");
        }

    window.add = function (data) {
        window.addPageIndex = top.layui.dg.popupRight({
            id: 'Add',
            title: ' @T("新增")',
            closeBtn: 1,
            area: ['480px'],
            success: function () {
                $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("AddUser")'+abp.utils.formatString("?CompanyId={0}", '@Model.Id')+'" frameborder="0" class="layadmin-iframe" name="iframe' + data.index + '"></iframe>');
            }
        });
        window.name = 'CompanyUser';
    }

        //监听开关事件
        form.on('switch(statusedit)', function (data) {
            //开关是否开启，true或者false
            var checked = data.elem.checked;
            //获取所属属性值
            var id = data.elem.attributes['data-id'].nodeValue;
            $.post('@Url.Action("ModifyState")', { id: id, status: checked }, function (res) {
                if (res.success) {
                    abp.notify.success(res.msg);
                }
                else {
                    abp.notify.error(res.msg);
                }
            });
        });

    });
</script>

<script type="text/html" id="tool">
    <div class="operation-column">
        {{#  layui.each(window.operationButtons, function(index, button){ }}
            {{#  var isEnabled = button.condition(d); }}
            {{#  var buttonClass = button.class + ' pear-btn-xs'; }}
            {{#  if(!isEnabled){ }}
                {{#  buttonClass += ' disabled-button'; }}
            {{#  } }}
            <a class="{{buttonClass}}" lay-event="{{isEnabled ? button.event : 'disabled'}}"
               title="{{!isEnabled ? '当前状态下不可操作' : ''}}"
               data-enabled="{{isEnabled}}">{{button.text}}</a>
        {{#  }); }}
    </div>
</script>

<script type="text/html" id="switchTpl">
    <input type="checkbox" name="status" data-id="{{d.Id}}" lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" {{ d.Enabled ? 'checked' : ''}}>
</script>

<script type="text/html" id="user-toolbar">
    @if (this.Has((PermissionFlags)2))
    {
            <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
                <i class="layui-icon  layui-icon-add-1"></i>
                @T("新增")
            </button>
    }
</script>