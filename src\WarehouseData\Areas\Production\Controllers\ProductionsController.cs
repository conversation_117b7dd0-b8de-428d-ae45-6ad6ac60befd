﻿using DG.Web.Framework;

using HlktechIoT.Dto.Export;
using HlktechIoT.Entity;

using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.Models;

using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Production.Controllers;

/// <summary>生产测试列表</summary>
[DisplayName("生产测试数据")]
[Description("硬件生产中的测试数据")]
[ProductionArea]
[DHMenu(65,ParentMenuName = "ProductionsManager", ParentMenuDisplayName = "生产管理", ParentMenuUrl = "", ParentMenuOrder = 30, ParentIcon = "layui-icon-component", CurrentMenuUrl = "~/{area}/Productions", CurrentMenuName = "ProductionsList", LastUpdate = "20241010")]
public class ProductionsController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 65;

    /// <summary>
    /// 测试数据列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("测试数据列表")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 根据条件搜索测试数据
    /// </summary>
    /// <param name="batchNum">批次号</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="moduleId">模块id</param>
    /// <param name="moduleType">模块型号</param>
    /// <param name="sdkVersion">固件版本</param>
    /// <param name="deviceMac">mac地址</param>
    /// <param name="testFactory">检测工厂</param>
    /// <param name="saleInfo">销售信息</param>
    /// <param name="customerInfo">客户信息</param>
    /// <param name="deduplicateInfo">去重信息</param>
    /// <param name="returnFactory">是否返厂</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("测试数据")]
    public IActionResult GetList(String batchNum, DateTime start, DateTime end, String moduleId, String moduleType, String sdkVersion, String deviceMac, String testFactory, String saleInfo, String customerInfo, String deduplicateInfo, Boolean? returnFactory, Int32 page = 1, Int32 limit = 10)
    {
        if (!batchNum.IsNullOrWhiteSpace()) batchNum = batchNum.SafeString().Trim();
        if (!moduleId.IsNullOrWhiteSpace()) moduleId = moduleId.SafeString().Trim();
        if (!moduleType.IsNullOrWhiteSpace()) moduleType = moduleType.SafeString().Trim();
        if (!sdkVersion.IsNullOrWhiteSpace()) sdkVersion = sdkVersion.SafeString().Trim();
        if (!deviceMac.IsNullOrWhiteSpace()) deviceMac = deviceMac.SafeString().Trim();
        if (!testFactory.IsNullOrWhiteSpace()) testFactory = testFactory.SafeString().Trim();
        if (!saleInfo.IsNullOrWhiteSpace()) saleInfo = saleInfo.SafeString().Trim();
        if (!customerInfo.IsNullOrWhiteSpace()) customerInfo = customerInfo.SafeString().Trim();
        if (!deduplicateInfo.IsNullOrWhiteSpace()) deduplicateInfo = deduplicateInfo.SafeString().Trim();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = DeviceTestLogs._.CreateTime,
            Desc = true,
        };

        if (start <= DateTime.MinValue)
        {
            start = new DateTime(DateTime.Now.Year, 1, 1);
        }

        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now.AddDays(1).Date;
        }

        var list = DeviceTestLogs.SearchWithOptimize("", batchNum, start, end, moduleId, moduleType, sdkVersion, deviceMac, testFactory, saleInfo, customerInfo, deduplicateInfo, returnFactory, pages);

        var data = list.Select(x => new {
            Id = x.DId.ToString(), // 将 long 类型转换为字符串
            x.BatchNum,
            x.ModuleId,
            x.ModuleType,
            x.SdkVersion,
            x.DeviceMac,
            x.TestFactory,
            x.SaleInfo,
            x.CustomerInfo,
            x.CreateTime,
            x.TestPosition,
            Reserve1 = x.Reserve1.SafeString() == "null" ? "" : x.Reserve1.SafeString(),
            Reserve2 = x.Reserve2.SafeString() == "null" ? "" : x.Reserve2.SafeString(),
            x.TestData,
            x.DeduplicateInfo
        });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 删除
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除")]
    [HttpPost]
    public IActionResult Delete(Int64 Id, DateTime Time)
    {
        var res = new DResult();

        //var model = DeviceTestLogs.FindByDIdWithTime(Id, Time);
        //if (model == null)
        //{
        //    res.success = false;
        //    res.msg = GetResource("数据不存在或已被删除");
        //    return Json(res);
        //}
        //model.Delete();

        DeviceTestLogs.Delete(DeviceTestLogs._.DId == Id & DeviceTestLogs._.CreateTime >= Time);

        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>导出</summary>
    /// <param name="ids">勾选中的ID</param>
    /// <param name="batchNum">批次号</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="moduleId">模块id</param>
    /// <param name="moduleType">模块型号</param>
    /// <param name="sdkVersion">固件版本</param>
    /// <param name="deviceMac">mac地址</param>
    /// <param name="testFactory">检测工厂</param>
    /// <param name="saleInfo">销售信息</param>
    /// <param name="customerInfo">客户信息</param>
    /// <param name="deduplicateInfo">去重信息</param>
    /// <param name="returnFactory">是否返厂</param>
    /// <returns></returns>
    [DisplayName("导出")]
    [EntityAuthorize((PermissionFlags)32)]
    public async Task<IActionResult> ExportAll(String ids, String batchNum, DateTime start, DateTime end, String moduleId, String moduleType, String sdkVersion, String deviceMac, String testFactory, String saleInfo, String customerInfo, String deduplicateInfo, Boolean? returnFactory)
    {
        // 校验开始时间和结束时间是否为空
        if (start <= DateTime.MinValue || end <= DateTime.MinValue)
        {
            return Json(new { success = false, msg = GetResource("开始时间和结束时间不能为空") });
        }
        // 校验其他查询条件是否至少填写一个
        if (batchNum.IsNullOrWhiteSpace() &&
            moduleId.IsNullOrWhiteSpace() &&
            moduleType.IsNullOrWhiteSpace() &&
            sdkVersion.IsNullOrWhiteSpace() &&
            deviceMac.IsNullOrWhiteSpace() &&
            testFactory.IsNullOrWhiteSpace() &&
            saleInfo.IsNullOrWhiteSpace() &&
            customerInfo.IsNullOrWhiteSpace() &&
            deduplicateInfo.IsNullOrWhiteSpace())
        {
            return Json(new { success = false, msg = GetResource("请至少填写一个查询条件") });
        }

        if (!ids.IsNullOrWhiteSpace()) ids = ids.SafeString().Trim();
        if (!batchNum.IsNullOrWhiteSpace()) batchNum = batchNum.SafeString().Trim();
        if (!moduleId.IsNullOrWhiteSpace()) moduleId = moduleId.SafeString().Trim();
        if (!moduleType.IsNullOrWhiteSpace()) moduleType = moduleType.SafeString().Trim();
        if (!sdkVersion.IsNullOrWhiteSpace()) sdkVersion = sdkVersion.SafeString().Trim();
        if (!deviceMac.IsNullOrWhiteSpace()) deviceMac = deviceMac.SafeString().Trim();
        if (!testFactory.IsNullOrWhiteSpace()) testFactory = testFactory.SafeString().Trim();
        if (!saleInfo.IsNullOrWhiteSpace()) saleInfo = saleInfo.SafeString().Trim();
        if (!customerInfo.IsNullOrWhiteSpace()) customerInfo = customerInfo.SafeString().Trim();
        if (!deduplicateInfo.IsNullOrWhiteSpace()) deduplicateInfo = deduplicateInfo.SafeString().Trim();

        var pages = new PageParameter
        {
            PageIndex = 1,
            PageSize = 0,
            RetrieveTotalCount = true,
            Sort = DeviceTestLogs._.CreateTime,
            Desc = true,
        };

        IExporter exporter = new ExcelExporter();
        List<DeviceTestLogsExport>? list = null;
        
        var data = DeviceTestLogs.Search(ids, batchNum, start, end, moduleId, moduleType, sdkVersion, deviceMac, testFactory, saleInfo, customerInfo, deduplicateInfo, returnFactory, pages);
            list = data.Select(e => new DeviceTestLogsExport()
            {
                BatchNum = e.BatchNum,
                ModuleType = e.ModuleType,
                ModuleId = e.ModuleId,
                SdkVersion = e.SdkVersion,
                DeviceMac = e.DeviceMac,
                DeduplicateInfo = e.DeduplicateInfo,
                TestTime = e.TestTime,
                TestFactory = e.TestFactory,
                TestPosition = e.TestPosition,
                TestData = e.TestData,
                SaleInfo = e.SaleInfo,
                CustomerInfo = e.CustomerInfo,
                Reserve1 = e.Reserve1.SafeString() == "null" ? "" : e.Reserve1.SafeString(),
                Reserve2 = e.Reserve2.SafeString() == "null" ? "" : e.Reserve2.SafeString(),
                CreateTime = e.CreateTime <= DateTime.MinValue ? null : e.CreateTime,
                CreateUser = e.CreateUser,
                CreateIP = e.CreateIP,
                UpdateTime = e.UpdateTime <= DateTime.MinValue ? null : e.UpdateTime,
                UpdateUser = e.UpdateUser,
                UpdateIP = e.UpdateIP,
            }).ToList();
        var result = await exporter.ExportAsByteArray(list);
        return File(result, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"ExportDeviceTestLogs{DateTime.Now:yyyyMMddhhmm}.xlsx");
    }
}
