﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>库存告警</summary>
[Serializable]
[DataObject]
[Description("库存告警")]
[BindIndex("IU_DH_InventoryAlarm_MaterialNumber_SpecificationModel_WarehouseNumber", true, "MaterialNumber,SpecificationModel,WarehouseNumber")]
[BindTable("DH_InventoryAlarm", Description = "库存告警", ConnName = "DH", DbType = DatabaseType.None)]
public partial class InventoryAlarm : IInventoryAlarm, IEntity<IInventoryAlarm>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String _MaterialNumber = null!;
    /// <summary>物料编号</summary>
    [DisplayName("物料编号")]
    [Description("物料编号")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("MaterialNumber", "物料编号", "")]
    public String MaterialNumber { get => _MaterialNumber; set { if (OnPropertyChanging("MaterialNumber", value)) { _MaterialNumber = value; OnPropertyChanged("MaterialNumber"); } } }

    private String? _MaterialName;
    /// <summary>物料名称</summary>
    [DisplayName("物料名称")]
    [Description("物料名称")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("MaterialName", "物料名称", "")]
    public String? MaterialName { get => _MaterialName; set { if (OnPropertyChanging("MaterialName", value)) { _MaterialName = value; OnPropertyChanged("MaterialName"); } } }

    private String? _Category;
    /// <summary>种类</summary>
    [DisplayName("种类")]
    [Description("种类")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Category", "种类", "")]
    public String? Category { get => _Category; set { if (OnPropertyChanging("Category", value)) { _Category = value; OnPropertyChanged("Category"); } } }

    private String _SpecificationModel = null!;
    /// <summary>规格型号</summary>
    [DisplayName("规格型号")]
    [Description("规格型号")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("SpecificationModel", "规格型号", "")]
    public String SpecificationModel { get => _SpecificationModel; set { if (OnPropertyChanging("SpecificationModel", value)) { _SpecificationModel = value; OnPropertyChanged("SpecificationModel"); } } }

    private String? _Unit;
    /// <summary>单位</summary>
    [DisplayName("单位")]
    [Description("单位")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Unit", "单位", "")]
    public String? Unit { get => _Unit; set { if (OnPropertyChanging("Unit", value)) { _Unit = value; OnPropertyChanged("Unit"); } } }

    private String _WarehouseNumber = null!;
    /// <summary>仓库编号</summary>
    [DisplayName("仓库编号")]
    [Description("仓库编号")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("WarehouseNumber", "仓库编号", "")]
    public String WarehouseNumber { get => _WarehouseNumber; set { if (OnPropertyChanging("WarehouseNumber", value)) { _WarehouseNumber = value; OnPropertyChanged("WarehouseNumber"); } } }

    private String? _WarehouseName;
    /// <summary>仓库名称</summary>
    [DisplayName("仓库名称")]
    [Description("仓库名称")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("WarehouseName", "仓库名称", "")]
    public String? WarehouseName { get => _WarehouseName; set { if (OnPropertyChanging("WarehouseName", value)) { _WarehouseName = value; OnPropertyChanged("WarehouseName"); } } }

    private Int32 _AlarmQuantity;
    /// <summary>告警数量</summary>
    [DisplayName("告警数量")]
    [Description("告警数量")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("AlarmQuantity", "告警数量", "")]
    public Int32 AlarmQuantity { get => _AlarmQuantity; set { if (OnPropertyChanging("AlarmQuantity", value)) { _AlarmQuantity = value; OnPropertyChanged("AlarmQuantity"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IInventoryAlarm model)
    {
        Id = model.Id;
        MaterialNumber = model.MaterialNumber;
        MaterialName = model.MaterialName;
        Category = model.Category;
        SpecificationModel = model.SpecificationModel;
        Unit = model.Unit;
        WarehouseNumber = model.WarehouseNumber;
        WarehouseName = model.WarehouseName;
        AlarmQuantity = model.AlarmQuantity;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "MaterialNumber" => _MaterialNumber,
            "MaterialName" => _MaterialName,
            "Category" => _Category,
            "SpecificationModel" => _SpecificationModel,
            "Unit" => _Unit,
            "WarehouseNumber" => _WarehouseNumber,
            "WarehouseName" => _WarehouseName,
            "AlarmQuantity" => _AlarmQuantity,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "MaterialNumber": _MaterialNumber = Convert.ToString(value); break;
                case "MaterialName": _MaterialName = Convert.ToString(value); break;
                case "Category": _Category = Convert.ToString(value); break;
                case "SpecificationModel": _SpecificationModel = Convert.ToString(value); break;
                case "Unit": _Unit = Convert.ToString(value); break;
                case "WarehouseNumber": _WarehouseNumber = Convert.ToString(value); break;
                case "WarehouseName": _WarehouseName = Convert.ToString(value); break;
                case "AlarmQuantity": _AlarmQuantity = value.ToInt(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static InventoryAlarm? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据物料编号、规格型号、仓库编号查找</summary>
    /// <param name="materialNumber">物料编号</param>
    /// <param name="specificationModel">规格型号</param>
    /// <param name="warehouseNumber">仓库编号</param>
    /// <returns>实体对象</returns>
    public static InventoryAlarm? FindByMaterialNumberAndSpecificationModelAndWarehouseNumber(String materialNumber, String specificationModel, String warehouseNumber)
    {
        if (materialNumber.IsNullOrEmpty()) return null;
        if (specificationModel.IsNullOrEmpty()) return null;
        if (warehouseNumber.IsNullOrEmpty()) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.MaterialNumber.EqualIgnoreCase(materialNumber) && e.SpecificationModel.EqualIgnoreCase(specificationModel) && e.WarehouseNumber.EqualIgnoreCase(warehouseNumber));

        return Find(_.MaterialNumber == materialNumber & _.SpecificationModel == specificationModel & _.WarehouseNumber == warehouseNumber);
    }

    /// <summary>根据物料编号查找</summary>
    /// <param name="materialNumber">物料编号</param>
    /// <returns>实体列表</returns>
    public static IList<InventoryAlarm> FindAllByMaterialNumber(String materialNumber)
    {
        if (materialNumber.IsNullOrEmpty()) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.MaterialNumber.EqualIgnoreCase(materialNumber));

        return FindAll(_.MaterialNumber == materialNumber);
    }

    /// <summary>根据物料编号、规格型号查找</summary>
    /// <param name="materialNumber">物料编号</param>
    /// <param name="specificationModel">规格型号</param>
    /// <returns>实体列表</returns>
    public static IList<InventoryAlarm> FindAllByMaterialNumberAndSpecificationModel(String materialNumber, String specificationModel)
    {
        if (materialNumber.IsNullOrEmpty()) return [];
        if (specificationModel.IsNullOrEmpty()) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.MaterialNumber.EqualIgnoreCase(materialNumber) && e.SpecificationModel.EqualIgnoreCase(specificationModel));

        return FindAll(_.MaterialNumber == materialNumber & _.SpecificationModel == specificationModel);
    }
    #endregion

    #region 字段名
    /// <summary>取得库存告警字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>物料编号</summary>
        public static readonly Field MaterialNumber = FindByName("MaterialNumber");

        /// <summary>物料名称</summary>
        public static readonly Field MaterialName = FindByName("MaterialName");

        /// <summary>种类</summary>
        public static readonly Field Category = FindByName("Category");

        /// <summary>规格型号</summary>
        public static readonly Field SpecificationModel = FindByName("SpecificationModel");

        /// <summary>单位</summary>
        public static readonly Field Unit = FindByName("Unit");

        /// <summary>仓库编号</summary>
        public static readonly Field WarehouseNumber = FindByName("WarehouseNumber");

        /// <summary>仓库名称</summary>
        public static readonly Field WarehouseName = FindByName("WarehouseName");

        /// <summary>告警数量</summary>
        public static readonly Field AlarmQuantity = FindByName("AlarmQuantity");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得库存告警字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>物料编号</summary>
        public const String MaterialNumber = "MaterialNumber";

        /// <summary>物料名称</summary>
        public const String MaterialName = "MaterialName";

        /// <summary>种类</summary>
        public const String Category = "Category";

        /// <summary>规格型号</summary>
        public const String SpecificationModel = "SpecificationModel";

        /// <summary>单位</summary>
        public const String Unit = "Unit";

        /// <summary>仓库编号</summary>
        public const String WarehouseNumber = "WarehouseNumber";

        /// <summary>仓库名称</summary>
        public const String WarehouseName = "WarehouseName";

        /// <summary>告警数量</summary>
        public const String AlarmQuantity = "AlarmQuantity";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
