﻿using NewLife;
using XCode.Membership;

namespace HlktechIoT.Entity
{
    public class DepartmentEx : Department
    {
        /// <summary>根据名称查找部门，若不存在则创建</summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static Department GetOrAdd(String name)
        {
            if (name.IsNullOrEmpty()) return null;

            return Add(name, false);
        }

        /// <summary>添加部门，如果存在，则直接返回，否则创建</summary>
        /// <param name="name"></param>
        /// <param name="issys"></param>
        /// <param name="remark"></param>
        /// <returns></returns>
        public static Department Add(String name, Boolean issys, String remark = null)
        {
            //var entity = FindByName(name);
            var entity = Find(__.Name, name);
            if (entity != null) return entity;

            entity = new Department
            {
                Name = name,
                Enable = true,
                Remark = remark
            };
            entity.Save();

            return entity;
        }
    }
}
