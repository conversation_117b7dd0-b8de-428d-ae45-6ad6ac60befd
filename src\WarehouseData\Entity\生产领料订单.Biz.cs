﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using HlktechIoT.Common;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using Pek;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;
using XCode.Shards;

namespace HlktechIoT.Entity;

public partial class WmsPickingOrder : CubeEntityBase<WmsPickingOrder>
{
    #region 对象操作
    static WmsPickingOrder()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(OrderingID));

        // 按年分表
        Meta.ShardPolicy = new TimeShardPolicy(nameof(OrderingTime), Meta.Factory)
        {
                TablePolicy = "{0}_{1:yy}",
                Step = TimeSpan.FromDays(365),
        };

        // 过滤器 UserModule、TimeModule、IPModule

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;

        // 单对象缓存
        var sc = Meta.SingleCache;
        // sc.Expire = 60;
        sc.FindSlaveKeyMethod = k => Find(_.OrderId == k);
        sc.GetSlaveKeyMethod = e => e.OrderId;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 这里验证参数范围，建议抛出参数异常，指定参数名，前端用户界面可以捕获参数异常并聚焦到对应的参数输入框
        if (OrderId.IsNullOrEmpty()) throw new ArgumentNullException(nameof(OrderId), "ERP订单编号不能为空！");

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 检查唯一索引
        // CheckExist(method == DataMethod.Insert, nameof(OrderId));

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化WmsPickingOrder[生产领料订单]数据……");

    //    var entity = new WmsPickingOrder();
    //    entity.Id = 0;
    //    entity.OrderId = "abc";
    //    entity.OrderingID = 0;
    //    entity.OrderingTime = DateTime.Now;
    //    entity.PickingID = 0;
    //    entity.PickingTime = DateTime.Now;
    //    entity.ProductionID = 0;
    //    entity.ProductionTime = DateTime.Now;
    //    entity.AuditingID = 0;
    //    entity.AuditingTime = DateTime.Now;
    //    entity.PackID = 0;
    //    entity.PackTime = DateTime.Now;
    //    entity.CancelID = 0;
    //    entity.CancelTime = DateTime.Now;
    //    entity.IsDuplicate = true;
    //    entity.Status = 0;
    //    entity.IsEnd = true;
    //    entity.HasRemark = true;
    //    entity.RemarkTime = DateTime.Now;
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化WmsPickingOrder[生产领料订单]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    /// <summary>打单人</summary>
    [XmlIgnore, IgnoreDataMember]
    [ScriptIgnore]
    public User? OrderingUser => Extends.Get(nameof(OrderingUser), k => User.FindByID(OrderingID));

    /// <summary>领料人</summary>
    [XmlIgnore, IgnoreDataMember]
    [ScriptIgnore]
    public User? PickingUser => Extends.Get(nameof(PickingUser), k => User.FindByID(PickingID));

    /// <summary>生产人</summary>
    [XmlIgnore, IgnoreDataMember]
    [ScriptIgnore]
    public User? ProductionUser => Extends.Get(nameof(ProductionUser), k => User.FindByID(ProductionID));

    /// <summary>审核人</summary>
    [XmlIgnore, IgnoreDataMember]
    [ScriptIgnore]
    public User? AuditingUser => Extends.Get(nameof(AuditingUser), k => User.FindByID(AuditingID));

    /// <summary>出货人</summary>
    [XmlIgnore, IgnoreDataMember]
    [ScriptIgnore]
    public User? PackUser => Extends.Get(nameof(PackUser), k => User.FindByID(PackID));
    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="orderId">ERP订单编号</param>
    /// <param name="orderingId">打单人</param>
    /// <param name="pickingId">领料人</param>
    /// <param name="productionId">生产人</param>
    /// <param name="auditingId">审核人</param>
    /// <param name="packId">入库人</param>
    /// <param name="packTime">入库时间</param>
    /// <param name="isDuplicate">重复打单</param>
    /// <param name="status">状态。0为正常，1为取消</param>
    /// <param name="isEnd">结束完成</param>
    /// <param name="hasRemark">是否有备注</param>
    /// <param name="progress">进度</param>
    /// <param name="start">打单时间开始</param>
    /// <param name="end">打单时间结束</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<WmsPickingOrder> Search(String orderId, Int32 orderingId, Int32 pickingId, Int32 productionId, Int32 auditingId, Int32 packId, DateTime packTime, Boolean? isDuplicate, Int16 status, Boolean? isEnd, Int16? hasRemark,Int16? progress, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (!orderId.IsNullOrEmpty()) exp &= _.OrderId == orderId;
        if (orderingId >= 0) exp &= _.OrderingID == orderingId;
        if (pickingId >= 0) exp &= _.PickingID == pickingId;
        if (productionId >= 0) exp &= _.ProductionID == productionId;
        if (auditingId >= 0) exp &= _.AuditingID == auditingId;
        if (packId >= 0) exp &= _.PackID == packId;
        if (isDuplicate != null) exp &= _.IsDuplicate == isDuplicate;
        if (status >= 0) exp &= _.Status == status;
        //if (isEnd != null) exp &= _.IsEnd == isEnd;
        if (hasRemark >= 0)
        {
            exp &= _.HasRemark == (hasRemark == 1);
        }
        if (progress == 0)
        {
            exp &= _.IsEnd == false & (_.PickingTime.IsNull()) & _.PackID == 0 & _.AuditingID == 0;
        }
        else if (progress == 1)
        {
            exp &= _.IsEnd == false & _.PickingTime.NotIsNull() & _.ProductionTime.IsNull() & _.AuditingTime.IsNull();
        }
        else if (progress == 2)
        {
            exp &= _.IsEnd == false & _.ProductionTime.NotIsNull() & _.AuditingTime.IsNull();
        }
        else if (progress == 4)
        {
            exp &= _.IsEnd == true & _.Status == 0;
        }
        else if (progress == 5)
        {
            exp &= _.IsEnd == false & _.OrderingTime > "2000-1-1" & _.OrderingTime < DateTime.Now.AddHours(-IoTSetting.Current.POTimeout);   // 订单超时
        }
        exp &= _.OrderingTime > "2000-1-1".ToDateTime();
        exp &= _.OrderingTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);

        return FindAll(exp, page);
    }
    /// <summary>根据ERP订单编号查找</summary>
    /// <param name="orderId">ERP订单编号</param>
    /// <param name="dTime">打单时间</param>
    /// <returns>实体对象</returns>
    public static WmsPickingOrder? FindByOrderIdWithTime(String orderId, DateTime dTime)
    {
        if (orderId.IsNullOrEmpty()) return null;
        if (dTime <= DateTime.MinValue) return null;

        var year = orderId.Mid(2, 2);
        var nowyear = dTime.Year.ToString().Right(2);
        if (year.ToInt() == nowyear.ToInt())
        {
            return Find(_.OrderId == orderId & _.OrderingTime >= dTime);
        }
        else
        {
            return Find(_.OrderId == orderId & _.OrderingTime >= dTime.AddYears(-1));
        }
    }

    /// <summary>根据订单编号集合查找订单</summary>
    /// <returns>实体列表</returns>
    public static IList<WmsPickingOrder> FindAllByIds(String ids)
    {
        if (ids.IsNullOrEmpty()) return new List<WmsPickingOrder>();

        return FindAll(_.OrderingTime > "2000-1-1" & _.Id.In(ids.Split(",")));
    }

    /// <summary>查找未完成的订单</summary>
    /// <returns>实体列表</returns>
    public static IList<WmsPickingOrder> FindAllByNoFinish()
    {
        return FindAll(_.IsEnd == false & _.OrderingTime > "2000-1-1" & _.Status == 0);
    }

    /// <summary>获取所有的订单数量</summary>
    /// <returns>实体列表</returns>
    public static Int64 FindAllCount()
    {
        return FindCount(_.OrderingTime > "2000-1-1" & _.Status == 0);
    }

    /// <summary>获取所有已入库订单数量</summary>
    /// <returns>实体列表</returns>
    public static Int64 FindAllIntoboundCount()
    {
        return FindCount(_.OrderingTime > "2000-1-1" & _.IsEnd == true & _.Status == 0);
    }

    /// <summary>获取今天的订单数量</summary>
    /// <returns>实体列表</returns>
    public static Int64 FindTodayCount()
    {
        return FindCount(_.OrderingTime.Today() & _.Status == 0);
    }

    /// <summary>获取今天已入库订单数量</summary>
    /// <returns>实体列表</returns>
    public static Int64 FindTodayIntoboundCount()
    {
        return FindCount(_.OrderingTime.Today() & _.IsEnd == true & _.Status == 0);
    }

    /// <summary>高级查询</summary>
    /// <param name="UId">用户Id</param>
    /// <param name="key">关键字</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<WmsPickingOrder> Search(Int32 UId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (UId > 0) exp &= _.OrderingID == UId | _.PickingID == UId | _.ProductionID == UId | _.AuditingID == UId | _.PackID == UId;
        exp &= _.OrderingTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);

        exp &= _.Status == 0;

        return FindAll(exp, page);
    }

    /// <summary>高级查询 订单超时</summary>
    /// <param name="UId">用户Id</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<WmsPickingOrder> Search(Int32 UId, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (UId > 0) exp &= _.OrderingID == UId | _.PickingID == UId | _.ProductionID == UId | _.AuditingID == UId | _.PackID == UId;
        exp &= _.IsEnd == false & _.OrderingTime > "2000-1-1" & _.OrderingTime < DateTime.Now.AddHours(-IoTSetting.Current.POTimeout);   // 订单超时
        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);

        return FindAll(exp, page);
    }

    /// <summary>高级查询 待领料</summary>
    /// <param name="UId">用户Id</param>
    /// <param name="key">关键字</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<WmsPickingOrder> SearchPicking(Int32 UId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (UId > 0) exp &= _.OrderingID == UId | _.PickingID == UId | _.ProductionID == UId | _.AuditingID == UId | _.PackID == UId;
        exp &= _.IsEnd == false & (_.PickingTime.IsNull());
        exp &= _.OrderingTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);

        exp &= (_.PackID == 0 & _.AuditingID == 0);
        exp &= _.Status == 0;

        return FindAll(exp, page);
    }

    /// <summary>高级查询 待生产</summary>
    /// <param name="UId">用户Id</param>
    /// <param name="key">关键字</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<WmsPickingOrder> SearchProduction(Int32 UId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (UId > 0) exp &= _.OrderingID == UId | _.PickingID == UId | _.ProductionID == UId | _.AuditingID == UId | _.PackID == UId;
        exp &= _.IsEnd == false & _.PickingTime.NotIsNull() & _.ProductionTime.IsNull() & _.AuditingTime.IsNull();
        exp &= _.OrderingTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);
        exp &= _.Status == 0;

        return FindAll(exp, page);
    }

    /// <summary>高级查询 生产中</summary>
    /// <param name="UId">用户Id</param>
    /// <param name="key">关键字</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<WmsPickingOrder> SearchInProduction(Int32 UId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (UId > 0) exp &= _.OrderingID == UId | _.PickingID == UId | _.ProductionID == UId | _.AuditingID == UId | _.PackID == UId;
        exp &= _.IsEnd == false & _.ProductionTime.NotIsNull() & _.AuditingTime.IsNull();
        exp &= _.OrderingTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);
        exp &= _.Status == 0;

        return FindAll(exp, page);
    }

    /// <summary>高级查询 待入库</summary>
    /// <param name="UId">用户Id</param>
    /// <param name="key">关键字</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<WmsPickingOrder> SearchPack(Int32 UId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (UId > 0) exp &= _.OrderingID == UId | _.PickingID == UId | _.ProductionID == UId | _.AuditingID == UId | _.PackID == UId;
        exp &= _.IsEnd == false & _.AuditingTime.NotIsNull() & _.PackTime.IsNull();
        exp &= _.OrderingTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);
        exp &= _.Status == 0;

        return FindAll(exp, page);
    }

    /// <summary>高级查询 已出库</summary>
    /// <param name="UId">用户Id</param>
    /// <param name="key">关键字</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<WmsPickingOrder> SearchShippedOutbound(Int32 UId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (UId > 0) exp &= _.OrderingID == UId | _.PickingID == UId | _.ProductionID == UId | _.AuditingID == UId | _.PackID == UId;
        exp &= _.IsEnd == true;
        exp &= _.OrderingTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);
        exp &= _.Status == 0;

        return FindAll(exp, page);
    }

    // Select Count(Id) as Id,Category From DH_WmsPickingOrder Where PackTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<WmsPickingOrder> _CategoryCache = new(nameof(Category))
    //{
    //Where = _.PackTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public IWmsPickingOrder ToModel()
    {
        var model = new WmsPickingOrder();
        model.Copy(this);

        return model;
    }

    #endregion
}
