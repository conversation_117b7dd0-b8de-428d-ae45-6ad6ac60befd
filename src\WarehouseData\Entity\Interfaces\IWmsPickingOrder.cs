﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>生产领料订单</summary>
public partial interface IWmsPickingOrder
{
    #region 属性
    /// <summary>编号</summary>
    Int64 Id { get; set; }

    /// <summary>ERP订单编号</summary>
    String OrderId { get; set; }

    /// <summary>打单人</summary>
    Int32 OrderingID { get; set; }

    /// <summary>打单时间</summary>
    DateTime OrderingTime { get; set; }

    /// <summary>领料人</summary>
    Int32 PickingID { get; set; }

    /// <summary>领料时间</summary>
    DateTime PickingTime { get; set; }

    /// <summary>生产人</summary>
    Int32 ProductionID { get; set; }

    /// <summary>生产时间</summary>
    DateTime ProductionTime { get; set; }

    /// <summary>审核人</summary>
    Int32 AuditingID { get; set; }

    /// <summary>审核时间</summary>
    DateTime AuditingTime { get; set; }

    /// <summary>入库人</summary>
    Int32 PackID { get; set; }

    /// <summary>入库时间</summary>
    DateTime PackTime { get; set; }

    /// <summary>取消人</summary>
    Int32 CancelID { get; set; }

    /// <summary>取消时间</summary>
    DateTime CancelTime { get; set; }

    /// <summary>重复打单</summary>
    Boolean IsDuplicate { get; set; }

    /// <summary>状态。0为正常，1为取消</summary>
    Int16 Status { get; set; }

    /// <summary>结束完成</summary>
    Boolean IsEnd { get; set; }

    /// <summary>是否有备注</summary>
    Boolean HasRemark { get; set; }

    /// <summary>备注</summary>
    String? Remark { get; set; }

    /// <summary>备注时间</summary>
    DateTime RemarkTime { get; set; }
    #endregion
}
