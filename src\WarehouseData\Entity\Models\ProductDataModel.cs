﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>产品资料</summary>
public partial class ProductDataModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>类型 0普通 1产测固件 2出货固件 3标签模板</summary>
    public Int32 Ptype { get; set; }

    /// <summary>资料类别Id1</summary>
    public Int32 ProductDataCategoryId1 { get; set; }

    /// <summary>资料类别Id2</summary>
    public Int32 ProductDataCategoryId2 { get; set; }

    /// <summary>文件名</summary>
    public String? FileName { get; set; }

    /// <summary>资料路径</summary>
    public String? FilePath { get; set; }

    /// <summary>版本号</summary>
    public String? Version { get; set; }

    /// <summary>备注</summary>
    public String? Remark { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductData model)
    {
        Id = model.Id;
        Ptype = model.Ptype;
        ProductDataCategoryId1 = model.ProductDataCategoryId1;
        ProductDataCategoryId2 = model.ProductDataCategoryId2;
        FileName = model.FileName;
        FilePath = model.FilePath;
        Version = model.Version;
        Remark = model.Remark;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
