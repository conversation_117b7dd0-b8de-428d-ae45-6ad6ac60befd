﻿@using DG.Web.Framework.Models
@{
    Layout = null;
}

</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@T("仓库发货系统登录")</title>
    <script src="../libs/layui/layui.js"></script>
    <link rel="stylesheet" href="../libs/layui/css/layui.css" />
</head>
<style>
    .box {
        width: 100%;
        height: 100%;
        background-image: url('../images/bg1.png');
        /* background-image: url('../images/bg.png'); */
        /* background:linear-gradient(rgb(17, 63, 143),rgb(16, 39, 155),white 170%); */
        /* background-repeat: no-repeat; */
        /* background-size: 100% 50%; */
    }

    ._loginBox {
        min-width: 600px;
        min-height: 450px;
        width: 35%;
        height: 20%;
        position: absolute;
        top: 50%;
        left: 50%;
        /* filter: blur(0px); */
        background-color: rgba(9, 49, 161, 0.1);
        transform: translate(-50%,-50%);
    }

    .loginBox {
        min-width: 600px;
        min-height: 450px;
        width: 35%;
        height: 20%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
        background-color: rgba(255, 255, 255, 0.7);
        border-radius: 10px;
        box-shadow: 0 0 5px #ccc;
        /* border: 2px solid white; */
        display: flex;
        flex-direction: column;
    }

    section {
        margin-left: 20%;
        width: 60%;
        padding: 10px 0px;
        text-align: center;
        color: black;
    }

    .title {
        font-size: 30px;
        cursor: pointer;
    }

    .layui-input-wrap {
        width: 100%;
    }

    a {
        text-decoration-style: none;
        transition: all .5s;
        color: rgb(80, 80, 80);
    }

        a:hover {
            color: rgb(113, 233, 193);
        }
</style>
<body>
    <div class="box">
        <!-- <div class="_loginBox"></div> -->
        <div class="loginBox">
            <form class="layui-form" action="javascript:void(0);">
                <section>
                    <div class="title">@T("仓库发货系统")</div>
                </section>

                <section>
                    <div style="display: flex;margin-top: 10px;">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix layui-input-split">
                                <i class="layui-icon layui-icon-user"></i>
                            </div>
                            <input type="text" name="username" placeholder="@T("用户名/邮箱/手机/编码")" hover class="layui-input dhvalidate" autofocus maxlength="256" id="LAY-user-login-username">
                        </div>
                    </div>
                </section>
                <section>
                    <div style="display: flex;">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix layui-input-split">
                                <i class="layui-icon layui-icon-lock"></i>
                            </div>
                            <input type="password" name="password" placeholder="@T("密码")" hover class="layui-input dhvalidate" maxlength="32" id="LAY-user-login-password">
                        </div>
                    </div>
                </section>
                <section>
                    <div style="display: flex;">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix layui-input-split">
                                <i class="layadmin-user-login-icon layui-icon layui-icon-vercode" for="LAY-user-login-vercode"></i>
                            </div>
                            <input type="text" name="checkcode" placeholder="@T("图形验证码")" hover class="layui-input" id="LAY-user-login-vercode" />
                        </div>
                        <div style="margin-left: 10px;">
                            <img src="@DG.Setting.Current.CaptChaUrl" class="layadmin-user-login-codeimg" id="LAY-user-get-vercode" style="width: 100%; cursor: pointer;">
                        </div>
                    </div>
                </section>
                <section>
                    <button class="layui-btn" style="width: 100%;" lay-submit lay-filter="LAY-user-login-submit">@T("登陆")</button>
                </section>
                <section>
                    <div style="display: flex;">
                        <div class="layui-form">
                            <input type="checkbox" name="keeplogin" id="keeplogin" value="true" title="@T("记住密码")" checked>
                        </div>
                        <div class="layui-form" style="margin-left: auto; cursor: pointer;">
                            <a lay-submit lay-filter="forget">@T("忘记密码")</a> | <a lay-submit lay-filter="other">@T("遇到困难？")</a>
                        </div>
                </section>
            </form>
        </div>
    </div>
    <script src="~/js/Storage.js" asp-append-version="true"></script>
    <script src="~/js/crypto.js" asp-append-version="true"></script>
    <script>
        layui.use(['form', 'element', 'jquery', 'layer'], function () {
            var $ = layui.jquery;
            var form = layui.form;
            var element = layui.element;
            const generateHash = function (str) {
                return CryptoJS.MD5(str).toString().toUpperCase();
            };
            form.render();

            const urlParams = new URLSearchParams(window.location.search);
            const params = Object.fromEntries(urlParams.entries());

            if (params.isLogin == 'false') {
                layer.msg('@T("登陆信息验证失败!")', { icon: 5, time: 3000 });
            }
            $("#LAY-user-get-vercode").on('click', function () {
                this.src = '@(DG.Setting.Current.CaptChaUrl)?t=' + new Date().getTime()
            });

            $('.dhvalidate').on('input', function () {
                var passwordValue = $(this).val();
                var trimmedValue = passwordValue.replace(/\s/g, '');
                $(this).val(trimmedValue);
            });

            /** 去大屏 */
            const toBigData = function () {
                if (window.location.hostname != 'localhost' && window.location.hostname != '127.0.0.1') {  // 生产模式下的逻辑
                    let currentURL = window.location.href;
                    let newURL = currentURL.replace("/index", "/BigData");
                    window.location.href = newURL;
                } else {  // 开发模式下的逻辑
                    let currentURL = window.location.href;
                    let newURL = currentURL.replace(":5173/index", ":9145/BigData");
                    window.location.href = newURL;
                }
            }
            form.on('submit(forget)', function (data) {
                layer.msg('@T("功能暂未开通")');
            })
            form.on('submit(other)', function (data) {
                layer.msg('@T("功能暂未开通")');
            })

            form.on('submit(LAY-user-login-submit)', function (data) {
                data.field.keeplogin = $("#keeplogin").prop('checked') ? true : false;
                var field = data.field; //获取提交的字段

                if (field.username.length == 0) {

                    layer.msg('@T("账号不能为空")');
                    return;
                }

                if (field.password.length == 0) {
                    layer.msg('@T("密码不能为空")');
                    return;
                }

                if (field.checkcode.length == 0) {
                    layer.msg('@T("验证码不能为空")');
                    return;
                }
                field.password = generateHash(field.password);
                var waitIndex = layer.load(2);
                $.ajax({
                    method: 'post',
                    url: "@Url.Action("Login")",
                    data: field,
                    contentType: "application/x-www-form-urlencoded; charset=utf-8",
                    abpHandleError: false
                }).fail(function (jqXHR) {
                    $('#LAY-user-get-vercode').attr('src', '@(DG.Setting.Current.CaptChaUrl)?' + (new Date().getTime()));
                    $("#LAY-user-login-vercode").val("");

                    layer.msg(jqXHR.message, { icon: 5 });
                }).always(function (res) {
                    console.log(res);
                    layer.close(waitIndex);
                    if (res.success) {
                        var now = new Date().getTime(); // 获取当前时间戳
                        var seconds = res.data.RefreshUtcExpires - now; // 计算时间戳与当前时间之间的毫秒数

                        const storage = new Storage();  // new Storage(3)

                        if (data.field.keeplogin) {
                            storage.set("remember", 365 * 24 * 60 * 60, seconds);
                        }
                        else {
                            storage.set("remember", 2 * 60 * 60, seconds);
                        }

                        storage.set("AccessToken", res.data.AccessToken, seconds);
                        storage.set("RefreshToken", res.data.RefreshToken, seconds);
                        storage.set("AccessTokenUtcExpires", res.data.AccessTokenUtcExpires, seconds);
                        storage.set("RefreshUtcExpires", res.data.RefreshUtcExpires, seconds);
                        toBigData()
                    }
                    else {
                        layer.msg(res.msg);
                        $('#LAY-user-get-vercode').attr('src', '@(DG.Setting.Current.CaptChaUrl)?' + (new Date().getTime()));
                        $("#LAY-user-login-vercode").val("");
                    }
                });

                return false;
            });
        })

    </script>

</body>
</html>