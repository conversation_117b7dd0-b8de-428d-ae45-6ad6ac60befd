﻿using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

namespace HlktechIoT.Dto.Export;

[ExcelExporter(Name = "Sheet1", AutoFitAllColumn = true, TableStyle = OfficeOpenXml.Table.TableStyles.None, AutoCenter = true)]
public class DeviceExport {
    /// <summary> 
    /// 设备Mac地址
    /// </summary> 
    [ExporterHeader(DisplayName = "设备Mac地址", IsBold = false)]
    public string? Mac { get; set; }

    /// <summary> 
    /// 设备号
    /// </summary> 
    [ExporterHeader(DisplayName = "设备号", IsBold = false)]
    public string? Code { get; set; }

    /// <summary> 
    /// 设备类型
    /// </summary> 
    [ExporterHeader(DisplayName = "设备类型", IsBold = false)]
    public string? HType { get; set; }

    /// <summary> 
    /// 设备型号
    /// </summary> 
    [ExporterHeader(DisplayName = "设备型号", IsBold = false)]
    public string? DeviceModel { get; set; }

    /// <summary> 
    /// 绑定用户姓名
    /// </summary> 
    [ExporterHeader(DisplayName = "绑定用户姓名", IsBold = false)]
    public string? BindUserRealName { get; set; }

    /// <summary> 
    /// 绑定用户姓名
    /// </summary> 
    [ExporterHeader(DisplayName = "设备所在工序", IsBold = false)]
    public string? Status { get; set; }

    /// <summary> 
    /// 备注
    /// </summary> 
    [ExporterHeader(DisplayName = "备注", IsBold = false)]
    public string? Remark { get; set; }
}
