﻿@{
    Html.AppendTitleParts(T("用户列表").Text);
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }
</style>
<script asp-location="Head">
    var pleaseSelect = '@T("请选择")';
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("账户名")：</label>
            <div class="layui-input-inline">
                <input type="text" name="name" id="name" placeholder="@T("请输入用户名/手机号/邮箱")" autocomplete="off" class="layui-input" lay-filter="name">
            </div>
            <div class="layui-inline select">
                <label class="layui-form-label">@T("部门")：</label>
                <div class="layui-input-inline ">
                    <ul id="demoTree2" class="dtree" data-id="0" style="z-index: 99999"></ul>
                </div>
            </div>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetPageUser")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 80
            , cols: [[
                { field: 'ID', title: '@T("ID")',width:80 }
                , { field: 'Name', title: '@T("账号")', minWidth:120 }
                , { field: 'DisplayName', title: '@T("姓名")', minWidth: 100 }
                , { field: 'RoleName', title: '@T("用户组")',width:100 }
                , { field: 'Mobile', title: '@T("手机号")',width:120 }
                , { field: 'DepartmentName', title: '@T("部门")', width: 120 }
                , { field: 'Mail', title: '@T("邮箱")', minWidth: 200 }
                , { field: 'Enable', title: '@T("是否启用")', templet: '#switchTpl', width: 130 }
                , { field: 'RegisterTime', title: '@T("注册时间")', width: 160 }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: 150 }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-130'
            , id: 'tables'
            ,where:{

            }
        });

        dtree.render({
            elem: "#demoTree2",
            initLevel: "1",
            width: "100%",
            method: 'get',
            url: "@Url.Action("GetDepartmentList")",
            //url: "/test.json",
            select: true
        });

        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        where: {
                            name: $("#name").val(),
                        }
                    });
            }
        }

        $("#name").on("input", function (e) {
            active.reload();
        });

        var lastSelectValue = null; //上一次选中的值

        dtree.on('node("demoTree2")', function (obj) {
            @* console.log('选中',obj.param,lastSelectValue) *@
            if(lastSelectValue != null && lastSelectValue === obj.param.context ){
                lastSelectValue = null
                //清除选中值
                @* console.log('触发',dtree) *@
                dtree.reload("demoTree2", {
                    initLevel: "1",
                    width: "100%",
                    method: 'get',
                    url: "@Url.Action("GetDepartmentList")",
                    select: true,
                    where: { "dId": '' },
                    checkData: [] // 清空选中的值
                });
                @* 重新拉数据 *@
                table.reload('tables', {
                    initLevel: "1",
                    where: { "dId": '' },
                    select: true,
                    page: {
                        curr: 1
                    }
                    , checkData: [''] // 清空选中的值
                })
                return;
            }
            if(lastSelectValue == null || lastSelectValue != obj.param.context ){
                lastSelectValue = obj.param.context 
            }
            @* console.log(obj) *@
            table.reload('tables', {
                where: { "dId": obj.param.nodeId },
                page: {
                    curr: 1
                }
            })
        });

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'add') {
                window.add();
                //top.layui.dg.popupRight({
                //    id: 'EditUser'
                //    , title: ' @T("新增用户")'
                //    , closeBtn: 1
                //    , area: ['710px']
                //    , success: function () {
                //        $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("EditUser")" frameborder="0" class="layadmin-iframe"></iframe>');
                //    }

                //});
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("MemberDelete")', { Id: data.ID }, function (data) {
                        if (data.success) {
                            abp.notify.success('@T("删除成功")');
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'edit') {
                window.edit(data);
            }

        });

        window.saveCallback = function (data) {
            layer.close(data.index);
            abp.notify.success(data.msg);
            table.reload("tables");
        }

        window.add = function () {
            layer.open({
                type: 2,
                title: "@T("添加用户")",
                content: "@Url.Action("Add")",
                area: ["710px", "436px"],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    window['layui-layer-iframe' + index].submitForm();
                }
            });
        }

        window.edit = function (data) {
            layer.open({
                type: 2,
                title: "@T("编辑用户")",
                content: "@Url.Action("Edit")" + abp.utils.formatString("?id={0}", data.ID),
                area: ["710px", "436px"],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    window['layui-layer-iframe' + index].submitForm();
                }
            });
        }

        //监听开关事件
        form.on('switch(statusedit)', function (data) {
            //开关是否开启，true或者false
            var checked = data.elem.checked;
            //获取所属属性值
            var id = data.elem.attributes['data-id'].nodeValue;
            $.post('@Url.Action("ModifyState")', { id: id, status: checked }, function (res) {
                if (res.success) {
                    abp.notify.success(res.msg);
                }
                else {
                    abp.notify.error(res.msg);
                }
            });
        });

    });
</script>

<script type="text/html" id="tool">
    @if (this.Has((PermissionFlags)4)){
    <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="edit"> @T("编辑")</a>
    }
    @if (this.Has((PermissionFlags)8)){
    <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="del"> @T("删除")</a>
    }
</script>

<script type="text/html" id="switchTpl">
    @if (this.Has((PermissionFlags)4)){
    <input type="checkbox" name="status" data-id="{{d.ID}}" lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" {{ d.Enable == "1"?'checked':''}}>
    }else{
    <input type="checkbox" name="status" data-id="{{d.ID}}" lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" {{ d.Enable == "1"?'checked':''}} disabled>
    }
</script>

<script type="text/html" id="user-toolbar">
    @if (this.Has((PermissionFlags)2)){
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon  layui-icon-add-1"></i>
        @T("新增")
    </button>
    }
</script>