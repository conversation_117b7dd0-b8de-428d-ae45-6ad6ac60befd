﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>工具版本表</summary>
public partial class ToolVersionModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>工具标识</summary>
    public String? Code { get; set; }

    /// <summary>版本号</summary>
    public String? Version { get; set; }

    /// <summary>升级内容</summary>
    public String? Content { get; set; }

    /// <summary>文件包名</summary>
    public String? FileName { get; set; }

    /// <summary>文件大小</summary>
    public Int32 FileSize { get; set; }

    /// <summary>下载地址</summary>
    public String? FilePath { get; set; }

    /// <summary>是否强制升级</summary>
    public Boolean IsQiangZhi { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IToolVersion model)
    {
        Id = model.Id;
        Code = model.Code;
        Version = model.Version;
        Content = model.Content;
        FileName = model.FileName;
        FileSize = model.FileSize;
        FilePath = model.FilePath;
        IsQiangZhi = model.IsQiangZhi;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
