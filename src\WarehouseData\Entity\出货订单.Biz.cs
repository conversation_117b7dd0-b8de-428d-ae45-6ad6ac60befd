using DH;

using HlktechIoT.Common;

using NewLife;
using NewLife.Data;

using Pek;

using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

using XCode;
using XCode.Membership;
using XCode.Shards;

namespace HlktechIoT.Entity;

public partial class WmsOrder : CubeEntityBase<WmsOrder>
{
    #region 对象操作
    static WmsOrder()
    {
        // 按年分表
        Meta.ShardPolicy = new TimeShardPolicy(nameof(OrderingTime), Meta.Factory)
        {
            TablePolicy = "{0}_{1:yy}",
            Step = TimeSpan.FromDays(366),
        };

        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(CreateUserID));

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add(new UserModule { AllowEmpty = false });
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add(new IPModule { AllowEmpty = false });

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (method == DataMethod.Insert && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
            if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
        }*/
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
        //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化WmsOrder[出货订单]数据……");

    //    var entity = new WmsOrder();
    //    entity.CreateUser = "abc";
    //    entity.CreateUserID = 0;
    //    entity.CreateTime = DateTime.Now;
    //    entity.CreateIP = "abc";
    //    entity.UpdateUser = "abc";
    //    entity.UpdateUserID = 0;
    //    entity.UpdateTime = DateTime.Now;
    //    entity.UpdateIP = "abc";
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化WmsOrder[出货订单]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    /// <summary>打单人</summary>
    [XmlIgnore, IgnoreDataMember]
    [ScriptIgnore]
    public User? OrderingUser => Extends.Get(nameof(OrderingUser), k => User.FindByID(OrderingID));

    /// <summary>领料人</summary>
    [XmlIgnore, IgnoreDataMember]
    [ScriptIgnore]
    public User? PickingUser => Extends.Get(nameof(PickingUser), k => User.FindByID(PickingID));

    /// <summary>生产人</summary>
    [XmlIgnore, IgnoreDataMember]
    [ScriptIgnore]
    public User? ProductionUser => Extends.Get(nameof(ProductionUser), k => User.FindByID(ProductionID));

    /// <summary>审核人</summary>
    [XmlIgnore, IgnoreDataMember]
    [ScriptIgnore]
    public User? AuditingUser => Extends.Get(nameof(AuditingUser), k => User.FindByID(AuditingID));

    /// <summary>打包人</summary>
    [XmlIgnore, IgnoreDataMember]
    [ScriptIgnore]
    public User? ShippingUser => Extends.Get(nameof(ShippingUser), k => User.FindByID(ShippingID));

    /// <summary>出货人</summary>
    [XmlIgnore, IgnoreDataMember]
    [ScriptIgnore]
    public User? PackUser => Extends.Get(nameof(PackUser), k => User.FindByID(PackID));
    #endregion

    #region 扩展查询
    /// <summary>根据打单人查找</summary>
    /// <param name="orderingId">打单人</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> FindAllByOrderingID(Int32 orderingId)
    {
        if (orderingId <= 0) return new List<WmsOrder>();

        return FindAll(_.OrderingID == orderingId);
    }

    /// <summary>根据领料人查找</summary>
    /// <param name="pickingId">领料人</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> FindAllByPickingID(Int32 pickingId)
    {
        if (pickingId <= 0) return new List<WmsOrder>();

        return FindAll(_.PickingID == pickingId);
    }

    /// <summary>根据生产人查找</summary>
    /// <param name="productionId">生产人</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> FindAllByProductionID(Int32 productionId)
    {
        if (productionId <= 0) return new List<WmsOrder>();

        return FindAll(_.ProductionID == productionId);
    }

    /// <summary>根据审核人查找</summary>
    /// <param name="auditingId">审核人</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> FindAllByAuditingID(Int32 auditingId)
    {
        if (auditingId <= 0) return new List<WmsOrder>();

        return FindAll(_.AuditingID == auditingId);
    }

    /// <summary>根据打包人查找</summary>
    /// <param name="packId">打包人</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> FindAllByPackID(Int32 packId)
    {
        if (packId <= 0) return new List<WmsOrder>();

        return FindAll(_.PackID == packId);
    }

    /// <summary>根据打包时间查找</summary>
    /// <param name="packTime">打包时间</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> FindAllByPackTime(DateTime packTime)
    {
        return FindAll(_.PackTime == packTime);
    }

    /// <summary>查找未完成的订单</summary>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> FindAllByNoFinish()
    {
        return FindAll(_.IsEnd == false & _.OrderingTime > "2000-1-1" & _.Status == 0);
    }

    /// <summary>获取所有的订单数量</summary>
    /// <returns>实体列表</returns>
    public static Int64 FindAllCount()
    {
        return FindCount(_.OrderingTime > "2000-1-1" & _.Status == 0);
    }

    /// <summary>获取所有已出库订单数量</summary>
    /// <returns>实体列表</returns>
    public static Int64 FindAllOutboundCount()
    {
        return FindCount(_.OrderingTime > "2000-1-1" & _.IsEnd == true & _.Status == 0);
    }

    /// <summary>获取今天的订单数量</summary>
    /// <returns>实体列表</returns>
    public static Int64 FindTodayCount()
    {
        return FindCount(_.OrderingTime.Today() & _.Status == 0);
    }

    /// <summary>获取今天已出库订单数量</summary>
    /// <returns>实体列表</returns>
    public static Int64 FindTodayOutboundCount()
    {
        return FindCount(_.OrderingTime.Today() & _.IsEnd == true & _.Status  == 0);
    }

    /// <summary>
    /// 返回所有数据
    /// </summary>
    /// <returns>实体集合</returns>
    public static IList<WmsOrder> GetAll(String? selects = null)
    {
        return FindAll(_.OrderingTime > "2000-1-1", new PageParameter { Desc = true, Sort = _.Id, PageSize = DHSetting.Current.MaxExport }, selects);
    }

    /// <summary>
    /// 返回指定数据
    /// </summary>
    /// <returns>实体集合</returns>
    public static IList<WmsOrder> GetAll(String Ids, String? selects = null)
    {
        return FindAll(_.OrderingTime > "2000-1-1" & _.Id.In(Ids.Split(",")), new PageParameter { Desc = true, Sort = _.Id, PageSize = DHSetting.Current.MaxExport }, selects);
    }

    /// <summary>根据打单时间查找</summary>
    /// <param name="orderingTime">打单时间</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> FindAllByOrderingTime(DateTime orderingTime)
    {
        return FindAll(_.OrderingTime == orderingTime);
    }

    /// <summary>根据状态查找</summary>
    /// <param name="status">状态</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> FindAllByStatus(Int16 status)
    {
        if (status <= 0) return new List<WmsOrder>();

        return FindAll(_.Status == status);
    }

    /// <summary>根据订单编号集合查找订单</summary>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> FindAllByIds(String ids)
    {
        if (ids.IsNullOrEmpty()) return new List<WmsOrder>();

        return FindAll(_.OrderingTime > "2000-1-1" & _.Id.In(ids.Split(",")));
    }

    /// <summary>根据ERP订单编号查找</summary>
    /// <param name="orderId">ERP订单编号</param>
    /// <param name="dTime">打单时间</param>
    /// <returns>实体对象</returns>
    public static WmsOrder? FindByOrderIdWithTime(String orderId, DateTime dTime)
    {
        if (orderId.IsNullOrEmpty()) return null;
        if (dTime <= DateTime.MinValue) return null;

        var year = orderId.Mid(2, 2);
        var nowyear = dTime.Year.ToString().Right(2);
        if (year.ToInt() == nowyear.ToInt())
        {
            return Find(_.OrderId == orderId & _.OrderingTime >= dTime);
        }
        else
        {
            return Find(_.OrderId == orderId & _.OrderingTime >= dTime.AddYears(-1));
        }
    }
    #endregion

    #region 高级查询

    /// <summary>高级查询</summary>
    /// <param name="UId">用户Id</param>
    /// <param name="key">关键字</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> Search(Int32 UId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (UId > 0) exp &= _.OrderingID == UId | _.PickingID == UId | _.ProductionID == UId | _.AuditingID == UId | _.PackID == UId;
        exp &= _.OrderingTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);

        exp &= _.Status == 0;

        return FindAll(exp, page);
    }

    /// <summary>高级查询 订单超时</summary>
    /// <param name="UId">用户Id</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> Search(Int32 UId, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (UId > 0) exp &= _.OrderingID == UId | _.PickingID == UId | _.ProductionID == UId | _.AuditingID == UId | _.PackID == UId;
        exp &= _.IsEnd == false & _.OrderingTime > "2000-1-1" & _.OrderingTime < DateTime.Now.AddHours(-IoTSetting.Current.Timeout);   // 订单超时
        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);

        return FindAll(exp, page);
    }

    /// <summary>高级查询 待领料</summary>
    /// <param name="UId">用户Id</param>
    /// <param name="key">关键字</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> SearchPicking(Int32 UId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (UId > 0) exp &= _.OrderingID == UId | _.PickingID == UId | _.ProductionID == UId | _.AuditingID == UId | _.PackID == UId;
        exp &= _.IsEnd == false & (_.PickingTime.IsNull());
        exp &= _.OrderingTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);

        exp &= (_.PackID == 0 & _.AuditingID == 0);
        exp &= _.Status == 0;

        return FindAll(exp, page);
    }

    /// <summary>高级查询 待生产</summary>
    /// <param name="UId">用户Id</param>
    /// <param name="key">关键字</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> SearchProduction(Int32 UId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (UId > 0) exp &= _.OrderingID == UId | _.PickingID == UId | _.ProductionID == UId | _.AuditingID == UId | _.PackID == UId;
        exp &= _.IsEnd == false & _.PickingTime.NotIsNull() & _.ProductionTime.IsNull() & _.AuditingTime.IsNull();
        exp &= _.OrderingTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);
        exp &= _.Status == 0;

        return FindAll(exp, page);
    }

    /// <summary>高级查询 生产中</summary>
    /// <param name="UId">用户Id</param>
    /// <param name="key">关键字</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> SearchInProduction(Int32 UId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (UId > 0) exp &= _.OrderingID == UId | _.PickingID == UId | _.ProductionID == UId | _.AuditingID == UId | _.PackID == UId;
        exp &= _.IsEnd == false & _.ProductionTime.NotIsNull() & _.AuditingTime.IsNull();
        exp &= _.OrderingTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);
        exp &= _.Status == 0;

        return FindAll(exp, page);
    }

    /// <summary>高级查询 待打包</summary>
    /// <param name="UId">用户Id</param>
    /// <param name="key">关键字</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> SearchPack(Int32 UId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (UId > 0) exp &= _.OrderingID == UId | _.PickingID == UId | _.ProductionID == UId | _.AuditingID == UId | _.PackID == UId;
        exp &= _.IsEnd == false & _.AuditingTime.NotIsNull() & _.PackTime.IsNull();
        exp &= _.OrderingTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);
        exp &= _.Status == 0;

        return FindAll(exp, page);
    }

    /// <summary>高级查询 综合</summary>
    /// <param name="UId">用户Id</param>
    /// <param name="key">关键字</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="progress">进度</param>
    /// <param name="status">状态</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <param name="hasRemark">是否有备注</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> Search(Int32 UId, DateTime start, DateTime end, String key, Int32 progress, Int32 status, Int16 hasRemark, PageParameter? page)
    {
        var exp = new WhereExpression();

        if (UId > 0) exp &= _.OrderingID == UId | _.PickingID == UId | _.ProductionID == UId | _.AuditingID == UId | _.PackID == UId;
        exp &= _.OrderingTime.Between(start, end);

        if (progress == 0)
        {
            exp &= _.IsEnd == false & (_.PickingTime.IsNull()) & _.PackID == 0 & _.AuditingID == 0;
        }
        else if (progress == 1)
        {
            exp &= _.IsEnd == false & _.PickingTime.NotIsNull() & _.ProductionTime.IsNull() & _.AuditingTime.IsNull();
        }
        else if (progress == 2)
        {
            exp &= _.IsEnd == false & _.ProductionTime.NotIsNull() & _.AuditingTime.IsNull();
        }
        else if (progress == 3)
        {
            exp &= _.IsEnd == false & _.AuditingTime.NotIsNull() & _.ShippingTime.IsNull() & _.PackTime.IsNull();
        }
        else if (progress == 4)
        {
            exp &= _.IsEnd == true;
        }
        else if (progress == 5)
        {
            exp &= _.IsEnd == false & _.OrderingTime > "2000-1-1" & _.OrderingTime < DateTime.Now.AddHours(-IoTSetting.Current.Timeout);   // 订单超时
        }
        else if (progress == 6)
        {
            exp &= _.IsEnd == false & _.ShippingTime.NotIsNull() & _.PackTime.IsNull();
        }

        if (status >= 0)
        {
            exp &= _.Status == status;
        }

        if (hasRemark >= 0)
        {
            exp &= _.HasRemark == (hasRemark == 1);
        }

        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);

        return FindAll(exp, page);
    }

    /// <summary>高级查询 已出库</summary>
    /// <param name="UId">用户Id</param>
    /// <param name="key">关键字</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrder> SearchShippedOutbound(Int32 UId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (UId > 0) exp &= _.OrderingID == UId | _.PickingID == UId | _.ProductionID == UId | _.AuditingID == UId | _.PackID == UId;
        exp &= _.IsEnd == true;
        exp &= _.OrderingTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);
        exp &= _.Status == 0;

        return FindAll(exp, page);
    }

    // Select Count(Id) as Id,Category From DH_WmsOrder Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<WmsOrder> _CategoryCache = new FieldCache<WmsOrder>(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public IWmsOrder ToModel()
    {
        var model = new WmsOrder();
        model.Copy(this);

        return model;
    }

    #endregion
}