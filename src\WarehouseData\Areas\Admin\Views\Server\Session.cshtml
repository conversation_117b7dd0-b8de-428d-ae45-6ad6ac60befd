﻿@using Microsoft.AspNetCore.Http
@inject IHttpContextAccessor HttpContextAccessor
@{
    Layout = null;

    ViewBag.Title = "Session列表";

    var session = HttpContextAccessor.HttpContext.Session;
    var session2 = HttpContextAccessor.HttpContext.Items["Session"] as IDictionary<String, Object>;
}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit|ie-stand">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width" />
    @*<link href="/Content/bootstrap.min.css" rel="stylesheet" />

    <script src="/Scripts/jquery1111.min.js"></script>
    <script src="/Scripts/bootstrap.min.js"></script>*@

    <style>
        .container .table > tbody > tr > td {
            padding: 5px 5px;
        }

        .container .table {
            margin: 0;
            background-color: #fff;
            color: #494e52;
            font-size: 13px;
        }

        .table-bordered {
            border: 1px solid #ddd;
        }

        .table > tbody > tr > td {
            border-top: 0;
            border-bottom: 1px solid #e7e7e7;
        }

        .container .table > thead > tr > th, .table > tbody > tr > td {
            vertical-align: middle;
        }

        .table-bordered > tbody > tr > td, .table-bordered > tbody > tr > th, .table-bordered > tfoot > tr > td, .table-bordered > tfoot > tr > th, .table-bordered > thead > tr > td, .table-bordered > thead > tr > th {
            border: 1px solid #ddd;
        }

        .container .table thead tr th {
            border: 0;
            height: 45px;
            font-weight: 900 !important;
        }

        .table thead th:first-child {
            border-top-left-radius: 5px;
        }

        table {
            border-spacing: 0;
            border-collapse: collapse;
        }

        .table#list, .table.category_table, .table#applyList, .table#HandSlideDatagrid, .table#typeDatagrid, .table#shopDatagrid, .table#topicGrid, .table#listAutoReplay, .table#productList {
            margin-top: 5px;
            margin-bottom: 60px;
        }

        .table {
            width: 100%;
            max-width: 100%;
            margin-bottom: 20px;
        }

            .table thead {
                background-color: #f9f9f9;
                border-bottom: 1px solid #e7e7e7;
                border-top-left-radius: 15px !important;
                border-top-right-radius: 15px !important;
                color: #494e52;
                font-size: 14px;
            }
    </style>
</head>
<body>
    <div class="container" style="width:100%;">
        <div>
            <table class="table table-bordered table-hover table-striped table-condensed">
                <tr>
                    <th colspan="6">
                        @T("Session列表") (@session.Keys.Count())
                    </th>
                </tr>
                <tr>
                    <th>@T("名称")</th>
                    <th>@T("类型")</th>
                    <th>@T("数值")</th>
                </tr>
                @foreach (String item in session.Keys)
                {
                    <tr>
                        <td>@item</td>
                        <td></td>
                        <td>@session.GetString(item)</td>
                    </tr>
                }
            </table>
            @if (session2 != null)
            {
                <table class="table table-bordered table-hover table-striped table-condensed">
                    <tr>
                        <th colspan="6">
                            @T("Session2列表") (@session2.Count)
                        </th>
                    </tr>
                    <tr>
                        <th>@T("名称")</th>
                        <th>@T("类型")</th>
                        <th>@T("数值")</th>
                    </tr>
                    @foreach (var item in session2)
                    {
                        <tr>
                            <td>@item.Key</td>
                            <td>@item.Value?.GetType().FullName</td>
                            <td>@item.Value</td>
                        </tr>
                    }
                </table>
            }
        </div>
    </div>
</body>
</html>