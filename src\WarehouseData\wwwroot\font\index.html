<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出货智慧大屏可视化</title>
    <!-- 引入layui -->
    <link href="./node_modules/layui/dist/css/layui.css" rel="stylesheet">
    <link href="./css/index.css" rel="stylesheet">
</head>
<style>
    
</style>
<body>
    <div id="app">
        <div class="title">
            <!-- 物料/出货智慧大屏可视化 -->
            <div class="searchBox">
                <input type="text" name="Key" placeholder="请输入关键词" class="layui-input transparent">
                <button type="button" class="layui-btn transparent"> 
                    <img src="./Images/search_icon.png" alt="搜索" style="height: 70%;object-fit: contain;">
                </button>
            </div>
        </div>
        <!-- 左侧 -->
        <div class="boxContent">
            <div class="item_left">
                <div class="item_left_box" data-index="1">
                    <div class="leftTile">已打号</div>
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <table class="layui-hide" id="left_table1" lay-filter="tool"></table>
                        </div>
                    </div>
                </div>
                <div class="item_left_box" data-index="2">
                    <div class="leftTile">已领料</div>
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <table class="layui-hide" id="left_table2" lay-filter="tool"></table>
                        </div>
                    </div>
                </div>
                <div class="item_left_box" data-index="3">
                    <div class="leftTile">已入库</div>
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <table class="layui-hide" id="left_table3" lay-filter="tool"></table>
                        </div>
                    </div>
                </div>  
                <div class="item_left_box" data-index="4">
                    <div class="leftTile">已打包</div>
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <table class="layui-hide" id="left_table4" lay-filter="tool"></table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 中间 -->
            <div class="item_center">
                <div class="item_center_title">
                    <span class="item_center_title_span" onclick="selectOption(1,this)" data-selected="true">所有产品 \</span>
                    <span class="item_center_title_span" onclick="selectOption(1,this)">未发货 \</span> 
                    <span class="item_center_title_span" onclick="selectOption(1,this)">完成订单</span></div>
                <div style="margin-top: 40px;"></div>
                <div class="layui-card">
                    <div class="layui-card-body">
                        <table class="layui-hide" id="centerTable" lay-filter="tool"></table>
                    </div>
                </div>
            </div>
            <!-- 右边 -->
            <div class="item_right">
                <!-- 折线图 -->
                <div id="lineBox">
                    <div class="rightTile">物料/出货统计</div>
                    <div id="line"></div>
                </div>
                <!-- 饼图 -->
                <div id="pieBox">
                    <div class="rightTile">物料统计</div>
                    <div id="pie"></div>
                </div>
                <!-- 统计图 -->
                <div id="barBox">
                    <div class="rightTile">今日数据统计</div>
                    <div id="bar"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页面js -->
    <script>
        // 选择-所有产品/未发货/完成订单
        const selectOption = (index,dom)=>{
            layui.$(dom).attr('data-selected',true).siblings().attr('data-selected',false)
        }
    </script>
</body>
<script src="./node_modules/layui/dist/layui.js"></script>
<script src="./node_modules/echarts/echarts.js"></script>
<script src="./node_modules/echarts/echarts.js"></script>
<script type="module">
 
    // 导入中间部分的table options
    import centerTableOptions from './tables/centerTable.js';
    import left_table1_options from './tables/left_table1.js';
    import left_table2_options from './tables/left_table2.js';
    import left_table3_options from './tables/left_table3.js';
    import left_table4_options from './tables/left_table4.js';
    // 渲染table
    let centerTable = layui.table.render(centerTableOptions)
    let left_table1 = layui.table.render(left_table1_options)
    let left_table2 = layui.table.render(left_table2_options)
    let left_table3 = layui.table.render(left_table3_options)
    let left_table4 = layui.table.render(left_table4_options)

    // 初始化ECharts实例
    var barChart = echarts.init(document.getElementById('bar'));
    var pieChart = echarts.init(document.getElementById('pie'));
    var lineChart = echarts.init(document.getElementById('line'));
    // 导入echarts配置图表参数 options
    import pieChartData from './echarts/pieChart.js';
    import lineChartData from './echarts/lineChart.js';
    import barChartData from './echarts/barChart.js';
    //  图像渲染
    lineChart.setOption(lineChartData);   
    pieChart.setOption(pieChartData);
    barChart.setOption(barChartData);

    window.addEventListener('resize', function() {
        lineChart.resize();
        pieChart.resize();
        barChart.resize();
    });

</script>
</html>