﻿using DG.Web.Framework;

using DH.Entity;

using HlktechIoT.Common;
using HlktechIoT.Entity;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;
using NewLife.Log;

using Pek;
using Pek.Helpers;
using Pek.Models;
using Pek.MVC;
using Pek.Swagger;
using Pek.Timing;

namespace HlktechIoT.Controllers.Api;

/// <summary>
/// 订单管理
/// </summary>
[Produces("application/json")]
[CustomRoute(ApiVersions.V1)]
[Authorize("jwt")]
public class OrderController : ApiControllerBaseX {

    /// <summary>
    /// 订单查询
    /// </summary>
    /// <param name="OrderId">订单编号</param>
    /// <param name="Id">请求标识，建议用时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("Search")]
    //[ApiSignature]
    public IActionResult Search([FromHeader] string Lng, [FromHeader] string Id, [FromForm] String OrderId)
    {
        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        if (OrderId.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("订单号为空", Lng);
            return result;
        }
        OrderId = OrderId.SafeString().Trim();

        using var split = WmsOrder.Meta.CreateSplit("DH", $"DH_WmsOrder_{OrderId.Mid(2, 2)}");
        var model = WmsOrder.FindByOrderId(OrderId);
        if (model == null)
        {
            result.Message = GetResource("订单不存在", Lng);
            return result;
        }

        result.Data = new { model.Id, model.OrderId, model.OrderingID, OrderingUser = model.OrderingUser?.DisplayName, model.OrderingTime, model.PickingID, PickingUser = model.PickingUser?.DisplayName, model.PickingTime, model.ProductionID, ProductionUser = model.ProductionUser?.DisplayName, model.ProductionTime, model.AuditingID, AuditingUser = model.AuditingUser?.DisplayName, model.AuditingTime, model.PackID, PackUser = model.PackUser?.DisplayName, model.PackTime };
        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 制造单查询
    /// </summary>
    /// <param name="OrderId">制造单编号</param>
    /// <param name="Id">请求标识，建议用时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("SearchMD")]
    //[ApiSignature]
    public IActionResult SearchMD([FromHeader] string Lng, [FromHeader] string Id, [FromForm] String OrderId)
    {
        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        if (OrderId.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("订单号为空", Lng);
            return result;
        }
        OrderId = OrderId.SafeString().Trim();

        using var split = WmsPickingOrder.Meta.CreateSplit("DH", $"DH_WmsPickingOrder_{OrderId.Mid(2, 2)}");
        var model = WmsPickingOrder.FindByOrderId(OrderId);
        if (model == null)
        {
            result.Message = GetResource("订单不存在", Lng);
            return result;
        }

        result.Data = new { model.Id, model.OrderId, model.OrderingID, OrderingUser = model.OrderingUser?.DisplayName, model.OrderingTime, model.PickingID, PickingUser = model.PickingUser?.DisplayName, model.PickingTime, model.ProductionID, ProductionUser = model.ProductionUser?.DisplayName, model.ProductionTime, model.AuditingID, AuditingUser = model.AuditingUser?.DisplayName, model.AuditingTime, model.PackID, PackUser = model.PackUser?.DisplayName, model.PackTime };
        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 订单大屏数据
    /// </summary> 
    /// <param name="Id">请求标识，建议用时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <returns></returns>
    [HttpPost("GetData")]
    //[ApiSignature]
    public IActionResult GetData([FromHeader] string Lng, [FromHeader] string Id)
    {
        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        var count = WmsOrder.FindAllCount();  // 总的订单数量
        var outboundCount = WmsOrder.FindAllOutboundCount();  // 总的已出库订单数量

        var list = WmsOrder.FindAllByNoFinish();  // 获取所有没有完成的订单数据

        var TimeoutCount = list.Count(e => e.OrderingTime < DateTime.Now.AddHours(-IoTSetting.Current.Timeout));  // 订单超时数量

        // 已打单
        var OrderingList = list.Where(e => e.PickingTime <= DateTime.MinValue && (e.ProductionID == 0 && e.AuditingID == 0)).Select(e => new { e.OrderId, User = e.OrderingUser?.DisplayName, ProcessTime = e.OrderingTime, ManHour = DateTimeUtil.BusinessDateFormat(e.OrderingTime) });

        // 已领料
        var PickingList = list.Where(e => e.PickingTime > DateTime.MinValue && e.ProductionTime <= DateTime.MinValue && e.AuditingTime <= DateTime.MinValue).Select(e => new { e.OrderId, User = e.PickingUser?.DisplayName, ProcessTime = e.PickingTime, ManHour = DateTimeUtil.BusinessDateFormat(e.PickingTime) });

        // 生产中
        var ProductionList = list.Where(e => e.ProductionTime > DateTime.MinValue && e.AuditingTime <= DateTime.MinValue).Select(e => new { e.OrderId, User = e.ProductionUser?.DisplayName, ProcessTime = e.ProductionTime, ManHour = DateTimeUtil.BusinessDateFormat(e.ProductionTime) });

        // 待打包
        var PackList = list.Where(e => e.PackTime <= DateTime.MinValue && e.AuditingTime > DateTime.MinValue).Select(e => new { e.OrderId, ManHour = DateTimeUtil.BusinessDateFormat(e.OrderingTime) });

        // 所有未完成订单
        var NoList = list.OrderBy(e => e.OrderingTime).Select(e =>
        {
            var status = String.Empty;
            var processUser = String.Empty;
            var processTime = DateTime.Now;

            if (e.PickingTime <= DateTime.MinValue)
            {
                status = GetResource("已打单");
                processUser = e.OrderingUser?.DisplayName;
                processTime = e.OrderingTime;
            }
            else if (e.PickingTime > DateTime.MinValue && e.ProductionTime <= DateTime.MinValue && e.AuditingTime <= DateTime.MinValue)
            {
                status = GetResource("已领料");
                processUser = e.PickingUser?.DisplayName;
                processTime = e.PickingTime;
            }
            else if (e.ProductionTime > DateTime.MinValue && e.AuditingTime <= DateTime.MinValue)
            {
                status = GetResource("生产中");
                processUser = e.ProductionUser?.DisplayName;
                processTime = e.ProductionTime;
            }
            else if (e.PackTime <= DateTime.MinValue && e.AuditingTime > DateTime.MinValue)
            {
                status = GetResource("生产审核");
                processUser = e.AuditingUser?.DisplayName;
                processTime = e.AuditingTime;
            }

            return new
            {
                e.OrderId,
                ManHour = DateTimeUtil.BusinessDateFormat(e.OrderingTime),
                Status = status,
                ProcessUser = processUser,
                ProcessTime = processTime,
            };
        });

        var waitPicking = OrderingList.Count();  // 待领料数量
        var waitProduction = PickingList.Count();  // 待生产数量
        var inProduction = ProductionList.Count();    // 生产中数量
        var waitPack = PackList.Count();  // 待打包数量

        #region 今天数据统计
        var todayCount = WmsOrder.FindTodayCount();  // 今天所有的订单数量
        var todayoutboundCount = WmsOrder.FindTodayOutboundCount();  // 今天所有的已出库订单数量
        var today = list.Where(e => e.OrderingTime >= DateTime.Now.Date);  // 获取今天没有完成的订单数据

        // 已打单
        var OrderingList1 = today.Where(e => e.PickingTime <= DateTime.MinValue && (e.ProductionID == 0 && e.AuditingID == 0)).Select(e => new { e.OrderId, User = e.OrderingUser?.DisplayName, ProcessTime = e.OrderingTime, ManHour = DateTimeUtil.BusinessDateFormat(e.OrderingTime) });

        // 已领料
        var PickingList1 = today.Where(e => e.PickingTime > DateTime.MinValue && e.ProductionTime <= DateTime.MinValue && e.AuditingTime <= DateTime.MinValue).Select(e => new { e.OrderId, User = e.PickingUser?.DisplayName, ProcessTime = e.PickingTime, ManHour = DateTimeUtil.BusinessDateFormat(e.PickingTime) });

        // 生产中
        var ProductionList1 = today.Where(e => e.ProductionTime > DateTime.MinValue && e.AuditingTime <= DateTime.MinValue).Select(e => new { e.OrderId, User = e.ProductionUser?.DisplayName, ProcessTime = e.ProductionTime, ManHour = DateTimeUtil.BusinessDateFormat(e.ProductionTime) });

        // 待打包
        var PackList1 = today.Where(e => e.PackTime <= DateTime.MinValue && e.AuditingTime > DateTime.MinValue).Select(e => new { e.OrderId, ManHour = DateTimeUtil.BusinessDateFormat(e.OrderingTime) });

        // 所有未完成订单
        var NoList1 = today.OrderBy(e => e.OrderingTime).Select(e =>
        {
            var status = String.Empty;
            var processUser = String.Empty;
            var processTime = DateTime.Now;

            if (e.PickingTime <= DateTime.MinValue)
            {
                status = GetResource("已打单");
                processUser = e.OrderingUser?.DisplayName;
                processTime = e.OrderingTime;
            }
            else if (e.PickingTime > DateTime.MinValue && e.ProductionTime <= DateTime.MinValue && e.AuditingTime <= DateTime.MinValue)
            {
                status = GetResource("已领料");
                processUser = e.PickingUser?.DisplayName;
                processTime = e.PickingTime;
            }
            else if (e.ProductionTime > DateTime.MinValue && e.AuditingTime <= DateTime.MinValue)
            {
                status = GetResource("生产中");
                processUser = e.ProductionUser?.DisplayName;
                processTime = e.ProductionTime;
            }
            else if (e.PackTime <= DateTime.MinValue && e.AuditingTime > DateTime.MinValue)
            {
                status = GetResource("生产审核");
                processUser = e.AuditingUser?.DisplayName;
                processTime = e.AuditingTime;
            }

            return new
            {
                e.OrderId,
                ManHour = DateTimeUtil.BusinessDateFormat(e.OrderingTime),
                Status = status,
                ProcessUser = processUser,
                ProcessTime = processTime,
            };
        });

        var todayWaitPicking = OrderingList1.Count();  // 待领料数量
        var todayWaitProduction = PickingList1.Count();  // 待生产数量
        var todayInProduction = ProductionList1.Count();    // 生产中数量
        var todayWaitPack = PackList1.Count();  // 待打包数量
        #endregion

        // 制造单领料订单
        var pickingOrderCount= WmsPickingOrder.FindAllCount();//总订单数量
        var pickingOrderIntoCount = WmsPickingOrder.FindAllIntoboundCount();  // 总入库订单数量
        var pickingOrderlist = WmsPickingOrder.FindAllByNoFinish();  // 获取所有没有完成的订单数据
        var pickingOrderTimeOutCount = pickingOrderlist.Count(e => e.OrderingTime < DateTime.Now.AddHours(-IoTSetting.Current.POTimeout));//总超时订单数量
        // 已打单
        var pOrderingList = pickingOrderlist.Where(e => e.PickingTime <= DateTime.MinValue && (e.ProductionID == 0 && e.AuditingID == 0)).Select(e => new { e.OrderId, User = e.OrderingUser?.DisplayName, ProcessTime = e.OrderingTime, ManHour = DateTimeUtil.BusinessDateFormat(e.OrderingTime) });

        // 已领料
        var pPickingList = pickingOrderlist.Where(e => e.PickingTime > DateTime.MinValue && e.ProductionTime <= DateTime.MinValue && e.AuditingTime <= DateTime.MinValue).Select(e => new { e.OrderId, User = e.PickingUser?.DisplayName, ProcessTime = e.PickingTime, ManHour = DateTimeUtil.BusinessDateFormat(e.PickingTime) });

        // 生产中
        var pProductionList = pickingOrderlist.Where(e => e.ProductionTime > DateTime.MinValue && e.AuditingTime <= DateTime.MinValue).Select(e => new { e.OrderId, User = e.ProductionUser?.DisplayName, ProcessTime = e.ProductionTime, ManHour = DateTimeUtil.BusinessDateFormat(e.ProductionTime) });

        var pWaitPicking = pOrderingList.Count();  // 待领料数量
        var pWaitProduction = pPickingList.Count();  // 待生产数量
        var pInProduction = pProductionList.Count();    // 生产中数量
        var pIntosStorage = pickingOrderlist.Count(e => e.AuditingTime > DateTime.MinValue && e.PackTime <= DateTime.MinValue); //待入库数量

        //今日数据统计
        var pTodayCount = WmsPickingOrder.FindTodayCount();  // 今天所有的订单数量
        var pTodayIntoCount = WmsPickingOrder.FindTodayIntoboundCount();  // 今天所有的已入库订单数量
        var pToday = pickingOrderlist.Where(e => e.OrderingTime >= DateTime.Now.Date);  // 获取今天没有完成的订单数据

        var pTodayWaitPicking = pToday.Count(e => e.PickingTime <= DateTime.MinValue && (e.ProductionID == 0 && e.AuditingID == 0));  // 待领料数量
        var pTodayWaitProduction = pToday.Count(e => e.PickingTime > DateTime.MinValue && e.ProductionTime <= DateTime.MinValue && e.AuditingTime <= DateTime.MinValue);  // 待生产数量
        var pTodayInProduction = pToday.Count(e => e.ProductionTime > DateTime.MinValue && e.AuditingTime <= DateTime.MinValue);    // 生产中数量
        var pTodayIntosStorage = pToday.Count(e => e.AuditingTime > DateTime.MinValue && e.PackTime <= DateTime.MinValue); //待入库数量

        var PickingOrder = new
        {
            All = new { pickingOrderCount, pickingOrderTimeOutCount, pWaitPicking, pWaitProduction, pInProduction, pIntosStorage, pickingOrderIntoCount },
            Today = new { pTodayCount, pTodayWaitPicking, pTodayWaitProduction , pTodayInProduction , pTodayIntosStorage , pTodayIntoCount },
        };

        result.Data = new { OrderingList, PickingList, ProductionList, PackList, NoList, WaitPicking = waitPicking, WaitProduction = waitProduction, InProduction = inProduction, WaitPack = waitPack, AllCount = count, OutboundCount = outboundCount, TodayCount = todayCount, TodayWaitPicking = todayWaitPicking, TodayWaitProduction = todayWaitProduction, TodayInProduction = todayInProduction, TodayWaitPack = todayWaitPack, TimeoutCount, TodayoutboundCount = todayoutboundCount, PickingOrder };
        result.Code = StateCode.Ok;
        return result;
    }

    /// <summary>
    /// 全部订单
    /// </summary>
    /// <param name="key">订单号等</param>
    /// <param name="Id">请求标识，建议用时间戳</param>
    /// <param name="UId">搜索用户Id</param>
    /// <param name="Lng">语言标识符</param>
    /// <param name="limit">分页数量</param>
    /// <param name="page">页码</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <returns></returns>
    [HttpPost("SearchAll")]
    //[ApiSignature]
    public IActionResult SearchAll([FromHeader] string Lng, [FromHeader] string Id, [FromForm] DateTime start, [FromForm] DateTime end, [FromForm] String key, [FromForm] Int32 UId, [FromForm] Int32 page = 1, [FromForm] Int32 limit = 10)
    {
        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsOrder._.OrderingTime,
            Desc = true,
        };

        if (start <= DateTime.MinValue)
        {
            start = "2000-01-01".ToDateTime();
        }

        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now.ToShortDateString().ToDateTime();
        }

        XTrace.WriteLine($"获取到的时间：{start}:{end}");

        //start = "2023-12-31 00:00:00".ToDGDate();
        //end = "2024-07-25 00:00:00".ToDGDate();

        key = key.SafeString().Trim();
        var list = WmsOrder.Search(UId, start, end, key, pages);
        var data = list.Select(x => new { x.Id, x.OrderId, OrderingUser = x.OrderingUser?.DisplayName, x.OrderingTime, PickingUser = x.PickingUser?.DisplayName, x.PickingTime, ProductionUser = x.ProductionUser?.DisplayName, x.ProductionTime, AuditingUser = x.AuditingUser?.DisplayName, x.AuditingTime, PackUser = x.PackUser?.DisplayName, x.PackTime, IsDuplicate = x.IsDuplicate ? GetResource("是") : GetResource("否") });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 全部订单-入库
    /// </summary>
    /// <param name="key">订单号等</param>
    /// <param name="Id">请求标识，建议用时间戳</param>
    /// <param name="UId">搜索用户Id</param>
    /// <param name="Lng">语言标识符</param>
    /// <param name="limit">分页数量</param>
    /// <param name="page">页码</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <returns></returns>
    [HttpPost("PSearchAll")]
    //[ApiSignature]
    public IActionResult PSearchAll([FromHeader] string Lng, [FromHeader] string Id, [FromForm] DateTime start, [FromForm] DateTime end, [FromForm] String key, [FromForm] Int32 UId, [FromForm] Int32 page = 1, [FromForm] Int32 limit = 10)
    {
        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsPickingOrder._.OrderingTime,
            Desc = true,
        };

        if (start <= DateTime.MinValue)
        {
            start = "2000-01-01".ToDateTime();
        }

        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now.ToShortDateString().ToDateTime();
        }

        XTrace.WriteLine($"获取到的时间：{start}:{end}");

        //start = "2023-12-31 00:00:00".ToDGDate();
        //end = "2024-07-25 00:00:00".ToDGDate();

        key = key.SafeString().Trim();
        var list = WmsPickingOrder.Search(UId, start, end, key, pages);
        var data = list.Select(x => new { x.Id, x.OrderId, OrderingUser = x.OrderingUser?.DisplayName, x.OrderingTime, PickingUser = x.PickingUser?.DisplayName, x.PickingTime, ProductionUser = x.ProductionUser?.DisplayName, x.ProductionTime, AuditingUser = x.AuditingUser?.DisplayName, x.AuditingTime, PackUser = x.PackUser?.DisplayName, x.PackTime, IsDuplicate = x.IsDuplicate ? GetResource("是") : GetResource("否") });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 超时订单
    /// </summary>
    /// <param name="key">订单号等</param>
    /// <param name="Id">请求标识，建议用时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <param name="limit">分页数量</param>
    /// <param name="page">页码</param>
    /// <returns></returns>
    [HttpPost("SearchTimeout")]
    //[ApiSignature]
    public IActionResult SearchTimeout([FromHeader] string Lng, [FromHeader] string Id, [FromForm] String key, [FromForm] Int32 page = 1, [FromForm] Int32 limit = 10)
    {
        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsOrder._.OrderingTime,
            Desc = false,
        };

        key = key.SafeString().Trim();
        var list = WmsOrder.Search(0, key, pages);
        var data = list.Select(x => new { x.Id, x.OrderId, OrderingUser = x.OrderingUser?.DisplayName, x.OrderingTime, PickingUser = x.PickingUser?.DisplayName, x.PickingTime, ProductionUser = x.ProductionUser?.DisplayName, x.ProductionTime, AuditingUser = x.AuditingUser?.DisplayName, x.AuditingTime, PackUser = x.PackUser?.DisplayName, x.PackTime, IsDuplicate = x.IsDuplicate ? GetResource("是") : GetResource("否"), x.HasRemark, x.Remark, x.RemarkTime });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 超时订单-入库
    /// </summary>
    /// <param name="key">订单号等</param>
    /// <param name="Id">请求标识，建议用时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <param name="limit">分页数量</param>
    /// <param name="page">页码</param>
    /// <returns></returns>
    [HttpPost("PSearchTimeout")]
    //[ApiSignature]
    public IActionResult PSearchTimeout([FromHeader] string Lng, [FromHeader] string Id, [FromForm] String key, [FromForm] Int32 page = 1, [FromForm] Int32 limit = 10)
    {
        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsPickingOrder._.OrderingTime,
            Desc = false,
        };

        key = key.SafeString().Trim();
        var list = WmsPickingOrder.Search(0, key, pages);
        var data = list.Select(x => new { x.Id, x.OrderId, OrderingUser = x.OrderingUser?.DisplayName, x.OrderingTime, PickingUser = x.PickingUser?.DisplayName, x.PickingTime, ProductionUser = x.ProductionUser?.DisplayName, x.ProductionTime, AuditingUser = x.AuditingUser?.DisplayName, x.AuditingTime, PackUser = x.PackUser?.DisplayName, x.PackTime, IsDuplicate = x.IsDuplicate ? GetResource("是") : GetResource("否"), x.HasRemark, x.Remark, x.RemarkTime });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 待领料订单
    /// </summary>
    /// <param name="key">订单号等</param>
    /// <param name="Id">请求标识，建议用时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <param name="limit">分页数量</param>
    /// <param name="page">页码</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <returns></returns>
    [HttpPost("ToBePicked")]
    //[ApiSignature]
    public IActionResult ToBePicked([FromHeader] string Lng, [FromHeader] string Id, [FromForm] String key, [FromForm] DateTime start, [FromForm] DateTime end, [FromForm] Int32 page = 1, [FromForm] Int32 limit = 10)
    {
        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsOrder._.OrderingTime,
            Desc = false,
        };

        if (start <= DateTime.MinValue)
        {
            start = "2000-01-01".ToDateTime();
        }

        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now.ToShortDateString().ToDateTime();
        }

        key = key.SafeString().Trim();
        var list = WmsOrder.SearchPicking(0, start, end, key, pages);
        var data = list.Select(x => new { x.Id, x.OrderId, OrderingUser = x.OrderingUser?.DisplayName, x.OrderingTime, PickingUser = x.PickingUser?.DisplayName, x.PickingTime, ProductionUser = x.ProductionUser?.DisplayName, x.ProductionTime, AuditingUser = x.AuditingUser?.DisplayName, x.AuditingTime, PackUser = x.PackUser?.DisplayName, x.PackTime, IsDuplicate = x.IsDuplicate ? GetResource("是") : GetResource("否") });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 待领料订单-入库
    /// </summary>
    /// <param name="key">订单号等</param>
    /// <param name="Id">请求标识，建议用时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <param name="limit">分页数量</param>
    /// <param name="page">页码</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <returns></returns>
    [HttpPost("PToBePicked")]
    //[ApiSignature]
    public IActionResult PToBePicked([FromHeader] string Lng, [FromHeader] string Id, [FromForm] String key, [FromForm] DateTime start, [FromForm] DateTime end, [FromForm] Int32 page = 1, [FromForm] Int32 limit = 10)
    {
        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsPickingOrder._.OrderingTime,
            Desc = false,
        };

        if (start <= DateTime.MinValue)
        {
            start = "2000-01-01".ToDateTime();
        }

        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now.ToShortDateString().ToDateTime();
        }

        key = key.SafeString().Trim();
        var list = WmsPickingOrder.SearchPicking(0, start, end, key, pages);
        var data = list.Select(x => new { x.Id, x.OrderId, OrderingUser = x.OrderingUser?.DisplayName, x.OrderingTime, PickingUser = x.PickingUser?.DisplayName, x.PickingTime, ProductionUser = x.ProductionUser?.DisplayName, x.ProductionTime, AuditingUser = x.AuditingUser?.DisplayName, x.AuditingTime, PackUser = x.PackUser?.DisplayName, x.PackTime, IsDuplicate = x.IsDuplicate ? GetResource("是") : GetResource("否") });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 待生产订单
    /// </summary>
    /// <param name="key">订单号等</param>
    /// <param name="Id">请求标识，建议用时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <param name="limit">分页数量</param>
    /// <param name="page">页码</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <returns></returns>
    [HttpPost("ToBeProduced")]
    //[ApiSignature]
    public IActionResult ToBeProduced([FromHeader] string Lng, [FromHeader] string Id, [FromForm] String key, [FromForm] DateTime start, [FromForm] DateTime end, [FromForm] Int32 page = 1, [FromForm] Int32 limit = 10)
    {
        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsOrder._.OrderingTime,
            Desc = false,
        };

        if (start <= DateTime.MinValue)
        {
            start = "2000-01-01".ToDateTime();
        }

        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now.ToShortDateString().ToDateTime();
        }

        key = key.SafeString().Trim();
        var list = WmsOrder.SearchProduction(0, start, end, key, pages);
        var data = list.Select(x => new { x.Id, x.OrderId, OrderingUser = x.OrderingUser?.DisplayName, x.OrderingTime, PickingUser = x.PickingUser?.DisplayName, x.PickingTime, ProductionUser = x.ProductionUser?.DisplayName, x.ProductionTime, AuditingUser = x.AuditingUser?.DisplayName, x.AuditingTime, PackUser = x.PackUser?.DisplayName, x.PackTime, IsDuplicate = x.IsDuplicate ? GetResource("是") : GetResource("否") });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 待生产订单-入库
    /// </summary>
    /// <param name="key">订单号等</param>
    /// <param name="Id">请求标识，建议用时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <param name="limit">分页数量</param>
    /// <param name="page">页码</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <returns></returns>
    [HttpPost("PToBeProduced")]
    //[ApiSignature]
    public IActionResult PToBeProduced([FromHeader] string Lng, [FromHeader] string Id, [FromForm] String key, [FromForm] DateTime start, [FromForm] DateTime end, [FromForm] Int32 page = 1, [FromForm] Int32 limit = 10)
    {
        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsPickingOrder._.OrderingTime,
            Desc = false,
        };

        if (start <= DateTime.MinValue)
        {
            start = "2000-01-01".ToDateTime();
        }

        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now.ToShortDateString().ToDateTime();
        }

        key = key.SafeString().Trim();
        var list = WmsPickingOrder.SearchProduction(0, start, end, key, pages);
        var data = list.Select(x => new { x.Id, x.OrderId, OrderingUser = x.OrderingUser?.DisplayName, x.OrderingTime, PickingUser = x.PickingUser?.DisplayName, x.PickingTime, ProductionUser = x.ProductionUser?.DisplayName, x.ProductionTime, AuditingUser = x.AuditingUser?.DisplayName, x.AuditingTime, PackUser = x.PackUser?.DisplayName, x.PackTime, IsDuplicate = x.IsDuplicate ? GetResource("是") : GetResource("否") });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 生产中订单
    /// </summary>
    /// <param name="key">订单号等</param>
    /// <param name="Id">请求标识，建议用时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <param name="limit">分页数量</param>
    /// <param name="page">页码</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <returns></returns>
    [HttpPost("InProduction")]
    //[ApiSignature]
    public IActionResult InProduction([FromHeader] string Lng, [FromHeader] string Id, [FromForm] String key, [FromForm] DateTime start, [FromForm] DateTime end, [FromForm] Int32 page = 1, [FromForm] Int32 limit = 10)
    {
        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsOrder._.OrderingTime,
            Desc = false,
        };

        if (start <= DateTime.MinValue)
        {
            start = "2000-01-01".ToDateTime();
        }

        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now.ToShortDateString().ToDateTime();
        }

        key = key.SafeString().Trim();
        var list = WmsOrder.SearchInProduction(0, start, end, key, pages);
        var data = list.Select(x => new { x.Id, x.OrderId, OrderingUser = x.OrderingUser?.DisplayName, x.OrderingTime, PickingUser = x.PickingUser?.DisplayName, x.PickingTime, ProductionUser = x.ProductionUser?.DisplayName, x.ProductionTime, AuditingUser = x.AuditingUser?.DisplayName, x.AuditingTime, PackUser = x.PackUser?.DisplayName, x.PackTime, IsDuplicate = x.IsDuplicate ? GetResource("是") : GetResource("否") });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 生产中订单-入库
    /// </summary>
    /// <param name="key">订单号等</param>
    /// <param name="Id">请求标识，建议用时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <param name="limit">分页数量</param>
    /// <param name="page">页码</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <returns></returns>
    [HttpPost("PInProduction")]
    //[ApiSignature]
    public IActionResult PInProduction([FromHeader] string Lng, [FromHeader] string Id, [FromForm] String key, [FromForm] DateTime start, [FromForm] DateTime end, [FromForm] Int32 page = 1, [FromForm] Int32 limit = 10)
    {
        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsPickingOrder._.OrderingTime,
            Desc = false,
        };

        if (start <= DateTime.MinValue)
        {
            start = "2000-01-01".ToDateTime();
        }

        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now.ToShortDateString().ToDateTime();
        }

        key = key.SafeString().Trim();
        var list = WmsPickingOrder.SearchInProduction(0, start, end, key, pages);
        var data = list.Select(x => new { x.Id, x.OrderId, OrderingUser = x.OrderingUser?.DisplayName, x.OrderingTime, PickingUser = x.PickingUser?.DisplayName, x.PickingTime, ProductionUser = x.ProductionUser?.DisplayName, x.ProductionTime, AuditingUser = x.AuditingUser?.DisplayName, x.AuditingTime, PackUser = x.PackUser?.DisplayName, x.PackTime, IsDuplicate = x.IsDuplicate ? GetResource("是") : GetResource("否") });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 待打包订单
    /// </summary>
    /// <param name="key">订单号等</param>
    /// <param name="Id">请求标识，建议用时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <param name="limit">分页数量</param>
    /// <param name="page">页码</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <returns></returns>
    [HttpPost("ToBePacked")]
    //[ApiSignature]
    public IActionResult ToBePacked([FromHeader] string Lng, [FromHeader] string Id, [FromForm] String key, [FromForm] DateTime start, [FromForm] DateTime end, [FromForm] Int32 page = 1, [FromForm] Int32 limit = 10)
    {
        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsOrder._.OrderingTime,
            Desc = false,
        };

        if (start <= DateTime.MinValue)
        {
            start = "2000-01-01".ToDateTime();
        }

        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now.ToShortDateString().ToDateTime();
        }

        key = key.SafeString().Trim();
        var list = WmsOrder.SearchPack(0, start, end, key, pages);
        var data = list.Select(x => new { x.Id, x.OrderId, OrderingUser = x.OrderingUser?.DisplayName, x.OrderingTime, PickingUser = x.PickingUser?.DisplayName, x.PickingTime, ProductionUser = x.ProductionUser?.DisplayName, x.ProductionTime, AuditingUser = x.AuditingUser?.DisplayName, x.AuditingTime, PackUser = x.PackUser?.DisplayName, x.PackTime, IsDuplicate = x.IsDuplicate ? GetResource("是") : GetResource("否") });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 待入库订单
    /// </summary>
    /// <param name="key">订单号等</param>
    /// <param name="Id">请求标识，建议用时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <param name="limit">分页数量</param>
    /// <param name="page">页码</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <returns></returns>
    [HttpPost("PToBePacked")]
    //[ApiSignature]
    public IActionResult PToBePacked([FromHeader] string Lng, [FromHeader] string Id, [FromForm] String key, [FromForm] DateTime start, [FromForm] DateTime end, [FromForm] Int32 page = 1, [FromForm] Int32 limit = 10)
    {
        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsPickingOrder._.OrderingTime,
            Desc = false,
        };

        if (start <= DateTime.MinValue)
        {
            start = "2000-01-01".ToDateTime();
        }

        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now.ToShortDateString().ToDateTime();
        }

        key = key.SafeString().Trim();
        var list = WmsPickingOrder.SearchPack(0, start, end, key, pages);
        var data = list.Select(x => new { x.Id, x.OrderId, OrderingUser = x.OrderingUser?.DisplayName, x.OrderingTime, PickingUser = x.PickingUser?.DisplayName, x.PickingTime, ProductionUser = x.ProductionUser?.DisplayName, x.ProductionTime, AuditingUser = x.AuditingUser?.DisplayName, x.AuditingTime, PackUser = x.PackUser?.DisplayName, x.PackTime, IsDuplicate = x.IsDuplicate ? GetResource("是") : GetResource("否") });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 已出库订单
    /// </summary>
    /// <param name="key">订单号等</param>
    /// <param name="Id">请求标识，建议用时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <param name="limit">分页数量</param>
    /// <param name="page">页码</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <returns></returns>
    [HttpPost("ShippedOutbound")]
    //[ApiSignature]
    public IActionResult ShippedOutbound([FromHeader] string Lng, [FromHeader] string Id, [FromForm] String key, [FromForm] DateTime start, [FromForm] DateTime end, [FromForm] Int32 page = 1, [FromForm] Int32 limit = 10)
    {
        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsOrder._.OrderingTime,
            Desc = false,
        };

        if (start <= DateTime.MinValue)
        {
            start = "2000-01-01".ToDateTime();
        }

        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now.ToShortDateString().ToDateTime();
        }

        key = key.SafeString().Trim();
        var list = WmsOrder.SearchShippedOutbound(0, start, end, key, pages);
        var data = list.Select(x => new { x.Id, x.OrderId, OrderingUser = x.OrderingUser?.DisplayName, x.OrderingTime, PickingUser = x.PickingUser?.DisplayName, x.PickingTime, ProductionUser = x.ProductionUser?.DisplayName, x.ProductionTime, AuditingUser = x.AuditingUser?.DisplayName, x.AuditingTime, PackUser = x.PackUser?.DisplayName, x.PackTime, IsDuplicate = x.IsDuplicate ? GetResource("是") : GetResource("否") });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 已出库订单
    /// </summary>
    /// <param name="key">订单号等</param>
    /// <param name="Id">请求标识，建议用时间戳</param>
    /// <param name="Lng">语言标识符</param>
    /// <param name="limit">分页数量</param>
    /// <param name="page">页码</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <returns></returns>
    [HttpPost("PShippedOutbound")]
    //[ApiSignature]
    public IActionResult PShippedOutbound([FromHeader] string Lng, [FromHeader] string Id, [FromForm] String key, [FromForm] DateTime start, [FromForm] DateTime end, [FromForm] Int32 page = 1, [FromForm] Int32 limit = 10)
    {
        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsPickingOrder._.OrderingTime,
            Desc = false,
        };

        if (start <= DateTime.MinValue)
        {
            start = "2000-01-01".ToDateTime();
        }

        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now.ToShortDateString().ToDateTime();
        }

        key = key.SafeString().Trim();
        var list = WmsPickingOrder.SearchShippedOutbound(0, start, end, key, pages);
        var data = list.Select(x => new { x.Id, x.OrderId, OrderingUser = x.OrderingUser?.DisplayName, x.OrderingTime, PickingUser = x.PickingUser?.DisplayName, x.PickingTime, ProductionUser = x.ProductionUser?.DisplayName, x.ProductionTime, AuditingUser = x.AuditingUser?.DisplayName, x.AuditingTime, PackUser = x.PackUser?.DisplayName, x.PackTime, IsDuplicate = x.IsDuplicate ? GetResource("是") : GetResource("否") });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>搜索用户</summary>
    /// <returns></returns>
    [HttpPost("SearchUser")]
    public IActionResult SearchUser(String keyword, Int32 page)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = UserE._.ID,
            Desc = true,
        };

        var departmentId = DepartmentEx.GetOrAdd("仓储部").ID;

        res.data = UserE.Searchs(false, pages, departmentId, keyword).Select(e =>
        {
            return new Xmselect<Int32>
            {
                name = e.DisplayName!,
                value = e.ID
            };
        });

        res.success = true;

        res.extdata = new { pages.PageCount };

        return Json(res);
    }

    /// <summary>
    /// 快递单号扫描上传
    /// </summary>
    /// <param name="Codes">条形码数据</param>
    /// <param name="Times">要写入的时间</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("SetBarcode")]
    public IActionResult SetBarcode([FromForm] String Codes, [FromForm] DateTime? Times)
    {
        var res = new DResult();

        if (Codes.IsNullOrWhiteSpace())
        {
            res.msg = "快递单号为空";
            return Json(res);
        }

        if (Codes.Length <= 6)
        {
            res.msg = "快递单号字符长度不符";
            return Json(res);
        }

        if (Codes.StartsWith("http", StringComparison.OrdinalIgnoreCase))
        {
            res.msg = "快递单号字符不符";
            return Json(res);
        }

        //var model = new BarCode
        //{
        //    Code = Codes
        //};

        var model = ExpressLogs.FindByCode(Codes);
        if (model != null)
        {
            res.code = 2;
            res.msg = "快递单号重复扫描";
            return Json(res);
        }

        model = new ExpressLogs
        {
            Code = Codes
        };

        if (Times.HasValue)
        {
            model.CreateTime = Times.Value;
        }

        if (Codes.Contains("DPK", StringComparison.OrdinalIgnoreCase))
        {
            model.Types = "DPK";
        }
        else if (Codes.Contains("SF", StringComparison.OrdinalIgnoreCase))
        {
            model.Types = "SF";
        }
        else
        {
            model.Types = "Others";
        }
        model.Insert();

        res.msg = "成功";
        res.success = true;
        return Json(res);
    }
}

