{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "RedisCache": "server=127.0.0.1;password=;db=2",
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "DG": "Data Source=..\\..\\Data\\DG.db;ShowSql=false;Provider=SQLite",
    //"DG": "Server=localhost;Port=3306;Database=cangku;Uid=root;Pwd=*****;provider=mysql",
    "DH": "MapTo=DG",
    "Membership": "MapTo=DG"
  }
}
