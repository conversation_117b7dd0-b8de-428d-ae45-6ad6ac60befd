﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>验证码</summary>
[Serializable]
[DataObject]
[Description("验证码")]
[BindTable("DH_VerifyCode", Description = "验证码", ConnName = "DH", DbType = DatabaseType.None)]
public partial class VerifyCode : IVerifyCode, IEntity<IVerifyCode>
{
    #region 属性
    private String? _Key;
    /// <summary>验证码唯一键</summary>
    [DisplayName("验证码唯一键")]
    [Description("验证码唯一键")]
    [DataObjectField(true, false, true, 50)]
    [BindColumn("Key", "验证码唯一键", "")]
    public String? Key { get => _Key; set { if (OnPropertyChanging("Key", value)) { _Key = value; OnPropertyChanged("Key"); } } }

    private String? _Code;
    /// <summary>验证码</summary>
    [DisplayName("验证码")]
    [Description("验证码")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Code", "验证码", "")]
    public String? Code { get => _Code; set { if (OnPropertyChanging("Code", value)) { _Code = value; OnPropertyChanged("Code"); } } }

    private DateTime _EndTime;
    /// <summary>过期时间</summary>
    [DisplayName("过期时间")]
    [Description("过期时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("EndTime", "过期时间", "")]
    public DateTime EndTime { get => _EndTime; set { if (OnPropertyChanging("EndTime", value)) { _EndTime = value; OnPropertyChanged("EndTime"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IVerifyCode model)
    {
        Key = model.Key;
        Code = model.Code;
        EndTime = model.EndTime;
        CreateTime = model.CreateTime;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Key" => _Key,
            "Code" => _Code,
            "EndTime" => _EndTime,
            "CreateTime" => _CreateTime,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Key": _Key = Convert.ToString(value); break;
                case "Code": _Code = Convert.ToString(value); break;
                case "EndTime": _EndTime = value.ToDateTime(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据验证码唯一键查找</summary>
    /// <param name="key">验证码唯一键</param>
    /// <returns>实体对象</returns>
    public static VerifyCode? FindByKey(String? key)
    {
        if (key == null) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Key.EqualIgnoreCase(key));

        // 单对象缓存
        return Meta.SingleCache[key];

        //return Find(_.Key == key);
    }
    #endregion

    #region 字段名
    /// <summary>取得验证码字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>验证码唯一键</summary>
        public static readonly Field Key = FindByName("Key");

        /// <summary>验证码</summary>
        public static readonly Field Code = FindByName("Code");

        /// <summary>过期时间</summary>
        public static readonly Field EndTime = FindByName("EndTime");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得验证码字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>验证码唯一键</summary>
        public const String Key = "Key";

        /// <summary>验证码</summary>
        public const String Code = "Code";

        /// <summary>过期时间</summary>
        public const String EndTime = "EndTime";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";
    }
    #endregion
}
