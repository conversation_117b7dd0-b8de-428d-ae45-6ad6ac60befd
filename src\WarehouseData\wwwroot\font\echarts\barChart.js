var bar_option = {
    tooltip: {},
    grid: {
        show: true, // 是否显示网格
        borderWidth: 4, // 网格边框宽度
        borderColor: '#032966', // 网格边框颜色
        // backgroundColor: 'rgba(0, 0, 0, 0)', // 网格背景颜色
        bottom: 0, // 网格距离容器底部的距离
        containLabel: true, // 网格区域是否包含坐标轴的标签
        top:'5%',
        left: '3%',
        right: '4%',
        bottom: '0%',
        containLabel: true
      },
    xAxis: {
        type: 'category',
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        axisLine: {
            show: true, // 显示 x 轴边框
            lineStyle: {
              color: '#032966', // 设置边框颜色
              width: 2 // 设置边框粗细
            }
          }
       
    },
    yAxis: {  
        type: 'value',
        axisLine: {
            show: true, // 显示 y 轴边框
            lineStyle: {
              color: '#032966', // 设置边框颜色
              width: 2 // 设置边框粗细
            }
          }
   
    },
    series: [{
        name: '销量',
        type: 'bar',
        data: [
            {value: 40, itemStyle: {color: '#14c7fa'}},
            {value: 20, itemStyle: {color: '#5470c6'}},
            {value: 30, itemStyle: {color: '#91cc75'}},
            {value: 40, itemStyle: {color: '#fac858'}},
            {value: 50, itemStyle: {color: '#ee6666'}},
            {value: 40, itemStyle: {color: '#2c63fd'}},
            {value: 50, itemStyle: {color: '#14fa24'}}
        ],
        center: ['20%', '20%'],
        // filter:'invert(100%)',
        // color:'white'
    }],
};
// 导出js
export default bar_option