﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>生产品质反馈扩展表</summary>
public partial class ProductionQualityFeedbackExModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>品质反馈编号</summary>
    public Int32 FeedbackId { get; set; }

    /// <summary>类型 0补充 1处理</summary>
    public Int32 DType { get; set; }

    /// <summary>内容</summary>
    public String? Content { get; set; }

    /// <summary>图片。多个图片用,分隔</summary>
    public String? PicPath { get; set; }

    /// <summary>视频。多个视频用,分隔</summary>
    public String? VideoPath { get; set; }

    /// <summary>文件。多个文件用,分隔</summary>
    public String? FilePath { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductionQualityFeedbackEx model)
    {
        Id = model.Id;
        FeedbackId = model.FeedbackId;
        DType = model.DType;
        Content = model.Content;
        PicPath = model.PicPath;
        VideoPath = model.VideoPath;
        FilePath = model.FilePath;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
    }
    #endregion
}
