﻿using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

namespace HlktechIoT.Dto.Export;

[ExcelExporter(Name = "语言包", AutoFitAllColumn = true, TableStyle = OfficeOpenXml.Table.TableStyles.None, AutoCenter = false, HeaderRowIndex = 1)]
public class LocalStringExport {
    /// <summary> 
    /// 语言
    /// </summary> 
    [ExporterHeader(DisplayName = "语言", IsBold = false, AutoCenterColumn = true, Width = 20)]
    public string? Culture { get; set; }

    /// <summary> 
    /// 翻译项
    /// </summary> 
    [ExporterHeader(DisplayName = "翻译项", IsBold = false, AutoCenterColumn = true, Width = 30)]
    public string? LanKey { get; set; }

    /// <summary> 
    /// 翻译内容
    /// </summary> 
    [ExporterHeader(DisplayName = "翻译内容", IsBold = false, AutoCenterColumn = true, Width = 30)]
    public string? LanValue { get; set; }

    /// <summary> 
    /// 翻译结果
    /// </summary> 
    [ExporterHeader(DisplayName = "翻译结果", IsBold = false, AutoCenterColumn = true, Width = 30)]
    public string? TransValue { get; set; }
}