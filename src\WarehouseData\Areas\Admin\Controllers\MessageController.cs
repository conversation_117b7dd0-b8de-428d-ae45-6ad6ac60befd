﻿using DG.Web.Framework;
using DH.Entity;
using HlktechIoT.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife;
using NewLife.Data;
using Pek.Helpers;
using Pek.Models;
using System.ComponentModel;
using XCode.Membership;
using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers
{
    /// <summary>
    ///消息管理
    /// </summary>
    [DisplayName("消息管理")]
    [Description("用于消息的管理")]
    [AdminArea]
    [DHMenu(67, ParentMenuName = "System", CurrentMenuUrl = "~/{area}/Message", CurrentMenuName = "MessageList", LastUpdate = "20250804")]
    public class MessageController : BaseAdminControllerX
    {
        /// <summary>菜单顺序。扫描是会反射读取</summary>
        protected static Int32 MenuOrder { get; set; } = 67;

        /// <summary>
        /// 列表
        /// </summary>
        /// <returns></returns>
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 列表
        /// </summary>
        /// <returns></returns>
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult GetList(Int32 page, Int32 limit, string key)
        {
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = Message._.Id,
                Desc = true,
            };
            var data = Message.Search(0, null, DateTime.MinValue, DateTime.MinValue, key, pages).Select(e => new
            {
                e.Id,
                e.Title,
                e.Content,
                e.ReceiveUserID,
                ReceiveUserName = UserE.FindByID(e.ReceiveUserID)?.DisplayName,
                e.Status,
                e.CreateTime,
                e.CreateUser,
            });
            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
        }

        /// <summary>
        /// 搜索用户
        /// </summary>
        /// <param name="keyword">关键字</param>
        /// <param name="page">页码</param>
        /// <param name="Id">用户id</param>
        /// <returns></returns>
        [DisplayName("搜索用户")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult SearchUser(String keyword, Int32 page, Int32 Id)
        {
            var res = new DResult();

            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = 10,
                RetrieveTotalCount = true,
                Sort = UserE._.ID,
                Desc = true,
            };

            res.data = UserE.Searchs(false, pages, 0, keyword).Select(e =>
            {
                return new Xmselect<Int32>
                {
                    name = e.DisplayName,
                    value = e.ID
                };
            });

            var model = UserE.FindByID(Id);
            if (model != null)
            {
                res.extdata = new { pages.PageCount, data = new List<NameValueL<Int32?>>() { new() { name = model.DisplayName, value = model.ID } } };
            }
            else
            {
                res.extdata = new { pages.PageCount };
            }

            res.success = true;

            return Json(res);
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <returns></returns>
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add()
        {
            return View();
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add(String title,String content, Int32 receiveUserID) 
        {
            DResult res = new();

            if (title.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("消息标题不能为空");
                return Json(res);
            }
            if (content.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("消息内容不能为空");
                return Json(res);
            }
            if (receiveUserID <= 0)
            {
                res.msg = GetResource("消息接收人不能为空");
                return Json(res);
            }

            var model = new Message()
            {
                Title = title,
                Content = content,
                ReceiveUserID = receiveUserID,
                Status = false,
            };
            model.Insert();
            res.success = true;
            res.msg = GetResource("添加成功");
            return Json(res);
        }

        /// <summary>
        /// 编辑
        /// </summary>
        /// <returns></returns>
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Edit(Int32 Id)
        {
            var model = Message.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("消息不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 编辑
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Edit(String title, String content, Int32 receiveUserID,Int32 Id)
        {
            DResult res = new();

            if (title.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("消息标题不能为空");
                return Json(res);
            }
            if (content.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("消息内容不能为空");
                return Json(res);
            }
            if (receiveUserID <= 0)
            {
                res.msg = GetResource("消息接收人不能为空");
                return Json(res);
            }

            var model = Message.FindById(Id);
            if(model == null)
            {
                res.msg = GetResource("消息不存在");
                return Json(res);
            }

            model.Title = title;
            model.Content = content;
            model.ReceiveUserID = receiveUserID;
            model.Update();
            res.success = true;
            res.msg = GetResource("添加成功");
            return Json(res);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("删除")]
        [EntityAuthorize(PermissionFlags.Delete)]
        public IActionResult Delete(Int32 Id)
        {
            var res = new DResult();
            Message.Delete(Message._.Id == Id);
            Message.Meta.Cache.Clear("");
            res.success = true;
            res.msg = GetResource("删除成功");
            return Json(res);
        }
    }
}
