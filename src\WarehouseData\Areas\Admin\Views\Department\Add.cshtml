﻿@{
    Html.AppendTitleParts(T("添加机构部门").Text);
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }
</style>

<form class="layui-form" lay-filter="organization-form" style="padding: 15px 0 0 0;">
    <input type="hidden" name="parentId" value="@ViewBag.ParentId" />
    <div class="layui-form-item">
        <label class="layui-form-label">@T("部门名称")</label>
        <div class="layui-input-block">
            <input type="text" name="displayName" lay-verify="required" lay-verType="tips" placeholder="@T("请输入部门名称")" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn layui-hide" lay-submit lay-filter="organization-submit" id="organization-submit">@T("提交")</button>
        </div>
    </div>
</form>

<script asp-location="Footer">
    layui.use(['abp', 'form'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        window.submitForm = function () {
            $("#organization-submit").click();
        }

        form.on('submit(organization-submit)', function (data) {
            var field = data.field;

            var waitIndex = parent.layer.load(2);
            abp.ajax({
                url: "@Url.Action("Add", "Department")",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: field,
                abpHandleError: false
            }).done(function (data) {
                if (data.success) {
                    data.index = index;
                    let parentWindow = parent.selectedWindow().window;
                    parentWindow.saveCallback(data);
                } else {
                    let parentWindow = parent.selectedWindow().window;
                    parentWindow.warning(data.msg);
                }
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });
        form.verify({
            required: function (value, item) {
                if (!value) {
                    return '@T("请输入部门名称")';
                }
            }
        });
    });
</script>