﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>产品型号</summary>
[Serializable]
[DataObject]
[Description("产品型号")]
[BindIndex("IU_DH_ProductType_Name", true, "Name")]
[BindIndex("IX_DH_ProductType_SoftwareId", false, "SoftwareId")]
[BindIndex("IX_DH_ProductType_HardwareId", false, "HardwareId")]
[BindIndex("IX_DH_ProductType_Material", false, "Material")]
[BindTable("DH_ProductType", Description = "产品型号", ConnName = "DH", DbType = DatabaseType.None)]
public partial class ProductType : IProductType, IEntity<IProductType>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String _Name = null!;
    /// <summary>型号名称</summary>
    [DisplayName("型号名称")]
    [Description("型号名称")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("Name", "型号名称", "", Master = true)]
    public String Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private Int32 _SoftwareId;
    /// <summary>软件工程师</summary>
    [DisplayName("软件工程师")]
    [Description("软件工程师")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("SoftwareId", "软件工程师", "")]
    public Int32 SoftwareId { get => _SoftwareId; set { if (OnPropertyChanging("SoftwareId", value)) { _SoftwareId = value; OnPropertyChanged("SoftwareId"); } } }

    private Int32 _HardwareId;
    /// <summary>硬件工程师</summary>
    [DisplayName("硬件工程师")]
    [Description("硬件工程师")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("HardwareId", "硬件工程师", "")]
    public Int32 HardwareId { get => _HardwareId; set { if (OnPropertyChanging("HardwareId", value)) { _HardwareId = value; OnPropertyChanged("HardwareId"); } } }

    private Boolean _Status;
    /// <summary>状态</summary>
    [DisplayName("状态")]
    [Description("状态")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Status", "状态", "")]
    public Boolean Status { get => _Status; set { if (OnPropertyChanging("Status", value)) { _Status = value; OnPropertyChanged("Status"); } } }

    private String? _Material;
    /// <summary>物料编号</summary>
    [DisplayName("物料编号")]
    [Description("物料编号")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("Material", "物料编号", "")]
    public String? Material { get => _Material; set { if (OnPropertyChanging("Material", value)) { _Material = value; OnPropertyChanged("Material"); } } }

    private String? _Remark;
    /// <summary>备注</summary>
    [DisplayName("备注")]
    [Description("备注")]
    [DataObjectField(false, false, true, 255)]
    [BindColumn("Remark", "备注", "")]
    public String? Remark { get => _Remark; set { if (OnPropertyChanging("Remark", value)) { _Remark = value; OnPropertyChanged("Remark"); } } }

    private Int16 _PType;
    /// <summary>产品类型。0为无，1为电源</summary>
    [DisplayName("产品类型")]
    [Description("产品类型。0为无，1为电源")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("PType", "产品类型。0为无，1为电源", "", DefaultValue = "0")]
    public Int16 PType { get => _PType; set { if (OnPropertyChanging("PType", value)) { _PType = value; OnPropertyChanged("PType"); } } }

    private Boolean _NeedSn;
    /// <summary>生成Sn</summary>
    [DisplayName("生成Sn")]
    [Description("生成Sn")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("NeedSn", "生成Sn", "")]
    public Boolean NeedSn { get => _NeedSn; set { if (OnPropertyChanging("NeedSn", value)) { _NeedSn = value; OnPropertyChanged("NeedSn"); } } }

    private Boolean _NeedMac;
    /// <summary>生成主Mac</summary>
    [DisplayName("生成主Mac")]
    [Description("生成主Mac")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("NeedMac", "生成主Mac", "")]
    public Boolean NeedMac { get => _NeedMac; set { if (OnPropertyChanging("NeedMac", value)) { _NeedMac = value; OnPropertyChanged("NeedMac"); } } }

    private Boolean _NeedMac1;
    /// <summary>生成副Mac</summary>
    [DisplayName("生成副Mac")]
    [Description("生成副Mac")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("NeedMac1", "生成副Mac", "")]
    public Boolean NeedMac1 { get => _NeedMac1; set { if (OnPropertyChanging("NeedMac1", value)) { _NeedMac1 = value; OnPropertyChanged("NeedMac1"); } } }

    private Boolean _PCBCycle;
    /// <summary>PCB周期</summary>
    [DisplayName("PCB周期")]
    [Description("PCB周期")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("PCBCycle", "PCB周期", "")]
    public Boolean PCBCycle { get => _PCBCycle; set { if (OnPropertyChanging("PCBCycle", value)) { _PCBCycle = value; OnPropertyChanged("PCBCycle"); } } }

    private Boolean _ShieldCycle;
    /// <summary>屏蔽罩周期</summary>
    [DisplayName("屏蔽罩周期")]
    [Description("屏蔽罩周期")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ShieldCycle", "屏蔽罩周期", "")]
    public Boolean ShieldCycle { get => _ShieldCycle; set { if (OnPropertyChanging("ShieldCycle", value)) { _ShieldCycle = value; OnPropertyChanged("ShieldCycle"); } } }

    private Boolean _MainChipCycle;
    /// <summary>主芯片周期</summary>
    [DisplayName("主芯片周期")]
    [Description("主芯片周期")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("MainChipCycle", "主芯片周期", "")]
    public Boolean MainChipCycle { get => _MainChipCycle; set { if (OnPropertyChanging("MainChipCycle", value)) { _MainChipCycle = value; OnPropertyChanged("MainChipCycle"); } } }

    private String? _TestItems;
    /// <summary>测试项</summary>
    [DisplayName("测试项")]
    [Description("测试项")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("TestItems", "测试项", "text")]
    public String? TestItems { get => _TestItems; set { if (OnPropertyChanging("TestItems", value)) { _TestItems = value; OnPropertyChanged("TestItems"); } } }

    private String? _ExpansionItems;
    /// <summary>配置项</summary>
    [DisplayName("配置项")]
    [Description("配置项")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("ExpansionItems", "配置项", "text")]
    public String? ExpansionItems { get => _ExpansionItems; set { if (OnPropertyChanging("ExpansionItems", value)) { _ExpansionItems = value; OnPropertyChanged("ExpansionItems"); } } }

    private Int32 _ModuleType;
    /// <summary>模组类型(0:无,1:ACDC,2:DCDC)</summary>
    [DisplayName("模组类型(0")]
    [Description("模组类型(0:无,1:ACDC,2:DCDC)")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ModuleType", "模组类型(0:无,1:ACDC,2:DCDC)", "", DefaultValue = "0")]
    public Int32 ModuleType { get => _ModuleType; set { if (OnPropertyChanging("ModuleType", value)) { _ModuleType = value; OnPropertyChanged("ModuleType"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IProductType model)
    {
        Id = model.Id;
        Name = model.Name;
        SoftwareId = model.SoftwareId;
        HardwareId = model.HardwareId;
        Status = model.Status;
        Material = model.Material;
        Remark = model.Remark;
        PType = model.PType;
        NeedSn = model.NeedSn;
        NeedMac = model.NeedMac;
        NeedMac1 = model.NeedMac1;
        PCBCycle = model.PCBCycle;
        ShieldCycle = model.ShieldCycle;
        MainChipCycle = model.MainChipCycle;
        TestItems = model.TestItems;
        ExpansionItems = model.ExpansionItems;
        ModuleType = model.ModuleType;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "Name" => _Name,
            "SoftwareId" => _SoftwareId,
            "HardwareId" => _HardwareId,
            "Status" => _Status,
            "Material" => _Material,
            "Remark" => _Remark,
            "PType" => _PType,
            "NeedSn" => _NeedSn,
            "NeedMac" => _NeedMac,
            "NeedMac1" => _NeedMac1,
            "PCBCycle" => _PCBCycle,
            "ShieldCycle" => _ShieldCycle,
            "MainChipCycle" => _MainChipCycle,
            "TestItems" => _TestItems,
            "ExpansionItems" => _ExpansionItems,
            "ModuleType" => _ModuleType,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "SoftwareId": _SoftwareId = value.ToInt(); break;
                case "HardwareId": _HardwareId = value.ToInt(); break;
                case "Status": _Status = value.ToBoolean(); break;
                case "Material": _Material = Convert.ToString(value); break;
                case "Remark": _Remark = Convert.ToString(value); break;
                case "PType": _PType = Convert.ToInt16(value); break;
                case "NeedSn": _NeedSn = value.ToBoolean(); break;
                case "NeedMac": _NeedMac = value.ToBoolean(); break;
                case "NeedMac1": _NeedMac1 = value.ToBoolean(); break;
                case "PCBCycle": _PCBCycle = value.ToBoolean(); break;
                case "ShieldCycle": _ShieldCycle = value.ToBoolean(); break;
                case "MainChipCycle": _MainChipCycle = value.ToBoolean(); break;
                case "TestItems": _TestItems = Convert.ToString(value); break;
                case "ExpansionItems": _ExpansionItems = Convert.ToString(value); break;
                case "ModuleType": _ModuleType = value.ToInt(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static ProductType? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据型号名称查找</summary>
    /// <param name="name">型号名称</param>
    /// <returns>实体对象</returns>
    public static ProductType? FindByName(String name)
    {
        if (name.IsNullOrEmpty()) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Name.EqualIgnoreCase(name));

        // 单对象缓存
        return Meta.SingleCache.GetItemWithSlaveKey(name) as ProductType;

        //return Find(_.Name == name);
    }

    /// <summary>根据软件工程师查找</summary>
    /// <param name="softwareId">软件工程师</param>
    /// <returns>实体列表</returns>
    public static IList<ProductType> FindAllBySoftwareId(Int32 softwareId)
    {
        if (softwareId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.SoftwareId == softwareId);

        return FindAll(_.SoftwareId == softwareId);
    }

    /// <summary>根据硬件工程师查找</summary>
    /// <param name="hardwareId">硬件工程师</param>
    /// <returns>实体列表</returns>
    public static IList<ProductType> FindAllByHardwareId(Int32 hardwareId)
    {
        if (hardwareId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.HardwareId == hardwareId);

        return FindAll(_.HardwareId == hardwareId);
    }

    /// <summary>根据物料编号查找</summary>
    /// <param name="material">物料编号</param>
    /// <returns>实体列表</returns>
    public static IList<ProductType> FindAllByMaterial(String? material)
    {
        if (material == null) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Material.EqualIgnoreCase(material));

        return FindAll(_.Material == material);
    }
    #endregion

    #region 字段名
    /// <summary>取得产品型号字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>型号名称</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>软件工程师</summary>
        public static readonly Field SoftwareId = FindByName("SoftwareId");

        /// <summary>硬件工程师</summary>
        public static readonly Field HardwareId = FindByName("HardwareId");

        /// <summary>状态</summary>
        public static readonly Field Status = FindByName("Status");

        /// <summary>物料编号</summary>
        public static readonly Field Material = FindByName("Material");

        /// <summary>备注</summary>
        public static readonly Field Remark = FindByName("Remark");

        /// <summary>产品类型。0为无，1为电源</summary>
        public static readonly Field PType = FindByName("PType");

        /// <summary>生成Sn</summary>
        public static readonly Field NeedSn = FindByName("NeedSn");

        /// <summary>生成主Mac</summary>
        public static readonly Field NeedMac = FindByName("NeedMac");

        /// <summary>生成副Mac</summary>
        public static readonly Field NeedMac1 = FindByName("NeedMac1");

        /// <summary>PCB周期</summary>
        public static readonly Field PCBCycle = FindByName("PCBCycle");

        /// <summary>屏蔽罩周期</summary>
        public static readonly Field ShieldCycle = FindByName("ShieldCycle");

        /// <summary>主芯片周期</summary>
        public static readonly Field MainChipCycle = FindByName("MainChipCycle");

        /// <summary>测试项</summary>
        public static readonly Field TestItems = FindByName("TestItems");

        /// <summary>配置项</summary>
        public static readonly Field ExpansionItems = FindByName("ExpansionItems");

        /// <summary>模组类型(0:无,1:ACDC,2:DCDC)</summary>
        public static readonly Field ModuleType = FindByName("ModuleType");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得产品型号字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>型号名称</summary>
        public const String Name = "Name";

        /// <summary>软件工程师</summary>
        public const String SoftwareId = "SoftwareId";

        /// <summary>硬件工程师</summary>
        public const String HardwareId = "HardwareId";

        /// <summary>状态</summary>
        public const String Status = "Status";

        /// <summary>物料编号</summary>
        public const String Material = "Material";

        /// <summary>备注</summary>
        public const String Remark = "Remark";

        /// <summary>产品类型。0为无，1为电源</summary>
        public const String PType = "PType";

        /// <summary>生成Sn</summary>
        public const String NeedSn = "NeedSn";

        /// <summary>生成主Mac</summary>
        public const String NeedMac = "NeedMac";

        /// <summary>生成副Mac</summary>
        public const String NeedMac1 = "NeedMac1";

        /// <summary>PCB周期</summary>
        public const String PCBCycle = "PCBCycle";

        /// <summary>屏蔽罩周期</summary>
        public const String ShieldCycle = "ShieldCycle";

        /// <summary>主芯片周期</summary>
        public const String MainChipCycle = "MainChipCycle";

        /// <summary>测试项</summary>
        public const String TestItems = "TestItems";

        /// <summary>配置项</summary>
        public const String ExpansionItems = "ExpansionItems";

        /// <summary>模组类型(0:无,1:ACDC,2:DCDC)</summary>
        public const String ModuleType = "ModuleType";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
