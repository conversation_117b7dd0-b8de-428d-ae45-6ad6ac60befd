﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace HlktechIoT.Entity;

/// <summary>单页文章翻译</summary>
[Serializable]
[DataObject]
[Description("单页文章翻译")]
[BindIndex("IU_DH_SingleArticleLan_SId_LId", true, "SId,LId")]
[BindTable("DH_SingleArticleLan", Description = "单页文章翻译", ConnName = "DH", DbType = DatabaseType.None)]
public partial class SingleArticleLan : ISingleArticleLan, IEntity<ISingleArticleLan>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int32 _SId;
    /// <summary>单页文章Id</summary>
    [DisplayName("单页文章Id")]
    [Description("单页文章Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("SId", "单页文章Id", "")]
    public Int32 SId { get => _SId; set { if (OnPropertyChanging("SId", value)) { _SId = value; OnPropertyChanged("SId"); } } }

    private Int32 _LId;
    /// <summary>所属语言Id</summary>
    [DisplayName("所属语言Id")]
    [Description("所属语言Id")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("LId", "所属语言Id", "")]
    public Int32 LId { get => _LId; set { if (OnPropertyChanging("LId", value)) { _LId = value; OnPropertyChanged("LId"); } } }

    private String? _Name;
    /// <summary>文章标题</summary>
    [DisplayName("文章标题")]
    [Description("文章标题")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("Name", "文章标题", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private String? _Content;
    /// <summary>内容</summary>
    [DisplayName("内容")]
    [Description("内容")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("Content", "内容", "text")]
    public String? Content { get => _Content; set { if (OnPropertyChanging("Content", value)) { _Content = value; OnPropertyChanged("Content"); } } }

    private String? _FileUrl;
    /// <summary>上传的文件</summary>
    [DisplayName("上传的文件")]
    [Description("上传的文件")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("FileUrl", "上传的文件", "")]
    public String? FileUrl { get => _FileUrl; set { if (OnPropertyChanging("FileUrl", value)) { _FileUrl = value; OnPropertyChanged("FileUrl"); } } }

    private String? _Md5;
    /// <summary>Md5</summary>
    [DisplayName("Md5")]
    [Description("Md5")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Md5", "Md5", "")]
    public String? Md5 { get => _Md5; set { if (OnPropertyChanging("Md5", value)) { _Md5 = value; OnPropertyChanged("Md5"); } } }

    private String? _Ex1;
    /// <summary>扩展字段1</summary>
    [DisplayName("扩展字段1")]
    [Description("扩展字段1")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Ex1", "扩展字段1", "")]
    public String? Ex1 { get => _Ex1; set { if (OnPropertyChanging("Ex1", value)) { _Ex1 = value; OnPropertyChanged("Ex1"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(ISingleArticleLan model)
    {
        Id = model.Id;
        SId = model.SId;
        LId = model.LId;
        Name = model.Name;
        Content = model.Content;
        FileUrl = model.FileUrl;
        Md5 = model.Md5;
        Ex1 = model.Ex1;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "SId" => _SId,
            "LId" => _LId,
            "Name" => _Name,
            "Content" => _Content,
            "FileUrl" => _FileUrl,
            "Md5" => _Md5,
            "Ex1" => _Ex1,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "SId": _SId = value.ToInt(); break;
                case "LId": _LId = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "Content": _Content = Convert.ToString(value); break;
                case "FileUrl": _FileUrl = Convert.ToString(value); break;
                case "Md5": _Md5 = Convert.ToString(value); break;
                case "Ex1": _Ex1 = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static SingleArticleLan? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据单页文章Id、所属语言Id查找</summary>
    /// <param name="sId">单页文章Id</param>
    /// <param name="lId">所属语言Id</param>
    /// <returns>实体对象</returns>
    public static SingleArticleLan? FindBySIdAndLId(Int32 sId, Int32 lId)
    {
        if (sId < 0) return null;
        if (lId < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.SId == sId && e.LId == lId);

        return Find(_.SId == sId & _.LId == lId);
    }
    #endregion

    #region 字段名
    /// <summary>取得单页文章翻译字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>单页文章Id</summary>
        public static readonly Field SId = FindByName("SId");

        /// <summary>所属语言Id</summary>
        public static readonly Field LId = FindByName("LId");

        /// <summary>文章标题</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>内容</summary>
        public static readonly Field Content = FindByName("Content");

        /// <summary>上传的文件</summary>
        public static readonly Field FileUrl = FindByName("FileUrl");

        /// <summary>Md5</summary>
        public static readonly Field Md5 = FindByName("Md5");

        /// <summary>扩展字段1</summary>
        public static readonly Field Ex1 = FindByName("Ex1");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得单页文章翻译字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>单页文章Id</summary>
        public const String SId = "SId";

        /// <summary>所属语言Id</summary>
        public const String LId = "LId";

        /// <summary>文章标题</summary>
        public const String Name = "Name";

        /// <summary>内容</summary>
        public const String Content = "Content";

        /// <summary>上传的文件</summary>
        public const String FileUrl = "FileUrl";

        /// <summary>Md5</summary>
        public const String Md5 = "Md5";

        /// <summary>扩展字段1</summary>
        public const String Ex1 = "Ex1";
    }
    #endregion
}
