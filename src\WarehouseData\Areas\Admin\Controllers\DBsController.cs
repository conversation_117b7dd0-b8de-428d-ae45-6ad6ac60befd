﻿using DG.Web.Framework;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Collections;

using Pek.IO;
using Pek.Models;

using System.ComponentModel;

using XCode.DataAccessLayer;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers;

[DisplayName("数据备份列表")]
[Description("用于查看数据库部分列表")]
[AdminArea]
[DHMenu(10,ParentMenuName = "DBP", CurrentMenuUrl = "~/{area}/DBs", CurrentMenuName = "DBbackup", CurrentIcon = "&#xe6f5;", LastUpdate = "20240124")]
public class DBsController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 10;

    /// <summary>
    /// 备份列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("数据备份列表")]
    public IActionResult Index()
    {
        return View();
    }

    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("备份列表")]
    public IActionResult GetPage()
    {
        var BackupPath = NewLife.Setting.Current.BackupPath.GetFullPath();

        if (BackupPath.IsNullOrEmpty())
        {
            return Json(new { message = GetResource("BackupPath备份目类为空") });
        }
        BackupPath.EnsureDirectory(false);
        var list = FileSystemObject.GetDirectoryInfos(BackupPath, FsoMethod.All);
        Double Size = 0;
        foreach (var item in list)
        {
            Size += item.size;
        }

        list = list.OrderByDescending(x => x.lastWriteTime).ToList();
        return Json(new { code = 0, msg = "success", count = list.Count, data = list.Select(x => new { x.name, size = ((Double)x.size / 1024 / 1024).ToString("#0.00") + "MB", x.lastWriteTime }) });
    }

    /// <summary>
    /// 下载数据库备份
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("下载数据库备份")]
    public IActionResult DownFile(String name)
    {
        var bakFile = NewLife.Setting.Current.BackupPath.CombinePath(name);

        var Files = bakFile.AsFile();

        if (!Files.Exists)
        {
            //return Prompt(new PromptModel { Message = GetResource("文件不存在") });
            return Json(new DResult { success = false, msg = GetResource("文件不存在") });
        }
        WriteLog("下载", "下载数据库备份 " + name, UserHost);
        return File(Files.ReadBytes(), "application/octet-stream", name);
    }

    /// <summary>
    /// 删除备份
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除备份")]
    public IActionResult Del(String name)
    {
        var bakFile = NewLife.Setting.Current.BackupPath.CombinePath(name);

        bakFile.AsFile().Delete();

        WriteLog("删除", "删除备份 " + name, UserHost);

        return Json(new DResult { success = true, msg = GetResource("删除成功") });
        //return Prompt(new PromptModel { Message = GetResource("删除成功"), IsOk = true, BackUrl = Url.Action("Restore") });
    }

    /// <summary>
    /// 还原数据库
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)32)]
    [DisplayName("还原数据库")]
    public IActionResult RestoreAll(String name)
    {
        if (name.IsNullOrWhiteSpace() || name.IndexOf("_") == -1)
        {
            return Json(new DResult() { msg = GetResource("文件不存在或文件名未按格式") });
        }

        var dbName = name.Split('_')[0];
        var dal = DAL.Create(dbName);
        var tables = dal.Tables;

        if (dal.DbType == DatabaseType.SQLite)
        {
            var sb = Pool.StringBuilder.Get();
            foreach (var item in tables)
            {
                sb.Append($"DELETE FROM '{item.TableName}';DELETE FROM sqlite_sequence WHERE name = '{item.TableName}';");
            }
            dal.Execute(sb.Put(true));
            dal.Execute("VACUUM");

            var bakFile = NewLife.Setting.Current.BackupPath.CombinePath(name);

            dal.RestoreAll(bakFile);
        }
        else if (dal.DbType == DatabaseType.MySql)
        {
            var sb = Pool.StringBuilder.Get();

            foreach (var item in tables)
            {
                sb.Append($"TRUNCATE TABLE `{item.TableName}`;");
            }
            dal.Execute(sb.Put(true));

            var bakFile = NewLife.Setting.Current.BackupPath.CombinePath(name);

            dal.RestoreAll(bakFile);
        }
        else if (dal.DbType == DatabaseType.MySql)
        {
            var sb = Pool.StringBuilder.Get();

            foreach (var item in tables)
            {
                sb.Append($"TRUNCATE TABLE {item.TableName};");
            }
            dal.Execute(sb.Put(true));

            var bakFile = NewLife.Setting.Current.BackupPath.CombinePath(name);

            dal.RestoreAll(bakFile);
        }
        else if (dal.DbType == DatabaseType.PostgreSQL)
        {
            var sb = Pool.StringBuilder.Get();

            foreach (var item in tables)
            {
                sb.Append($"TRUNCATE TABLE {item.TableName} RESTART IDENTITY;");
            }
            dal.Execute(sb.Put(true));

            var bakFile = NewLife.Setting.Current.BackupPath.CombinePath(name);

            dal.RestoreAll(bakFile);
        }

        return Json(new DResult() { success = true, msg = GetResource("还原成功") });
    }
    private static void WriteLog(String action, String remark, String? ip = null) => LogProvider.Provider.WriteLog(typeof(DBsController), action, true, remark, ip: ip);
}