﻿using DG.Web.Framework;
using HlktechIoT.Entity;
using Microsoft.AspNetCore.Mvc;
using YRY.Web.Controllers;

namespace HlktechIoT.Controllers
{
    public class OnlineReportController : ControllerBaseX
    {
        /// <summary>
        /// 在线报告
        /// </summary>
        /// <returns></returns>
        public IActionResult Index(string sn ,string orderId)
        {
            if (sn == null || orderId == null)
            {
                return NotFound("找不到文件");
            }
            try
            {
                var modelProductOrders = ProductOrders.FindByOrderId(orderId);
                if (modelProductOrders==null)
                {
                    return NotFound("文件不存在");
                }
                var list = ProductSnMac.FindAllOtherBySn(sn, modelProductOrders.StartTime, modelProductOrders.EndTime);
                if (list == null)
                {
                    return NotFound("文件不存在");
                }
                return DGView(list.FirstOrDefault());
            }
            catch (Exception ex)
            {
                return NotFound("异常："+ex.Message);
            }
           
        }
    }
}
