﻿@model WmsPickingOrder
@{
    Html.AppendTitleParts(T("领料订单日志").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }

    .online {
        color: 'mediumaquamarine' !important;
    }

    .unline {
        color: 'gray' !important;
    }
</style>
<script asp-location="Head">
    var pleaseSelect = '@T("请选择")';
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<form class="layui-form dg-form">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("关键词")：</label>
            <div class="layui-input-inline">
                <input type="text" name="key" id="key" placeholder="@T("请输入名称")" autocomplete="off" class="layui-input" lay-filter="key">
            </div>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;
        var dTime = window.location.search.split('&')[1]
        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetLogList")?'+dTime
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 80
            , cols: [[
                { field: 'Id', title: '@T("编号")', width: 80 }
                , { field: 'OrderId', title: '@T("订单编号")', width: 140 }
                , { field: 'Process', title: '@T("操作")', width: 120 }
                , { field: 'CreateUser', title: '@T("创建者")', width: 140 }
                , { field: 'CreateTime', title: '@T("创建时间")', width: 160 }
    @* , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', minWidth: 160 } *@
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'tables'
            , where:{
                 orderId:'@Model.OrderId'
            }
        });

        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        where: {
                            orderId: $("#key").val() === ''?'@Model.OrderId':$("#key").val(),
                        }
                    });
            }
        }

        $("#key").on("input", function (e) {
            active.reload();
        });

        dtree.on('node("demoTree2")', function (obj) {
            table.reload('tables', {
                where: { "dId": obj.param.nodeId },
                page: {
                    curr: 1
                }
            })
        });

        table.on('toolbar(tool)', function (obj) {
            let data = obj.config
            if (obj.event === 'add') {
                window.add(data);
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success(data.msg);
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'edit') {
                window.edit(data);
            }
        });

        window.saveCallback = function (data) {
            parent.layer.close(data.index);
            abp.notify.success(data.msg);
            table.reload("tables");
        }

        window.add = function (data) {
            top.layui.dg.popupRight({
                id: 'Add'
                , title: ' @T("新增设备")'
                , closeBtn: 1
                , area: ['580px']
                , success: function () {
                   $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Add")" frameborder="0" class="layadmin-iframe" name="iframe' + data.index + '"></iframe>');
                }
            });
        }
        window.edit = function (data) {
            top.layui.dg.popupRight({
                id: 'Edit'
                , title: ' @T("编辑设备")'
                , closeBtn: 1
                , area: ['580px']
                , success: function (obj, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Edit")' + abp.utils.formatString("?id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        window.details = function (data) {
            top.layui.dg.popupRight({
                id: 'DeviceDetails'
                , title: ' @T("查看")' + '(' + data.Name + ')'
                , closeBtn: 1
                , area: ['780px']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Details")' + abp.utils.formatString("?id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }


        window.warning = function (msg) {
            os.warning(msg);
        }
    });
</script>

<script type="text/html" id="tool">
    <!-- @if (this.Has((PermissionFlags)1))
    {
        <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="details"> @T("查看")</a>
    } -->
    @if (this.Has((PermissionFlags)4))
    {
         <a class="pear-btn pear-btn-primary pear-btn-xs" lay-event="edit"> @T("编辑")</a>
    }
    @if (this.Has((PermissionFlags)8))
    {
         <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="del"> @T("删除")</a>
    }
</script>

<script type="text/html" id="online">
    {{# if(d.Online) { }}
      <i class="layui-icon layui-icon-ok" style="color: #16b777"></i>
    {{# } else { }}
      <i class="layui-icon layui-icon-close" style="color: #ff5722"></i>
    {{# } }}
</script>

<script type="text/html" id="switchTpl">
    @if (this.Has((PermissionFlags)4))
    {
        <input type="checkbox" name="status" data-id="{{d.Id}}" lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" {{ d.Enable == "1" ? 'checked' : ''}}>
    }
    else
    {
        <input type="checkbox" name="status" data-id="{{d.Id}}" lay-skin="switch" lay-text="ON|OFF" lay-filter="statusedit" {{ d.Enable == "1" ? 'checked' : ''}} disabled>
    }
</script>

<script type="text/html" id="user-toolbar">
    @* <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        @T("新增")
    </button>  *@
</script>