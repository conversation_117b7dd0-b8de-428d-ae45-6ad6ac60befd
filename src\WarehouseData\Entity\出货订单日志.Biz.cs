using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;
using XCode.Shards;

namespace HlktechIoT.Entity;

public partial class WmsOrderLogs : CubeEntityBase<WmsOrderLogs>
{
    #region 对象操作
    static WmsOrderLogs()
    {
        Meta.Table.DataTable.InsertOnly = true;

        // 按年分表
        Meta.ShardPolicy = new TimeShardPolicy(nameof(CreateTime), Meta.Factory)
        {
            TablePolicy = "{0}_{1:yy}",
            Step = TimeSpan.FromDays(366),
        };

        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(Process));

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add(new UserModule { AllowEmpty = false });
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add(new IPModule { AllowEmpty = false });

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;

        // 单对象缓存
        var sc = Meta.SingleCache;
        // sc.Expire = 60;
        sc.FindSlaveKeyMethod = k => Find(_.OrderId == k);
        sc.GetSlaveKeyMethod = e => e.OrderId;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 这里验证参数范围，建议抛出参数异常，指定参数名，前端用户界面可以捕获参数异常并聚焦到对应的参数输入框
        if (OrderId.IsNullOrEmpty()) throw new ArgumentNullException(nameof(OrderId), "ERP订单编号不能为空！");

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (method == DataMethod.Insert && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
        }*/
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;

        // 检查唯一索引
        // CheckExist(method == DataMethod.Insert, nameof(OrderId));

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化WmsOrderLogs[出货订单日志]数据……");

    //    var entity = new WmsOrderLogs();
    //    entity.OrderId = "abc";
    //    entity.Process = 0;
    //    entity.CreateUser = "abc";
    //    entity.CreateUserID = 0;
    //    entity.CreateTime = DateTime.Now;
    //    entity.CreateIP = "abc";
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化WmsOrderLogs[出货订单日志]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    #endregion

    #region 扩展查询
    /// <summary>根据ERP订单编号查找</summary>
    /// <param name="orderId">ERP订单编号</param>
    /// <returns>实体对象</returns>
    public static WmsOrderLogs? FindByOrderId(String orderId)
    {
        if (orderId.IsNullOrEmpty()) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.OrderId.EqualIgnoreCase(orderId));

        // 单对象缓存
        //return Meta.SingleCache.GetItemWithSlaveKey(orderId) as WmsOrderLogs;

        return Find(_.OrderId == orderId);
    }

    /// <summary>根据ERP订单编号、操作工序查找</summary>
    /// <param name="orderId">ERP订单编号</param>
    /// <param name="process">操作工序</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrderLogs> FindAllByOrderIdAndProcess(String orderId, Int32 process)
    {
        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.OrderId.EqualIgnoreCase(orderId) && e.Process == process);

        return FindAll(_.OrderId == orderId & _.Process == process);
    }

    /// <summary>根据创建者查找</summary>
    /// <param name="createUserId">创建者</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrderLogs> FindAllByCreateUserID(Int32 createUserId)
    {
        if (createUserId <= 0) return new List<WmsOrderLogs>();

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.CreateUserID == createUserId);

        return FindAll(_.CreateUserID == createUserId);
    }
    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="orderId">ERP订单编号</param>
    /// <param name="start">创建时间开始</param>
    /// <param name="end">创建时间结束</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrderLogs> Search(String orderId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (!orderId.IsNullOrEmpty()) exp &= _.OrderId == orderId;
        exp &= _.CreateTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);

        return FindAll(exp, page);
    }

    /// <summary>高级查询</summary>
    /// <param name="orderId">ERP订单编号</param>
    /// <param name="dTime">打单时间</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<WmsOrderLogs> Search(String orderId, DateTime dTime, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (!orderId.IsNullOrEmpty()) exp &= _.OrderId == orderId;

        if (dTime <= DateTime.MinValue)
        {
            dTime = "2000-1-1".ToDateTime();
        }
        exp &= _.CreateTime >= dTime;
        if (!key.IsNullOrEmpty()) exp &= _.OrderId.Contains(key);

        return FindAll(exp, page);
    }

    // Select Count(Id) as Id,Category From DH_WmsOrderLogs Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<WmsOrderLogs> _CategoryCache = new FieldCache<WmsOrderLogs>(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public IWmsOrderLogs ToModel()
    {
        var model = new WmsOrderLogs();
        model.Copy(this);

        return model;
    }

    #endregion
}