﻿@using DH.Helpers
@{
    Html.AppendTitleParts(T("操作日志").Text);

    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");

    var page =Pek.Helpers.DHWeb.HttpContext.Items["DGPage"] as DH.AspNetCore.Extensions.Pager;
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

        xm-select {
            min-height: 30px!important;
            max-height: 30px!important;
            line-height: 30px!important;
        }

            xm-select .xm-label .xm-label-block {
                height: 20px !important;
                line-height: 20px !important;
            }

        .seller-inline-3 {
            width: 130px !important;
        }
        @if (language.UniqueSeoCode == "en")
        { 
        <text>
        .layui-form-item.layui-inline .layui-form-label {
            width: auto
        }
        .dg-form .layui-form-label {
        width:auto
        }
      
        </text>
        }
</style>

<script asp-location="Head">
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage='@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn='@T("确定")';
    var layuiGoPage='@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc='@T("升序")';
    var layuiDesc='@T("降序")';
</script>

<form class="layui-form dg-form">
   <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
            <div class="layui-inline">
                <label class="layui-form-label">@T("类别")：</label>
                <div class="layui-input-inline seller-inline-3">
                    @Html.ForDropDownList("category", Log.FindAllCategoryName(), page!["category"], T("类别").ToString(), false, new Dictionary<String, String> { { "lay-filter", "category" } })
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">@T("操作")：</label>
                <div class="layui-input-inline seller-inline-3">
                    @Html.ForDropDownList("actions", Log.FindAllActionName(), page["actions"], T("全部").ToString(), false, new Dictionary<String, String> { { "lay-filter", "actions" } })
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">@T("选择用户")：</label>
                <div class="layui-input-inline width700">
                    <div id="demo1"></div>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">@T("操作时间")：</label>
                <div class="layui-input-inline seller-inline-4">
                    <input type="text" name="date" id="date" placeholder="@T("开始时间") @T("到") @T("结束时间")" autocomplete="off" class="layui-input">
                </div>
            </div>
        </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
        layui.use(['abp', 'form', 'laydate', 'table'], function () {
            var $ = layui.jquery;
            var abp = layui.abp;
            var form = layui.form;
            var laydate = layui.laydate;
            var table = layui.table;
            
            laydate.render({
                elem: '#date',
                range: '@T("到")',
                format: 'yyyy-MM-dd',
                lang: '@language.UniqueSeoCode',
                trigger: 'click', //自动弹出控件的事件，采用click弹出
                done: function (value, date, endDate) {
                    //console.log(value);
                    $("#date").val(value)
                    active.reload();
                }
            });

            table.render({
                elem: '#tablist'
                , url: '@Url.Action("GetList")'
                , page: true
                , toolbar: '#user-toolbar'
                , defaultToolbar: [{
                    title: '@T("刷新")',
                    layEvent: 'refresh',
                    icon: 'layui-icon-refresh',
                }, 'filter', 'print']
                , cellMinWidth: 80
                , smartReloadModel: true
                , cols: [[
                    { field: 'Category', title: '@T("类别")', width: 89 }
                    , { field: 'Action', title: '@T("操作")', width: 90  }
                    , { field: 'Success', title: '@T("成功")', templet: '#success', width: 60 }
                    , { field: 'Remark', title: '@T("详细信息")' }
                    , { field: 'Trace', title: '@T("追踪")', templet: '#trace' }
                    , { field: 'UserName', title: '@T("用户名")', width: 100 }
                    , { field: 'CreateIP', title: 'IP @T("地址")', width: 100 }
                    , { field: 'PhysicalAddress', title: '@T("物理地址")', width: 130 }
                    , { field: 'CreateTime', title: '@T("时间")', width: 160 }
                ]]
                , limit: 13
                , limits: [10, 13, 20, 30, 50, 100]
                , height: 'full-100'
                , id: 'tables'
            });

            window.active = {
                reload: function () {
                    table.reload('tables',
                        {
                            where: {
                                category: $("#category").val(),
                                date: $("#date").val(),
                                actions: $("#actions").val(),
                                username: Names
                            }
                        });
                }
            }

            $("#username").on("input", function (e) {
                active.reload();
            });

            form.on("select(category)", function () {
                active.reload();
            });

            form.on("select(actions)", function () {
                active.reload();
            });

            table.on('toolbar(tool)', function(obj) {
                if (obj.event === 'exports') {
                    var href = "@Url.Action("ExportLog")?category=" + $("#category").val() + "&date=" + $("#date").val() + "&actions=" + $("#actions").val() + "&username=" + Names;
                    $("#InverterExport").attr("href", href);
                } else if (obj.event === 'refresh') {
                    active.reload();
                }
            });

            var demo1 = xmSelect.render({
                el: '#demo1',
                radio: true,
                autoRow: true,
                language: '@language.UniqueSeoCode',
                toolbar: { show: true },
                filterable: true,
                remoteSearch: true,
                remoteMethod: function (val, cb, show) {
                    //console.log("响应打印值==" + val);
                    //这里如果val为空, 则不触发搜索
                    if (!val) {
                        return cb([]);
                    }
                    Names = "";
                    //这里引入了一个第三方插件axios, 相当于$.ajax
                    $.ajax({
                        method: 'get',
                        url: '@Url.Action("FindBylikeName")?Key=' + val,
                        params: {
                            Key: val,
                        }
                    }).then(response => {
                        var res = response;
                        cb(res)
                    }).catch(err => {
                        cb([]);
                    });
                    },
                    
                    on: function (data) {
                        Names = "";
                        var arr = data.arr;
                        if (arr.length > 0) {
                            Names= arr[0].name;
                        }
                        active.reload();
                    },
                    })
        });
</script>

<script type="text/html" id="success">
        {{# if(d.Success) { }}
        <i class="pear-icon pear-icon-select-bold" style="color: green;"></i>
        {{# } else { }}
        <i class="pear-icon pear-icon-close-bold" style="color: red;"></i>
        {{# } }}
</script>

<script type="text/html" id="trace">
   {{# if(d.Trace != "") { }}
   <a href="{{d.Trace}}" target="_blank">@T("追踪")</a>
   {{# } }}
</script>

<script type="text/html" id="user-toolbar">
	<a id="InverterExport" class="pear-btn pear-btn-primary pear-btn-md" lay-event="exports">
		<i class="layui-icon">&#xe601;</i>
			@T("导出日志")
	</a>
</script>