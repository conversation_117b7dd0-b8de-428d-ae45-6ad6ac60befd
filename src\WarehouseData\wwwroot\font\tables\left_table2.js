// const layui = require('./node_modules/layui/dist/layui.js')


    let left_table2_options = {
      elem: '#left_table2',
      id:'left_table2',
      cols: [[ //标题栏
        {field: 'OrderId', title: '订单号',width:"25%",templet:(d)=>{
          return '<div data-table="left_table2">'+d.OrderId+'</div>'
        }},
        {field: 'User', title: '操作人',width:"15%" },
        {field: 'ProcessTime', title: '操作时间',width:"35%"},
        {field: 'ManHour', title: '工时',width:"25%",align:'center' },
      ]],
      data: [{OrderId: '无数据',}]
      ,cellMinWidth: 80
      ,
      height: (()=>{

        let tableHeight = layui.$('div.item_left_box[data-index="2"]').height() 
        return ((tableHeight- (tableHeight * 0.3))  + 'px')
      })(),
        page: false, // 是否显示分页
        limit:3,
      //   limits: [5, 10, 15],
      //   limit: 5, // 每页默认显示的数量
        parseData: function(res){ // res 即为原始返回的数据
         console.log('表格初始化了',res);
        },
        error: function(e, msg) {
          console.log('调用错误：',e, msg)
        }
      }
// 导出js
export default left_table2_options
