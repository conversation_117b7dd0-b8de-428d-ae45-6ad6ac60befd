﻿@{
    Html.AppendTitleParts(T("新增开放平台信息").Text);
}
<style>
    .containers {
        padding-top: 30px;
    }

    .layui-form-item.btn {
        text-align: center;
        padding-top: 10px;
    }

    .label-width span {
        color: #f00;
    }

    #inp {
        width: 200px;
        float: left;
    }

    #but1 {
        float: left
    }

    .layui-form-label {
        width: 120px;
    }

    .text-width2 {
        width: 250px;
    }

    .butsj {
        margin-left: 10px;
    }
</style>

<div class="containers">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label label-width">
                <span>*</span>@T("商户号")
            </label>
            <div class="layui-input-inline">
                <input type="text" name="AccessId" id="AccessId" autocomplete="off" class="layui-input text-width2" style="width:365px">
            </div>
        </div>


        <div class="layui-form-item">
            <label class="layui-form-label label-width">
                <span>*</span>@T("商户密钥")
            </label>
            <div class="layui-input-inline" style="width: 250px;">
                <input type="text" name="AccessKey" id="AccessKey" autocomplete="off" class="layui-input">
            </div>
            <input type="hidden" name="people" value="" />
            <div class="layui-input-inline" style="width: 100px;">
                <button type="button" class="layui-btn layui-btn-normal butsj">@T("生成密钥")</button>
            </div>
            <div class="layui-form-mid layui-word-aux"></div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width">
                <span>*</span>@T("项目名称")
            </label>
            <div class="layui-input-inline">
                <input type="text" name="ProjectName" id="ProjectName" autocomplete="off" class="layui-input text-width2" style="width:365px">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label-width">
                @T("备注")
            </label>
            <div class="layui-input-inline">
                <input type="text" name="Remark" id="Remark" autocomplete="off" class="layui-input text-width2" style="width:365px">
            </div>
        </div>

        @* <div class="layui-form-item">
            <label class="layui-form-label label-width">
                第三方服务器地址
            </label><div class="layui-input-inline">
                <input type="text" name="OfficialUrl" id="OfficialUrl" autocomplete="off" class="layui-input text-width2">
            </div><div class="layui-form-mid layui-word-aux"></div>
        </div> *@
        <div class="layui-form-item btn">
            <input type="hidden" value="0" name="ID" />
            <button type="button" class="layui-btn layui-btn-normal" lay-submit="" lay-filter="submit">@T("保存")</button>
        </div>
    </form>
</div>
<script asp-location="Footer">
    var active, layuiIndex;
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
            var r = layui.jquery,
                t = layui.form,
                n = layui.common,
                u = layui.table, i;
             var abp = layui.abp;
             var dg = layui.dg;


            t.render();

            t.on("submit(submit)", function (t) {
                $.post("@Url.Action("AddThirdParty")", $('form').serialize(), function (result) {
                    if (result.success) {
                        parent.layer.closeAll();
                        dg.reload('tables');
                    }
                    else{
                        abp.notify.warn(result.msg);
                    }
                });
            });


            $(".butsj").click(() => {
                var len = 32
                var rdmString = "";
                for (; rdmString.length < len; rdmString += Math.random().toString(36).substr(2));
                $("#AccessKey").val(rdmString.substr(0, len));
            })
        });
</script>
