﻿using DG;
using DG.Web.Framework;

using DH.Entity;
using DH.Models;
using HlktechIoT.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife.Data;
using Pek.Models;
using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>控制台</summary>
[DisplayName("控制台")]
[Description("后台登录之后的首页调用的默认区域")]
[AdminArea]
[DHMenu(90,ParentMenuName = "Home", CurrentMenuUrl = "~/{area}/Main", CurrentMenuName = "Main", LastUpdate = "20240124")]
public class MainController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 90;

    /// <summary>
    /// 控制台首页
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("控制台首页")]
    public IActionResult Index()
    {
        //NamedPipeClient? client = null;
        //try
        //{
        //    client = new NamedPipeClient("PeiKePrint", ".");

        //    var response = client.Request(new { SN = "12346" }.ToJson());
        //}
        //catch (Exception ex)
        //{
        //    XTrace.WriteException(ex);
        //}
        //finally
        //{
        //    client = null;
        //}

        //TcpClient? client = null;
        //try
        //{
        //    client = new TcpClient();
        //    await client.ConnectAsync("127.0.0.1", 12345);
        //    var ns = client.GetStream();

        //    // 接收服务端握手
        //    var buf = new Byte[1024];
        //    var count = await ns.ReadAsync(buf);
        //    XTrace.WriteLine("<={0}", buf.ToStr(null, 0, count));

        //    // 发送数据
        //    var str = new { SN = "12346" }.ToJson();
        //    XTrace.WriteLine("=>{0}", str);
        //    await ns.WriteAsync(str.GetBytes());
        //}
        //catch(Exception ex)
        //{
        //    XTrace.WriteException(ex);
        //}
        //finally
        //{
        //    client?.Close();
        //    client.TryDispose();
        //}
        var userDetail = UserDetail.FindById(ManageProvider.User.ID);
        if (MultiTenancyConfig.Current.IsEnabled && userDetail.TenantId > 0)
        {
            return View("TenantConsole");
        }

        return View("HostConsole");
    }

    /// <summary>
    /// 获取当前用户的消息列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult GetMessageList(Int32 page,Int32 limit)
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = Message._.Id,
            Desc = true,
        };
        var userId = ManageProvider.User?.ID ?? 0;
        var data = Message.Search(userId, null, DateTime.MinValue, DateTime.MinValue, "", pages).Select(e => new
        {
            e.Id,
            e.Title,
            e.Content,
            e.ReceiveUserID,
            ReceiveUserName = UserE.FindByID(e.ReceiveUserID)?.DisplayName,
            e.Status,
            e.CreateTime,
            e.CreateUser,
        });
        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 查看消息详情
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult ViewMessage(Int32 Id)
    {
        var model = Message.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("消息不存在"));
        }
        if (!model.Status)
        {
            model.Status = true;
            model.Update();
        }
        return View(model);
    }
}
