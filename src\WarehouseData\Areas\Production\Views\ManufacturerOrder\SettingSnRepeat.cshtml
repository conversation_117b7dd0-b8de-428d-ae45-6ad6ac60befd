﻿@{


    Html.AppendTitleParts(T("设置生产Sn").Text);

    Html.AppendCssFileParts(ResourceLocation.Head, "~/libs/pear/css/module/dtree/dtree.css");
    Html.AppendCssFileParts(ResourceLocation.Head, "~/libs/pear/css/module/dtree/font/dtreefont.css");

    var time = "";
    if (Model.IsRepetitionDate == DateTime.MinValue)
    {
        time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    }
    else
    {
        time = Model.IsRepetitionDate.ToString("yyyy-MM-dd HH:mm:ss");
    }
}

<style asp-location="true">
    .sex {
        min-width: 30px;
        /* margin-top:10px; */
        margin: 10px 0px 0px -20px;
    }
</style>

<div class="layui-tab layui-tab-brief" lay-filter="user-tab">
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show">
            <form class="layui-form" lay-filter="user-form" style="padding: 10px 0 0 0;" autocomplete="off">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:120px">@T("是否允许重复")</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" name="IsRepetition" lay-filter="IsRepetition" lay-skin="switch" @(Model.IsRepetition == true ? "checked" : "")>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:120px">@T("重复截止时间")</label>
                        <div class="layui-input-inline">
                            <input type="text" name="IsRepetitionDate" id="IsRepetitionDate" class="layui-input" readonly placeholder="@T("重复截止时间")">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <input type="hidden" name="Id" value="@Model.Id" />
                    <button class="pear-btn pear-btn-primary pear-btn-normal" lay-submit lay-filter="user-submit" id="user-submit" style="margin-left:40px">@T("提交")</button>
                </div>
            </form>
        </div>

    </div>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'dg', 'form', 'element', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var dg = layui.dg;
        var dtree = layui.dtree;
        var element = layui.element;
        var laydate = layui.laydate;

        //时间插件
        var startDate = laydate.render({
            elem: '#IsRepetitionDate',
            btns: ['clear', "confirm"],//只显示清空和确定按钮
            type: 'datetime',       // 设置日期选择类型为年月
            format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
            value:'@time',
            choose: function (date) {
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });

        // var index = parent.layer.getFrameIndex(window.name);
        // console.log("Index",index)
        // parent.layer.iframeAuto(index);

        var parentList = window.parent
        var parentPage = null;
        // 第二步：拿到对应层
        for (let i = 0; i < parentList.length; i++) {
            if (parentList[i].name === 'SnView') { //这里的name自己去对应层DIY
                parentPage = parentList[i]
                break;
            }
        }
        // 第三步：操作对应层
        var parent_window = parentPage.window  //获取父层的window层
        var parent_layer = parentPage.layer //获取父层的layer
        var parent_notify = parentPage.layui.abp.notify //获取父层的layui.notify --消息通知
        var parent_layui  = parentPage.layui  //获取父层的layui
        var currentPageCloseIndex = parent_window.settingPageIndex //当前层的关闭index下标



        form.on('submit(user-submit)', function (data) {
            data.field.IsRepetition = data.field.IsRepetition == "on";//switch默认值是on


            var waitIndex = parent.layer.load(2);

            var url = "@Url.Action("SettingSnRepeat")";

            abp.ajax({
                url: url,
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.message);
                    return false;
                }
                                            // 关闭当前编辑页面
                    parent.layer.close(currentPageCloseIndex);
                    parent_notify.success(data.msg);
                    parentPage.active.reload();
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;

        });

        element.on('tab(user-tab)', function (data) {
            parent.layer.iframeAuto(index);
        });

        window.submitForm = function () {
            $("#user-submit").click();
        }

        function getOrganizationUnitIds() {
            var selectedNode = dtree.getCheckbarNodesParam("organization-tree");
            var ids = selectedNode.map(function (d) { return d.nodeId });
            return ids;
        }

        function getRoleNames() {
            var roleNames = [];
            var _$roleCheckboxes = $("input[name='role']:checked");
            if (_$roleCheckboxes) {
                for (var roleIndex = 0; roleIndex < _$roleCheckboxes.length; roleIndex++) {
                    var _$roleCheckbox = $(_$roleCheckboxes[roleIndex]);
                    roleNames.push(_$roleCheckbox.val());
                }
            }
            return roleNames;
        }

    });
</script>