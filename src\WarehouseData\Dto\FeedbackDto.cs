﻿namespace HlktechIoT.Dto
{
    public class FeedbackDto
    {
        /// <summary>
        /// 反馈问题id
        /// </summary>
        public Int32 FeedbackId { get; set; }

        /// <summary>
        /// 产品型号id
        /// </summary>
        public Int32 ProductTypeId { get; set; }

        /// <summary>
        /// 生产订单id
        /// </summary>
        public Int64 ProductOrderId { get; set; }

        /// <summary>
        /// 生产订单号
        /// </summary>
        public String? OrderId { get; set; }

        /// <summary>
        /// 问题标题
        /// </summary>
        public String? Title { get; set; }

        /// <summary>
        /// 问题内容
        /// </summary>
        public String? Content { get; set; }

        /// <summary>
        /// 问题类型 0待分配 1软件 2硬件
        /// </summary>
        public Int32 DType { get; set; }

        /// <summary>
        /// 问题图片
        /// </summary>
        public List<IFormFile> Images { get; set; } = new List<IFormFile>();

        /// <summary>
        /// 问题视频
        /// </summary>
        public List<IFormFile> Videos { get; set; } = new List<IFormFile>();

        /// <summary>
        /// 问题文件
        /// </summary>
        public List<IFormFile> Files { get; set; } = new List<IFormFile>();
    }
}
