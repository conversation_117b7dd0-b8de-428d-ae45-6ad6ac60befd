﻿@using DG.Web.Framework
@using System.Diagnostics;
@using DH.AspNetCore.Webs

@{
    Layout = null;

    ViewBag.Title = "进程模块";

    var isAll = String.Equals("All", Context.Request.GetRequestValue("Mode"), StringComparison.OrdinalIgnoreCase);

    var process = Process.GetCurrentProcess();
    var list = new List<ProcessModule>();
    foreach (ProcessModule item in process.Modules)
    {
        try
        {
            if (isAll || item.FileVersionInfo.CompanyName != "Microsoft Corporation") { list.Add(item); }
        }
        catch { }
    }
}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit|ie-stand">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width" />
    @*<link href="/Content/bootstrap.min.css" rel="stylesheet" />

    <script src="/Scripts/jquery1111.min.js"></script>
    <script src="/Scripts/bootstrap.min.js"></script>*@

    <style>
        .container .table > tbody > tr > td {
            padding: 5px 5px;
        }
        .container .table {
            margin: 0;
            background-color: #fff;
            color: #494e52;
            font-size: 13px;
        }

        .table-bordered {
            border: 1px solid #ddd;
        }

        .table > tbody > tr > td {
            border-top: 0;
            border-bottom: 1px solid #e7e7e7;
        }

        .container .table > thead > tr > th, .table > tbody > tr > td {
            vertical-align: middle;
        }

        .table-bordered > tbody > tr > td, .table-bordered > tbody > tr > th, .table-bordered > tfoot > tr > td, .table-bordered > tfoot > tr > th, .table-bordered > thead > tr > td, .table-bordered > thead > tr > th {
            border: 1px solid #ddd;
        }

        .container .table thead tr th {
            border: 0;
            height: 45px;
            font-weight: 900 !important;
        }

        .table thead th:first-child {
            border-top-left-radius: 5px;
        }

        table {
            border-spacing: 0;
            border-collapse: collapse;
        }

        .table#list, .table.category_table, .table#applyList, .table#HandSlideDatagrid, .table#typeDatagrid, .table#shopDatagrid, .table#topicGrid, .table#listAutoReplay, .table#productList {
            margin-top: 5px;
            margin-bottom: 60px;
        }

        .table {
            width: 100%;
            max-width: 100%;
            margin-bottom: 20px;
        }

            .table thead {
                background-color: #f9f9f9;
                border-bottom: 1px solid #e7e7e7;
                border-top-left-radius: 15px !important;
                border-top-right-radius: 15px !important;
                color: #494e52;
                font-size: 14px;
            }
    </style>
</head>
<body>
    <div class="container" style="width:100%;">
        <div>
            <table class="table table-bordered table-hover table-striped table-condensed">
                <tr>
                    <th colspan="7">
                       进程模块(@process.ProcessName , PID=@process.Id )
                        @if (!isAll)
                        {
                            <text>（<a href="?Mode=All">完整</a>，仅用户）：</text>
                        }
                        else
                        {
                            <text>（完整，<a href="?Mode=OnlyUser">仅用户</a>）：</text>
                        }
                    </th>
                </tr>
                <tr>
                    <th>
                        @T("模块名称")
                    </th>
                    <th>
                        @T("公司名称")
                    </th>
                    <th>
                        @T("产品名称")
                    </th>
                    <th>
                        @T("描述")
                    </th>
                    <th>
                        @T("版本")
                    </th>
                    <th>
                        @T("大小")
                    </th>
                    <th>
                        @T("路径") 
                    </th>
                </tr>
                @foreach (ProcessModule item in list)
                {
                    <tr>
                        <td>
                            @item.ModuleName
                        </td>
                        <td>
                            @item.FileVersionInfo.CompanyName
                        </td>
                        <td>
                            @item.FileVersionInfo.ProductName
                        </td>
                        <td>
                            @item.FileVersionInfo.FileDescription
                        </td>
                        <td>
                            @item.FileVersionInfo.FileVersion
                        </td>
                        <td>
                            @item.ModuleMemorySize
                        </td>
                        <td>
                            @item.FileName
                        </td>
                    </tr>
                }
            </table>
        </div>
    </div>
</body>
</html>


