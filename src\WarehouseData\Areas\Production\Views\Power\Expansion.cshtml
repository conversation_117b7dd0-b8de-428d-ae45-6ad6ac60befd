﻿@{
    Html.AppendTitleParts(T("型号配置").Text);

    Int32 Id = ViewBag.Id;
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-input {
        height: 32px;
        line-height: 34px;
    }
</style>

<script asp-location="Head">
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetExpansionList", new { Id = Id })'
            , page: false //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 120
            , cols: [[
                 { field: 'Sort', title: ' @T("排序")', templet: function(d){
                    return '<input type="number" class="layui-input" name = "itemsort" style="width:60px" min="0" value="' + d.Sort + '" data-code="' + d.Code + '">';
                } }
                , { field: 'Name', title: ' @T("名称")' }
                , { field: 'Code', title: ' @T("Code")' }
                , { 
                    field: 'Value', title: ' @T("值")', templet: function(d){
                        return '<input type="number" class="layui-input" name = "itemvalue" style="width:60px" value="' + d.Value + '" data-code="' + d.Code + '">';
                    }
                 }
                , { field: 'Unit', title: ' @T("单位")' }
                , { field: 'Remark', title: ' @T("备注")' }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool' }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'tables'
        });

        window.active = {
            reload: function () {
                table.reload('tables',
                    {
                        where: {
                            name: $("#name").val(),
                        }
                    });
            }
        }

     

        $("#name").on("input", function (e) {
            active.reload();
        });

        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', function (index) {
                    //console.log(data.Id);
                    $.post('@Url.Action("DeleteExItem")', { Id: '@ViewBag.Id', Code: data.Code }, function (data) {
                        if (data.success) {
                            abp.notify.success('@T("删除成功")');
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            }
        });

        table.on('toolbar(tool)', function (obj) {
            if (obj.event === 'select') {
                window.select();
            } else if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                active.reload();
            }
        });

        window.select = function () {
            layer.open({
                type: 2,
                title: "@T("选择配置项")",
                content: "@Url.Action("SelectItems", new { Id = Id })",
                area: ["460px", "336px"],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    window['layui-layer-iframe' + index].submitForm();
                }
            });
        }

        window.add = function () {
            layer.open({
                type: 2,
                title: "@T("新增配置")",
                content: "@Url.Action("AddExItem",new {Id = Id})",
                area: ["460px", "336px"],
                shade: 0.1,
                btn: ['@T("确定")', '@T("取消")'],
                yes: function (index, layero) {
                    window['layui-layer-iframe' + index].submitForm();
                }
            });
        }

        window.warning = function (msg) {
            abp.notify.warn(msg);
        }

        window.saveCallback = function (data) {
            layer.close(data.index);
            abp.notify.success(data.msg);
            active.reload();
        }

    });

    $(document).on('change', 'input[name="itemsort"]', function(){
        var code = $(this).data('code');
        var newValue = $(this).val();
        //提交到后端
        $.post('@Url.Action("UpdateConfigSort")' + '?Id=@ViewBag.Id&Code=' + code + '&Sort=' + newValue, function(res){
            if(res.success){
                active.reload();
            }else{
                abp.notify.warn(res.msg);
            }
        });
    });

    $(document).on('change', 'input[name="itemvalue"]', function(){
        var code = $(this).data('code');
        var value = $(this).val();
        //提交到后端
        $.post('@Url.Action("UpdateConfigValue")' + '?Id=@ViewBag.Id&Code=' + code + '&Value=' + value, function(res){
            if(res.success){
                active.reload();
            }else{
                abp.notify.warn(res.msg);
            }
        });
    });
</script>

<script type="text/html" id="tool">
    <a class="layui-btn layui-btn-xs" lay-event="del"> @T("删除")</a>
</script>

<script type="text/html" id="user-toolbar">
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="select">
        <i class="layui-icon layui-icon-edit"></i>
        @T("选择")
    </button>
</script>