﻿/**
 * 初始化连接
 * @param {object} _option 参数
 * @param {string} _option.url 连接的url地址
 * @param {string} _option.loggingLevel 日志级别,默认为 Error
 * @param {number} _option.delay 延迟连接 默认为3000毫秒
 * @param {function} _option.onStarted 启动时触发
 * @param {function} _option.onLine 启动时触发
 * @param {function} _option.offLine 启动时触发
 * @returns {object} 连接的实例
 */
var option = null;
function initSignalr(_option) {
    option = _option
    var config = Object.assign(true, {
        loggingLevel: signalR.LogLevel.Error,
        delay: 3000,
        url: ''
    }, option);

    var connection = new signalR.HubConnectionBuilder()
        .configureLogging(config.loggingLevel)
        .withUrl(config.url, {
            accessTokenFactory: () => config.accessTokenFactory
        })
        .withHubProtocol(new signalR.protocols.msgpack.MessagePackHubProtocol())
        .withAutomaticReconnect([0, 2000, 5000, 10000, 20000, 60000])
        .build();

    connection.onreconnecting(function (info) {
        console.log('正在重连');
        console.info('----------------------------------signalr-- onreconnecting', info);
    });

    connection.onclose(function (err) {
        console.log('关闭'); //走2
        checkTheNetwork()
        console.info('--------------------------------signalr-- onclose', err);
    });

    connection.on('OnNotify', config.onNotify);

    connection.on('OnLine', config.onLine);

    connection.on('OffLine', config.offLine);

    setTimeout(function () {
        connection.start().then(function (data) {
            option.onStarted && option.onStarted(data);
            console.log("连接成功");
        }).catch(function (error) {
            console.log('连接失败：',error);
            checkTheNetwork()
            console.error(error.toString());
        });
    }, option.delay);
    return connection;
}



/** 02.发送检查网络请求 */
const checkTheNetwork = async ()=>{
    var currentUrl = window.location.href.split("/bigData")[0];  //获取当前url
    createRequest(`${currentUrl}/health`);
    
}

/**01.创建ajax请求，等网络连接失败的时候，发送看看是否能够重连 */ 
function createRequest(url) {
    // 创建一个新的XMLHttpRequest对象
    var xhr = new XMLHttpRequest();
    
    // 配置请求，这里假设你要发送GET请求到"https://www.example.com"
    xhr.open('GET', url, true);
    
    // 发送请求
    xhr.send();
    
    // 监听请求状态变化
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                // 请求成功，返回true
                window.location.reload();
            } else {
                // 网络错误，返回false
                // console.log('重新执行');
                option.delay = 10000
                initSignalr(option)//重新调用当前函数
            }
        }
    };
}