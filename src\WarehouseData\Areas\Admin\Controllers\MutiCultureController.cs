﻿using DG.Web.Framework;

using DH.Entity;
using DH.Models;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;
using Pek.Models;
using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>基础数据管理</summary>
[DisplayName("语言管理")]
[Description("语言管理")]
[AdminArea]
[DHMenu(90,ParentMenuName = "System", CurrentMenuUrl = "", CurrentMenuName = "Language", LastUpdate = "20240124")]
public class LanguageController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 90;
}

/// <summary>
/// 多语言
/// </summary>
[DisplayName("多语言")]
[Description("用于系统多语言本地化管理")]
[AdminArea]
[DHMenu(96,ParentMenuName = "Language", ParentMenuDisplayName = "语言管理", CurrentMenuUrl = "~/{area}/MutiCulture", CurrentMenuName = "MutiCulture", LastUpdate = "20240124")]
public class MutiCultureController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 96;

    /// <summary>多语言列表</summary>
    /// <returns></returns>
    [DisplayName("多语言列表")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 多语言列表接口
    /// </summary>
    /// <returns></returns>
    [DisplayName("多语言列表接口")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult List(int page, int limit)
    {
        var pages = new PageParameter();
        pages.PageIndex = page;
        pages.PageSize = limit;
        pages.RetrieveTotalCount = true;

        var result = Language.GetWithPage(pages).Select(item => new { item.Id, item.Name, item.DisplayName, item.EnglishName, item.LanguageCulture, item.UniqueSeoCode, item.Status, item.IsDefault });
        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data = result.ToList() });
    }

    /// <summary>添加语言</summary>
    /// <returns></returns>
    [DisplayName("添加语言")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult Add()
    {
        var model = new Language();
        model.DisplayOrder = 34;
        return View(model);
    }

    /// <summary>添加语言</summary>
    /// <returns></returns>
    [DisplayName("添加语言")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult Add(Language model)
    {
        if (model.Name.IsNullOrWhiteSpace())
            return Json(new { success = false, msg = GetResource("标题不能为空") });

        var modelCulture = new Language();
        modelCulture.Name = model.Name;
        modelCulture.DisplayName = model.DisplayName;
        modelCulture.EnglishName = model.EnglishName;
        modelCulture.LanguageCulture = model.LanguageCulture;
        modelCulture.UniqueSeoCode = model.UniqueSeoCode;
        modelCulture.Flag = model.Flag;
        modelCulture.Domain = model.Domain;
        modelCulture.Lcid = model.Lcid;
        modelCulture.Status = model.Status;
        modelCulture.DisplayOrder = model.DisplayOrder;
        modelCulture.Remark = model.Remark;
        modelCulture.IsDefault = model.IsDefault;
        modelCulture.Insert();
        return Json(new DResult { success = true, msg = GetResource("添加成功") });
    }

    /// <summary>修改语言</summary>
    /// <returns></returns>
    [DisplayName("修改语言")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Edit(Int32 id)
    {
        var model = Language.FindById(id);
        ViewBag.DefaultCulture = Language.FindByDefault().Id;
        return View(model);
    }

    /// <summary>修改语言</summary>
    /// <returns></returns>
    [DisplayName("修改语言")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Edit(Language model)
    {
        if (model.Name.IsNullOrWhiteSpace())
            return Json(new { success = false, msg = GetResource("标题不能为空") });

        var modelCulture = Language.FindById(model.Id);
        modelCulture.Name = model.Name;
        modelCulture.DisplayName = model.DisplayName;
        modelCulture.EnglishName = model.EnglishName;
        modelCulture.LanguageCulture = model.LanguageCulture;
        modelCulture.UniqueSeoCode = model.UniqueSeoCode;
        modelCulture.Flag = model.Flag;
        modelCulture.Domain = model.Domain;
        modelCulture.Lcid = model.Lcid;
        modelCulture.Status = model.Status;
        modelCulture.DisplayOrder = model.DisplayOrder;
        modelCulture.Remark = model.Remark;
        modelCulture.IsDefault = model.IsDefault;
        modelCulture.Update();

        return Json(new DResult { success = true, msg = GetResource("修改成功") });
    }

    /// <summary>删除语言</summary>
    /// <returns></returns>
    [DisplayName("删除语言")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult Delete(Int32 Id)
    {
        using (var tran1 = Language.Meta.CreateTrans())
        {
            Language.Delete(Language._.Id == Id);

            LocaleStringResource.Delete(LocaleStringResource._.CultureId == Id);

            tran1.Commit();
        }
        Language.Meta.Cache.Clear("", true); //清除缓存
        LocaleStringResource.Meta.Cache.Clear("", true);
        return Json(new DResult() { success = true, msg = GetResource("删除成功") });
    }

}