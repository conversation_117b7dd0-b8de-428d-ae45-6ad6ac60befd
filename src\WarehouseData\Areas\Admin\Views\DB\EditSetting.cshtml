﻿@using XCode.DataAccessLayer
@{
    Html.AppendTitleParts(T("编辑数据库链接").Text);

    var DBType = (DatabaseType)Model!.DBType;
}
<style asp-location="true">
    .width {
        width: 230px
    }

    .layui-form-item {
        margin-bottom: 10px;
    }

    .layui-form-select {
        width: 230px
    }

        .layui-form-select .layui-edge {
            right: 10px;
        }

    .layui-input-inline.inlineSelect {
        width: 230px;
    }
</style>

<form class="layui-form form-cus" lay-filter="">
    <div class="layui-form-item" style="margin-top:20px;">
        <div class="layui-inline">
            <label class="layui-form-label">@T("类型")</label>
            <div class="layui-input-inline inlineSelect">
                <select name="DBType" lay-verify="required" lay-verType="tips">
                    <option value="">@T("请选择")</option>
                    <!option value="SQLite" @(DBType == DatabaseType.SQLite ? Html.Raw("selected") : Html.Raw(""))>SQLite</!option>
                    <!option value="MySql" @(DBType == DatabaseType.MySql ? Html.Raw("selected") : Html.Raw(""))>MySql</!option>
                    <!option value="PostgreSQL" @(DBType == DatabaseType.PostgreSQL ? Html.Raw("selected") : Html.Raw(""))>PostgreSQL</!option>
                    <!option value="SqlServer" @(DBType == DatabaseType.SqlServer ? Html.Raw("selected") : Html.Raw(""))>SqlServer</!option>
                </select>
            </div>
        </div>

        <div class="layui-inline">
            <label class="layui-form-label">@T("链接"):</label>
            <div class="layui-input-inline">
                <input type="text" name="ConnStr" lay-verify="required" lay-verType="tips" value="@Model!.ConnStr" autocomplete="off" class="layui-input width">
            </div>
        </div>
    </div>

    <div class="layui-form-item" style="text-align:center;">
        <div class="layui-inline">
            <input type="hidden" name="Name" value="@Model!.Name" />
            <button type="button" lay-submit class="pear-btn pear-btn-primary" lay-filter="Submit"><i class="layui-icon layui-icon-loading layui-icon layui-anim layui-anim-rotate layui-anim-loop layui-hide"></i>@T("确定")</button>
            <button type="reset" class="layui-btn btn-open-close" style="border: 1px solid #C9C9C9;background-color: #fff !important;color: #555; margin-left: 20px;">@T("取消")</button>
        </div>
    </div>
</form>

<script asp-location="Footer">
    layui.use(['abp', 'dg', 'form'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var dg = layui.dg;
        var index = parent.layer.getFrameIndex(window.name); //获取窗口索引

        form.on('submit(Submit)', function (data) {
            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("EditSetting")",
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                parent.active.reload();
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;

        });

        $(".btn-open-close").on('click', function () {
            parent.layer.close(index);
        });

    });
</script>