﻿using DG.Web.Framework;

using DH.Core.Infrastructure;
using DH.Entity;
using DH.SignalR;
using DH.SignalR.Dtos;

using HlktechIoT.Dto;
using HlktechIoT.Dto.Export;
using HlktechIoT.Entity;
using HlktechIoT.SignalR;

using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;

using NewLife;
using NewLife.Data;
using NewLife.Log;

using Pek;
using Pek.Models;
using Pek.Timing;

using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Wms.Controllers;

/// <summary>订单列表</summary>
[DisplayName("订单列表")]
[Description("对接ERP的订单管理")]
[WmsArea]
[DHMenu(90,ParentMenuName = "OrdersManager", ParentMenuDisplayName = "订单管理", ParentMenuUrl = "", ParentMenuOrder = 50, ParentIcon = "layui-icon-component", CurrentMenuUrl = "~/{area}/Orders", CurrentMenuName = "OrdersList", LastUpdate = "20240420")]
public class OrdersController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 90;

    /// <summary>
    /// 订单列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("订单列表")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <param name="key">订单号</param>
    /// <param name="UId">搜索用户Id</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="progress">进度</param>
    /// <param name="status">订单状态</param>
    /// <param name="hasRemark">有无备注</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("设备列表")]
    public IActionResult GetList(String key, Int32 UId, DateTime start, DateTime end, Int32 progress = -1, Int32 status = -1, Int16 hasRemark = -1, Int32 page = 1, Int32 limit = 10)
    {
        if (!key.IsNullOrWhiteSpace()) key = key.SafeString().Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsOrder._.Id,
            Desc = true,
        };

        if (start <= DateTime.MinValue)
        {
            start = (DateTime.Now.Year + "-1" + "-1").ToDateTime();
        }

        if (end <= DateTime.MinValue)
        {
            end = DateTime.Now.AddDays(1).ToShortDateString().ToDateTime();
        }

        key = key.SafeString().Trim();

        //var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        //if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",001,")))
        //{
        //    UId = ManageProvider.User?.ID ?? 0;
        //}

        var list = (List<WmsOrder>)WmsOrder.Search(UId, start, end, key, progress, status, hasRemark, pages);

        var data = list.Select(x => new {
            x.Id,
            x.OrderId,
            OrderingUser = x.OrderingUser?.DisplayName,
            x.OrderingTime,
            PickingUser = x.PickingUser?.DisplayName,
            x.PickingTime,
            ProductionUser = x.ProductionUser?.DisplayName,
            x.ProductionTime,
            AuditingUser = x.AuditingUser?.DisplayName,
            x.AuditingTime,
            ShippingUser = x.ShippingUser?.DisplayName,
            x.ShippingTime,
            PackUser = x.PackUser?.DisplayName,
            x.PackTime,
            IsDuplicate = x.IsDuplicate ? GetResource("是") : GetResource("否"),
            x.IsEnd,
            SpecificState = x.Status == 1 ? "已取消" : (x.IsEnd ? "已完结" : (x.Status == 0 ? "正常" : null))
        });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>搜索用户</summary>
    /// <returns></returns>
    [DisplayName("搜索用户")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult SearchUser(String keyword, Int32 page)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = UserE._.ID,
            Desc = true,
        };

        var departmentId = DepartmentEx.GetOrAdd("仓储部").ID;

        res.data = UserE.Searchs(false, pages, departmentId, keyword).Select(e =>
        {
            return new Xmselect<Int32>
            {
                name = e.DisplayName,
                value = e.ID
            };
        });

        res.success = true;

        res.extdata = new { pages.PageCount };

        return Json(res);
    }

    /// <summary>
    /// 查看日志
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("查看日志")]
    public IActionResult Logs(String orderId, DateTime dTime)
    {
        if (orderId.IsNullOrWhiteSpace())
        {
            return Content(GetResource("订单编号不能为空"));
        }

        //using var split = WmsOrder.Meta.CreateSplit("DH", $"DH_WmsOrder_{dTime.Year.SafeString().Right(2)}");
        var model = WmsOrder.FindByOrderIdWithTime(orderId, dTime);
        if (model == null)
        {
            return Content(GetResource("订单不存在"));
        }

        return View(model);
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <param name="orderId">订单号</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <param name="dTime"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("查看日志")]
    public IActionResult GetLogList(String orderId, DateTime dTime, Int32 page = 1, Int32 limit = 10)
    {
        //using var split = WmsOrder.Meta.CreateSplit("DH", $"DH_WmsOrder_{dTime.Year.SafeString().Right(2)}");
        var model = WmsOrder.FindByOrderIdWithTime(orderId, dTime);
        if (model == null) return Json(new { code = 0, msg = "success", count = 0 });

        if (!orderId.IsNullOrWhiteSpace()) orderId = orderId.SafeString().Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = WmsOrderLogs._.Id,
            Desc = true,
        };

        //using var split1 = WmsOrderLogs.Meta.CreateSplit("DH", $"DH_WmsOrderLogs_{orderId.Mid(2, 2)}");
        var list = WmsOrderLogs.Search(orderId, dTime, String.Empty, pages);

        var data = list.Select(x => new { x.Id, x.OrderId, Process = (x.Process == 0 ? "" : GetResource(((HardwareStatus)x.Process).ToString())), x.CreateUser, x.CreateTime });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>导出</summary>
    /// <param name="Ids">勾选中的ID</param>
    /// <param name="key">订单号</param>
    /// <param name="UId">搜索用户Id</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="progress">进度</param>
    /// <param name="status">订单状态</param>
    /// <param name="hasRemark">是否有备注</param>
    /// <returns></returns>
    [DisplayName("导出")]
    [EntityAuthorize((PermissionFlags)32)]
    public async Task<IActionResult> ExportAll(String Ids, String key, Int32 UId, DateTime start, DateTime end, Int32 progress = -1, Int32 status = -1, Int16 hasRemark = -1)
    {
        IExporter exporter = new ExcelExporter();
        List<OrderExport>? list = null;

        //var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
        //if (!modelRole.IsAdmin && (modelRole.Roles.IsNullOrWhiteSpace() || !modelRole.Roles.Contains(",001,")))
        //{
        //    UId = ManageProvider.User?.ID ?? 0;
        //}

        if (string.IsNullOrWhiteSpace(Ids))
        {
            var data = WmsOrder.Search(UId, start, end, key, progress, status, hasRemark, null);
            list = data.Select(e => new OrderExport()
            {
                OrderId = e.OrderId,
                OrderingUser = e.OrderingUser?.DisplayName,
                OrderingTime = e.OrderingTime <= DateTime.MinValue ? null : e.OrderingTime,
                PickingUser = e.PickingUser?.DisplayName,
                PickingTime = e.PickingTime <= DateTime.MinValue ? null : e.PickingTime,
                ProductionUser = e.ProductionUser?.DisplayName,
                ProductionTime = e.ProductionTime <= DateTime.MinValue ? null : e.ProductionTime,
                AuditingUser = e.AuditingUser?.DisplayName,
                AuditingTime = e.AuditingTime <= DateTime.MinValue ? null : e.AuditingTime,
                PackUser = e.PackUser?.DisplayName,
                PackTime = e.PackTime <= DateTime.MinValue ? null : e.PackTime,
                SpecificState = e.Status == 1 ? GetResource("已取消") : (e.IsEnd ? GetResource("已完结") : (e.Status == 0 ? GetResource("正常") : null)),
                Remark = e.Remark
            }).ToList();
        }
        else
        {
            var things = WmsOrder.FindAllByIds(Ids);
            list = things.Select(e => new OrderExport()
            {
                OrderId = e.OrderId,
                OrderingUser = e.OrderingUser?.DisplayName,
                OrderingTime = e.OrderingTime <= DateTime.MinValue ? null : e.OrderingTime,
                PickingUser = e.PickingUser?.DisplayName,
                PickingTime = e.PickingTime <= DateTime.MinValue ? null : e.PickingTime,
                ProductionUser = e.ProductionUser?.DisplayName,
                ProductionTime = e.ProductionTime <= DateTime.MinValue ? null : e.ProductionTime,
                AuditingUser = e.AuditingUser?.DisplayName,
                AuditingTime = e.AuditingTime <= DateTime.MinValue ? null : e.AuditingTime,
                PackUser = e.PackUser?.DisplayName,
                PackTime = e.PackTime <= DateTime.MinValue ? null : e.PackTime,
                SpecificState = e.Status == 1 ? GetResource("已取消") : (e.IsEnd ? GetResource("已完结") : (e.Status == 0 ? GetResource("正常") : null)),
                Remark = e.Remark
            }).ToList();
        }
        var result = await exporter.ExportAsByteArray(list);
        return File(result, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"ExportOrder{DateTime.Now:yyyyMMddhhmm}.xlsx");
    }

    /// <summary>
    /// 刷新投屏
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)64)]
    [DisplayName("刷新投屏")]
    [HttpPost]
    public async Task<IActionResult> Refresh()
    {
        var _tracer = EngineContext.Current.Resolve<ITracer>();
        using var span = _tracer?.NewSpan("Refresh");

        var res = new DResult();

        var _notifyHub = EngineContext.Current.Resolve<IHubContext<BigDataHub, IClientNotifyHub>>();
        var model = new NotifyConnectsData()
        {
            UserId = Sid,
            TenantType = "Refresh",
        };
        //await _notifyHub.Clients.Group("bigdata_index").OnNotify(model);
        await _notifyHub.Clients.All.OnNotify(model);

        res.success = true;
        res.msg = GetResource("投屏成功");
        return Json(res);
    }

    /// <summary>
    /// 调测推送
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)128)]
    [DisplayName("调测推送")]
    public IActionResult TestPush(String orderId, DateTime dTime)
    {
        if (orderId.IsNullOrWhiteSpace())
        {
            return Content(GetResource("订单编号不能为空"));
        }

        //using var split = WmsOrder.Meta.CreateSplit("DH", $"DH_WmsOrder_{dTime.Year.SafeString().Right(2)}");

        var model = WmsOrder.FindByOrderIdWithTime(orderId, dTime);
        if (model == null)
        {
            return Content(GetResource("订单不存在"));
        }

        return View(model);
    }

    /// <summary>
    /// 调测推送
    /// </summary>
    /// <param name="orderId">订单号</param>
    /// <param name="ProcessTime">操作时间</param>
    /// <param name="UName">用户姓名</param>
    /// <param name="PType">状态类型，1为已下单，2为已领料，3为生产中，4为生产审核，5为打包完成</param>
    /// <param name="dTime">打单时间</param>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)128)]
    [DisplayName("调测推送")]
    [HttpPost]
    public async Task<IActionResult> TestPush(String orderId, String UName, DateTime ProcessTime, Int32 PType, DateTime dTime)
    {
        var _tracer = EngineContext.Current.Resolve<ITracer>();
        using var span = _tracer?.NewSpan("Refresh", orderId);

        var res = new DResult();

        if (orderId.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("订单编号不能为空");
            return Json(res);
        }

        if (PType == 0)
        {
            res.msg = GetResource("请选择状态类型");
            return Json(res);
        }

        //using var split = WmsOrder.Meta.CreateSplit("DH", $"DH_WmsOrder_{dTime.Year.SafeString().Right(2)}");

        var modelWmsOrder = WmsOrder.FindByOrderIdWithTime(orderId, dTime);
        if (modelWmsOrder == null)
        {
            res.msg = GetResource("订单不存在");
            return Json(res);
        }

        var _notifyHub = EngineContext.Current.Resolve<IHubContext<BigDataHub, IClientNotifyHub>>();

        NotifyConnectsData? modelNotify = null;
        if (PType == 1)
        {
            // 已下单
            modelNotify = new NotifyConnectsData()
            {
                UserId = Sid,
                TenantType = "Ordering",
                NotifyObj = new { OrderId = orderId, User = UName, ProcessTime, ManHour = DateTimeUtil.BusinessDateFormat(ProcessTime), Status = LocaleStringResource.GetResource("已打单") },
            };
        }
        else if (PType == 2)
        {
            // 已领料
            modelNotify = new NotifyConnectsData()
            {
                UserId = Sid,
                TenantType = "Picking",
                NotifyObj = new { OrderId = orderId, User = UName, ProcessTime, ManHour = DateTimeUtil.BusinessDateFormat(ProcessTime), Status = LocaleStringResource.GetResource("已领料") },
            };
        }
        else if (PType == 3)
        {
            // 生产中
            modelNotify = new NotifyConnectsData()
            {
                UserId = Sid,
                TenantType = "Production",
                NotifyObj = new { OrderId = orderId, User = UName, ProcessTime, ManHour = DateTimeUtil.BusinessDateFormat(ProcessTime), Status = LocaleStringResource.GetResource("生产中") },
            };
        }
        else if (PType == 4)
        {
            // 生产审核
            modelNotify = new NotifyConnectsData()
            {
                UserId = Sid,
                TenantType = "Pack",  // 待打包
                NotifyObj = new { OrderId = orderId, User = UName, ProcessTime, ManHour = DateTimeUtil.BusinessDateFormat(ProcessTime), Status = LocaleStringResource.GetResource("生产审核") },
            };
        }
        else if (PType == 5)
        {
            // 打包完成
            modelNotify = new NotifyConnectsData()
            {
                UserId = Sid,
                TenantType = "Finish",  // 完成
                NotifyObj = new { OrderId = orderId },
            };
        }

        if (modelNotify != null)
            await _notifyHub.Clients.Group("bigdata_index").OnNotify(modelNotify);
        else
        {
            res.msg = GetResource("参数有误");
            return Json(res);
        }

        res.success = true;
        res.msg = GetResource("推送成功");
        return Json(res);
    }

    /// <summary>
    /// 完结订单
    /// </summary>
    /// <param name="orderId">订单编号</param>
    /// <param name="dTime">打单时间</param>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)256)]
    [DisplayName("完结订单")]
    [HttpPost]
    public IActionResult EndOrder(String orderId, DateTime dTime)
    {
        var _tracer = EngineContext.Current.Resolve<ITracer>();
        using var span = _tracer?.NewSpan("EndOrder", orderId);

        var res = new DResult();

        //using var split = WmsOrder.Meta.CreateSplit("DH", $"DH_WmsOrder_{dTime.Year.SafeString().Right(2)}");

        var modelWmsOrder = WmsOrder.FindByOrderIdWithTime(orderId, dTime);
        if (modelWmsOrder == null)
        {
            res.msg = GetResource("订单不存在");
            return Json(res);
        }

        modelWmsOrder.PackTime = DateTime.Now;
        modelWmsOrder.PackID = ManageProvider.User?.ID ?? 0;
        modelWmsOrder.IsEnd = true;

        var num = modelWmsOrder.Update();

        if (num == 0)
        {
            using var split = WmsOrder.Meta.CreateSplit("DH", $"DH_WmsOrder_{modelWmsOrder.OrderId.Mid(2, 2)}");
            var m = new WmsOrder();
            m.Copy(modelWmsOrder);
            m.Update();
        }

        var modelWmsOrderLogs = new WmsOrderLogs();
        modelWmsOrderLogs.OrderId = orderId;
        modelWmsOrderLogs.Process = 6;
        modelWmsOrderLogs.Remark = GetResource("订单完结");
        modelWmsOrderLogs.Insert();

        res.success = true;
        res.msg = GetResource("处理成功");
        return Json(res);
    }

    /// <summary>
    /// 取消订单
    /// </summary>
    /// <param name="orderId">订单编号</param>
    /// <param name="dTime">打单时间</param>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)512)]
    [DisplayName("取消订单")]
    [HttpPost]
    public IActionResult CancelOrder(String orderId, DateTime dTime)
    {
        var _tracer = EngineContext.Current.Resolve<ITracer>();
        using var span = _tracer?.NewSpan("CancelOrder", orderId);

        var res = new DResult();

        //using var split = WmsOrder.Meta.CreateSplit("DH", $"DH_WmsOrder_{dTime.Year.SafeString().Right(2)}");

        var modelWmsOrder = WmsOrder.FindByOrderIdWithTime(orderId, dTime);
        if (modelWmsOrder == null)
        {
            res.msg = GetResource("订单不存在");
            return Json(res);
        }

        modelWmsOrder.CancelTime = DateTime.Now;
        modelWmsOrder.CancelID = ManageProvider.User?.ID ?? 0;
        modelWmsOrder.IsEnd = true;
        modelWmsOrder.Status = 1;
        var num = modelWmsOrder.Update();

        if (num == 0)
        {
            using var split = WmsOrder.Meta.CreateSplit("DH", $"DH_WmsOrder_{(dTime.Year - 1).SafeString().Right(2)}");
            var m = new WmsOrder();
            m.Copy(modelWmsOrder);
            m.Update();
        }

        var modelWmsOrderLogs = new WmsOrderLogs();
        modelWmsOrderLogs.OrderId = orderId;
        modelWmsOrderLogs.Process = 7;
        modelWmsOrderLogs.Remark = GetResource("订单取消");
        modelWmsOrderLogs.Insert();

        res.success = true;
        res.msg = GetResource("处理成功");
        return Json(res);
    }

    /// <summary>
    /// 设置
    /// </summary>
    /// <param name="orderId">订单编号</param>
    /// <param name="dTime">时间</param>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)1024)]
    [DisplayName("设置")]
    public IActionResult Settings(String orderId, DateTime dTime)
    {
        if (orderId.IsNullOrWhiteSpace())
        {
            return Content(GetResource("订单编号不能为空"));
        }

        //using var split = WmsOrder.Meta.CreateSplit("DH", $"DH_WmsOrder_{dTime.Year.SafeString().Right(2)}");

        var model = WmsOrder.FindByOrderIdWithTime(orderId, dTime);
        if (model == null)
        {
            return Content(GetResource("订单不存在"));
        }

        return View(model);
    }

    /// <summary>
    /// 设置
    /// </summary>
    /// <param name="orderId">订单编号</param>
    /// <param name="Remark">备注</param>
    /// <param name="dTime">时间</param>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)1024)]
    [DisplayName("设置")]
    [HttpPost]
    public IActionResult Settings(String orderId, String Remark, DateTime dTime)
    {
        var _tracer = EngineContext.Current.Resolve<ITracer>();
        using var span = _tracer?.NewSpan("Settings", orderId);

        var res = new DResult();

        //using var split = WmsOrder.Meta.CreateSplit("DH", $"DH_WmsOrder_{dTime.Year.SafeString().Right(2)}");

        var modelWmsOrder = WmsOrder.FindByOrderIdWithTime(orderId, dTime);
        if (modelWmsOrder == null)
        {
            res.msg = GetResource("订单不存在");
            return Json(res);
        }

        if (Remark.IsNullOrWhiteSpace())
        {
            modelWmsOrder.HasRemark = false;
        }
        else
        {
            modelWmsOrder.HasRemark = true;
        }
        modelWmsOrder.Remark = Remark;
        modelWmsOrder.RemarkTime = DateTime.Now;
        modelWmsOrder.Update();

        var modelWmsOrderLogs = new WmsOrderLogs
        {
            OrderId = orderId,
            Process = 8,
            Remark = GetResource("订单设置")
        };
        modelWmsOrderLogs.Insert();

        res.success = true;
        res.msg = GetResource("设置成功");
        return Json(res);
    }
}
