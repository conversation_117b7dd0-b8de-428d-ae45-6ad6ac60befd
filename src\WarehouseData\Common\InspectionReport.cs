﻿using iTextSharp.text.pdf;
using iTextSharp.text;
using NewLife.Log;
using System.Text;
using DH;

using HlktechIoT.Dto;
using Pek;
using iTextSharp.text.pdf.security;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Pkcs;
using Org.BouncyCastle.X509;
using NewLife.Serialization;
using Pek.Helpers;

namespace HlktechIoT.Common
{
    public class InspectionReport
    {
        private static readonly object _certLock = new object();  // 新增锁对象

        public static void CreateInspectionReport(AS201InspectionReport report, string name,string modeltype, out string data)
        {
            var set = new IoTSetting();
            // 注册编码提供程序
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
            var saveFileName = Path.Combine(DHSetting.Current.UploadPath, "Products", modeltype, $"{name}.pdf");
            var signedFileName = Path.Combine(DHSetting.Current.UploadPath, "Products", modeltype, $"{name}_signed.pdf");
            // 创建 PDF 文档
            Document document = new Document(new iTextSharp.text.Rectangle(PageSize.A4), 15, 25, 25, 10);
            int fullRowCount = 0;
            try
            {
                // 确保输出目录存在
                Directory.CreateDirectory(Path.GetDirectoryName(saveFileName));

                // 创建 PDF 写入器
                PdfWriter.GetInstance(document, new FileStream(saveFileName, FileMode.Create));

                // 打开文档
                document.Open();

                XTrace.WriteLine("开始生成PDF文件");
                string baseDir = AppDomain.CurrentDomain.BaseDirectory;
                // 使用系统字体 SimSun
                string fontPath = Path.Combine(DHSetting.Current.WebRootPath, "fonts", "simhei.ttf");
                BaseFont baseFont = BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                Font font18 = new Font(baseFont, 18, Font.BOLD);
                Font font = new Font(baseFont, 10, Font.NORMAL);

                // 添加标题
                AddTitle(document, "质检报告\n Factory Inspection Report", font18);

                // 添加表格
                AddTable(document, report, font,out fullRowCount);

                // 添加尾部落款
                AddFooter(document, "深圳市海凌科电子有限公司\nShenzhen Hilink Electronics Co., Ltd", font);
                Dictionary<string, string> linkDict = new Dictionary<string, string>();
                linkDict.Add("App下载", "https://www.hlktech.com/Mobile/App/12.html");
                linkDict.Add("产品手册", "https://r0.hlktech.com/download/HLK-AS201/1/HLK-AS201十轴姿态传感器模块说明书V1.0.pdf");
                //链接
                AddLink(document, linkDict, font);

                data = report.ToJson();

            }
            catch (Exception ex)
            {
                XTrace.WriteLine($"异常：{ex.Message}");
                throw;
            }
            finally
            {
                // 关闭文档
                document.Close();
            }
            // 添加加密签章
            AddDigitalSignature(saveFileName, signedFileName, set.CertPath, set.CertPathPassword, set.SignImagePath, fullRowCount);
        }
        private static void AddTableCells(PdfPTable table, string text, Font font, int cosSpan = 1, float fixedHeight = 30f)
        {
            PdfPCell textCell = new PdfPCell(new Phrase(text, font));
            textCell.Border = PdfPCell.BOX;
            textCell.BorderColor = BaseColor.BLACK; // 边框颜色
            textCell.VerticalAlignment = Element.ALIGN_CENTER;
            if (cosSpan == 1)
            {
                textCell.HorizontalAlignment = Element.ALIGN_CENTER;
            }
            textCell.Colspan = cosSpan;
            textCell.FixedHeight = fixedHeight;//单元格高度
            textCell.PaddingBottom = 0; // 设置底部间距
            textCell.PaddingTop = 10;//设置顶部间距
            table.AddCell(textCell);
        }
        private static void AddTitle(Document document, string title, Font font)
        {
            Paragraph paragraph = new Paragraph(title, font);
            paragraph.Alignment = Element.ALIGN_CENTER;
            document.Add(paragraph);
        }
        private static void AddTable(Document document, AS201InspectionReport report, Font font,out int fullRowCount)
        {
            fullRowCount = 0;//填充行数
            int titleIndex = 1;
            // 添加表头
            PdfPTable tablehead = new PdfPTable(new float[] { 1.5f, 1f, 1.5f, 1f });
            tablehead.WidthPercentage = 100;
            tablehead.SpacingBefore = 30;

            AddTableCells(tablehead, "产品型号(Product Model)", font);
            AddTableCells(tablehead, report.ProductModel, font);
            AddTableCells(tablehead, "固件版本(Firmware Version)", font);
            AddTableCells(tablehead, report.FirmwareVersion, font);

            AddTableCells(tablehead, "产品 ID(SN)", font);
            AddTableCells(tablehead, report.SN, font);
            AddTableCells(tablehead, "检测平台", font);
            AddTableCells(tablehead, report.InspectionTurntable, font);

            AddTableCells(tablehead, "检测人员", font);
            AddTableCells(tablehead, report.Inspectors, font);
            AddTableCells(tablehead, "检测日期", font);
            AddTableCells(tablehead, report.DetectionDate.ToString("yyyy-MM-dd HH:mm"), font);

            document.Add(tablehead);
            if (report.AccelerationZeroBias != null)
            {
                if (report.AccelerationZeroBias.X != 0 && report.AccelerationZeroBias.Y != 0 && report.AccelerationZeroBias.Z != 0)
                {
                    // 添加零偏信息
                    PdfPTable tableZero = new PdfPTable(new float[] { 1f, 1.2f, 1f, 1.2f, 1f, 1.2f });
                    tableZero.WidthPercentage = 100;

                    AddTableCells(tableZero, $"{titleIndex}.零偏信息(Acceleration Zero Bias)", font, 6);
                    AddTableCells(tableZero, "X 轴(X Axis)", font);
                    AddTableCells(tableZero, report.AccelerationZeroBias?.X.SafeString(), font);
                    AddTableCells(tableZero, "Y 轴(Y Axis)", font);
                    AddTableCells(tableZero, report.AccelerationZeroBias?.Y.SafeString(), font);
                    AddTableCells(tableZero, "Z 轴(Z Axis)", font);
                    AddTableCells(tableZero, report.AccelerationZeroBias?.Z.SafeString(), font);

                    document.Add(tableZero);
                    titleIndex++;
                }
                else
                {
                    //记录填充行
                    fullRowCount += 2;
                }
            }
            else
            {
                fullRowCount += 2;
            }




            if (report.GyroscopeCalibrationPolynomial != null)
            {
                // 添加标定信息
                PdfPTable tableFlag = new PdfPTable(new float[] { 1f, 1.6f, 1.6f, 1.6f });
                tableFlag.WidthPercentage = 100;
                AddTableCells(tableFlag, $"{titleIndex}.标定信息(Gyroscope Calibration Polynomial)", font, 4);

                AddTableCells(tableFlag, "X 轴(X Axis)", font);
                foreach (var item in report.GyroscopeCalibrationPolynomial?.X ?? new List<double>())
                {
                    AddTableCells(tableFlag, item.SafeString(), font);
                }

                AddTableCells(tableFlag, "Y 轴(Y Axis)", font);
                foreach (var item in report.GyroscopeCalibrationPolynomial?.Y ?? new List<double>())
                {
                    AddTableCells(tableFlag, item.SafeString(), font);
                }

                AddTableCells(tableFlag, "Z 轴(Z Axis)", font);
                foreach (var item in report.GyroscopeCalibrationPolynomial?.Z ?? new List<double>())
                {
                    AddTableCells(tableFlag, item.SafeString(), font);
                }

                document.Add(tableFlag);
                titleIndex++;
            }
            else
            {
                fullRowCount += 4;
            }


            // 添加检测数据
            PdfPTable tableTest = new PdfPTable(5);
            tableTest.WidthPercentage = 100;

            AddTableCells(tableTest, $"{titleIndex}.检测数据(Angle Detection Data)", font, 5);
            AddTableCells(tableTest, "转台角度\n(Turntable's Angle)", font, 1, 45);
            AddTableCells(tableTest, "X 轴采集角度\n(Angle of X-axis)", font, 1, 45);
            AddTableCells(tableTest, "X 轴偏差值\n(Offset of X-axis)", font, 1, 45);
            AddTableCells(tableTest, "Y 轴采集角度\n(Angle of Y-axis)", font, 1, 45);
            AddTableCells(tableTest, "Y 轴偏差值\n(Offset of Y-axis)", font, 1, 45);

            Random random = new Random();
            foreach (var item in report.AngleDetectionData)
            {
                AddTableCells(tableTest, item.TumtablesAngle.SafeString(), font);//转台角度

                double OffsetOfXaxis = item.XReference - item.AngleOfXaxis;//计算片差值
                item.OffsetOfXaxis = GetDeviationValue(OffsetOfXaxis);//调整偏差值在±0.2范围内
                item.AngleOfXaxis = item.TumtablesAngle + item.OffsetOfXaxis;//计算采集角度，转台角度+偏差值
                AddTableCells(tableTest, item.AngleOfXaxis.SafeString(), font);//X轴采集角度
                AddTableCells(tableTest, item.OffsetOfXaxis.SafeString(), font);//X轴偏差值

                double OffsetOfYaxis = item.YReference - item.AngleOfYaxis;
                item.OffsetOfYaxis = GetDeviationValue(OffsetOfYaxis);
                item.AngleOfYaxis = item.TumtablesAngle + item.OffsetOfYaxis;//计算采集角度，转台角度+偏差值
                AddTableCells(tableTest, item.AngleOfYaxis.SafeString(), font);//Y轴采集角度
                AddTableCells(tableTest, item.OffsetOfYaxis.SafeString(), font);//Y轴偏差值
            }
            fullRowCount += (7 - report.AngleDetectionData.Count); // 7行表头和数据行，减去实际数据行数` 

            //for (int i = 0; i < fullRowCount; i++)//填充空白区域，保持打印效果完整
            //{
            //    AddTableCells(tableTest, "", font);
            //    AddTableCells(tableTest, "", font);
            //    AddTableCells(tableTest, "", font);
            //    AddTableCells(tableTest, "", font);
            //    AddTableCells(tableTest, "", font);
            //}
            document.Add(tableTest);

            // 添加检测结果
            PdfPTable tableResult = new PdfPTable(4);
            tableResult.WidthPercentage = 100;

            AddTableCells(tableResult, "检测结果(Inspection Result)", font, 4);
            AddTableCells(tableResult, "合格(PASS)", font);
            AddTableCells(tableResult, "√", font);
            AddTableCells(tableResult, "不合格(FAIL)", font);
            AddTableCells(tableResult, "", font);

            document.Add(tableResult);

        }
        private static void AddFooter(Document document, string companyName, Font font)
        {
            Paragraph footerParagraph = new Paragraph();
            footerParagraph.Add(new Phrase(companyName, font));
            footerParagraph.Alignment = Element.ALIGN_RIGHT;
            footerParagraph.SpacingBefore = 6; // 设置尾部落款前的间距
            document.Add(footerParagraph);
        }
        // 添加链接
        private static void AddLink(Document document, Dictionary<string, string> linkDict, Font font)
        {
            // 创建链接字体样式
            Font linkFont = new Font(font.BaseFont, font.Size, Font.UNDERLINE);
            linkFont.Color = BaseColor.BLUE;

            // 创建一个表格来放置链接
            PdfPTable linkTable = new PdfPTable(1);
            linkTable.WidthPercentage = 100;
            //linkTable.SpacingBefore = fullRowCount * 30f;
            // 创建一个锚点单元格
            foreach (var item in linkDict)
            {
                Anchor anchor = new Anchor(item.Key + "：" + item.Value, linkFont);
                anchor.Reference = item.Value;
                PdfPCell linkCell = new PdfPCell(new Phrase(anchor));
                linkCell.Border = PdfPCell.NO_BORDER;
                linkCell.HorizontalAlignment = Element.ALIGN_LEFT;
                linkCell.Padding = 10;
                // 将单元格添加到表格
                linkTable.AddCell(linkCell);
            }
            // 将表格添加到文档
            document.Add(linkTable);
        }
        //进行数字签名
        private static void AddDigitalSignature(string sourceFile, string destFile, string certPath, string certPassword, string signatureImagePath,int fullRowCount)
        {
            try
            {
                if (!File.Exists(sourceFile))
                {
                    XTrace.WriteLine("源PDF文件不存在：" + sourceFile);
                    throw new FileNotFoundException("源PDF文件不存在", sourceFile);
                }


                // 验证证书文件是否存在
                if (!File.Exists(certPath))
                {
                    XTrace.WriteLine("证书文件不存在：" + certPath);
                    throw new FileNotFoundException("证书文件不存在", certPath);
                }

                using (PdfReader reader = new PdfReader(sourceFile))
                using (FileStream os = new FileStream(destFile, FileMode.Create))
                using (FileStream fs = new FileStream(certPath, FileMode.Open, FileAccess.Read, FileShare.Read))
                {
                    // 加载证书
                    Pkcs12Store pk12 = new Pkcs12StoreBuilder().Build();
                    pk12.Load(fs, certPassword.ToCharArray());

                    string alias = pk12.Aliases.Cast<string>().FirstOrDefault(a => pk12.IsKeyEntry(a));
                    if (alias == null)
                    {
                        throw new Exception("证书文件中未找到有效的密钥条目");
                    }
                    AsymmetricKeyParameter pk = pk12.GetKey(alias).Key;
                    X509CertificateEntry[] chain = pk12.GetCertificateChain(alias);
                    X509Certificate[] certChain = new X509Certificate[chain.Length];
                    for (int i = 0; i < chain.Length; i++)
                    {
                        certChain[i] = new X509Certificate(chain[i].Certificate.GetEncoded());
                    }

                    // 创建签名外观
                    PdfStamper stamper = PdfStamper.CreateSignature(reader, os, '\0');
                    // stamper.MoreInfo = new Dictionary<string, string>();
                    // stamper.MoreInfo["SignaturePosition"] = "1"; // 确保签名在最上层

                    PdfSignatureAppearance appearance = stamper.SignatureAppearance;
                    // 设置签名信息
                    //appearance.Reason = "Document Certification";
                    //appearance.Location = "Shenzhen, China";
                    // 设置签名位置（右下角，与落款对齐）
                    Rectangle pageSize = reader.GetPageSize(reader.NumberOfPages);
                    float documentHeight = pageSize.Height;

                    // 计算落款位置（假设落款在文档底部，距离底部100像素）
                    float signatureY = 100; // 默认值
                    float footerHeight = 65; // 落款内容高度估计值

                    // 动态调整Y坐标，确保印章在落款上方 fullRowCount空行数
                    signatureY = footerHeight + (fullRowCount * 30);

                    // 设置签名位置（右下角）
                    float x = pageSize.Right - 150;  // 距离右边150像素
                    Rectangle rect = new Rectangle(x, signatureY, x + 145, signatureY + 121);
                    appearance.SetVisibleSignature(rect, reader.NumberOfPages, "HLK");

                    // 添加签章图片
                    if (!string.IsNullOrEmpty(signatureImagePath) && File.Exists(signatureImagePath))
                    {
                        Image signatureImage = Image.GetInstance(signatureImagePath);
                        //signatureImage.ScaleToFit(145, 121); // 调整图片大小
                        // 确保图片使用原始透明度
                        signatureImage.Transparency = new int[] { 0x00, 0xFF }; // 设置透明范围
                        appearance.SignatureGraphic = signatureImage;
                        appearance.SignatureRenderingMode = PdfSignatureAppearance.RenderingMode.GRAPHIC;
                    }
                    else
                    {
                        XTrace.WriteLine("签章图片不存在");
                        throw new Exception("签章图片不存在");
                    }

                    // 创建签名
                    IExternalSignature signature = new PrivateKeySignature(pk, "SHA-256");
                    MakeSignature.SignDetached(
                        appearance,
                        signature,
                        certChain,
                        null,
                        null,
                        null,
                        0,
                        CryptoStandard.CMS
                    );
                }
            }
            catch (Exception ex)
            {
                XTrace.WriteLine($"数字签名过程中发生错误: {ex}");
                throw;
            }
        }
        /// <summary>
        /// 获取偏差值
        /// </summary>
        /// <returns></returns>
        private static double GetDeviationValue(double num)
        {
            if (num > -0.2 && num < 0.2)
            {
                return Math.Round(num, 3);
            }
            return GetDeviationValue(num / 2);
        }
    }
}
