﻿using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

namespace HlktechIoT.Dto.Export;

[ExcelExporter(Name = "Sheet1", AutoFitAllColumn = true, TableStyle = OfficeOpenXml.Table.TableStyles.None, AutoCenter = true)]
public class InventoryAlarmExport
{
    /// <summary>
    /// 物料编号
    /// </summary>
    [ExporterHeader(DisplayName = "物料编号", IsBold = false)]
    public string? MaterialNumber { get; set; }

    /// <summary>
    /// 物料名称
    /// </summary>
    [ExporterHeader(DisplayName = "物料名称", IsBold = false)]
    public string? MaterialName { get; set; }

    /// <summary>
    /// 种类
    /// </summary>
    [ExporterHeader(DisplayName = "种类", IsBold = false)]
    public string? Category { get; set; }

    /// <summary>
    /// 规格型号
    /// </summary>
    [ExporterHeader(DisplayName = "规格型号", IsBold = false)]
    public string? SpecificationModel { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [ExporterHeader(DisplayName = "单位", IsBold = false)]
    public string? Unit { get; set; }

    /// <summary>
    /// 仓库编号
    /// </summary>
    [ExporterHeader(DisplayName = "仓库编号", IsBold = false)]
    public string? WarehouseNumber { get; set; }

    /// <summary>
    /// 仓库名称
    /// </summary>
    [ExporterHeader(DisplayName = "仓库名称", IsBold = false)]
    public string? WarehouseName { get; set; }

    /// <summary>
    /// 告警数量
    /// </summary>
    [ExporterHeader(DisplayName = "告警数量", IsBold = false)]
    public int AlarmQuantity { get; set; }

    /// <summary>
    /// 可用数
    /// </summary>
    [ExporterHeader(DisplayName = "可用数", IsBold = false)]
    public int AvailableQuantity { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [ExporterHeader(DisplayName = "创建时间", IsBold = false, Format = "yyyy-MM-dd HH:mm:ss", Width = 20)]
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [ExporterHeader(DisplayName = "更新时间", IsBold = false, Format = "yyyy-MM-dd HH:mm:ss", Width = 20)]
    public DateTime UpdateTime { get; set; }
}
